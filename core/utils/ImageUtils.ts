import axios from 'axios';
import imageSize from 'image-size';
import { ISizeCalculationResult } from 'image-size/dist/types/interface';
import * as ExcelJS from 'exceljs';
const sharp = require('sharp');

async function downloadImage(url: string): Promise<Buffer> {
  try {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    let sharpInstance = sharp(response.data);

    const metadata = await sharpInstance.metadata();
    let currentWidth = metadata.width || 0;

    if (currentWidth > 80) {
      sharpInstance = sharpInstance.resize(80, undefined, {
        fit: 'inside',
        withoutEnlargement: true,
      });
    }
    const compressedBuffer = await sharpInstance.jpeg({ quality: 70 }).toBuffer();
    return compressedBuffer;
  } catch (error) {
    console.error(`Không thể tải hình ảnh từ URL: ${url}`);
    return null;
  }
}

async function getImageDimensions(imageBuffer: Buffer): Promise<ISizeCalculationResult> {
  return new Promise((resolve, reject) => {
    try {
      const dimensions = imageSize(imageBuffer);
      if (!dimensions || dimensions.width === undefined || dimensions.height === undefined) {
        reject(new Error('Không thể xác định kích thước hình ảnh'));
        return;
      }
      resolve(dimensions);
    } catch (error) {
      reject(error);
    }
  });
}

export async function saveImageToExcel(
  imagesArray: any[],
  workbook: ExcelJS.Workbook,
  worksheet: ExcelJS.Worksheet,
  imageColumn: number,
) {
  const imagePromises = imagesArray.map(async item => {
    const imageBuffer = await downloadImage(item.image);
    if (!imageBuffer) {
      return { imageId: null, width: null, height: null, rowIndex: item.rowIndex };
    }
    const dimensions = await getImageDimensions(imageBuffer);

    if (!dimensions.width || !dimensions.height) {
      console.log('Kích thước hình ảnh không hợp lệ');
      return { imageId: null, width: null, height: null, rowIndex: item.rowIndex };
    }

    const width = dimensions.width;
    const height = dimensions.height;
    const imageId = workbook.addImage({
      buffer: imageBuffer,
      extension: 'jpeg',
    });

    return { imageId, width, height, rowIndex: item.rowIndex };
  });

  const imageResults = await Promise.allSettled(imagePromises);

  imageResults.forEach(result => {
    if (result.status === 'fulfilled' && result.value.imageId != null) {
      const { imageId, width, height, rowIndex } = result.value;
      worksheet.addImage(imageId, {
        tl: { col: imageColumn - 1, row: rowIndex - 1 },
        ext: { width, height },
      });
      worksheet.getRow(rowIndex).height = height + 2;
    } else {
      console.error('Error adding image', result);
    }
  });
}
