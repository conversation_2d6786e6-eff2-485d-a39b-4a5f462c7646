import axios from 'axios';
import * as cheerio from 'cheerio';
import { isEmpty } from 'lodash';

export default class PageUtils {
  public static getPageName = async (pageId: string) => {
    const rawResponse = await axios.get('https://facebook.com/' + pageId);

    let name = '';

    if (rawResponse.data) {
      const $ = cheerio.load(rawResponse.data);
      const title = $('title').text();
      name = title;
    }

    return name;
  };

  public static getPageAvatar = (pageId: string) => {
    return `http://graph.facebook.com/${pageId}/picture?type=square&width=512`;
  };
}
