import { OpenAISafeTokens, OpenAIModel } from 'core/enums/open-ai.enum';
import { encoding_for_model } from 'tiktoken';

export default class OpenAIUtils {
  static encMap = {
    [OpenAIModel.GPT_3_5_TURBO]: encoding_for_model(OpenAIModel.GPT_3_5_TURBO),
    [OpenAIModel.GPT_4]: encoding_for_model(OpenAIModel.GPT_4),
    [OpenAIModel.GPT_4_o]: encoding_for_model(OpenAIModel.GPT_4_o),
  };

  static countTokens(text: string, model: OpenAIModel = OpenAIModel.GPT_4): number {
    return this.encMap[model].encode(text).length;
  }

  static splitConversationByTokenLimit(
    conversation,
    maxTokens = OpenAISafeTokens[OpenAIModel.GPT_4],
    model: OpenAIModel = OpenAIModel.GPT_4,
  ) {
    const chunks = [];
    let currentChunk = [];
    let currentTokenCount = 0;

    for (const msg of conversation) {
      const tokenCount = this.countTokens(`${msg.role}: ${msg.content}`, model);
      if (currentTokenCount + tokenCount > maxTokens) {
        currentChunk = [msg];
        currentTokenCount = tokenCount;
      } else {
        currentChunk.push(msg);
        currentTokenCount += tokenCount;
      }
    }

    if (currentChunk.length > 0) chunks.push(currentChunk);
    return chunks;
  }
}
