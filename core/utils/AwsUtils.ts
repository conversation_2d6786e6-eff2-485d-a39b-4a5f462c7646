import { S3 } from 'aws-sdk';
import * as mime from 'mime-types';
import * as crypto from 'crypto';
import { TYPE_DB } from 'apps/facebook-bot/src/constants/message-parse.constants';
export class AwsUtils {
  private static s3Instance: S3;

  private static getS3Instance() {
    if (!this.s3Instance) {
      this.s3Instance = new S3({
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        endpoint: `https://${process.env.AWS_ACCOUNT_ID}.r2.cloudflarestorage.com`,
        region: 'auto',
        signatureVersion: 'v4',
      });
    }

    return this.s3Instance;
  }

  public static uploadS3(buffer, mimetype, name) {
    const s3 = this.getS3Instance();
    const params: S3.PutObjectRequest = {
      Bucket: process.env.AWS_BUCKET,
      Key: String(name),
      Body: buffer,
      // ContentEncoding: 'base64',
      ContentType: mimetype,
      // ContentDisposition: 'attachment', // Add this header to force download
      //   ACL: 'public-read',
    };
    return new Promise<string>((resolve, reject) => {
      s3.upload(params, (err, data) => {
        if (err) {
          console.log('uploadS3 error', err);
          reject(err);
          return;
        }
        resolve(`${process.env.AWS_CDN_URL}/${data['Key']}`);
      });
    });
  }

  public static async getPreSignUrl(
    file,
  ): Promise<{
    presignUrl: string;
    type: string;
    key: string;
    url: string;
  }> {
    const s3 = this.getS3Instance();
    const type = mime.contentType(file?.originalname) || 'application/octet-stream'; // Default to binary if MIME type is not found.
    const buffer = Buffer.from(file.buffer, 'binary');
    const key = crypto
      .createHash('md5')
      .update(buffer.toString())
      .digest('hex');
    const filename = `${process.env.SERVICE_NAME}/${key}.${TYPE_DB[file.mimetype]?.extensions[0]}`;
    try {
      const presignUrl = await s3.getSignedUrlPromise('putObject', {
        Bucket: process.env.AWS_BUCKET,
        Key: filename,
        Expires: 5 * 60, // 5 minutes
        ContentType: type,
      });

      return { presignUrl, type, key: filename, url: `${process.env.AWS_CDN_URL}/${filename}` };
    } catch (err) {
      console.error('getPreSignUrl error:', err);
      throw err;
    }
  }

  public static hasObject(key: string) {
    const s3 = this.getS3Instance();
    const params: S3.HeadObjectRequest = {
      Bucket: process.env.AWS_BUCKET,
      Key: key,
    };
    return new Promise((resolve, reject) => {
      s3.headObject(params, (err, data) => {
        resolve(err ? false : true);
      });
    });
  }
}
