import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable } from '@nestjs/common';
import { TUserRole } from 'core/enums/sale-permissions';
import { TFfmUserRole } from 'core/enums/ffm-permissions';
import { isEmpty, isNull } from 'lodash';

@Injectable()
export class AuthService {
  constructor(private readonly amqpConnection: AmqpConnection) {
    if (process.env.RMQ_IDENTITY_HOST && process.env.RMQ_IDENTITY_HOST.length > 0) {
      this.amqpConnection = new AmqpConnection({
        uri: process.env.RMQ_IDENTITY_HOST,
      });
      this.initConnection();
    }
  }

  private async initConnection() {
    try {
      await this.amqpConnection.init();
      console.log('Custom AMQP connection established');
    } catch (error) {
      console.error('Failed to establish custom AMQP connection', error);
    }
  }

  // async checkCompany(id: number): Promise<Company> {
  //   const data = await this.amqpConnection.getData(
  //     'identity-service-companies',
  //     'get-company-by-id',
  //     { id },
  //   );
  //   return data;
  // }

  async checkUserProfiles(
    uid: number,
    requiredPermissions: (TUserRole | TFfmUserRole)[],
    countryId?: string,
    projectIds?: string[],
  ) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service',
      routingKey: 'check-user-profiles',
      payload: { id: uid },
      timeout: 10000,
    });

    return data.filter((record, idx) => {
      const [, , scopes, userPermissions, profileType, departmentId] = record;
      data[idx][4] = scopes;
      data[idx][5] = profileType;
      data[idx][6] = departmentId;
      // console.log(`scopes`, scopes)
      if (countryId) {
        if (!projectIds) return false;

        // const validScopes = scopes.filter(
        //   sc =>
        //     (sc[0] === null || sc[0] === countryId) &&
        //     (sc[1] === null || projectIds.includes(sc[1])),
        // );
        let hasAllProjects = false;
        let validScopes = [];
        validScopes = scopes.reduce((prev, sc) => {
          if (!isNull(sc[0]) && String(sc[0]) !== countryId) return prev;
          if (sc[1] === null) hasAllProjects = true;
          prev.push([countryId, sc[1]]);
          return prev;
        }, []);

        if (hasAllProjects) validScopes = projectIds.map(p => [countryId, p]);

        if (isEmpty(validScopes)) return false;
        data[idx][2] = validScopes;
      }

      if (isEmpty(requiredPermissions)) return true;

      for (const perm of requiredPermissions) {
        const idx = perm[0];
        const hasPerm = userPermissions[idx] & perm[1];

        // console.log(`has perm`, hasPerm);

        if (hasPerm) return true;
      }
      return false;
    });
  }

  async checkUserDescendants(id: number) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service',
      routingKey: 'check-user-descendants',
      payload: { id },
      timeout: 10000,
    });
    return data;
  }

  async checkNewPermissions(id: number) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-module-permissions',
      payload: { userId: id },
      timeout: 10000,
    });
    return data;
  }

  async checkNewScopes(id: number) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-new-scopes',
      payload: { id },
      timeout: 10000,
    });
    return data;
  }

  async checkScopes(id: number) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-scopes',
      payload: { id },
      timeout: 10000,
    });
    return data;
  }

  async checkRoles(role: string, id: number) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-roles',
      payload: { id, role },
      timeout: 10000,
    });
    return data;
  }

  async checkActive(id: number, sid?: string): Promise<-1 | 0 | 1> {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-active',
      payload: { id, sid },
      timeout: 10000,
    });
    return data;
  }

  async checkWarehouse(id: number) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-warehouse',
      payload: { id },
      timeout: 10000,
    });
    return data;
  }

  async checkRoot(id: number) {
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-root',
      payload: { id },
      timeout: 10000,
    });
    return data > 0;
  }
}
