export enum Permission {
  // -----------------General-----------------------
    createOrder                       = 0b100000,
    order                             = 0b1000000,
    // Khách hàng
    customer                          = 0b1000000000,
    product                           = 0b10000000000,
    warehouse                         = 0b100000000000, // quản lý tồn kho | Kho bên FFM
    import                            = 0b1000000000000, // nhập kho
    export                            = 0b10000000000000, // xuất kho
    stockTaking                       = 0b100000000000000, // kiểm kê hàng hóa
    // Quản lý nhân viên
    manageUsers                       = 0b100000000000000000000,
    manageCountries                   = 0b10000000000000000000000,
    manageRoles                       = 0b10000000000000000000000000,
    configShipping                    = 0b100000000000000000000000000000000,
    configBilling                     = 0b10000000000000000000000000000000000,
    createWarehouse                   = 0b10000000000000000000000000000000000000,

  // IMPORTANT !! Tách quyền AG vs FFM.


  // -----------------AG SALE-----------------------

    // Dashboard
    ordersReport                        = 0b1,
    carePageManagerReport             = 0b10,
    carePageStaffReport               = 0b100,
    // Sale
    marketing                         = 0b1000,
    chat                              = 0b10000,
    // Order
    orderDraft                        = 0b10000000,
    telesalesLeads                    = 0b100000000,
    returningChecking                 = 0b1000000000000000,
    // Marketing automation
    manageBots                        = 0b10000000000000000,
    managePages                       = 0b100000000000000000,
    manageCampaigns                   = 0b1000000000000000000,
    automationConfig                  = 0b10000000000000000000,
    manageShifts                      = 0b1000000000000000000000,
    manageProjects                    = 0b100000000000000000000000,
    manageTeams                       = 0b1000000000000000000000000,
    configReportReasons               = 0b100000000000000000000000000,
    configCancelReasons               = 0b1000000000000000000000000000,
    configOrderSources                = 0b10000000000000000000000000000,
    configTags                        = 0b100000000000000000000000000000,
    configCareFailReasons             = 0b1000000000000000000000000000000,
    configPrintNotes                  = 0b10000000000000000000000000000000,
    configPartners                    = 0b1000000000000000000000000000000000,
    manageTransactions                = 0b100000000000000000000000000000000000,
    telesalesManualDistribution       = 0b1000000000000000000000000000000000000,
    telesalesAutoDistribution         = 0b100000000000000000000000000000000000000,
    telesalesReport                   = 0b1000000000000000000000000000000000000000,
    telesalesManagerReport            = 0b10000000000000000000000000000000000000000,
    telesalesConfigurations           = 0b100000000000000000000000000000000000000000,
    configLandingPage                 = 0b1000000000000000000000000000000000000000000,
    superAdmin                        = 0b10000000000000000000000000000000000000000000,
    carePageSettings                  = 0b100000000000000000000000000000000000000000000,
    marketingManagerReport            = 0b1000000000000000000000000000000000000000000000,
    // orderProblem          = 0b10000000000000000000000000000000000000000000000,
    integrateServices                 = 0b100000000000000000000000000000000000000000000000,
    // bulk                  = 0b1000000000000000000000000000000000000000000000000,
    // bulkExport            = 0b10000000000000000000000000000000000000000000000000,
    // bulkStatus            = 0b100000000000000000000000000000000000000000000000000,
    // bulk3PL               = 0b1000000000000000000000000000000000000000000000000000,
    // bulkPrint             = 0b10000000000000000000000000000000000000000000000000000,
    // bulkRequest3pl        = 0b100000000000000000000000000000000000000000000000000000,
    // pancakeSyncOrders     = 0b1000000000000000000000000000000000000000000000000000000,
    // importProduct         = 0b10000000000000000000000000000000000000000000000000000000,
    // orderAllocationRule   = 0b100000000000000000000000000000000000000000000000000000000,
    // multiWarehouse        = 0b1000000000000000000000000000000000000000000000000000000000,
    // carrier3pl            = 0b10000000000000000000000000000000000000000000000000000000000,
    // reason                = 0b1000000000000000000000000000000000000000000000000000000000000,
    // ptView                = 0b10000000000000000000000000000000000000000000000000000000000000,
    // ptManager             = 0b100000000000000000000000000000000000000000000000000000000000000,
}
