import { AccountPermission } from './account-permission.enum';
import { BotManagementPermission } from './bot-management-permission.enum';
import { CampaignPermission } from './campaign-permission.enum';
import { CancelReasonPermission } from './cancel-reason-permission.enum';
import { CarePagePermission } from './care-page-permission.enum';
import { CountryPermission } from './country-permission.enum';
import { CurrencyPermission } from './currency-permission.enum';
import { CustomerPermission } from './customer-permission.enum';
import { DashboardPermission } from './dashboard-permission.enum';
import { CallCenterPermission } from './callcenter-permission.enum';
import { FanPagePermission } from './fanpage-permission.enum';
import { FfmSettingPermission } from './ffm-setting-permission.enum';
import { InboundPermission } from './inbound-permission.enum';
import { InventoryPermission } from './inventory-permission.enum';
import { MarketplacePermission } from './marketplace-permission.enum';
import { OrderPermission } from './order-permission.enum';
import { OrderSourcePermission } from './order-source-permission.enum';
import { OrgPermission } from './org-permission.enum';
import { OutboundPermission } from './outbound-permission.enum';
import { PrintNotePermission } from './print-note-permission.enum';
import { ProductPermission } from './product-permission.enum';
import { ProjectPermission } from './project-permission.enum';
import { ReportReasonPermission } from './report-reason-permission.enum';
import { ReturnHandlingPermission } from './return-handling-permission.enum';
import { RolePermission } from './role-permission.enum';
import { ShiftPermission } from './shift-permission.enum';
import { StocktakingPermission } from './stocktaking-permission.enum';
import { SupplierPermission } from './supplier-permission.enum';
import { TagPermission } from './tag-permission.enum';
import { TelesalesPermission } from './telesales-permission.enum';
import { TranslationPermission } from './translation-permission.enum';
import { AfterSalesLeadPermission } from './after-sales-lead.permission.enum';
import { AnalyticsAccountPermission } from './analytics-account-permission.enum';
import { IntegrationPermission } from './integration-permission.enum';
import { TestFeaturePermission } from './test-feature-permission';

export enum SalePermission {
  dashboard, // Báo cáo
  order, // Order Management
  customer, // Khách hàng
  product, // Sản phẩm
  supplier, // Nhà cung cấp
  inventory, // Quản lý tồn kho
  inbound, // Nhập kho
  outbound, // Xuất kho
  stocktaking, // Kiểm kê hàng hoá
  returnHandling, // Kiểm kê hàng hoàn
  telesales, // TeleSales
  carePage, // CarePage
  botManagement, // Quản lý bot
  fanPages, // Quản lý fan pages
  campaigns, // Quản lý chiến dịch
  projects, // Cài đặt dự án
  countries, // Cài đặt thị trường
  accounts, // Cài đặt tài khoản
  roles, // Cài đặt vai trò/ quyền
  org, // Sơ đồ hoạt động
  shift, // Ca làm việc
  tags, // Thẻ tag
  orderSource, // Nguồn đơn hàng
  reportReason, // Lý do báo xấu
  cancelReason, // Lý do hoàn/ huỷ đơn
  printNotes, // Ghi chú in đơn
  translation, // Cấu hình dịch
  fulfillment, // Cài đặt cấu hình FFM
  marketplace, // Marketplace
  currency, // Cấu hình tiền tệ
  afterSalesLead, // Chăm sóc hậu mãi
  analyticsAccount, // Chăm sóc hậu mãi
  integration, // Tích hợp
  testFeature, // Test feature
}

export const ENUM_PERMISSION_MAPPING: Record<SalePermission, any> = {
  [SalePermission.dashboard]: DashboardPermission,
  [SalePermission.order]: OrderPermission,
  [SalePermission.customer]: CustomerPermission,
  [SalePermission.product]: ProductPermission,
  [SalePermission.supplier]: SupplierPermission,
  [SalePermission.inventory]: InventoryPermission,
  [SalePermission.inbound]: InboundPermission,
  [SalePermission.outbound]: OutboundPermission,
  [SalePermission.stocktaking]: StocktakingPermission,
  [SalePermission.returnHandling]: ReturnHandlingPermission,
  [SalePermission.telesales]: TelesalesPermission,
  [SalePermission.carePage]: CarePagePermission,
  [SalePermission.botManagement]: BotManagementPermission,
  [SalePermission.fanPages]: FanPagePermission,
  [SalePermission.campaigns]: CampaignPermission,
  [SalePermission.projects]: ProjectPermission,
  [SalePermission.countries]: CountryPermission,
  [SalePermission.accounts]: AccountPermission,
  [SalePermission.roles]: RolePermission,
  [SalePermission.org]: OrgPermission,
  [SalePermission.shift]: ShiftPermission,
  [SalePermission.tags]: TagPermission,
  [SalePermission.orderSource]: OrderSourcePermission,
  [SalePermission.reportReason]: ReportReasonPermission,
  [SalePermission.cancelReason]: CancelReasonPermission,
  [SalePermission.printNotes]: PrintNotePermission,
  [SalePermission.translation]: TranslationPermission,
  [SalePermission.fulfillment]: FfmSettingPermission,
  [SalePermission.marketplace]: MarketplacePermission,
  [SalePermission.currency]: CurrencyPermission,
  [SalePermission.integration]: IntegrationPermission,
  [SalePermission.afterSalesLead]: AfterSalesLeadPermission,
  [SalePermission.analyticsAccount]: AnalyticsAccountPermission,
  [SalePermission.testFeature]: TestFeaturePermission,
};

export type PermPairType = typeof ENUM_PERMISSION_MAPPING;

export type TUserRole = {
  [K in keyof PermPairType]: [K, PermPairType[K][keyof PermPairType[K]]];
}[keyof PermPairType];
