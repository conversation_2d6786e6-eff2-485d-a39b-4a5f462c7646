import { registerEnumType } from '@nestjs/graphql';

export enum CarrierCode {
  jtexpress = 'jtexpress',
  ninjavan = 'ninjavan',
  bestexpress = 'bestexpress',
  flashexpress = 'flashexpress',
  kerryexpress = 'kerryexpress',
  nimbus = 'nimbus',
  poslaju = 'poslaju',
  jneexpress = 'jneexpress',
  bluedart = 'bluedart',
  anousith = 'anousith',
  hal = 'hal',
  lalamove = 'lalamove',
  wedo = 'wedo',
  system = 'system',
  shopee = 'shopee',
  tiktokshop = 'tiktokshop',
  usadropship = 'usadropship',
  viettelpost = 'viettelpost',
  mixayexpress = 'mixayexpress',
  ghtk = 'ghtk',
  vnpost = 'vnpost',
  kerry = 'kerryexpress',
  spxexpress = 'spxexpress',
  jntbangkok = 'jntbangkok',
  miaoshou = 'miaoshou',
}

export enum CarrierName {
  jtexpress = 'J&T Express',
  ninjavan = 'NJV',
  bestexpress = 'Best',
  flashexpress = 'Flash',
  kerryexpress = 'Kerry',
  nimbus = 'Nimbus',
  poslaju = 'POS Laju',
  jneexpress = 'JNE express',
  bluedart = 'Blue Dart',
  anousith = 'Anousith',
  hal = 'HAL',
  lalamove = 'LalaMove',
  wedo = 'Wedo Express',
  system = 'System Carrier',
  shopee = 'Shopee',
  tiktokshop = 'Tiktok Shop',
  usadropship = 'USA DropShip',
  viettelpost = 'Viettel Post',
  mixayexpress = 'Mixay Express',
  ghtk = 'GHTK',
  vnpost = 'VN Post',
  spxexpress = 'Shopee Express',
  jntbangkok = 'J&T Bangkok',
  miaoshou = 'Miaoshou',
}

export enum CarrierType {
  auto = 1,
  semiAuto = 2,
  manual = 3,
}

registerEnumType(CarrierCode, {
  name: 'CarrierCode',
});

export enum CountryID {
  TH = 66,
  PH = 63,
  MY = 60,
  IN = 91,
  VN = 84,
  MM = 95,
  ID = 62,
  LA = 856,
  US = 1,
  SG = 65,
  CN = 86,
}
