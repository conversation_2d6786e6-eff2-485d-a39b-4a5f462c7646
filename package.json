{"name": "bigdeal", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "NODE_ENV=production nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:catalog-api": "nest start --watch catalog-api", "start:indentity-api": "nest start --watch indentity-api", "start:facebook-bot": "nest start --watch facebook-bot", "start:order-api": "nest start --watch order-api", "start:identity-api": "nest start --watch identity-api", "start:analytics-api": "nest start --watch analytics-api", "start:ffm-order-api": "nest start --watch ffm-order-api", "start:ffm-catalog-api": "nest start --watch ffm-catalog-api", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "migration:generate:identity": "yarn build identity-api && yarn typeorm migration:generate -n migration -f apps/identity-api/typeorm-config.ts", "migration:revert:identity": "export NODE_ENV=staging && yarn build && yarn typeorm migration:revert -f apps/identity-api/typeorm-config.ts", "migration:run:identity": "yarn build identity-api && yarn typeorm migration:run -f apps/identity-api/typeorm-config.ts", "migration:generate:catalog": "yarn build catalog-api && yarn typeorm migration:generate -n migration -f apps/catalog-api/typeorm-config.ts", "migration:revert:catalog": "export NODE_ENV=staging && yarn build catalog-api && yarn typeorm migration:revert -f apps/catalog-api/typeorm-config.ts", "migration:run:catalog": "yarn build catalog-api && yarn typeorm migration:run -f apps/catalog-api/typeorm-config.ts", "migration:generate:order": "yarn build order-api && yarn typeorm migration:generate -n migration -f apps/order-api/typeorm-config.ts", "migration:revert:order": "export NODE_ENV=staging && yarn build order-api && yarn typeorm migration:revert -f apps/order-api/typeorm-config.ts", "migration:run:order": "yarn build order-api && yarn typeorm migration:run -f apps/order-api/typeorm-config.ts", "migration:generate:payment": "yarn build payment-api && yarn typeorm migration:generate -n migration -f apps/payment-api/typeorm-config.ts", "migration:revert:payment": "export NODE_ENV=staging && yarn build && yarn typeorm migration:revert -f apps/payment-api/typeorm-config.ts", "migration:run:payment": "yarn build payment-api && yarn typeorm migration:run -f apps/payment-api/typeorm-config.ts", "migration:generate:message": "yarn build facebook-bot && yarn typeorm migration:generate -n migration -f apps/facebook-bot/typeorm-config.ts", "migration:revert:message": "export NODE_ENV=staging && yarn build facebook-bot && yarn typeorm migration:revert -f apps/facebook-bot/typeorm-config.ts", "migration:run:message": "yarn build facebook-bot && yarn typeorm migration:run -f apps/facebook-bot/typeorm-config.ts", "postinstall": "patch-package", "migration:generate:ffm-order": "yarn build ffm-order-api && yarn typeorm migration:generate -n migration -f apps/ffm-order-api/typeorm-config.ts", "migration:revert:ffm-order": "export NODE_ENV=staging && yarn build ffm-order-api && yarn typeorm migration:revert -f apps/ffm-order-api/typeorm-config.ts", "migration:run:ffm-order": "yarn build ffm-order-api && yarn typeorm migration:run -f apps/ffm-order-api/typeorm-config.ts", "migration:generate:ffm-catalog": "yarn build ffm-catalog-api && yarn typeorm migration:generate -n migration -f apps/ffm-catalog-api/typeorm-config.ts", "migration:revert:ffm-catalog": "export NODE_ENV=staging && yarn build ffm-catalog-api && yarn typeorm migration:revert -f apps/ffm-catalog-api/typeorm-config.ts", "migration:run:ffm-catalog": "yarn build ffm-catalog-api && yarn typeorm migration:run -f apps/ffm-catalog-api/typeorm-config.ts"}, "dependencies": {"@apollo/gateway": "^2.0.1", "@clickhouse/client": "^1.11.0", "@golevelup/nestjs-rabbitmq": "^2.2.0", "@liaoliaots/nestjs-redis": "^8.2.1", "@nestjs-modules/mailer": "^1.7.0", "@nestjs/apollo": "^10.0.9", "@nestjs/bull": "^0.5.4", "@nestjs/common": "^8.2.0", "@nestjs/config": "^1.1.0", "@nestjs/core": "^8.2.0", "@nestjs/graphql": "^10.0.9", "@nestjs/jwt": "^8.0.0", "@nestjs/microservices": "^8.2.0", "@nestjs/mongoose": "^10.0.10", "@nestjs/passport": "^8.0.1", "@nestjs/platform-express": "^8.2.0", "@nestjs/platform-fastify": "^8.2.0", "@nestjs/platform-socket.io": "^8.4.6", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^5.1.5", "@nestjs/terminus": "^8.1.1", "@nestjs/typeorm": "^8.0.2", "@nestjs/websockets": "^8.4.6", "@nlpjs/basic": "^4.27.0", "@nlpjs/core": "^4.26.1", "@nlpjs/lang-en": "^4.26.1", "@nlpjs/nlu": "^4.27.0", "@socket.io/redis-adapter": "^7.2.0", "@types/query-string": "^6.3.0", "apollo-server-express": "^3.6.7", "async_hooks": "^1.0.0", "aws-sdk": "^2.1691.0", "axios": "^0.27.2", "bluebird": "^3.0.0", "bull": "^4.8.1", "bull-board": "1.7.1", "bullmq": "1.20.2", "cache-manager": "^3.6.1", "cache-manager-redis-store": "^2.0.0", "cheerio": "^1.0.0-rc.10", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "convert": "4.14.1", "exceljs": "^4.4.0", "express-basic-auth": "^1.2.1", "fast-xml-parser": "^4.3.2", "firebase-admin": "12.1.1", "form-data": "^4.0.0", "google-libphonenumber": "^3.2.25", "google-spreadsheet": "^3.2.0", "googleapis": "^118.0.0", "graphql": "^16.3.0", "graphql-redis-subscriptions": "^2.4.2", "graphql-subscriptions": "^2.0.0", "graphql-transport-ws": "^1.9.0", "graphql-type-json": "^0.3.2", "graphql-ws": "^5.7.0", "image-size": "^2.0.2", "ioredis": "4.17.3", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.29.1", "mongodb": "^3.7.3", "mongoose": "^8.5.1", "mqtt": "^3.0.0", "mysql": "^2.18.1", "nest-puppeteer": "^1.1.1", "nest-winston": "^1.10.2", "nestjs-redoc": "^2.2.2", "node-2fa": "^2.0.3", "node-excel-export": "^1.4.4", "node-nlp": "^4.27.0", "node-xlsx": "^0.21.0", "nodemailer": "^6.7.5", "numeral": "^2.0.6", "openai": "^4.52.2", "passport": "^0.5.0", "passport-jwt": "^4.0.0", "patch-package": "^6.4.7", "pdf-parse": "^1.1.1", "pg": "^8.7.1", "public-ip": "4.0.4", "puppeteer": "^13.5.1", "qrcode": "^1.5.4", "qs": "^6.10.3", "query-string": "^7.1.1", "redis": "^4.1.0", "reflect-metadata": "^0.1.13", "request": "^2.53.0", "requestretry": "^7.1.0", "rimraf": "^3.0.2", "rxjs": "^7.4.0", "seq-logging": "^2.2.0", "sharp": "0.32.0", "socket.io": "^4.5.1", "swagger-ui-express": "^4.1.6", "tiktoken": "^1.0.21", "ts-enum-util": "^4.0.2", "typeorm": "0.2.45", "typeorm-cursor-pagination": "^0.8.1", "util": "^0.12.4", "uuid": "^8.3.2", "websocket-stream": "^5.5.0", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/cli": "^7.0.0", "@nestjs/schematics": "^7.0.0", "@nestjs/testing": "^7.0.0", "@types/amqplib": "^0.8.2", "@types/axios": "^0.14.0", "@types/bluebird": "^3.5.36", "@types/bull": "^3.15.8", "@types/cache-manager": "^3.4.3", "@types/cache-manager-redis-store": "^2.0.1", "@types/exceljs": "^1.3.0", "@types/express": "^4.17.3", "@types/google-libphonenumber": "^7.4.23", "@types/google-spreadsheet": "^3.2.0", "@types/image-size": "^0.8.0", "@types/jest": "26.0.10", "@types/jsonwebtoken": "^8.5.8", "@types/lodash": "^4.14.177", "@types/node": "^16.11.11", "@types/node-xlsx": "^0.15.3", "@types/npmlog": "^4.1.4", "@types/passport-jwt": "^3.0.6", "@types/pdf-parse": "^1.1.1", "@types/puppeteer": "^5.4.5", "@types/qrcode": "^1.5.5", "@types/request": "^2.48.8", "@types/requestretry": "^1.12.8", "@types/supertest": "^2.0.8", "@typescript-eslint/eslint-plugin": "3.9.1", "@typescript-eslint/parser": "3.9.1", "env-cmd": "^10.1.0", "eslint": "7.7.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "jest": "26.4.2", "prettier": "^1.19.1", "supertest": "^4.0.2", "ts-jest": "26.2.0", "ts-loader": "^6.2.1", "ts-morph": "^14.0.0", "ts-node": "9.0.0", "tsconfig-paths": "^3.9.0", "typescript": "^4.5.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/"]}}