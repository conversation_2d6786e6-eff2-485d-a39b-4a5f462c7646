console.log(__dirname + '/../../dist/apps/order/apps/order/src/entities/*.entity{.ts,.js}');
module.exports = {
  type: 'postgres',
  host: 'staging.agbiz.vn',
  port: 5432,
  username: 'postgres',
  password: 'sHamc2eYqEyhykH0slyPrAHKh4oX7PGq',
  database: 'order_stg',
  entities: [
    __dirname + '/../../dist/apps/order-api/apps/order-api/src/entities/*.entity{.ts,.js}',
  ],
  migrations: [__dirname + '/../../dist/apps/order-api/apps/order-api/src/migrations/*{.js,.ts}'],
  cli: {
    migrationsDir: __dirname + '/src/migrations',
  },
  logging: true,
  synchronize: false,
  maxQueryExecutionTime: 3000,
};
