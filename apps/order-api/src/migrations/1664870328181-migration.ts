import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1664870328181 implements MigrationInterface {
  name = 'migration1664870328181';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "logs" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "action" character varying(255), "changes" text array, "before_changes" text array, "table_name" character varying(255), "record_id" character varying(255), "parent_table_name" character varying(255), "parent_id" character varying(255), "creator_id" character varying(255), CONSTRAINT "PK_fb1b805f2f7795de79fa69340ba" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`
        CREATE OR REPLACE FUNCTION "public"."fn_log_system"()
        RETURNS "pg_catalog"."trigger" AS $BODY$
  
      DECLARE
          changes hstore;
          before_changes hstore;
          parent_table_name TEXT;
          record_id TEXT;
          record_id_column_name TEXT;
          parent_id TEXT;
          parent_id_column_name TEXT;
          ACTION TEXT;
          array_fields_remove TEXT [];
          extra_fields_remove TEXT [];
          status TEXT;
          creator_column_name TEXT;
          creator_id TEXT;
          
      BEGIN 
          ACTION := TG_OP;
          parent_table_name := TG_ARGV[0];
          creator_column_name := TG_ARGV[1];
          record_id_column_name := TG_ARGV[2];
          parent_id_column_name := TG_ARGV[3];
          extra_fields_remove := TG_ARGV[4]::text[];
          
          IF (record_id_column_name = 'null' OR record_id_column_name IS NULL) THEN record_id_column_name := 'id'; END IF;
          IF parent_id_column_name = 'null' THEN parent_id_column_name := NULL; END IF;
          IF parent_table_name = 'null' THEN parent_table_name := NULL; END IF;
          IF creator_column_name = 'null' THEN creator_column_name := 'updated_by'; END IF;
          IF extra_fields_remove::text = 'null' THEN extra_fields_remove := ARRAY[]; END IF;
          
          IF exists (
                  select * 
                  from INFORMATION_SCHEMA.COLUMNS 
                  where TABLE_NAME = TG_TABLE_NAME 
                  and COLUMN_NAME = creator_column_name
          ) THEN 
                  IF TG_OP = 'DELETE' THEN creator_id := hstore(OLD)->creator_column_name; ELSE creator_id := hstore(NEW)->creator_column_name; END IF;
          END IF;
          IF creator_id = 'null' THEN creator_id := NULL; END IF;
          
          IF (TG_OP = 'DELETE') THEN 
              record_id := hstore(OLD) -> record_id_column_name;
              parent_id := hstore(OLD) -> parent_id_column_name;
              IF parent_id = 'null' THEN parent_id := NULL; END IF;
              
              EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, before_changes, creator_id) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING parent_table_name, parent_id, TG_OP, TG_TABLE_NAME, record_id, hstore_to_array(hstore(OLD)), creator_id;

          ELSE 
              record_id := hstore(NEW) -> record_id_column_name;
              parent_id := hstore(NEW) -> parent_id_column_name;
              IF parent_id = 'null' THEN parent_id := NULL; END IF;
                              
              array_fields_remove := ARRAY['updated_at', 'created_at', 'status', creator_column_name];
              IF (ARRAY_LENGTH(extra_fields_remove, 1) > 0) THEN array_fields_remove := (array_fields_remove || extra_fields_remove);  END IF;
              IF (TG_OP <> 'INSERT') THEN
                  before_changes := hstore(OLD) - hstore(NEW);
                  changes := hstore(NEW) - hstore(OLD);
              ELSE
                  changes := hstore(NEW);
              END IF;
                              
              IF (TG_OP <> 'INSERT') THEN
                  IF (exist(hstore(NEW) - hstore(OLD), 'status')) THEN
                      EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, before_changes, creator_id) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8))' USING parent_table_name, parent_id, 'STATUS', TG_TABLE_NAME, record_id, ARRAY[NEW.status], ARRAY[OLD.status], creator_id;
                  END IF;
              ELSE
                      IF (exist(hstore(NEW), 'status')) THEN
                          EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, creator_id) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING parent_table_name, parent_id, 'STATUS', TG_TABLE_NAME, record_id, ARRAY[NEW.status], creator_id;
                      END IF;
                  END IF;
                              
              changes := DELETE(changes, array_fields_remove);
              before_changes := DELETE(before_changes, array_fields_remove);
                              
              IF (changes <> '') THEN
                  EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, creator_id) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING parent_table_name, parent_id, TG_OP, TG_TABLE_NAME, record_id, hstore_to_array(changes), creator_id;
                  END IF;
              END IF;
                      
          RETURN NEW;

      END
          $BODY$
              LANGUAGE plpgsql VOLATILE
              COST 100
      
        `);

    await queryRunner.query(`
        CREATE TRIGGER "save_log_orders" AFTER INSERT OR UPDATE OR DELETE ON "public"."orders"
        FOR EACH ROW
        EXECUTE PROCEDURE "public"."fn_log_system"('null', 'last_updated_by');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP TRIGGER IF EXISTS save_log_orders ON "public"."orders"`,
    );
    await queryRunner.query(
      `DROP FUNCTION IF EXISTS "public"."fn_log_system"()`,
    );
    await queryRunner.query(`DROP TABLE "logs"`);
  }
}
