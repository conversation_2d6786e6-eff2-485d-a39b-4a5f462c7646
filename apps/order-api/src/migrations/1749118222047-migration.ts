import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1749118222047 implements MigrationInterface {
  name = 'migration1749118222047';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "filter_collections" ADD "type" integer DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "filter_collections" DROP COLUMN "type"`);
  }
}
