import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1748595188936 implements MigrationInterface {
  name = 'migration1748595188936';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
      CREATE TABLE etelecom_raw_logs (
        id SERIAL PRIMARY KEY,
        logsId VARCHAR(255) UNIQUE NOT NULL,
        log JSONB,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
      );
      `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
      DROP TABLE IF EXISTS etelecom_raw_logs;
      `,
    );
  }
}
