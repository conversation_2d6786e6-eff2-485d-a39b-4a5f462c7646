import { MigrationInterface, QueryRunner } from "typeorm";

export class migration1747879206287 implements MigrationInterface {
    name = 'migration1747879206287'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "call_centers" ADD COLUMN "email" TEXT;
            ALTER TABLE "call_centers" ADD COLUMN "phone" TEXT;
            ALTER TABLE "call_group" ADD COLUMN "call_center_id" INT REFERENCES "call_centers"("id");       
            ALTER TABLE "call_center_extensions" ADD COLUMN "last_assign_by" INT;
            ALTER TABLE "call_centers" ADD COLUMN "status" INT DEFAULT 1;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "call_group" DROP COLUMN "call_center_id";
            ALTER TABLE "call_center_extensions" DROP COLUMN "last_assign_by";
            ALTER TABLE "call_centers" DROP COLUMN "email";
            ALTER TABLE "call_centers" DROP COLUMN "phone";
            ALTER TABLE "call_centers" DROP COLUMN "status";
        `);
    }
}
