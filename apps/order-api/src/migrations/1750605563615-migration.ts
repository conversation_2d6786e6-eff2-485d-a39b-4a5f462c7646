import { MigrationInterface, QueryRunner } from "typeorm";

export class migration1750605563615 implements MigrationInterface {
  name = 'migration1750605563615';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" ADD "lead_id" integer`);
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" ADD "extension_id" integer`);
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" ADD "lead_care_id" integer`);
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" ADD "user_id" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" DROP COLUMN "user_id"`);
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" DROP COLUMN "lead_care_id"`);
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" DROP COLUMN "extension_id"`);
    await queryRunner.query(`ALTER TABLE "etelecom_raw_logs" DROP COLUMN "lead_id"`);
  }
}
