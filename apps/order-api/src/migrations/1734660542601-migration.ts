import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1734660542601 implements MigrationInterface {
  name = 'migration1734660542601';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "call_histories" ADD "context_summary" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "call_histories" DROP COLUMN "context_summary"`);
  }
}
