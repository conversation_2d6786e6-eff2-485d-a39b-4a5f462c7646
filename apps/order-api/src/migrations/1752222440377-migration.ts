import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1752222440377 implements MigrationInterface {
  name = 'migration1752222440377';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "call_center_extensions" ADD "prefix" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "call_center_extensions" DROP COLUMN "prefix"`);
  }
}
