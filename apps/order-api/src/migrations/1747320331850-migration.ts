import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1747320331850 implements MigrationInterface {
    name = 'migration1747320331850'

public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "leads" ADD "request_landing_id" character varying`,
    );
    await queryRunner.query(
      `CREATE INDEX "request_landing_index" ON "leads" ("request_landing_id")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "request_landing_index"`);
    await queryRunner.query(
      `ALTER TABLE "leads" DROP COLUMN "request_landing_id"`,
    );
  }


}
