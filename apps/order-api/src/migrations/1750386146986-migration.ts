import { MigrationInterface, QueryRunner } from "typeorm";

export class migration1750386146986 implements MigrationInterface {
    name = 'migration1750386146986'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "call_histories" ADD "lead_care_id" int NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "call_histories" DROP COLUMN "lead_care_id"`);
    }
}
