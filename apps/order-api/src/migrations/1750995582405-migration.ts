import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1750995582405 implements MigrationInterface {
  name = 'migration1750995582405';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" ADD "type" integer NULL`);
    await queryRunner.query(`ALTER TABLE "landing_pages" ADD "order_type" smallint DEFAULT 0 NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "landing_pages" DROP COLUMN "order_type"`);
  }
}
