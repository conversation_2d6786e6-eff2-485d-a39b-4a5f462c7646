import { MigrationInterface, QueryRunner } from "typeorm";

export class migration1748328876026 implements MigrationInterface {
    name = 'migration1748328876026'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "call_center_extensions" ADD status INT;`);
        await queryRunner.query(`ALTER TABLE "call_group_user" ADD added_by INT;`);
        await queryRunner.query(`ALTER TABLE "call_group_user" ADD status INT;`);
        await queryRunner.query(`ALTER TABLE "call_group" ADD created_by INT;`);
        await queryRunner.query(`ALTER TABLE "call_group" ADD updated_by INT;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "call_center_extensions" DROP COLUMN status;`);
        await queryRunner.query(`ALTER TABLE "call_group_user" DROP COLUMN added_by;`);
        await queryRunner.query(`ALTER TABLE "call_group_user" DROP COLUMN status;`);
        await queryRunner.query(`ALTER TABLE "call_group" DROP COLUMN created_by;`);
        await queryRunner.query(`ALTER TABLE "call_group" DROP COLUMN updated_by;`);
    }
}
