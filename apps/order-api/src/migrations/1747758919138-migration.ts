import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1747758919138 implements MigrationInterface {
  name = 'migration1747758919138';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE call_group (
        id SERIAL PRIMARY KEY,
        name TEXT,
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now(),
        status INT DEFAULT 1
      );

      CREATE TABLE call_group_user (
        id SERIAL PRIMARY KEY,
        call_group_id INT REFERENCES call_group(id),
        user_id INT,
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now(),
        last_call_id INT
      );

      ALTER TABLE "call_center_extensions" ADD "call_group_id" INT REFERENCES call_group(id);
      ALTER TABLE "call_centers" ADD "metadata" TEXT;
    `);
}

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "call_center_extensions" DROP COLUMN "call_group_id";
      ALTER TABLE "call_centers" DROP COLUMN "metadata";
      DROP TABLE IF EXISTS call_group_user;
      DROP TABLE IF EXISTS call_group;
    `);
  }
}
