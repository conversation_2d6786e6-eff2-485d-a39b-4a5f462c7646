import { Expose, Type } from 'class-transformer';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { VirtualColumn } from 'core/decorators/virtual-column.decorator';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { OrderType } from '../../enums/order-type.enum';

@Entity({
  name: 'fanpage',
  database: process.env.DATABASE_MESSAGE,
})
@Index('page_group_id', ['groupId'])
export class FanPage extends BaseEntity {
  @PrimaryColumn({
    name: 'id',
    type: 'bigint',
  })
  @Expose()
  id: number;

  @Column({
    name: 'name',
    type: 'varchar',
  })
  @Expose()
  name: string;

  @Column({
    name: 'is_bot_enabled',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isBotEnabled: boolean;

  @Column({
    name: 'is_sync_conversation',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isSyncConversation: boolean;

  @Column({
    name: 'project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  projectId?: number;

  @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  countryId?: number;

  @Column({
    name: 'product_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  productId?: number;

  @Column({
    name: 'group_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  groupId?: number;

  @Column({
    name: 'keyword_group_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  keywordGroupId?: number;

  @Column({
    name: 'marketer_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  marketerId?: number;

  @VirtualColumn('config_chatbot')
  @Expose()
  configChatbot?: boolean;

  @VirtualColumn('unread_conversations')
  @Expose()
  @Type(() => Number)
  @NonEmptyTransform()
  unreadConversations?: number;

  @Column({
    name: 'order_type',
    type: 'smallint',
    default: OrderType.normal,
  })
  @Expose()
  orderType?: OrderType;
}
