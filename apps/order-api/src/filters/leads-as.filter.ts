import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsIn, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import {
  GATHERABLE_STATES,
  GATHERABLE_STATES_AFTER_SALES,
} from '../constants/care-states.constant';
import { CareState, CareStateAfterSales } from '../enums/care-state.enum';
import { SourceEntity } from '../enums/source-entity.enum';
import { LeadCollectType } from '../enums/lead-collect-type.enum';
import { Sort } from 'core/enums/sort.enum';
import { TagMethodType, TagOperatorType } from '../enums/tag.enum';
import { ReasonTab } from '../enums/lead-aftersales-filter.enum';

export class LeadsASFilterCommon {
  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  from?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  to?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  projectIds?: (number | string)[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  productIds?: (number | string)[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  fromPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  toPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  orderBy?: string;

  @ApiProperty({ required: false, enum: Sort })
  @IsOptional()
  @EnumTransform(Sort)
  sort?: Sort;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isAssignedProject?: boolean = false;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isGetUnassignedProjectLead?: boolean;

  @ApiProperty({ required: false, enum: CareStateAfterSales, isArray: true })
  @IsOptional()
  // @IsIn(Object.keys(CareState), { each: true })
  @ArrayTransform()
  @EnumTransform(CareStateAfterSales)
  state?: CareStateAfterSales[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  ids?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getExternalSource?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getUpcomingAppointments?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getAllAppointments?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  hasDuplicates?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  userIds?: number[];

  @IsOptional()
  @EnumTransform(LeadCollectType)
  @ArrayTransform()
  @IsEnum(LeadCollectType, { each: true })
  collectType?: LeadCollectType[];

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromCurrentCare?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toCurrentCare?: Date;
}

export class LeadsASUnassignProjectFilter extends LeadsASFilterCommon {}

export class LeadsASAssignedProjectFilter extends LeadsASFilterCommon {
  @ApiProperty({ required: false })
  @IsOptional()
  query?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  reasonIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  tagIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  numberOfRepeats?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  userIds?: number[];

  @ApiProperty({ required: false, enum: TagMethodType })
  @IsEnum(TagMethodType)
  @IsOptional()
  @EnumTransform(TagMethodType)
  tagMethod?: TagMethodType = TagMethodType.Include;

  @ApiProperty({ required: false, enum: TagOperatorType })
  @IsEnum(TagOperatorType)
  @IsOptional()
  @EnumTransform(TagOperatorType)
  operator?: TagOperatorType = TagOperatorType.Or;

  @ApiProperty({
    description: 'Kiểu thu thập dữ liệu',
    enum: LeadCollectType,
    enumName: 'LeadCollectType',
    required: false,
  })
  @IsOptional()
  @EnumTransform(LeadCollectType)
  @ArrayTransform()
  @IsEnum(LeadCollectType, { each: true })
  collectType?: LeadCollectType[];

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @Type(() => Number)
  numberOfCares?: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @Type(() => Number)
  fromNumberOfCares?: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @Type(() => Number)
  toNumberOfCares?: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromLastCare?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toLastCare?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  sourceProductIds?: (number | string)[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  marketerIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  excludeUserIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  originalProjectIds?: (Number | String)[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ReasonTab)
  reasonTab?: ReasonTab;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromUpdatedAt?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toUpdatedAt?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromCurrentCare?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toCurrentCare?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  sourceIds?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  dateRangeType?: string;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromRange?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toRange?: Date;
}

export class GatherLeadsAfterSaleFilter extends LeadsASAssignedProjectFilter {
  @ApiProperty({
    required: false,
    enum: CareStateAfterSales,
    isArray: true,
  })
  @IsOptional()
  @IsIn(GATHERABLE_STATES_AFTER_SALES, {
    each: true,
    message:
      'state must be one of the following values: ' +
      GATHERABLE_STATES_AFTER_SALES.map(item => String(CareStateAfterSales[item])).join(', '),
  })
  @ArrayTransform()
  @EnumTransform(CareStateAfterSales)
  state?: CareStateAfterSales[];
}
