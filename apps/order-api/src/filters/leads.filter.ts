import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsIn, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { GATHERABLE_STATES } from '../constants/care-states.constant';
import { CareState } from '../enums/care-state.enum';
import { SourceEntity } from '../enums/source-entity.enum';
import { LeadCollectType } from '../enums/lead-collect-type.enum';
import { Sort } from 'core/enums/sort.enum';
import { TagMethodType, TagOperatorType } from '../enums/tag.enum';
import { ReasonTab } from '../enums/lead-aftersales-filter.enum';

export class LeadsFilter {
  @ApiProperty({ required: false })
  @IsOptional()
  query?: string;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  from?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  to?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromLastCare?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toLastCare?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @Type(() => Number)
  numberOfCares?: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @Type(() => Number)
  fromNumberOfCares?: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @Type(() => Number)
  toNumberOfCares?: number;

  @ApiProperty({ required: false, enum: CareState, isArray: true })
  @IsOptional()
  // @IsIn(Object.keys(CareState), { each: true })
  @ArrayTransform()
  @EnumTransform(CareState)
  state?: CareState[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  userIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  excludeUserIds?: number[];

  @ApiProperty({ required: false, enum: SourceEntity })
  @IsOptional()
  @EnumTransform(SourceEntity)
  sourceType?: SourceEntity;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  sourceIds?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  reasonIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  tagIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  marketerIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  numberOfRepeats?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getUpcomingAppointments?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getAllAppointments?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  ids?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getExternalSource?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  projectIds?: (number | string)[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isDistributable?: boolean;

  @ApiProperty({
    required: false,
    description:
      'Lọc kiểu thu thập dữ liệu có phải là data [Capture form] hay không, có thể lọc thay thế bằng collectType',
    deprecated: true,
  })
  @IsOptional()
  @BooleanTransform()
  isCaptureForm?: boolean;

  @ApiProperty({
    description: 'Kiểu thu thập dữ liệu',
    enum: LeadCollectType,
    enumName: 'LeadCollectType',
    required: false,
  })
  @IsOptional()
  @EnumTransform(LeadCollectType)
  @ArrayTransform()
  @IsEnum(LeadCollectType, { each: true })
  collectType?: LeadCollectType[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  hasDuplicates?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  orderBy?: string;

  @ApiProperty({ required: false, enum: Sort })
  @IsOptional()
  @EnumTransform(Sort)
  sort?: Sort;

  @ApiProperty({ required: false, enum: TagMethodType })
  @IsEnum(TagMethodType)
  @IsOptional()
  @EnumTransform(TagMethodType)
  tagMethod?: TagMethodType = TagMethodType.Include;

  @ApiProperty({ required: false, enum: TagOperatorType })
  @IsEnum(TagOperatorType)
  @IsOptional()
  @EnumTransform(TagOperatorType)
  operator?: TagOperatorType = TagOperatorType.Or;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isSearchLatestReason?: boolean;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromUpdatedAt?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toUpdatedAt?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromCurrentCare?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toCurrentCare?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  productIds?: (number | string)[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ReasonTab)
  reasonTab?: ReasonTab;

  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  sourceTypes?: string[];
}

export class MobileLeadsFilter extends LeadsFilter {
  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  productIds?: (number | string)[];

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  assignedFrom?: Date;
  
  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  assignedTo?: Date;
}

export class GatherLeadsFilter extends LeadsFilter {
  @ApiProperty({
    required: false,
    enum: CareState,
    isArray: true,
  })
  @IsOptional()
  @IsIn(GATHERABLE_STATES, {
    each: true,
    message:
      'state must be one of the following values: ' +
      GATHERABLE_STATES.map(item => String(CareState[item])).join(', '),
  })
  @ArrayTransform()
  @EnumTransform(CareState)
  state?: CareState[];
}
