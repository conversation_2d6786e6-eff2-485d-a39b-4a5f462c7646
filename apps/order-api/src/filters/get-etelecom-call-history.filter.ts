import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';

export class GetEtelecomCallHistoryFilter {
  @ApiProperty({ required: true })
  @IsNumber()
  @Type(() => Number)
  callCenterId: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  fromCallAt?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  toCallAt?: Date;
}
