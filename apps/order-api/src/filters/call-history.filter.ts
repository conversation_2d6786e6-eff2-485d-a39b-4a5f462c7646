import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';

export class CallHistoryFilter {
         @ApiProperty({ required: false })
         @IsOptional()
         @ArrayTransform()
         @Type(() => Number)
         leadIds?: number[];

         @ApiProperty({ required: false })
         @IsOptional()
         incomingNumber?: string;

         @ApiProperty({ required: false })
         @IsOptional()
         outgoingNumber?: string;

         @ApiProperty({ required: false })
         @IsOptional()
         @ArrayTransform()
         @Type(() => Number)
         extensionId?: number[];

         @ApiProperty({ required: false })
         @IsOptional()
         startAt?: Date;

         @ApiProperty({ required: false })
         @IsOptional()
         endAt?: Date;

         @ApiProperty({ required: false })
         @IsOptional()
         @ArrayTransform()
         @Type(() => Number)
         userId?: number[];

         @ApiProperty({ required: false })
         @IsOptional()
         @IsNumber()
         @Type(() => Number)
         status?: number;

         @ApiProperty({ required: false })
         @IsOptional()
         @IsNumber()
         @Type(() => Number)
         callCenterId?: number;
}


