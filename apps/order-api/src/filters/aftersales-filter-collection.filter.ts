import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { FilterCollectionType } from 'core/enums/filter-collection-type.enum';

export class AfterSalesFilterCollectionFilter {
  @ApiProperty({ required: false, enum: FilterCollectionType, isArray: true })
  @IsEnum(FilterCollectionType, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(FilterCollectionType)
  type?: FilterCollectionType[];
}
