import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';

export class LandingPagesFilter {
  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  ids?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  query?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  countryIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  projectIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  userIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getAllName?: boolean;

  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  sourcesType?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  ordersType?: number[];
}
