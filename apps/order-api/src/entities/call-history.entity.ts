import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../../../core/entities/base/base-entity.entity';
import { Expose } from 'class-transformer';
import { DateTransform } from '../../../../core/decorators/date-transform/date-transform.decorator';
import { CallHistoryStatus, CallHistoryType } from '../enums/call-center.enum';
import { CallCenterExtension } from 'apps/order-api/src/entities/call-center-extension.entity';

@Index('IDX_EXTENSION_ID', ['extensionId'])
@Entity('call_histories')
export class CallHistory extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id?: number;

  @Column({
    name: 'lead_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  leadId?: number;

  @Column({
    name: 'outgoing_number',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  outgoingNumber?: string;

  @Column({
    name: 'incoming_number',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  incomingNumber?: string;

  @Column({
    name: 'device_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  deviceId?: string;

  @Column({
    name: 'record_url',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recordUrl?: string;

  @Column({
    name: 'start_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  startAt?: Date;

  @Column({
    name: 'end_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  endAt?: Date;

  @Column({
    name: 'created_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  createdBy?: number;

  @Column({
    name: 'type',
    type: 'smallint',
    default: CallHistoryType.agcall,
    enum: CallHistoryType,
  })
  @Expose()
  type: number;

  @Column({
    name: 'status',
    type: 'smallint',
    enum: CallHistoryStatus,
    nullable: true,
  })
  @Expose()
  status?: number;

  @Column({
    name: 'extension_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  extensionId?: number;

  @ManyToOne(() => CallCenterExtension, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'extension_id' })
  @Expose()
  extension?: CallCenterExtension;

  @Column({
    name: 'call_center_id',
    type: 'varchar',
    length: 225,
    nullable: true,
  })
  @Expose()
  callCenterId?: string; // ID cuộc gọi phía Call Center

  @Column({
    name: 'duration',
    type: 'int',
    nullable: true,
  })
  @Expose()
  duration?: number;

  @Column({
    name: 'bill_sec',
    type: 'int',
    nullable: true,
  })
  @Expose()
  billSec?: number;

  @Column({
    name: 'context_summary',
    type: 'text',
    nullable: true,
  })
  @Expose()
  contextSummary?: string;

  @Column({
    name: 'lead_care_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  leadCareId?: number;
}
