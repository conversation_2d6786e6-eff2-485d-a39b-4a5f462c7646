import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntity } from '../../../../core/entities/base/base-entity.entity';
import { Expose } from 'class-transformer';

import { CallRelatedStatus } from '../enums/call-related-status.enum';
import { CallGroupUser } from './call-group-user.entity';
import { CallCenterExtension } from './call-center-extension.entity';
import { CallCenter } from './call-center.entity';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';

@Entity('call_group')
export class CallGroup extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'int',
  })
  @Expose()
  id: number;

  @Column({
    name: 'name',
    type: 'text',
    nullable: true,
  })
  @Expose()
  name?: string;

  @Column({
    name: 'status',
    type: 'int',
    nullable: false,
    default: CallRelatedStatus.connected,
  })
  @Expose()
  status?: CallRelatedStatus;

  @OneToMany(
    () => CallGroupUser,
    callGroupUser => callGroupUser.callGroup,
  )
  @Expose()
  users: CallGroupUser[];

  @OneToMany(
    () => CallCenterExtension,
    extension => extension.callGroup,
  )
  @Expose()
  extensions: CallCenterExtension[];

  @ManyToOne(
    () => CallCenter,
    callCenter => callCenter.callGroups,
    { nullable: true },
  )
  @JoinColumn({ name: 'call_center_id' })
  @Expose()
  callCenter: CallCenter;

  @Column({
    name: 'created_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  createdBy?: number;

  @Column({
    name: 'updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  updatedBy?: number;
}
