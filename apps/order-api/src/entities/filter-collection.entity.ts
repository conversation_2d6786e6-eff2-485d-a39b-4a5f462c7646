import { Expose } from 'class-transformer';
import { Scope } from 'core/decorators/typeorm-scope.decorator';
import { IntegerIdEntity } from 'core/entities/base/integer-id-entity.entity';
import { FilterCollectionType } from 'core/enums/filter-collection-type.enum';
import { Column, DeleteDateColumn, Entity } from 'typeorm';

@Scope<FilterCollection>([
  (qb, alias, user) => {
    return qb.andWhere(`${alias}.user_id = ${user.id}`);
  },
])
@Entity({ name: 'filter_collections' })
export class FilterCollection extends IntegerIdEntity {
  @Column({
    name: 'name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  name?: string;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyId: number;

  @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  countryId: number;

  @Column({
    name: 'raw',
    type: 'jsonb',
  })
  @Expose()
  raw: Record<string, any>;

  @DeleteDateColumn({
    name: 'deleted_at',
  })
  deletedAt?: Date;

  @Column({
    name: 'user_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  userId?: number;

  @Column({
    name: 'type',
    type: 'int',
    default: FilterCollectionType.FilterFreshLead,
    nullable: true,
  })
  @Expose()
  type: number;
}
