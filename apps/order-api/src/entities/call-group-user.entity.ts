import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Expose } from 'class-transformer';
import { CallGroup } from './call-group.entity';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { CallGroupUserStatus } from '../enums/call-group-user-status.enum';
@Entity('call_group_user')
export class CallGroupUser extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id: number;

  @ManyToOne(() => CallGroup)
  @JoinColumn({ name: 'call_group_id' })
  @Expose()
  callGroup?: CallGroup;

  @Column({
    name: 'user_id',
    type: 'int',
  })
  @Expose()
  userId?: number;

  @Column({
    name: 'last_call_id',
    type: 'int',
  })
  @Expose()
  lastCallId?: number;

  @Column({
    name: 'added_by',
    type: 'int',
  })
  @Expose()
  addedBy?: number;

  @Column({
    name: 'status',
    type: 'int',
    default: CallGroupUserStatus.active,
  })
  @Expose()
  status: CallGroupUserStatus;
}
