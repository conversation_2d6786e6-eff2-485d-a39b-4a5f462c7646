import { Field, Float, Int, ObjectType } from '@nestjs/graphql';
import { Exclude, Expose, Type, plainToInstance } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { OrderStatus } from 'core/enums/order-status.enum';
import GraphQLTypeJSON from 'graphql-type-json';
import {
  Brackets,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';
import { DiscountType } from '../enums/discount-type.enum';
import { TeamInCharge } from '../enums/team-in-charge.enum';
import { Market } from '../read-entities/identity/market.entity';
import { CancelReason } from './cancel-reasons.entity';
import { OrderCarrier } from './order-carrier.entity';
import { OrderProduct } from './order-product.entity';
import { PrintNote } from './print-notes.entity';
import { Tag } from './tag.entity';
import { PossibleDuplicateOrder } from './possible-duplicate-order.entity';
import { OrderSource } from './order-source.entity';
import { find, isEmpty } from 'lodash';
import { SourceEntity } from '../enums/source-entity.enum';
import { PartnerCode } from 'core/enums/partner-code.enum';
import { Scope } from 'core/decorators/typeorm-scope.decorator';
import { OrdersFilter } from '../filters/orders.filter';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';
import { VirtualColumn } from 'core/decorators/virtual-column.decorator';
import { LeadType } from '../enums/lead-type.enum';
import { OrderStatusHistory } from './order-status-history.entity';
import { OrderType } from '../enums/order-type.enum';

@Scope<Order>([
  (qb, alias, user, headers, query: OrdersFilter) => {
    if (!user || !user?.profiles) return qb;

    const mainAlias = qb.expressionMap.mainAlias.metadata.tableName;
    const isFetchDistributableLeads = query._distributableLeads;

    qb.andWhere(
      new Brackets(sqb => {
        for (const record of user.profiles) {
          if (query._byPassSeftData) break;
          const [dataAccessLevel, moduleInCharge, scopes] = record;
          sqb.orWhere(
            new Brackets(pQb => {
              const scopePairs = scopes.map(sc => `(${sc[0]}, ${sc[1]})`);
              pQb.andWhere(
                `(${alias}.country_id, ${alias}.project_id) IN (${scopePairs.join(',')})`,
              );
              if (!isEmpty(moduleInCharge) && !query._byPassCheckReps) {
                if (dataAccessLevel === DataAccessLevel.personal)
                  pQb.andWhere(
                    new Brackets(mQb => {
                      mQb.orWhere(`${alias}.creator_id = ${user.id}`);
                      for (const module of moduleInCharge) {
                        switch (module) {
                          case ModuleInCharge.carepage:
                            mQb.orWhere(`${alias}.care_page_id = ${user.id}`);
                            break;
                          case ModuleInCharge.telesale:
                            mQb.orWhere(`${alias}.sale_id = ${user.id}`);
                            if (mainAlias === 'leads' && isFetchDistributableLeads)
                              mQb.orWhere(`${alias}.sale_id IS NULL`);
                            break;
                          case ModuleInCharge.marketer:
                            mQb.orWhere(`${alias}.marketer_id = ${user.id}`);
                            break;
                          default:
                            break;
                        }
                      }
                    }),
                  );
                else
                  pQb.andWhere(
                    new Brackets(mQb => {
                      for (const module of moduleInCharge) {
                        switch (module) {
                          case ModuleInCharge.carepage:
                            mQb
                              .orWhere(`${alias}.team_in_charge = ${TeamInCharge.CarePage}`)
                              .orWhere(`${alias}.care_page_id IS NOT NULL`);
                            break;
                          case ModuleInCharge.telesale:
                            mQb
                              .orWhere(`${alias}.team_in_charge = ${TeamInCharge.Sale}`)
                              .orWhere(`${alias}.sale_id IS NOT NULL`)
                              .orWhere(`${alias}.cross_care = TRUE`);
                            break;
                          default:
                            break;
                        }
                      }
                    }),
                  );
              }
            }),
          );
        }
      }),
    );

    const isJoin = qb.expressionMap.joinAttributes.find(j => j.alias.name === alias);
    if (!isJoin) {
      query = plainToInstance(OrdersFilter, query);
      if (!isEmpty(query.projectIds))
        qb.andWhere(`${alias}.project_id IN (:...queryProjectIds)`, {
          queryProjectIds: query.projectIds,
        });
    }
    return qb.andWhere(`${alias}.company_id = :companyId`, { companyId: user.companyId });
  },
])
@Entity({
  name: 'orders',
  database: process.env.DATABASE_ORDER,
})
@Unique('UQ_DISPLAY_ID_COMPANY_ID', ['displayId', 'companyId'])
@Unique('UQ_PARTNER', ['partnerId', 'partnerCode'])
@Index('IDX_ORDER_COUNTRY', ['countryId'])
@Index('IDX_ORDER_PROJECT', ['projectId'])
@Index('IDX_ORDER_COUNTRY_PROJECT', ['countryId', 'projectId'])
@Index('IDX_ORDER_DISPLAY_ID', ['displayId'])
@Index('IDX_ORDER_FFM_DISPLAY_ID', ['ffmDisplayId'])
@ObjectType()
export class Order {
  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamp with time zone',
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field()
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    type: 'timestamp with time zone',
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field()
  @Index()
  updatedAt: Date;

  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  @Field(type => Int)
  id: number;

  @Column({
    name: 'sale_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(() => Int, { nullable: true })
  saleId?: number;

  @Column({
    name: 'care_page_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(() => Int, { nullable: true })
  carePageId?: number;

  @Column({
    name: 'team_in_charge',
    type: 'smallint',
    default: TeamInCharge.Sale,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  teamInCharge: TeamInCharge;

  @Column({
    name: 'source_id',
    type: 'int',
    nullable: true,
    insert: false,
    update: false,
    // select: false,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  sourceId?: number;

  @Column({
    name: 'channel_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  channelId?: number;

  @Column({
    name: 'discount',
    type: 'double precision',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  discount?: number;

  @Column({
    name: 'discount_type',
    type: 'double precision',
    default: DiscountType.fixed,
    nullable: false,
  })
  @Expose()
  @EnumTransform(DiscountType)
  @Field(type => DiscountType, { nullable: true })
  discountType: DiscountType;

  @Column({
    name: 'surcharge',
    type: 'double precision',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  surcharge?: number;

  @Column({
    name: 'shipping_fee',
    type: 'double precision',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  shippingFee?: number;

  @Column({
    type: 'double precision',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  paid?: number;

  @Column({
    name: 'total_price',
    type: 'double precision',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  totalPrice?: number;

  @Column({
    type: 'smallint',
    default: OrderStatus.New,
    nullable: false,
  })
  @Expose()
  @EnumTransform(OrderStatus)
  @Field(type => OrderStatus, { nullable: true })
  status: OrderStatus;

  @Column({
    name: 'creator_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  creatorId?: number;

  @Column({
    name: 'customer_name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String)
  customerName?: string;

  @Column({
    name: 'customer_phone',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String)
  customerPhone?: string;

  @Column({
    name: 'address_text',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String)
  addressText?: string;

  @Column({
    name: 'address_note',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  addressNote?: string;

  @Column({
    name: 'address_ward',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  addressWard?: string;

  @Column({
    name: 'address_ward_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  addressWardId?: string;

  @Column({
    name: 'address_district',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  addressDistrict: string;

  @Column({
    name: 'address_district_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  addressDistrictId: string;

  @Column({
    name: 'address_province',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  addressProvince: string;

  @Column({
    name: 'address_province_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  addressProvinceId: string;

  @Column({
    name: 'post_code_old',
    type: 'int',
    nullable: true,
  })
  // @Expose()
  // @Field(type => Int, { nullable: true })
  postCodeOld?: string;

  @Column({
    name: 'post_code',
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  postCode?: string;

  @Column({
    name: 'country_id',
    type: 'int',
  })
  @Expose()
  @Field(type => Int)
  countryId: number;

  @Column({
    name: 'project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  projectId?: number;

  @OneToMany(
    () => OrderProduct,
    product => product.order,
    { cascade: true },
  )
  @Type(() => OrderProduct)
  @Expose()
  @Field(() => [OrderProduct])
  products: OrderProduct[];

  @ManyToMany(
    () => Tag,
    item => item.orders,
    { nullable: true },
  )
  @JoinTable({
    name: 'order_tags',
    joinColumn: {
      name: 'order_id',
    },
    inverseJoinColumn: {
      name: 'tag_id',
    },
  })
  @Type(() => Tag)
  @Expose()
  @Field(() => [Tag], { nullable: true })
  tags: Tag[];

  @Column({
    name: 'cancel_reason_text',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  @Field(() => String, { nullable: true })
  cancelReasonText?: string;

  @ManyToMany(
    () => CancelReason,
    item => item.orders,
    { nullable: true },
  )
  @JoinTable({
    name: 'order_cancel_reasons',
    joinColumn: {
      name: 'order_id',
    },
    inverseJoinColumn: {
      name: 'cancel_reason_id',
    },
  })
  @Expose()
  @Field(() => [CancelReason])
  cancelReasons: CancelReason[];

  @Column({
    name: 'packing_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field({ nullable: true })
  packingAt?: Date;

  @Column({
    name: 'display_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Index({ where: 'display_id IS NOT NULL' })
  @Field(type => String, { nullable: true })
  displayId?: string;

  @Column({
    name: 'last_updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  @Field(type => Int, { nullable: true })
  lastUpdatedBy?: number;

  @Column({
    name: 'is_checked_after_return',
    type: 'boolean',
    nullable: true,
  })
  @Expose()
  @Field(type => Boolean, { nullable: true })
  @NonEmptyTransform()
  isCheckedAfterReturn?: boolean;

  @Column({
    name: 'partner_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  partnerId?: string;

  @Column({
    name: 'partner_display_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  partnerDisplayId?: string;

  @Column({
    name: 'partner_code',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  partnerCode: PartnerCode;

  @Column({
    name: 'page_id',
    type: 'bigint',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  pageId?: string;

  @Column({
    name: 'pancake_conversation_id',
    type: 'bigint',
    nullable: true,
  })
  @Expose()
  @Field(type => Float, { nullable: true })
  @NonEmptyTransform()
  pancakeConversationId?: number;

  @Column({
    name: 'source_detail',
    type: 'jsonb',
    nullable: true,
  })
  @Expose()
  @Field(type => GraphQLTypeJSON, { nullable: true })
  @NonEmptyTransform()
  sourceDetail?: Record<string, any>;

  @Column({
    name: 'confirmed_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field(type => Int, { nullable: true })
  confirmedAt?: Date;

  @Column({
    name: 'fb_global_id',
    type: 'bigint',
    nullable: true,
  })
  @Expose()
  @Index()
  @NonEmptyTransform()
  @Field(() => String)
  fbGlobalId: string;

  @Column({
    name: 'fb_scoped_user_id',
    type: 'bigint',
    nullable: true,
  })
  @Expose()
  @Index()
  @NonEmptyTransform()
  fbScopedUserId: string;

  @Column({
    name: 'expect_delivery_at',
    type: 'date',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  @Field(type => String, { nullable: true })
  expectDeliveryAt?: Date;

  @Column({
    name: 'fb_receipt_message_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  fbReceiptMessageId?: string;

  @Column({
    name: 'marketer_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(() => Int, { nullable: true })
  @NonEmptyTransform()
  marketerId?: number;

  @ManyToMany(
    () => PrintNote,
    item => item.orders,
    { nullable: true },
  )
  @JoinTable({
    name: 'order_print_notes',
    joinColumn: {
      name: 'order_id',
    },
    inverseJoinColumn: {
      name: 'print_note_id',
    },
  })
  @Expose()
  @NonEmptyTransform()
  printNotes: PrintNote[];

  @Column({
    name: 'print_note_text',
    nullable: true,
  })
  @Expose()
  // @NonEmptyTransform()
  @Field(() => String, { nullable: true })
  printNoteText?: string;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(() => Int, { nullable: true })
  @NonEmptyTransform()
  companyId?: number;

  @Column({
    name: 'note',
    type: 'text',
    nullable: true,
  })
  @Expose()
  // @NonEmptyTransform()
  @Field(() => String, { nullable: true })
  note?: string;

  @OneToOne(
    () => OrderCarrier,
    oc => oc.order,
    { nullable: true, cascade: true },
  )
  @Expose()
  @Field(() => OrderCarrier, { nullable: true })
  @NonEmptyTransform()
  carrier?: OrderCarrier;

  @Column({
    name: 'last_update_status',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  lastUpdateStatus?: Date;

  @Column({
    name: 'ffm_display_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  ffmDisplayId?: string;

  @Column({
    name: 'ffm_company_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  @NonEmptyTransform()
  ffmCompanyId?: number;

  @Column({
    name: 'ffm_partner_client_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  @NonEmptyTransform()
  ffmPartnerClientId?: number;

  @Column({
    name: 'ignore_duplicate_warning',
    type: 'boolean',
    default: false,
  })
  @Expose()
  @Field(type => Boolean, { nullable: true })
  @NonEmptyTransform()
  ignoreDuplicateWarning?: boolean;

  @Column({
    name: 'utm_link',
    type: 'text',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  utmLink?: string;

  @OneToMany(
    () => PossibleDuplicateOrder,
    it => it.order,
    { cascade: false },
  )
  @Expose()
  @NonEmptyTransform()
  possibleDuplicateOrders?: PossibleDuplicateOrder[];

  @Column({
    name: 'status_change_reason',
    type: 'text',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  statusChangeReason?: string;

  @Column({
    name: 'status_change_description',
    type: 'text',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  statusChangeDescription?: string;

  @Column({
    name: 'status_change_actor',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  statusChangeActor?: string;

  @Column({
    name: 'last_synced_ffm_status_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field()
  lastSyncedFfmStatusAt?: Date;

  @Column({
    name: 'last_synced_ffm_info_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field()
  lastSyncedFfmInfoAt?: Date;

  @Column({
    name: 'last_synced_ffm_tags_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field()
  lastSyncedFfmTagsAt?: Date;

  @Column({
    name: 'last_synced_lead_user_id_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Field()
  lastSyncedLeadUserIdAt?: Date;

  @Column({
    name: 'external_source_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  @Field(type => Int, { nullable: true })
  externalSourceId?: number;

  @ManyToOne(
    () => OrderSource,
    it => it.orders,
  )
  @JoinColumn({ name: 'external_source_id' })
  @Exclude()
  @NonEmptyTransform()
  externalSource: OrderSource;

  @Expose()
  @NonEmptyTransform()
  externalSources: OrderSource[];

  @Expose({ name: 'externalSource' })
  get extSource(): OrderSource {
    if (!isEmpty(this.externalSources)) {
      const fbPageSource = find(
        this.externalSources,
        s => s.entity === SourceEntity.fb_page || s.entity === SourceEntity.landing_page,
      );
      if (fbPageSource) return fbPageSource;
    }
    return this.externalSource;
  }

  market?: Market;

  @Column({
    name: 'cross_care',
    type: 'boolean',
    default: false,
  })
  @Expose()
  @NonEmptyTransform()
  crossCare?: boolean;

  @DateTransform()
  @Expose()
  @NonEmptyTransform()
  inTransitAt?: Date;

  @VirtualColumn('conversation_created_at')
  @DateTransform()
  @Expose()
  @NonEmptyTransform()
  conversationCreatedAt?: Date;

  @Column({
    name: 'metadata',
    type: 'text',
    nullable: true,
  })
  @Expose()
  metadata?: string;

  @Expose()
  @NonEmptyTransform()
  sourceDisplayId?: string;

  @Column({
    name: 'source_project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  sourceProjectId?: number;

  @Expose()
  @NonEmptyTransform()
  adId?: string;

  @Column({
    name: 'metadata_tag_log',
    type: 'text',
    nullable: true,
  })
  @Expose()
  metadataTagLog?: string;

  @Column({
    name: 'fb_ads_id',
    type: 'text',
    nullable: true,
  })
  @Expose()
  fbAdsId?: string;

   @Column({
    name: 'metadata_carrier_log',
    type: 'text',
    nullable: true,
  })
  @Expose()
  metadataCarrierLog?: string;

  @Column({
    name: 'last_waybill_number',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Field(type => String, { nullable: true })
  @NonEmptyTransform()
  lastWaybillNumber?: string;

  history?: OrderStatusHistory;

  @Column({
    name: 'type',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  @NonEmptyTransform()
  type?: OrderType;
}
