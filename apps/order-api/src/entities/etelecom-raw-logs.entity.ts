import { Expose } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { Entity, PrimaryGeneratedColumn, Column, Unique } from 'typeorm';
import { EtelecomCallLogDto } from '../dtos/etelecom-call-log.dto';

@Entity({ name: 'etelecom_raw_logs' })
@Unique(['logsId'])
export class EtelecomRawLogs {
  @PrimaryGeneratedColumn()
  @Expose()
  id: number;

  @Column({ name: 'logsid', type: 'varchar', length: 255, nullable: false })
  @Expose()
  logsId: string;

  @Column({ name: 'log', type: 'jsonb', nullable: true })
  @Expose()
  log: EtelecomCallLogDto;

  @Column({
    name: 'created_at',
    type: 'timestamp with time zone',
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  createdAt?: Date;

  @Column({ name: 'lead_id', type: 'integer', nullable: true })
  @Expose()
  leadId?: number;

  @Column({ name: 'extension_id', type: 'integer', nullable: true })
  @Expose()
  extensionId?: number;

  @Column({ name: 'lead_care_id', type: 'integer', nullable: true })
  @Expose()
  leadCareId?: number;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  @Expose()
  userId?: number;
}
