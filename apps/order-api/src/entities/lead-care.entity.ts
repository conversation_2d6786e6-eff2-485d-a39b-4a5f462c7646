import { Expose } from 'class-transformer';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { IntegerIdEntity } from 'core/entities/base/integer-id-entity.entity';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { CareState, CareStateAfterSales } from '../enums/care-state.enum';
import { Lead } from './lead.entity';
import { LeadCareItem } from './lead-care-item.entity';
import { AppointmentSchedule } from './appointment-schedule.entity';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';

@Entity({ name: 'lead_cares' })
export class LeadCare extends IntegerIdEntity {
  @Column({
    name: 'user_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  userId?: number;

  @Column({
    name: 'lead_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  @NonEmptyTransform()
  leadId?: number;

  @Column({
    name: 'shift_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  shiftId?: number;

  @Column({
    name: 'state',
    type: 'int',
    default: CareState.no_attempt,
  })
  @Expose()
  @EnumTransform(CareState)
  state?: CareState | CareStateAfterSales;

  @ManyToOne(
    () => Lead,
    it => it.cares,
  )
  @JoinColumn({ name: 'lead_id' })
  @Expose()
  @NonEmptyTransform()
  lead?: Lead;

  @OneToMany(
    () => LeadCareItem,
    it => it.leadCare,
    { nullable: true },
  )
  @Expose()
  @NonEmptyTransform()
  careItems?: LeadCareItem[];

  @Column({
    name: 'updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  updatedBy?: number;

  @Expose()
  @NonEmptyTransform()
  upcomingAppointments?: AppointmentSchedule[];
}
