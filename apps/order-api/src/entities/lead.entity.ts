import { Expose } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { IntegerIdEntity } from 'core/entities/base/integer-id-entity.entity';
import { Column, Entity, Index, JoinColumn, OneToMany, OneToOne } from 'typeorm';
import { CareState, CareStateAfterSales } from '../enums/care-state.enum';
import { AppointmentSchedule } from './appointment-schedule.entity';
import { DuplicateLead } from './duplicate-lead.entity';
import { LeadCareItem } from './lead-care-item.entity';
import { LeadCare } from './lead-care.entity';
import { Order } from './order.entity';
import { CareReason } from './care-reason.entity';
import { Field, Int } from '@nestjs/graphql';

@Entity({ name: 'leads' })
@Index('IDX_LEAD_STATE', ['state'])
@Index('IDX_LEAD_COUNTRY', ['countryId'])
@Index('IDX_LEAD_PROJECT', ['projectId'])
@Index('IDX_LEAD_COUNTRY_PROJECT', ['countryId', 'projectId'])
export class Lead extends IntegerIdEntity {
  @Column({
    name: 'order_id',
    type: 'int',
  })
  @Index({ unique: true })
  @Expose()
  orderId: number;

  @Column({
    name: 'user_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  userId?: number;

  @Column({
    name: 'current_care_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Index()
  @NonEmptyTransform()
  currentCareId?: number;

  @Column({
    name: 'state',
    type: 'smallint',
    default: CareState.new,
  })
  @Expose()
  @EnumTransform(CareState)
  state?: CareState | CareStateAfterSales;

  @Column({
    name: 'last_updated_state',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  lastUpdatedState?: Date;

  @OneToMany(
    () => LeadCare,
    it => it.lead,
    { nullable: true },
  )
  @Expose()
  @NonEmptyTransform()
  cares?: LeadCare[];

  @OneToOne(() => Order)
  @JoinColumn({ name: 'order_id' })
  @Expose()
  @NonEmptyTransform()
  order?: Order;

  @OneToOne(() => LeadCare)
  @JoinColumn({ name: 'current_care_id' })
  @Expose()
  @NonEmptyTransform()
  currentCare?: LeadCare;

  @Column({
    name: 'updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  updatedBy?: number;

  @Column({
    name: 'ignore_duplicate_warning',
    type: 'boolean',
    default: false,
  })
  @Expose()
  @NonEmptyTransform()
  ignoreDuplicateWarning?: boolean;

  @Expose()
  @NonEmptyTransform()
  appointments?: AppointmentSchedule[];

  @OneToOne(() => LeadCareItem, { nullable: true, createForeignKeyConstraints: false })
  @JoinColumn({ name: 'last_care_item_id' })
  @Expose()
  @NonEmptyTransform()
  latestCareItem: LeadCareItem;

  @OneToMany(
    () => DuplicateLead,
    it => it.lead,
  )
  @Expose()
  @NonEmptyTransform()
  duplicateLeads?: DuplicateLead[];

  @Column({
    name: 'form_captured_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  formCapturedAt?: Date;

  @Column({
    name: 'utm_source',
    type: 'varchar',
    nullable: true,
    comment: 'Example: facebook',
  })
  @Expose()
  utmSource?: string;

  @Column({
    name: 'utm_medium',
    type: 'varchar',
    nullable: true,
    comment: 'ad.id',
  })
  @Expose()
  utmMedium?: string;

  @Column({
    name: 'utm_campaign',
    type: 'varchar',
    nullable: true,
    comment: 'campaign.id',
  })
  @Expose()
  utmCampaign?: string;

  @Column({
    name: 'utm_term',
    type: 'varchar',
    nullable: true,
    comment: 'adset.id',
  })
  @Expose()
  utmTerm?: string;

  @Column({
    name: 'link',
    type: 'text',
    nullable: true,
  })
  @Expose()
  link?: string;

  @Column({
    name: 'last_care_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastCareId?: number;

  @OneToOne(() => LeadCare, { nullable: true, createForeignKeyConstraints: false })
  @JoinColumn({ name: 'last_care_id' })
  @Expose()
  @NonEmptyTransform()
  lastCare?: LeadCare;

  @Column({
    name: 'last_care_item_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastCareItemId?: number;

  @Column({
    name: 'last_care_reason_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastCareReasonId?: number;

  @OneToOne(() => CareReason, { nullable: true, createForeignKeyConstraints: false })
  @JoinColumn({ name: 'last_care_reason_id' })
  @Expose()
  @NonEmptyTransform()
  lastCareReason?: CareReason;

  @Expose()
  @NonEmptyTransform()
  hasDuplicateLead?: boolean;

  @Column({
    name: 'lead_type',
    type: 'int',
    default: 0,
  })
  @Expose()
  leadType: number;

  @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  countryId: number;

  @Column({
    name: 'project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  projectId?: number;

  @Column({
    name: 'request_landing_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  requestLandingId?: string;
}
