import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../../../core/entities/base/base-entity.entity';
import { Expose } from 'class-transformer';
import { CallCenter } from 'apps/order-api/src/entities/call-center.entity';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { CallGroup } from 'apps/order-api/src/entities/call-group.entity';
import { CallRelatedStatus } from '../enums/call-related-status.enum';
import { CallCenterExtensionStatus } from '../enums/call-center-extension-status.enum';
@Index('IDX_CALL_CENTER_ID', ['callCenterId'])
@Entity('call_center_extensions')
export class CallCenterExtension extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id: number;

  @Column({
    name: 'extension_number',
    type: 'varchar',
    length: 255,
  })
  @Expose()
  extensionNumber: string;

  @Column({
    name: 'extension_password',
    type: 'varchar',
    length: 255,
  })
  @Expose()
  extensionPassword: string;

  @Column({
    name: 'callcenter_id',
    type: 'int',
    nullable: false,
  })
  @Expose()
  callCenterId: number;

  @ManyToOne(() => CallCenter, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'callcenter_id' })
  @Expose()
  callCenter?: CallCenter;

  @Column({
    name: 'is_available',
    type: 'boolean',
    default: true,
  })
  @Expose()
  isAvailable: boolean;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyId?: number;

  @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  countryId?: number;

  @Column({
    name: 'created_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  createdBy?: number;

  @Column({
    name: 'updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  updatedBy?: number;

  @Column({
    name: 'last_used_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  lastUsedAt?: Date;

  @Column({
    name: 'updated_at_manual',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  updatedAtManual?: Date;

  @Column({
    name: 'domain',
    type: 'text',
    nullable: true,
  })
  @Expose()
  domain: string;

  @ManyToOne(() => CallGroup)
  @JoinColumn({ name: 'call_group_id' })
  @Expose()
  callGroup?: CallGroup;

  @Column({
    name: 'last_assign_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastAssignBy?: number;

  @Column({
    name: 'status',
    type: 'int',
    default: CallCenterExtensionStatus.available,
  })
  @Expose()
  status: CallCenterExtensionStatus;

  @Column({
    name: 'prefix',
    type: 'text',
    nullable: true,
  })
  @Expose()
  prefix: string;
}
