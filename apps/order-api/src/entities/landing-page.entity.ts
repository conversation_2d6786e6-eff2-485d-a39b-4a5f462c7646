import { Expose } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { StringIdEntity } from 'core/entities/base/string-id-entity.entity';
import { CommonStatus } from 'core/enums/common-status.enum';
import { Column, DeleteDateColumn, Entity } from 'typeorm';
import { Field, Int } from '@nestjs/graphql';
import { OrderSourceType } from 'core/enums/order-source-type.enum';
import { OrderType } from '../enums/order-type.enum';

@Entity({ name: 'landing_pages' })
export class LandingPage extends StringIdEntity {
  @Column({
    name: 'name',
    type: 'varchar',
  })
  @Expose()
  name: string;

  @Column({
    name: 'link',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  link: string;

  @Column({
    name: 'country_id',
    type: 'int',
  })
  @Expose()
  countryId: number;

  @Column({
    name: 'project_id',
    type: 'int',
  })
  @Expose()
  projectId?: number;

  @Column({
    name: 'product_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  productId?: number;

  @Column({
    name: 'user_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  userId: number;

  @DeleteDateColumn({
    name: 'deleted_at',
  })
  deletedAt: Date;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyId?: number;

  @Column({
    name: 'creator_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  creatorId: number;

  @Column({
    name: 'updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  updatedBy?: number;

  @Column({
    name: 'status',
    type: 'smallint',
    default: CommonStatus.activated,
  })
  @EnumTransform(CommonStatus)
  @Expose()
  status: CommonStatus;

  @Column({
    name: 'source_type',
    type: 'smallint',
    default: OrderSourceType.telesales,
  })
  @EnumTransform(OrderSourceType)
  @Expose()
  sourceType?: OrderSourceType;

  @Column({
    name: 'order_type',
    type: 'smallint',
    default: OrderType.normal,
  })
  @Expose()
  @Field(type => Int, { nullable: true })
  // @EnumTransform(OrderType)
  orderType?: OrderType;
}
