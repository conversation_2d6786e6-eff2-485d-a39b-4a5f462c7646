import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { BullModule } from '@nestjs/bull';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BaseAuthModule } from 'core/auth/auth.module';
import { Context } from 'graphql-ws';
import { mapKeys } from 'lodash';
import {
  identityConnection,
  orderConnection,
} from '../../../core/constants/database-connection.constant';
import { PubSubModule } from '../../../core/pubsub/pub-sub.module';
import { OrderModule } from './modules/order/order.module';
import { QueueUIModule } from './modules/queue-ui/queue-ui.module';
import { WebSocketService } from './modules/websocket/services/websocket.services';
import { WebSocketModule } from './modules/websocket/websocket.module';
import { ShiftModule } from './modules/shift/shift.module';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { HttpCacheInterceptor } from '../../../core/filters/http-cache/http-cache.filter';
import { RedisCacheModule } from '../../../core/cache/redisCache.module';
import { JwtPayload } from 'jsonwebtoken';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { AppController } from './app.controller';
import { getRmqHost } from 'core/utils/loadEnv';
import { AllExceptionsFilter } from 'core/filters/all-exception.filter';
import { LoggerMiddleware } from 'core/middlewares/logger.middleware';
import { ClickhouseModule } from './modules/clickhouse/clickhouse.module';

const jwt = require('jsonwebtoken');

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        'apps/order-api/' + (process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env'),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      ...(process.env.DATABASE_USERNAME_REPLICATE
        ? {
            replication: {
              master: {
                host: process.env.DATABASE_HOST,
                port: parseInt(process.env.DATABASE_PORT),
                username: process.env.DATABASE_USERNAME,
                password: process.env.DATABASE_PASSWORD,
                database: process.env.DATABASE_ORDER,
              },
              slaves: [
                {
                  host: process.env.DATABASE_HOST_REPLICATE,
                  port: parseInt(process.env.DATABASE_PORT_REPLICATE),
                  username: process.env.DATABASE_USERNAME_REPLICATE,
                  password: process.env.DATABASE_PASSWORD_REPLICATE,
                  database: process.env.DATABASE_ORDER,
                },
              ],
            },
          }
        : {
            host: process.env.DATABASE_HOST,
            port: parseInt(process.env.DATABASE_PORT),
            username: process.env.DATABASE_USERNAME,
            password: process.env.DATABASE_PASSWORD,
            database: process.env.DATABASE_ORDER,
          }),
      entities: [__dirname + '/entities/*.entity{ .ts,.js}'],
      name: orderConnection,
      logging: ['error', 'query'],
      logger: 'advanced-console',
      extra: {
        statement_timeout: 2 * 1000 * 60,
        idle_in_transaction_session_timeout: 2 * 1000 * 60,
        max: 30,
      },
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST_REPLICATE || process.env.DATABASE_HOST,
      port: parseInt(process.env.DATABASE_PORT_REPLICATE || process.env.DATABASE_PORT),
      username: process.env.DATABASE_USERNAME_REPLICATE || process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD_REPLICATE || process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_IDENTITY,
      entities: [__dirname + '/read-entities/identity/*.entity{ .ts,.js}'],
      name: identityConnection,
      logging: ['error'],
    }),
    RabbitMQModule.forRootAsync(RabbitMQModule, {
      useFactory: () => ({
        registerHandlers: !process.env.CONSUMER || process.env.CONSUMER == 'true' ? true : false,
        exchanges: [
          {
            name: 'OrderService.Orders.StatusChanged',
            type: 'fanout',
          },
          {
            name: 'CatalogService.Orders.StatusChanged',
            type: 'fanout',
          },
          {
            name: 'CatalogService.Orders.StockFilled',
            type: 'fanout',
          },
          {
            name: 'CatalogService.Orders.ReturnSheetCreated',
            type: 'fanout',
          },
          {
            name: 'OrderService.Orders.ReturnSheetValidated',
            type: 'fanout',
          },
          {
            name: 'OrderService.Orders.OnPosOrder',
            type: 'fanout',
          },
          {
            name: 'OrderService.Orders.OnPosUsers',
            type: 'fanout',
          },
          {
            name: 'IdentityService.Users.OnPosUsersInserted',
            type: 'fanout',
          },
          {
            name: 'IdentityService.Users.OnPosUsersLinked',
            type: 'fanout',
          },
          {
            name: 'OrderService.Orders.Upsale',
            type: 'direct',
          },
          {
            name: 'OrderService.Locations.Crawl',
            type: 'direct',
          },
          {
            name: 'order-service-pancake-orders',
            type: 'direct',
          },
          {
            name: 'order-service',
            type: 'direct',
          },
          {
            name: 'order-service-shifts',
            type: 'direct',
          },
          {
            name: 'voip-exchange',
            type: 'direct',
          },
          {
            name: 'shopify-webhook-event',
            type: 'direct',
          },
        ],
        uri: getRmqHost(),
        prefetchCount: 20,
        connectionInitOptions: { wait: false },
      }),
    }),
    BaseAuthModule.forRoot(),
    OrderModule,
    QueueUIModule,
    PubSubModule,
    ShiftModule,
    BullModule.forRoot({
      redis: {
        password: process.env.REDIS_PASSWORD,
        db: 1,
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
      },
      prefix: `agbiz-order-queue-${process.env.REDIS_DB}`,
    }),
    RedisModule.forRoot({
      config: {
        password: process.env.REDIS_PASSWORD,
        db: Number(process.env.REDIS_DB_NUMBER),
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
        keyPrefix: `ag-orders-cache-${process.env.REDIS_DB}`,
      },
    }),
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      imports: [WebSocketModule],
      inject: [WebSocketService],
      useFactory: (wsService: WebSocketService) => ({
        autoSchemaFile: true,
        installSubscriptionHandlers: true,
        buildSchemaOptions: {
          dateScalarMode: 'timestamp',
        },
        subscriptions: {
          'graphql-ws': {
            path: process.env.API_SUBPATH ? process.env.API_SUBPATH + '/graphql' : '/graphql',
            onConnect: (context: Context<any, any>) => {
              const { connectionParams, extra } = context;
              const mConnectionParams = mapKeys(connectionParams, (value, key) =>
                key.toLowerCase(),
              );
              // console.log(`mConnectionParams`, mConnectionParams);
              const authorization = mConnectionParams['authorization'];
              const authToken = authorization?.split(' ')[1];
              const payload: JwtPayload = jwt.verify(authToken, process.env.SECRET_KEY_BASE);
              // console.log(`jwt payload`, payload);
              extra.user = { companyId: payload.companyId };
              return payload;
            },
          },
          'subscriptions-transport-ws': {
            path: process.env.API_SUBPATH ? process.env.API_SUBPATH + '/graphql' : '/graphql',
            onConnect: async connectionParams => {
              // console.log(`connectionParams`, connectionParams);
              // connectionParams = mapKeys(connectionParams, (value, key) =>
              //   key.toLowerCase(),
              // );
              // // console.log(`connectionParams`, connectionParams);
              // const authorization = connectionParams['authorization'];
              // const clientId = connectionParams['clientid'];
              // const authToken = authorization?.split(' ')[1];
              // const auth = jwt.verify(authToken, process.env.SECRET_KEY_BASE);
              // // console.log(`connectionParams`, connectionParams);
              // // console.log(`auth`, auth);
              // // console.log(`clientId`, clientId);
              // if (!auth || !clientId)
              //   throw new UnauthorizedException('Unauthorized');
              // await wsService.onConnect(auth.sub, clientId);
              // return { ...auth, clientId };
            },
            onDisconnect: async (_, context) => {
              // const initialContext = await context.initPromise;
              // console.log(`on disconnect context`, initialContext);
              // const { sub, clientId } = initialContext;
              // await wsService.onDisconnect(sub, clientId);
            },
          },
        },
        playground: {
          endpoint: process.env.BASE_URL ? process.env.BASE_URL + '/graphql' : undefined,
          subscriptionEndpoint: process.env.SUBSCRIPTION_ENDPOINT,
        },
        introspection: true,
      }),
    }),
    RedisCacheModule,
    ClickhouseModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpCacheInterceptor,
    },
    // {
    //   provide: APP_FILTER,
    //   useClass: AllExceptionsFilter,
    // },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    if (process.env.NODE_ENV !== 'production') {
      consumer.apply(LoggerMiddleware).forRoutes('*');
    }
  }
}
