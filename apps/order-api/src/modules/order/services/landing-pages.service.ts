import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CreateLandingPageDto,
  UpdateLandingPageDto,
} from 'apps/order-api/src/dtos/landing-page.dto';
import { LandingPage } from 'apps/order-api/src/entities/landing-page.entity';
import { LandingPagesFilter } from 'apps/order-api/src/filters/landing-pages.filter';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { Brackets, DeleteResult, Repository } from 'typeorm';
import { isEmpty, isNil, intersection } from 'lodash';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';

@Injectable()
export class LandingPagesService {
  constructor(
    @InjectRepository(LandingPage, orderConnection)
    private landingPagesRepository: Repository<LandingPage>,
  ) {}

  async getAllLandingPages(
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<[LandingPage[], number]> {
    const companyId = request?.user?.companyId;
    const qb = this.landingPagesRepository
      .createQueryBuilder('p')
      .where('p.companyId = :companyId')
      .setParameters({ companyId })
      .select(['p.name', 'p.id']);
    return qb.getManyAndCount();
  }

  async getLandingPages(
    filter: LandingPagesFilter = {},
    pagination?: PaginationOptions,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<[LandingPage[], number]> {
    const companyId = request?.user?.companyId;
    const { ids, countryIds, projectIds, userIds, query, getAllName, sourcesType, ordersType } = filter;

    const qb = this.landingPagesRepository
      .createQueryBuilder('p')
      .where('p.companyId = :companyId')
      .setParameters({ companyId });
    // if (pagination) qb.take(pagination.limit).skip(pagination.skip);
    if (countryIds) qb.andWhere('p.countryId IN (:...countryIds)', { countryIds });
    if (projectIds) qb.andWhere('p.projectId IN (:...projectIds)', { projectIds });
    qb.andWhere(
      new Brackets(sqb => {
        for (const record of request?.user?.profiles) {
          const [dataAccessLevel, moduleInCharge, scopes] = record;
          sqb.orWhere(
            new Brackets(pQb => {
              if (!isEmpty(moduleInCharge)) {
                pQb.andWhere(
                  new Brackets(mQb => {
                    for (const module of moduleInCharge) {
                      switch (module) {
                        case ModuleInCharge.marketer:
                          if (dataAccessLevel === DataAccessLevel.personal) {
                            mQb.orWhere(`p.userId = ${request?.user?.id}`);
                          } else {
                            mQb.andWhere('p.userId IN (:...userAssignedIds)', {
                              userAssignedIds: request?.user.descendants.marketerIds,
                            });
                          }
                          break;
                        default:
                          break;
                      }
                    }
                  }),
                );
              }
            }),
          );
        }
      }),
    );
    if (userIds) qb.andWhere('p.userId IN (:...userIds)', { userIds });
    if (query) qb.andWhere('p.name ~* :query', { query });
    if (ids) qb.andWhereInIds(ids);
    if (sourcesType) qb.andWhere('p.sourceType IN (:...sourcesType)', { sourcesType });
    if (ordersType) qb.andWhere('p.orderType IN (:...ordersType)', { ordersType });
    qb.orderBy('p.createdAt', 'DESC');

    const [landingPages, count] = await qb.getManyAndCount();
    return [landingPages, count];
  }

  async getNames(filter: LandingPagesFilter = {}): Promise<LandingPage[]> {
    const { ids, countryIds, projectIds, userIds, query } = filter;

    const qb = this.landingPagesRepository.createQueryBuilder('p').select(['p.name', 'p.id']);
    if (countryIds) qb.andWhere('p.countryId IN (:...countryIds)', { countryIds });
    if (projectIds) qb.andWhere('p.projectId IN (:...projectIds)', { projectIds });
    if (userIds) qb.andWhere('p.userId IN (:...userIds)', { userIds });
    if (query) qb.andWhere('p.name ~* :query', { query });
    if (ids) qb.andWhereInIds(ids);
    qb.orderBy('p.createdAt', 'DESC');
    return qb.getMany();
  }

  async getLandingPage(
    id: string,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<LandingPage> {
    const companyId = request?.user?.companyId;
    const qb = this.landingPagesRepository
      .createQueryBuilder('p')
      .where('p.id = :id')
      .andWhere('p.companyId = :companyId')
      .setParameters({ id, companyId });
    const landingPage = await qb.getOne();
    return landingPage;
  }

  async createLandingPage(
    body: CreateLandingPageDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<LandingPage> {
    const companyId = request?.user?.companyId;

    const landingPage = plainToInstance(LandingPage, {
      ...body,
      companyId,
      creatorId: request.user.id,
    });
    return this.landingPagesRepository.save(landingPage);
  }

  async updateLandingPage(
    id: string,
    body: UpdateLandingPageDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<LandingPage> {
    const companyId = request?.user?.companyId;
    const oldRecord = await this.getLandingPage(id, headers, request);
    if (!oldRecord) throw new NotFoundException('Not found landing page');
    if (oldRecord.companyId !== companyId) throw new ForbiddenException();

    const landingPage = plainToInstance(LandingPage, {
      ...oldRecord,
      ...body,
      productId:
        isNil(oldRecord.productId) && body?.productId ? body?.productId : oldRecord.productId,
      updatedBy: request.user.id,
    });

    return this.landingPagesRepository.save(landingPage);
  }

  async deleteLandingPage(
    id: string,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<DeleteResult> {
    const companyId = request?.user?.companyId;
    const landingPage = await this.getLandingPage(id, headers, request);
    if (!landingPage) throw new NotFoundException('Not found landing page');
    if (landingPage.companyId !== companyId) throw new ForbiddenException();

    return this.landingPagesRepository.delete(id);
  }

  async findById(id: string): Promise<LandingPage> {
    const qb = this.landingPagesRepository
      .createQueryBuilder('p')
      .where('p.id = :id')
      .setParameters({ id });
    const landingPage = await qb.getOne();
    return landingPage;
  }
}
