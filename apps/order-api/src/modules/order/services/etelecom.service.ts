import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { orderConnection } from 'core/constants/database-connection.constant';
import { getConnection, Repository } from 'typeorm';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import {
  CallCenter,
  CallCenterExtension,
  CallHistory,
} from 'apps/order-api/src/entities';
import { CallGroup } from 'apps/order-api/src/entities/call-group.entity';
import { CallGroupUser } from 'apps/order-api/src/entities/call-group-user.entity';
import { CreateCallGroupDto } from 'apps/order-api/src/dtos/create-call-group.dto';
import { CallRelatedStatus } from 'apps/order-api/src/enums/call-related-status.enum';
import { AddCallGroupExtensionDto } from 'apps/order-api/src/dtos/add-call-group-extension';
import axios, { AxiosResponse } from 'axios';
import { EtelecomCreateExtensionResponseDto } from 'apps/order-api/src/dtos/etelecom-create-extension-response.dto';
import { plainToInstance } from 'class-transformer';
import { AddCallGroupUserDto } from 'apps/order-api/src/dtos/add-call-group-user.dto';
import { UpdateCallGroupDto } from 'apps/order-api/src/dtos/update-call-group.dto';
import { CallCenterDto } from 'apps/order-api/src/dtos/call-center.dto';
import { CallGroupUserStatus } from 'apps/order-api/src/enums/call-group-user-status.enum';
import { CallCenterExtensionStatus } from 'apps/order-api/src/enums/call-center-extension-status.enum';
import { SearchCallGroupDto } from 'apps/order-api/src/dtos/searchCallGroup.dto';
import { AddCallGroupExtensionByPickDto } from 'apps/order-api/src/dtos/add-call-group-extension-by-pick.dto';
import { GetEtelecomCallHistoryFilter } from 'apps/order-api/src/filters/get-etelecom-call-history.filter';
import { EtelecomCallLogDto, EtelecomRawLogDto } from 'apps/order-api/src/dtos/etelecom-call-log.dto';
import {
  CallHistoryStatus,
  CallHistoryType,
  convertEtelecomCallStatusToEnum,
} from 'apps/order-api/src/enums/call-center.enum';
import { AwsUtils } from 'core/utils/AwsUtils';
import * as FormData from 'form-data';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bullmq';
import * as moment from 'moment-timezone';
import { CallHistoryFilter } from 'apps/order-api/src/filters/call-history.filter';
import { SystemLog } from 'apps/order-api/src/entities/system-log.entity';
import { EtelecomRawLogs } from 'apps/order-api/src/entities/etelecom-raw-logs.entity';
import { Brackets } from 'typeorm';
import { CallPresaveDto } from 'apps/order-api/src/dtos/call-presave.dto';
import { log } from 'console';
import { random } from 'lodash';

@Injectable()
export class EtelecomService {
  constructor(
    @InjectRepository(CallCenterExtension, orderConnection)
    private callCenterExtensionRepository: Repository<CallCenterExtension>,
    @InjectRepository(CallCenter, orderConnection)
    private callCenterRepository: Repository<CallCenter>,
    @InjectRepository(CallGroup, orderConnection)
    private callGroupRepository: Repository<CallGroup>,
    @InjectRepository(CallHistory, orderConnection)
    private callHistoryRepository: Repository<CallHistory>,
    @InjectRepository(CallGroupUser, orderConnection)
    private callGroupUserRepository: Repository<CallGroupUser>,
    @InjectRepository(SystemLog, orderConnection)
    private logsRepo: Repository<SystemLog>,
    @InjectRepository(EtelecomRawLogs, orderConnection)
    private rawLogsRepo: Repository<EtelecomRawLogs>,
    @InjectQueue('callcenter')
    private callCenterQueue: Queue,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async getCallCenter(callCenterDto: CallCenterDto) {
    try {
      const callCenterQb = await this.callCenterRepository
        .createQueryBuilder('cc')
        .select([
          'cc.id, cc.name, cc.country_id, cc.company_id, cc.is_active, cc.created_by, cc.updated_by, cc.metadata, cc.status, cc.sip_wss_url, cc.sip_domain',
        ]);

      if (callCenterDto.callCenterId) {
        callCenterQb.andWhere('cc.id = :callCenterId', {
          callCenterId: callCenterDto.callCenterId,
        });
      }
      if (callCenterDto.customerId) {
        callCenterQb.andWhere('cc.customer_id = :customerId', {
          customerId: callCenterDto.customerId,
        });
      }
      if (callCenterDto.countryId) {
        callCenterQb.andWhere('cc.country_id = :countryId', {
          countryId: callCenterDto.countryId,
        });
      }
      if (callCenterDto.name) {
        callCenterQb.andWhere('cc.name = :name', {
          name: callCenterDto.name,
        });
      }
      const callCenter = await callCenterQb.getRawMany();
      if (!callCenter || callCenter.length === 0) {
        return [
          {
            id: null,
            name: null,
            country_id: null,
            company_id: null,
            is_active: null,
            created_by: null,
            updated_by: null,
            metadata: null,
            status: 1,
            sip_wss_url: null,
            sip_domain: null,
          },
        ];
      }
      return callCenter;
    } catch (error) {
      console.error('Error while getting call center:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getCallCenterUsers(callCenterId: number) {
    try {
      const listUsers = await this.callGroupUserRepository
        .createQueryBuilder('call_group_user')
        .innerJoinAndSelect('call_group_user.callGroup', 'call_group')
        .where('call_group.call_center_id = :callCenterId', { callCenterId })
        .getMany();

      if (!listUsers) {
        return [];
      }

      return listUsers;
    } catch (error) {
      console.error('Error while getting call center users:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getCallCenterCount(callCenterId: number) {
    try {
      const callGroups = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .leftJoin('call_group.users', 'user')
        .leftJoin('call_group.extensions', 'extension')
        .where('call_group.call_center_id = :callCenterId', { callCenterId })
        .select([
          'call_group.id AS groupId',
          'call_group.name AS groupName',
          'COUNT(DISTINCT user.id) AS  numberOfUsers',
          'COUNT(DISTINCT extension.id) AS numberOfExtensions',
        ])
        .groupBy('call_group.id')
        .addGroupBy('call_group.name')
        .getRawMany();

      return callGroups;
    } catch (error) {
      console.error('Error while getting call center count:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async findCallGroups(body: SearchCallGroupDto) {
    try {
      const qb = this.callGroupRepository.createQueryBuilder('call_group');

      if (body.callCenterId) {
        qb.where('call_group.call_center_id = :callCenterId', {
          callCenterId: body.callCenterId,
        });
      }

      if (body.callGroupName) {
        qb.andWhere('call_group.name ILIKE :name', {
          name: `%${body.callGroupName}%`,
        });
      }

      if (
        (body.createdBy && body.createdBy.length > 0) ||
        (body.userId && body.userId.length > 0)
      ) {
        qb.andWhere(
          new Brackets(orQb => {
            if (body.createdBy && body.createdBy.length > 0) {
              orQb.orWhere('call_group.created_by IN (:...createdByIds)', {
                createdByIds: body.createdBy,
              });
            }

            if (body.userId && body.userId.length > 0) {
              qb.leftJoin('call_group.users', 'call_group_user');
              orQb.orWhere('call_group_user.user_id IN (:...userIds)', {
                userIds: body.userId,
              });
            }
          }),
        );
      }
      return await qb.getMany();
    } catch (error) {
      console.error('Error while finding call groups:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getCallGroup(id: number) {
    try {
      const callGroup = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .leftJoinAndSelect('call_group.extensions', 'extension')
        .leftJoinAndSelect('call_group.users', 'user')
        .where('call_group.id = :id', { id })
        .getOne();

      if (!callGroup) {
        return [];
      }

      return callGroup;
    } catch (error) {
      console.error('Error while getting call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getCallCenterCallGroup(callCenterId: number) {
    try {
      const callGroup = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .where('call_center_id = :callCenterId', { callCenterId })
        .getMany();

      if (!callGroup) {
        return [];
      }

      return callGroup;
    } catch (error) {
      console.error('Error while getting call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async createCallGroup(createCallGroupDto: CreateCallGroupDto) {
    const { name, callCenterId } = createCallGroupDto;
    try {
      const callCenter = await this.callCenterRepository
        .createQueryBuilder('call_center')
        .where('call_center.id = :id', { id: callCenterId })
        .getOne();

      if (!callCenter) {
        return [];
      }

      const callGroup = new CallGroup();
      callGroup.name = name;
      callGroup.callCenter = callCenter;
      callGroup.status = CallRelatedStatus.connected;
      callGroup.createdBy = createCallGroupDto.createdBy;
      await this.callGroupRepository.save(callGroup);

      const log = plainToInstance(SystemLog, {
        action: 'CREATE',
        tableName: 'call_group',
        changes: [name],
        recordId: callGroup.id,
        parentTableName: 'call_center',
        parentId: callCenterId,
        creatorId: createCallGroupDto.createdBy,
      });

      await this.logsRepo.save(log);

      return callGroup;
    } catch (error) {
      console.error('Error while creating call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async deactivateCallGroup(id: number) {
    try {
      const callGroup = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .innerJoinAndSelect('call_group.callCenter', 'call_center')
        .where('call_group.id = :id', { id })
        .getOne();
      if (!callGroup) {
        return [];
      }
      callGroup.status = CallRelatedStatus.suspended;
      await this.callGroupRepository.save(callGroup);
      return callGroup;
    } catch (error) {
      console.error('Error while deactivating call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async updateCallGroup(id: number, updateCallGroupDto: UpdateCallGroupDto) {
    try {
      const callGroup = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .innerJoinAndSelect('call_group.callCenter', 'call_center')
        .where('call_group.id = :id', { id })
        .getOne();
      if (!callGroup) {
        throw new BadRequestException('Call group not found');
      }
      const oldName = callGroup.name;
      Object.assign(callGroup, updateCallGroupDto);

      const updatedCallGroup = await this.callGroupRepository.save(callGroup);
      const log = plainToInstance(SystemLog, {
        action: 'UPDATE',
        tableName: 'call_group',
        changes: [updatedCallGroup.name],
        beforeChanges: [oldName],
        recordId: callGroup.id,
        parentTableName: 'call_center',
        parentId: callGroup.callCenter.id,
        creatorId: updateCallGroupDto.updatedBy,
      });

      await this.logsRepo.save(log);

      return updatedCallGroup;
    } catch (error) {
      console.error('Error while updating call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async removeCallGroup(callGroupId: number, removedBy: number) {
    const connection = getConnection('orderConnection');
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callGroupId,
      );
      await this.redis.del(redisKey);

      if (!callGroup) {
        return [];
      }

      const log = plainToInstance(SystemLog, {
        action: 'DELETE',
        tableName: 'call_group',
        changes: [callGroup.name],
        parentTableName: 'call_center',
        recordId: callGroup.id,
        parentId: callGroup.callCenter.id,
        creatorId: removedBy,
      });

      await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from(this.callGroupUserRepository.target)
        .where('call_group_id = :callGroupId', { callGroupId })
        .execute();

      await queryRunner.manager
        .createQueryBuilder()
        .update(this.callCenterExtensionRepository.target)
        .set({ callGroup: null })
        .where('call_group_id = :callGroupId', { callGroupId })
        .execute();

      await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from(this.callGroupRepository.target)
        .where('id = :callGroupId', { callGroupId })
        .execute();

      await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(SystemLog)
        .values(log)
        .execute();

      await queryRunner.commitTransaction();
      return true;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Error while removing call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    } finally {
      await queryRunner.release();
    }
  }

  private async createSingleExtension(
    callCenter: any,
    extensionNumber: number,
  ): Promise<EtelecomCreateExtensionResponseDto> {
    const BASE_URL = process.env.ETELECOM_SANDBOX_BASE_URL;

    try {
      const response = await axios.post(
        `${BASE_URL}.Etelecom/CreateExtension`,
        {
          hotline_id: callCenter.hotlineId,
          extension_number: extensionNumber,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${callCenter.apiKey}`,
          },
        },
      );
      if (response.status !== 200) {
        return null;
      }
      return plainToInstance(EtelecomCreateExtensionResponseDto, response.data);
    } catch (err) {
      throw new Error('Failed to create extension');
    }
  }

  public async createExtension(numberExtensions: number, callCenterId: number) {
    try {
          const callCenter = await this.callCenterRepository.findOne({
            where: { id: callCenterId },
          });

          if (!callCenter) {
            throw new NotFoundException('Call center not found');
          }

          const existExtensionNumber = await this.callCenterExtensionRepository
            .createQueryBuilder('extension')
            .select(['extension.extensionNumber'])
            .where('extension.callCenterId = :callCenterId', { callCenterId })
            .getRawMany();
          const newExtensions = (() => {
            const existing = new Set(
              existExtensionNumber.map(e =>
                Number(e.extension_extension_number),
              ),
            );

            const result: number[] = [];
            let i = 1000;

            while (result.length < 1000) {
              if (!existing.has(i)) result.push(i);
              i++;
            }

            return result;
          })();
          // console.log('New extensions to create:', newExtensions);

          const extensions: EtelecomCreateExtensionResponseDto[] = [];
          let attempt = 0;
          let index = 0;
          while (extensions.length < numberExtensions) {
            attempt++;
            try {
              const ext = await this.createSingleExtension(
                callCenter,
                newExtensions[index],
              );
              index++;
              if (ext !== null) extensions.push(ext);
            } catch (err) {
              console.warn(
                `Extension creation failed at attempt ${attempt}. Retrying..., error: ${err.message}`,
              );
              index++;
              if (attempt >= 10) {
                break;
              }
            }
          }
          const newExtensionsSaveToDB = this.callCenterExtensionRepository.create(
            extensions.map(extension => ({
              callCenterId: callCenter.id,
              extensionNumber: extension.extension_number,
              extensionPassword: extension.extension_password,
              domain: extension.tenant_domain,
              countryId: callCenter.countryId,
              companyId: callCenter.companyId,
              isAvailable: true,
              status: CallCenterExtensionStatus.available,
            })),
          );
          const savedExtensions = await this.callCenterExtensionRepository.save(
            newExtensionsSaveToDB,
          );
          return savedExtensions;
        } catch (error) {
      console.error('Error while creating extension:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async addExtensionToCallGroup(
    callGroupId: number,
    body: AddCallGroupExtensionDto,
  ) {
    try {
      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callGroupId,
      );
      if (!callGroup) {
        throw new NotFoundException('Call group not found');
      }

      const callGroupCount = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .leftJoin('call_group.users', 'user')
        .leftJoin('call_group.extensions', 'extension')
        .where('call_group.id = :callGroupId', { callGroupId })
        .select([
          'COUNT(DISTINCT user.id) AS  numberOfUsers',
          'COUNT(DISTINCT extension.id) AS numberOfExtensions',
        ])
        .getRawOne();

      const numberOfUsers = Number(callGroupCount.numberofusers);
      const numberOfExtensions = Number(callGroupCount.numberofextensions);

      if (numberOfUsers < numberOfExtensions + body.numberExtension) {
        throw new BadRequestException(
          'Cannot add too many extensions to call group. The number of extensions cannot exceed the number of users in the group.',
        );
      }

      let newExtensions = await this.createExtension(
        body.numberExtension,
        callGroup.callCenter.id,
      );

      newExtensions = newExtensions.map(extension => {
        extension.callGroup = callGroup;
        extension.status = CallCenterExtensionStatus.available;
        extension.lastAssignBy = body.addByUserId;
        extension.createdBy = body.addByUserId;
        return extension;
      });
      const savedExtensions = await this.callCenterExtensionRepository.save(
        newExtensions,
      );
      for (const extension of savedExtensions) {
        await this.redis.lpush(redisKey, extension.id.toString());
      }

      const log = plainToInstance(SystemLog, {
        action: 'UPDATE',
        tableName: 'call_center_extensions',
        recordId: callGroup.id,
        changes: [
          callGroup.name,
          ...savedExtensions.map(ext => ext.extensionNumber),
        ],
        parentTableName: 'call_center',
        parentId: callGroup.callCenter.id,
        creatorId: body.addByUserId,
      });

      await this.logsRepo.save(log);

      return savedExtensions;
    } catch (error) {
      console.error('Error while adding extension to call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async addExtensionToCallGroupByPick(
    callGroupId: number,
    body: AddCallGroupExtensionByPickDto,
  ) {
    try {
          const { extensionIds, addByUserId } = body;
          const {
            key: redisKey,
            callGroup,
          } = await this.createCallGroupRedisKey(callGroupId);
          if (!callGroup) {
            throw new NotFoundException('Call group not found');
          }

          if (!extensionIds || extensionIds.length === 0) {
            throw new BadRequestException('No extension IDs provided');
          }

          // const callGroupCount = await this.callGroupRepository
          //   .createQueryBuilder('call_group')
          //   .leftJoin('call_group.users', 'user')
          //   .leftJoin('call_group.extensions', 'extension')
          //   .where('call_group.id = :callGroupId', { callGroupId })
          //   .select([
          //     'COUNT(DISTINCT user.id) AS  numberOfUsers',
          //     'COUNT(DISTINCT extension.id) AS numberOfExtensions',
          //   ])
          //   .getRawOne();

          // const numberOfUsers = Number(callGroupCount.numberofusers);
          // const numberOfExtensions = Number(callGroupCount.numberofextensions);

          // if (numberOfUsers < numberOfExtensions + body.extensionIds.length) {
          //   throw new BadRequestException(
          //     'Cannot add too many extensions to call group. The number of extensions cannot exceed the number of users in the group.',
          //   );
          // }

          const extensions = await this.callCenterExtensionRepository
            .createQueryBuilder('extension')
            .where('extension.id IN (:...extensionIds)', { extensionIds })
            .andWhere('extension.call_group_id IS NULL')
            .getMany();

          if (extensions.length === 0) {
            throw new NotFoundException('No extensions found to add');
          }
          await this.callCenterExtensionRepository
            .createQueryBuilder()
            .update()
            .set({
              callGroup: callGroup,
              lastAssignBy: addByUserId,
            })
            .whereInIds(extensions.map(ext => ext.id))
            .execute();

          for (const extension of extensions) {
            await this.redis.lpush(redisKey, extension.id.toString());
          }

          const log = plainToInstance(SystemLog, {
            action: 'UPDATE',
            tableName: 'call_center_extensions',
            recordId: callGroup.callCenter.id,
            changes: [
              callGroup.name,
              ...extensions.map(ext => ext.extensionNumber),
            ],
            parentTableName: 'call_center',
            parentId: callGroup.callCenter.id,
            creatorId: body.addByUserId,
          });

          await this.logsRepo.save(log);

          return extensions;
        } catch (error) {
      console.error('Error while adding extension to call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async removeExtensionFromCallGroup(
    callGroupId: number,
    numberExtensions: number,
  ) {
    try {
      const extensions = await this.callCenterExtensionRepository
        .createQueryBuilder('extension')
        .select(['extension.id'])
        .where('extension.call_group_id = :callGroupId', { callGroupId })
        .orderBy('extension.id', 'ASC')
        .limit(numberExtensions)
        .getMany();

      if (extensions.length === 0) {
        return [];
      }

      const extensionIds = extensions.map(ext => ext.id);

      await this.callCenterExtensionRepository
        .createQueryBuilder()
        .update()
        .set({ callGroup: null })
        .whereInIds(extensionIds)
        .execute();

      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callGroupId,
      );
      for (const extensionId of extensionIds) {
        await this.redis.lrem(redisKey, 0, extensionId.toString());
      }
      return extensionIds;
    } catch (error) {
      console.error('Error while removing extension from call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async removeExtensionFromCallGroupByPick(
    callGroupId: number,
    extIds: number[],
  ) {
    try {
      const extensions = await this.callCenterExtensionRepository
        .createQueryBuilder('extension')
        .select(['extension.id'])
        .where('extension.call_group_id = :callGroupId', { callGroupId })
        .orderBy('extension.id', 'ASC')
        .andWhere('extension.id IN (:...extIds)', {
          extIds,
        })
        .getMany();

      if (extensions.length === 0) {
        return [];
      }

      const extensionIds = extensions.map(ext => ext.id);

      await this.callCenterExtensionRepository
        .createQueryBuilder()
        .update()
        .set({ callGroup: null })
        .whereInIds(extensionIds)
        .execute();

      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callGroupId,
      );
      for (const extensionId of extensionIds) {
        await this.redis.lrem(redisKey, 0, extensionId.toString());
      }

      return extensionIds;
    } catch (error) {
      console.error('Error while removing extension from call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getExtensionNotInCallGroup(callCenterId: number) {
    try {
      const extensions = await this.callCenterExtensionRepository
        .createQueryBuilder('extension')
        .where('extension.callcenter_id = :callCenterId', { callCenterId })
        .andWhere('extension.call_group_id IS NULL')
        .getMany();

      return extensions;
    } catch (error) {
      console.error('Error while getting extensions not in call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getCallGroupUser(callGroupId: number) {
    try {
      const callGroup = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .where('call_group.id = :callGroupId', {
          callGroupId,
        })
        .getOne();
      if (!callGroup) {
        return [];
      }

      const listCallGroupUsers = await this.callGroupUserRepository
        .createQueryBuilder('call_group_user')
        .where('call_group_user.call_group_id = :callGroupId', {
          callGroupId,
        })
        .getMany();

      const listCallGroupUsersId = listCallGroupUsers.map(user => user.userId);
      if (listCallGroupUsersId.length === 0) {
        return [];
      }

      const userIdentity = { data: [] as any[] };
      const batchSize = 20;
      for (let i = 0; i < listCallGroupUsersId.length; i += batchSize) {
        const batchIds = listCallGroupUsersId.slice(i, i + batchSize);
        const batchResult = await this.getUserInfoFromIdentityService(batchIds);
        if (batchResult && batchResult.data) {
          userIdentity.data.push(...batchResult.data);
        }
      }
      if (!userIdentity) {
        throw new NotFoundException('Error while getting call group user');
      }
      const response = userIdentity.data.map(user => {
        return {
          IdentityInfo: user,
          InGroupInfo: listCallGroupUsers.find(call => call.userId === user.id),
        };
      });
      return response;
    } catch (error) {
      console.error('Error while getting call group user:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async addUserToCallGroup(callGroupId: number, body: AddCallGroupUserDto) {
    const connection = getConnection('orderConnection');
    const queryRunner = connection.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const callGroup = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .innerJoinAndSelect('call_group.callCenter', 'callCenter')
        .where('call_group.id = :callGroupId', { callGroupId })
        .getOne();

      if (!callGroup) {
        throw new NotFoundException('Call group not found');
      }

      const existCallGroupUser = await this.callGroupUserRepository
        .createQueryBuilder('call_group_user')
        .where('call_group_user.user_id IN (:...userIds)', {
          userIds: body.userIds,
        })
        .getMany();
      const listUserAlreadyInCallGroup = existCallGroupUser.map(
        user => user.userId,
      );

      // Filter user already in call group
      const listNewCallGroupUser = await body.userIds
        .filter(uId => !listUserAlreadyInCallGroup.includes(uId))
        .map(uId => ({
          callGroup,
          userId: uId,
          lastCallId: null,
          addedBy: body.addByUserId,
          status: CallGroupUserStatus.active,
        }));

      // Update call group id in users that already assign to another call group
      if (listUserAlreadyInCallGroup.length > 0) {
        await queryRunner.manager
          .createQueryBuilder()
          .update(this.callGroupUserRepository.target)
          .set({
            callGroup: callGroup,
            addedBy: body.addByUserId,
            status: CallGroupUserStatus.active,
          })
          .where('user_id IN (:...userIds)', {
            userIds: listUserAlreadyInCallGroup,
          })
          .execute();
      }
      if (listNewCallGroupUser.length > 0) {
        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(this.callGroupUserRepository.target)
          .values(listNewCallGroupUser)
          .execute();
      }

      const userInfoFromIdentity = await this.getUserInfoFromIdentityService(
        body.userIds,
      );

      const log = plainToInstance(SystemLog, {
        action: 'UPDATE',
        tableName: 'call_group_user',
        changes: [
          callGroup.name,
          ...userInfoFromIdentity.data.map(user => user.name),
        ],
        parentTableName: 'call_center',
        parentId: callGroup.callCenter.id,
        recordId: callGroup.id,
        creatorId: body.addByUserId,
      });

      await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(SystemLog)
        .values(log)
        .execute();

      await queryRunner.commitTransaction();
      return await this.getCallGroupUser(callGroupId);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Error while adding user to call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    } finally {
      await queryRunner.release();
    }
  }

  async removeUserFromCallGroup(
    callGroupId: number,
    ids: number | number[],
    removedBy: number,
  ) {
    try {
      if (!Array.isArray(ids)) {
        ids = [ids];
      }
      if (!ids || ids.length === 0) {
        return await this.getCallGroupUser(callGroupId);
      }

      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callGroupId,
      );

      await this.callGroupUserRepository
        .createQueryBuilder('call_group_user')
        .delete()
        .where('call_group_id = :callGroupId', { callGroupId })
        .andWhere('user_id IN (:...userIds)', { userIds: ids })
        .execute();

      const users = await this.getUserInfoFromIdentityService(ids);

      const log = plainToInstance(SystemLog, {
        action: 'DELETE',
        tableName: 'call_group_user',
        parentId: callGroup.callCenter.id,
        changes: [callGroup.name, ...users.data.map(user => user.name)],
        parentTableName: 'call_center',
        recordId: callGroup.id,
        creatorId: removedBy,
      });

      await this.logsRepo.save(log);

      return true;
    } catch (error) {
      console.error('Error while removing user from call group:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getUserInfoFromIdentityService(userId: number[]) {
    try {
      const api_key = process.env.ORDER_IDENTITY_API_KEY;
      const headers = {
        'Content-Type': 'application/json',
        'identity-api-key': api_key,
      };
      const response = await axios.get(
        `${process.env.IDENTITY_BASE_URL}/users/internal`,
        {
          headers,
          params: {
            ids: userId.map(Number),
          },
        },
      );
      if (response.status !== 200) {
        throw new BadRequestException(
          `Failed to fetch user info: ${response.status}`,
        );
      }
      return response.data;
    } catch (error) {
      console.error('Error fetching user info:', error);
      throw new BadRequestException('Failed to fetch user info');
    }
  }

  async createCallGroupRedisKey(
    callGroupId: number,
  ): Promise<{ key: string; callGroup: CallGroup }> {
    const callGroup = await this.callGroupRepository
      .createQueryBuilder('call_group')
      .innerJoinAndSelect('call_group.callCenter', 'callCenter')
      .where('call_group.id = :callGroupId', {
        callGroupId,
      })
      .getOne();
    if (!callGroup) throw new NotFoundException('Call group not found');
    const key = `Call_group:${callGroupId}-${callGroup.name}`;
    return { key, callGroup };
  }

  async reinitRedisSetForCallGroup(callGroupId: number) {
    try {
      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callGroupId,
      );
      if (!callGroup) {
        throw new NotFoundException('Call group not found');
      }
      const listCallExtension = await this.callCenterExtensionRepository
        .createQueryBuilder('extension')
        .where('extension.call_group_id = :callGroupId', { callGroupId })
        .getMany();
      if (listCallExtension.length === 0) {
        return [];
      }
      await this.redis.del(redisKey);
      for (const extension of listCallExtension) {
        await this.redis.lpush(redisKey, extension.id.toString());
      }
      return await this.redis.llen(redisKey);
    } catch (error) {
      console.error(
        'Error while reinitializing Redis set for call group:',
        error,
      );
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async createCountryRedisKey(countryId: number): Promise<string> {
    try {
      const key = `Etelecom_callcenter:${countryId}`;
      return key;
    } catch (error) {
      console.error('Error while creating country Redis key:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async checkEtelecomCallCenterStatusByCountryAndCompany(
    countryId: number,
    companyId: number,
  ) {
    try {
      const key = await this.createCountryRedisKey(countryId);
      const etelecomStatusInCountry = await this.redis.get(key);
      if (etelecomStatusInCountry) {
        return etelecomStatusInCountry;
      }
      const callCenter = await this.callCenterRepository
        .createQueryBuilder('call_center')
        .where('call_center.country_id = :countryId', {
          countryId,
        })
        .andWhere('call_center.company_id = :companyId', {
          companyId,
        })
        .andWhere('call_center.name =  :callCenterName', {
          callCenterName: 'ETelecom',
        })
        .getOne();
      if (!callCenter) {
        await this.redis.set(key, CallRelatedStatus.disconnected);
        return CallRelatedStatus.disconnected;
      }
      await this.redis.set(key, callCenter.status);
      return callCenter.status;
    } catch (error) {}
  }

  async getEtelecomExtensionToCall(
    callGroupId: number,
    countryId: number,
    companyId: number,
  ) {
    try {
      const etelecomStatusInCountryString = await this.checkEtelecomCallCenterStatusByCountryAndCompany(
        countryId,
        companyId,
      );
      const etelecomStatusInCountry = Number(etelecomStatusInCountryString);
      if (etelecomStatusInCountry !== CallRelatedStatus.connected) {
        throw new BadRequestException(
          'Etelecom call center is not connected in this country',
        );
      }

      const listCallExtension = await this.callCenterExtensionRepository
        .createQueryBuilder('extension')
        .where('extension.call_group_id = :callGroupId', { callGroupId })
        .getMany();
      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callGroupId,
      );
      let chosenExtension;
      let randomMember;
      while ((randomMember = await this.redis.lpop(redisKey))) {
        if (listCallExtension.some(ext => ext.id.toString() === randomMember)) {
          chosenExtension = listCallExtension.find(
            ext => ext.id.toString() === randomMember,
          );
          break;
        }
      }
      if (!chosenExtension) {
        throw new NotFoundException(
          'No available extensions found for this call group',
        );
      }

      const callCenter = await this.getCallCenter({
        callCenterId: chosenExtension.callCenterId,
        customerId: null,
        countryId: null,
        name: null,
      });
      if (!callCenter) {
        throw new NotFoundException('Call center not found');
      }

      await this.callCenterExtensionRepository
        .createQueryBuilder()
        .update('call_center_extensions')
        .set({
          status: CallCenterExtensionStatus.unavailable,
          lastUsedAt: new Date(),
        })
        .where('id = :id', { id: chosenExtension.id })
        .execute();

      const res = { extension: chosenExtension, callCenter: callCenter };
      return res;
    } catch (error) {
      console.error('Error while getting Etelecom extension to call:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async releaseEtelecomExtension(id: number) {
    try {
      const callExtension = await this.callCenterExtensionRepository
        .createQueryBuilder('extension')
        .innerJoinAndSelect('extension.callGroup', 'callGroup')
        .where('extension.id = :id', { id })
        .getOne();
      if (!callExtension) {
        return [];
      }

      const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
        callExtension.callGroup.id,
      );

      const listReleased = await this.getGroupRedisList(
        callExtension.callGroup.id,
      );
      if (listReleased.includes(callExtension.id.toString())) {
        return [];
      }

      await this.redis.lpush(redisKey, callExtension.id.toString());

      await this.callCenterExtensionRepository
        .createQueryBuilder()
        .update('call_center_extensions')
        .set({
          status: CallCenterExtensionStatus.available,
          lastUsedAt: new Date(),
        })
        .where('id = :id', { id: id })
        .execute();

      return callExtension;
    } catch (error) {
      console.error('Error while releasing Etelecom extension:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getGroupRedisList(callGroupId: number) {
    const { key: redisKey, callGroup } = await this.createCallGroupRedisKey(
      callGroupId,
    );
    const members = await this.redis.lrange(redisKey, 0, -1);
    if (!members || members.length === 0) {
      return [];
    }
    return members;
  }

  async checkCallGroupName(callCenterId: number, name: string) {
    try {
      name = name.trim();
      const callGroup = await this.callGroupRepository
        .createQueryBuilder('call_group')
        .where('call_group.call_center_id = :callCenterId', {
          callCenterId,
        })
        .andWhere('call_group.name = :name', { name })
        .getOne();

      if (callGroup) {
        return { exists: true, callGroupId: callGroup.id };
      }
      return { exists: false };
    } catch (error) {
      console.error('Error while checking call group name:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  // Call history
  async getCallHistoryByCrawl(body: GetEtelecomCallHistoryFilter) {
    try {
      const { callCenterId } = body;
      let { fromCallAt, toCallAt } = body;

      if (!fromCallAt || !toCallAt) {
        toCallAt = new Date();
        fromCallAt = new Date(toCallAt.getTime() - 30 * 60 * 1000);
      }

      const callCenter = await this.callCenterRepository
        .createQueryBuilder('call_center')
        .where('call_center.id = :callCenterId', { callCenterId })
        .getOne();
      if (!callCenter) {
        throw new NotFoundException('Call center not found');
      }
      const BASE_URL = process.env.ETELECOM_SANDBOX_BASE_URL;
      const callLogBody = {
        filter: {
          date_from: fromCallAt ? fromCallAt.toISOString() : null,
          date_to: toCallAt ? toCallAt.toISOString() : null,
        },
      };
      const response = await axios.post(
        `${BASE_URL}.Etelecom/ListCallLogs`,
        callLogBody,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${callCenter.apiKey}`,
          },
        },
      );

      const listCallLogs = response.data.call_logs;
      const callLogsToInsert = listCallLogs.map(log => ({
        log: plainToInstance(EtelecomCallLogDto, log),
      }));
      if (callLogsToInsert.length === 0) {
        return [];
      }
      for (const callLog of callLogsToInsert) {
        // console.log('Checking call log: ', callLog.log);
        await this.rawLogsRepo
          .createQueryBuilder('etelecom_raw_logs')
          .update()
          .set({ log: callLog.log })
          .where('etelecom_raw_logs.logsid = :logsid', {
            logsid: callLog.log.external_session_id,
          })
          .execute();
      }

      return listCallLogs;
    } catch (error) {
      console.error('Error while getting call history:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async processCallHistoryCrawl() {
    try {
      const listCallCenter = await this.callCenterRepository
        .createQueryBuilder('call_center')
        .where('call_center.name =  :callCenterName', {
          callCenterName: 'ETelecom',
        })
        .getMany();
      for (const callCenter of listCallCenter) {
        await this.getCallHistoryByCrawl({ callCenterId: callCenter.id });
      }
    } catch (error) {
      console.error('Error while processing call history crawl:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async processCallHistories() {
    try {
      const currentBatch = await this.rawLogsRepo
        .createQueryBuilder('raw_logs')
        .orderBy('raw_logs.created_at', 'DESC')
        .limit(500)
        .getMany();

      const listLogs = currentBatch.map(log => ({
        log: plainToInstance(EtelecomCallLogDto, log.log),
        userId: log.userId,
        leadId: log.leadId,
        extensionId: log.extensionId,
        leadCareId: log.leadCareId,
      }));
      if (listLogs.length === 0) {
        return [];
      }
      await this.processCallHistory(listLogs);
      return listLogs;
    } catch (error) {
      // console.error('Error while processing call histories:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  onModuleInit() {
    this.callHistoryCrawlJob();
    this.callHistoryProcessJob();
  }

  async callHistoryCrawlJob() {
    const jobName = 'call-history-crawl-job';
    try {
      const repeatableJobs = await this.callCenterQueue.getRepeatableJobs();
      for (const job of repeatableJobs) {
        if (job.id !== jobName) {
          continue;
        }
        await this.callCenterQueue.removeRepeatableByKey(job.key);
      }

      const queue = await this.callCenterQueue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            cron: '*/2 * * * *',
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: true,
        },
      );

      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm'),
      );
    } catch (e) {
      // console.log(e);
    }
  }

  async callHistoryProcessJob() {
    const jobName = 'call-history-process-job';
    try {
      const repeatableJobs = await this.callCenterQueue.getRepeatableJobs();
      for (const job of repeatableJobs) {
        if (job.id !== jobName) {
          continue;
        }
        await this.callCenterQueue.removeRepeatableByKey(job.key);
      }

      const queue = await this.callCenterQueue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            cron: '*/2 * * * *',
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: true,
        },
      );

      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm'),
      );
    } catch (e) {
      // console.log(e);
    }
  }

  async processCallRecording(
    callHistoryId: number,
    recordingUrl: string,
    leadId: number,
  ) {
    try {
      const callHistory = await this.callHistoryRepository
        .createQueryBuilder('call_history')
        .where('call_history.id = :callHistoryId', { callHistoryId })
        .getOne();
      if (!callHistory) {
        throw new NotFoundException('Call history not found');
      }
      if (callHistory.status === CallHistoryStatus.answered) {
        const response = await axios.get(recordingUrl, {
          responseType: 'arraybuffer',
        });
        const buffer = Buffer.from(response.data, 'binary');

        const mimetype = 'audio/mpeg';
        const filename = `Test/${process.env.SERVICE_NAME}/leads/${leadId}/${callHistory.callCenterId}.mp3`;

        const audioToTextData = new FormData();
        audioToTextData.append('file', buffer, filename);
        audioToTextData.append('model', 'whisper-1');

        let mp3Url, aiResponse;
        try {
          [mp3Url, aiResponse] = await Promise.all([
            AwsUtils.uploadS3(buffer, mimetype, filename),
            axios.post(
              `${process.env.OPEN_AI_BASE_URL}/v1/audio/transcriptions`,
              audioToTextData,
              {
                headers: {
                  Authorization: `Bearer ${process.env.OPEN_AI_TOKEN}`,
                  'Content-Type': 'multipart/form-data',
                },
              },
            ),
          ]);
        } catch (error) {
          console.error('Error while uploading audio to OpenAI:', error);
          throw new BadRequestException('Failed to upload audio to OpenAI');
        }
        // console.log('Checking aiResponse:', aiResponse.data.text);
        if (aiResponse.data.text) {
          // Trans text to Vietnamese
          const transTextRes: AxiosResponse<{
            choices: Array<{ message: { content: string } }>;
          }> = await axios.post(
            `${process.env.OPEN_AI_BASE_URL}/v1/chat/completions`,
            {
              model: 'gpt-4o-mini',
              messages: [
                {
                  role: 'system',
                  content: `
                    Bạn là một AI hỗ trợ phân tích và tóm tắt nội dung các cuộc gọi giữa nhân viên sale và khách hàng.

                    Nhiệm vụ:
                    1. Đọc văn bản cuộc gọi (được chuyển đổi từ audio).
                    2. TÓM TẮT nội dung chính nếu có thông tin rõ ràng.
                    3. Nếu nội dung rời rạc, không có ngữ cảnh rõ ràng, hãy trả về "Cuộc gọi không rõ nội dung".
                    4. Nếu khách hàng không hợp tác (chửi, cúp máy sớm, từ chối, im lặng...), vẫn tóm tắt lại, nhưng tag là "trash".

                    Phân loại cuộc gọi theo 2 tag:
                    - "useful": nếu có thông tin giá trị (khách trao đổi, hỏi, có nhu cầu…).
                    - "trash": nếu không có gì giá trị hoặc không đáng nghe lại.

                    Bắt buộc:
                    - Chỉ trả về kết quả ở định dạng JSON như sau:

                    {
                      "summary": "tóm tắt ngắn gọn nội dung cuộc gọi hoặc 'Cuộc gọi không rõ nội dung'",
                      "tag": "useful" hoặc "trash"
                    }

                    Không thêm bất kỳ giải thích, mô tả hay nội dung nào khác ngoài JSON.
                        `.trim(),
                },
                {
                  role: 'user',
                  content: aiResponse.data.text,
                },
              ],
            },
            {
              headers: {
                Authorization: `Bearer ${process.env.OPEN_AI_TOKEN}`,
              },
            },
          );
          // console.log('Checking transTextRes:', transTextRes.data);
          callHistory.contextSummary =
            transTextRes.data.choices[0].message.content;
        }

        callHistory.recordUrl = mp3Url;
      }
      await this.callHistoryRepository.save(callHistory);
      return {
        summary: `Summary for call history ID ${callHistoryId}: ${callHistory.contextSummary ||
          'No summary available'}`,
        callHistory,
      };
    } catch (error) {
      console.error('Error while summarizing call by AI:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async processCallHistory(callLogs: EtelecomRawLogDto[]) {
    const connection = getConnection('orderConnection');
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const callHistoriesToInsert = [];
      const processRecordings = [];

      for (const callLog of callLogs) {
        try {
          const { userId, leadId, extensionId, leadCareId } = callLog;
          if (!userId || !leadId || !extensionId) {
            console.error(
              'Missing userId, leadId, or extensionId in call data',
            );
            continue;
          }

          const extension = await this.callCenterExtensionRepository
            .createQueryBuilder('extension')
            .where('extension.id = :extensionId', { extensionId })
            .getOne();
          if (!extension) {
            console.error('Extension not found:', extensionId);
            continue;
          }

          const existCallLog = await this.callHistoryRepository
            .createQueryBuilder('call_history')
            .where('call_history.call_center_id = :callCenterId', {
              callCenterId: callLog.log.external_session_id,
            })
            .getOne();
          if (existCallLog) {
            // console.log('Call log already exists, skipping');
            continue;
          }

          callHistoriesToInsert.push({
            leadId: leadId,
            incomingNumber: callLog.log.callee,
            recordUrl: callLog.log.recording_urls[0] || null,
            startAt: callLog.log.started_at,
            endAt: callLog.log.ended_at,
            createdBy: userId,
            type: CallHistoryType.etelecom,
            status: convertEtelecomCallStatusToEnum(callLog.log.call_state),
            extensionId: extension.id,
            callCenterId: callLog.log.external_session_id,
            duration: callLog.log.duration,
            leadCareId: leadCareId || null,
          });

          processRecordings.push({
            recordingUrl: callLog.log.recording_urls[0],
            leadId,
            callLog,
          });
        } catch (error) {
          // console.error(
          //   'Error while preparing call history for bulk insert:',
          //   error,
          // );
        }
      }

      if (callHistoriesToInsert.length === 0) {
        return;
      }

      const insertResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(this.callHistoryRepository.target)
        .values(callHistoriesToInsert)
        .execute();

      const insertedIds = insertResult.identifiers.map(i => i.id);

      await queryRunner.commitTransaction();
      await this.clearRawLogsThatProcessed();

      for (let i = 0; i < insertedIds.length; i++) {
        const { recordingUrl, leadId, callLog } = processRecordings[i];
        if (recordingUrl) {
          this.processCallRecording(
            insertedIds[i],
            recordingUrl,
            leadId,
          ).catch(err =>
            console.error('Error processing call recording:', err),
          );
        }
      }
      return { inserted: callHistoriesToInsert.length };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(error.message || 'Unexpected error');
    } finally {
      await queryRunner.release();
    }
  }

  async clearRawLogsThatProcessed() {
    try {
      const listRawLogsProcessed = await this.rawLogsRepo
        .createQueryBuilder('raw_logs')
        .innerJoin(
          'call_histories',
          'ch',
          'ch.call_center_id = raw_logs.logsid',
        )
        .select('raw_logs.logsid')
        .getRawMany();
      // console.log(
      //   'Checking listRawLogs that processed: ',
      //   listRawLogsProcessed,
      // );
      const listIdsToClear = listRawLogsProcessed.map(item => item.logsid);
      await this.rawLogsRepo
        .createQueryBuilder()
        .delete()
        .from(this.rawLogsRepo.target)
        .where('logsid IN (:...ids)', {
          ids: listIdsToClear,
        })
        .execute();
    } catch (error) {
      console.error('Error while clearing processed raw logs:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async findCallHistory(
    body: CallHistoryFilter,
    limit: number = 100,
    offset: number = 0,
  ) {
    try {
      const callHistoryQuery = this.callHistoryRepository
        .createQueryBuilder('call_history')
        .where('call_history.type = :type', { type: CallHistoryType.etelecom });

      if (body.leadIds && body.leadIds.length > 0) {
        callHistoryQuery.andWhere('call_history.leadId IN (:...leadIds)', {
          leadIds: body.leadIds,
        });
      }

      if (body.startAt) {
        callHistoryQuery.andWhere('call_history.startAt >= :fromCallAt', {
          fromCallAt: body.startAt,
        });
      }

      if (body.endAt) {
        callHistoryQuery.andWhere('call_history.endAt <= :toCallAt', {
          toCallAt: body.endAt,
        });
      }

      if (body.extensionId && body.extensionId.length > 0) {
        callHistoryQuery.andWhere(
          'call_history.extensionId IN (:...extensionIds)',
          {
            extensionIds: body.extensionId,
          },
        );
      }

      if (body.userId && body.userId.length > 0) {
        callHistoryQuery.andWhere('call_history.createdBy IN (:...userIds)', {
          userIds: body.userId,
        });
      }
      if (body.callCenterId) {
        callHistoryQuery.andWhere('call_history.callCenterId = :callCenterId', {
          callCenterId: body.callCenterId,
        });
      }
      if (body.status !== undefined && body.status !== null) {
        callHistoryQuery.andWhere('call_history.status = :status', {
          status: body.status,
        });
      }

      if (body.incomingNumber) {
        callHistoryQuery.andWhere(
          'call_history.incomingNumber = :incomingNumber',
          {
            incomingNumber: body.incomingNumber,
          },
        );
      }

      callHistoryQuery.limit(limit);
      callHistoryQuery.offset(offset);
      callHistoryQuery.orderBy('call_history.startAt', 'DESC');

      const [data, total] = await callHistoryQuery.getManyAndCount();
      return { data, total };
    } catch (error) {
      console.error('Error while finding call history:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getLastCallHistoryByUserId(userIds: number[]) {
    try {
      const normalizedIds = Array.isArray(userIds) ? userIds : [userIds];

      const records = await this.callHistoryRepository
        .createQueryBuilder('call_history')
        .distinctOn(['call_history.createdBy'])
        .where('call_history.createdBy IN (:...userIds)', {
          userIds: normalizedIds,
        })
        .andWhere('call_history.startAt IS NOT NULL')
        .orderBy('call_history.createdBy', 'ASC')
        .addOrderBy('call_history.startAt', 'DESC')
        .getMany();

      if (!records || records.length === 0) {
        return [];
      }

      return records;
    } catch (error) {
      console.error('Error while getting last call history by user ID:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async getCallCenterLogs(
    callCenterId: number,
    creatorId: number[],
    limit: number,
    offset: number,
  ) {
    try {
      const callCenter = await this.callCenterRepository
        .createQueryBuilder('call_center')
        .where('call_center.id = :callCenterId', { callCenterId })
        .getOne();
      if (!callCenter) {
        throw new NotFoundException('Call center not found');
      }

      let creatorIds = Array.isArray(creatorId)
        ? creatorId.map(Number)
        : [Number(creatorId)];

      creatorIds = creatorIds.filter(id => !isNaN(id));

      const logsQuery = this.logsRepo
        .createQueryBuilder('system_log')
        .where('system_log.parentId = :callCenterId', {
          callCenterId: callCenter.id,
        })
        .andWhere('system_log.parentTableName = :parentTableName', {
          parentTableName: 'call_center',
        });

      if (creatorIds && creatorIds.length > 0) {
        logsQuery.andWhere('system_log.creatorId IN (:...creatorIds)', {
          creatorIds: creatorIds,
        });
      }

      logsQuery
        .limit(limit)
        .offset(offset)
        .orderBy('system_log.createdAt', 'DESC');

      const [data, total] = await logsQuery.getManyAndCount();
      return { data, total };
    } catch (error) {
      console.error('Error while getting call center logs:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async presaveCallHistory(
    externalSessionId: string,
    callPresaveDto: CallPresaveDto,
  ) {
    try {
      const { leadId, extensionId, userId, leadCareId } = callPresaveDto;

      if (!leadId || !extensionId || !userId || !leadCareId) {
        throw new BadRequestException('Missing required fields');
      }

      const newRawsLog = plainToInstance(EtelecomRawLogs, {
        logsId: externalSessionId,
        userId: userId,
        leadId: leadId,
        extensionId: extensionId,
        leadCareId: leadCareId,
      });

      const qb = this.rawLogsRepo
        .createQueryBuilder('raw_logs')
        .insert()
        .values(newRawsLog)
        .orIgnore();

      return await qb.execute();
    } catch (error) {
      console.error('Error while presaving call history:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }

  async testCallPrompt(url: string) {
    try {
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
      });
      const buffer = Buffer.from(response.data, 'binary');

      const mimetype = 'audio/mpeg';
      const filename = `Test-prompt/${process.env.SERVICE_NAME}/leads/${random(
        1,
        1000,
      )}.mp3`;

      const audioToTextData = new FormData();
      audioToTextData.append('file', buffer, filename);
      audioToTextData.append('model', 'whisper-1');

      let mp3Url, aiResponse;
      try {
        [mp3Url, aiResponse] = await Promise.all([
          AwsUtils.uploadS3(buffer, mimetype, filename),
          axios.post(
            `${process.env.OPEN_AI_BASE_URL}/v1/audio/transcriptions`,
            audioToTextData,
            {
              headers: {
                Authorization: `Bearer ${process.env.OPEN_AI_TOKEN}`,
                'Content-Type': 'multipart/form-data',
              },
            },
          ),
        ]);
      } catch (error) {
        console.error('Error while uploading audio to OpenAI:', error);
        throw new BadRequestException('Failed to upload audio to OpenAI');
      }
      // console.log('Checking aiResponse:', aiResponse.data.text);
      if (aiResponse.data.text) {
        // Trans text to Vietnamese
        const transTextRes: AxiosResponse<{
          choices: Array<{ message: { content: string } }>;
        }> = await axios.post(
          `${process.env.OPEN_AI_BASE_URL}/v1/chat/completions`,
          {
            model: 'gpt-4o-mini',
            messages: [
              {
                role: 'system',
                content: `
                  Bạn là một AI hỗ trợ phân tích và tóm tắt nội dung các cuộc gọi giữa nhân viên sale và khách hàng.

                  Nhiệm vụ:
                  1. Đọc văn bản cuộc gọi (được chuyển đổi từ audio).
                  2. TÓM TẮT nội dung chính nếu có thông tin rõ ràng.
                  3. Nếu nội dung rời rạc, không có ngữ cảnh rõ ràng, hãy trả về "Cuộc gọi không rõ nội dung".
                  4. Nếu khách hàng không hợp tác (chửi, cúp máy sớm, từ chối, im lặng...), vẫn tóm tắt lại, nhưng tag là "trash".

                  Phân loại cuộc gọi theo 2 tag:
                  - "useful": nếu có thông tin giá trị (khách trao đổi, hỏi, có nhu cầu…).
                  - "trash": nếu không có gì giá trị hoặc không đáng nghe lại.

                  📌 Bắt buộc:
                  - Chỉ trả về kết quả ở định dạng JSON như sau:

                  {
                    "summary": "tóm tắt ngắn gọn nội dung cuộc gọi hoặc 'Cuộc gọi không rõ nội dung'",
                    "tag": "useful" hoặc "trash"
                  }

                  ⚠️ Không thêm bất kỳ giải thích, mô tả hay nội dung nào khác ngoài JSON.
                      `.trim(),
              },
              {
                role: 'user',
                content: aiResponse.data.text,
              },
            ],
          },
          {
            headers: {
              Authorization: `Bearer ${process.env.OPEN_AI_TOKEN}`,
            },
          },
        );
        // console.log('Checking transTextRes:', transTextRes.data);
        return transTextRes;
      }
      return null;
    } catch (error) {
      console.error('Error while testing call prompt:', error);
      throw new BadRequestException(error.message || 'Unexpected error');
    }
  }
}
