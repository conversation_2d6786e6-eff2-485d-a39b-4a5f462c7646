import { Amqp<PERSON>onnection, Nack, <PERSON><PERSON><PERSON> } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Users } from 'apps/ffm-catalog-api/src/read-entities/identity-entities/Users';
import { PlainCareStateGroup } from 'apps/order-api/src/constants/care-state-groups.constant';
import {
  CANCEL_LEAD_STATES,
  CONFIRM_LEAD_STATES,
  GATHERABLE_STATES,
  GATHERABLE_STATES_AFTER_SALES,
  MAPPING_CARE_STATE_TO_GROUP,
  NEXT_CARE_STATES,
  NEXT_CARE_STATES_AFTER_SALES,
  NON_COUNT_REPEAT_TIMES_CARE_STATES,
  PlainCareState,
  PlainCareStateAfterSales,
} from 'apps/order-api/src/constants/care-states.constant';
import {
  LEAD_AFTER_SALES_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED,
  LEAD_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED,
  LEAD_STATES_CAN_BE_CONSIDERED_AS_PROCESSING,
} from 'apps/order-api/src/constants/distribute-lead.constant';

import { NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES } from 'apps/order-api/src/constants/order-statuses.constant';
import { CreateOrdersFilterDto } from 'apps/order-api/src/dtos/create-orders-filter.dto';
import { CreateLeadCareItemDto } from 'apps/order-api/src/dtos/lead-item.dto';
import {
  BulkCancelDuplicateLeadsDto,
  CreateLeadAfterSaleDto,
  CreateLeadDto,
  UpdateLeadDto,
} from 'apps/order-api/src/dtos/lead.dto';
import {
  ManualDistributeLeadsAfterSaleDto,
  ManualDistributeLeadsDto,
} from 'apps/order-api/src/dtos/manual-distribute-leads.dto';
import { OrderCarrierDto } from 'apps/order-api/src/dtos/order-carrier.dto';
import { UpdateOrderDto } from 'apps/order-api/src/dtos/update-order.dto';
import {
  CallCenterExtensionsDeleteDto,
  CreateCallCenterExtensionDto,
  DeActiveCallCenterDto,
  ReturnCallCenterExtensionsDto,
  UpdateCallCenterExtensionDto,
  YcallRequestWebhookDto,
} from 'apps/order-api/src/dtos/callcenter.dto';

import { AppointmentSchedule } from 'apps/order-api/src/entities/appointment-schedule.entity';
import { CareReason } from 'apps/order-api/src/entities/care-reason.entity';
import { DuplicateLead } from 'apps/order-api/src/entities/duplicate-lead.entity';
import { FilterCollection } from 'apps/order-api/src/entities/filter-collection.entity';
import { LandingPage } from 'apps/order-api/src/entities/landing-page.entity';
import { LeadCareItem } from 'apps/order-api/src/entities/lead-care-item.entity';
import { LeadCare } from 'apps/order-api/src/entities/lead-care.entity';
import { Lead } from 'apps/order-api/src/entities/lead.entity';
import { OrderSource } from 'apps/order-api/src/entities/order-source.entity';
import { Order } from 'apps/order-api/src/entities/order.entity';
import { ShiftUser } from 'apps/order-api/src/entities/shift-user.entity';
import { SystemLog } from 'apps/order-api/src/entities/system-log.entity';
import { CareState, CareStateAfterSales } from 'apps/order-api/src/enums/care-state.enum';
import { LeadCollectType } from 'apps/order-api/src/enums/lead-collect-type.enum';
import { LeadConfigType } from 'apps/order-api/src/enums/lead-config-type.enum';
import { LeadDistributeCondition } from 'apps/order-api/src/enums/lead-distribute-condition.enum';
import { LeadDistributeRuleType } from 'apps/order-api/src/enums/lead-distribute-rule-type.enum';
import { SourceEntity } from 'apps/order-api/src/enums/source-entity.enum';
import { TagMethodType, TagOperatorType, TagStatus } from 'apps/order-api/src/enums/tag.enum';
import { GatherLeadsFilter, LeadsFilter } from 'apps/order-api/src/filters/leads.filter';
import {
  ManualDistributeLeadsAfterSaleFilter,
  ManualDistributeLeadsFilter,
} from 'apps/order-api/src/filters/manual-distribute-leads.filter';
import { User } from 'apps/order-api/src/read-entities/identity/user.entity';
import { LeadDistributeRuleValue } from 'apps/order-api/src/read-entities/order/lead-distribute-rule-value.entity';
import { CallCenter, CallCenterExtension, CallHistory } from 'apps/order-api/src/entities';

import axios from 'axios';
import { Queue } from 'bullmq';
import { instanceToPlain, plainToClass, plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { AmountType } from 'core/enums/amount-type.enum';
import { OrderStatus } from 'core/enums/order-status.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import { RawResponse } from 'core/raw/raw-response';
import * as FormData from 'form-data';
import { Redis } from 'ioredis';
import {
  chunk,
  difference,
  find,
  flatMap,
  intersection,
  isEmpty,
  isNil,
  map,
  omit,
  reduce,
  remove,
  sumBy,
} from 'lodash';
import { Moment } from 'moment';
import * as moment from 'moment-timezone';
import xlsx from 'node-xlsx';
import {
  Brackets,
  FindConditions,
  getConnection,
  In,
  IsNull,
  Not,
  Repository,
  SelectQueryBuilder,
  TreeRepository,
  UpdateResult,
} from 'typeorm';
import { v4 as uuid } from 'uuid';
import { PANCAKE_API_ENDPOINT } from '../../../../../facebook-bot/src/constants/fb-api-endpoints.constant';
import { ICdrWebhook, PortSipClient } from '../../../clients/port-sip.client';
import { Cdr } from '../../../entities/cdr.entity';
import { RecordFile } from '../../../entities/record-file.entity';
import { ShiftsService } from '../../shift/services/shifts.service';
import { LandingPagesService } from './landing-pages.service';
import { LeadDistributeConfigsService } from './lead-distribute-configs.service';
import { OrdersService } from './orders.service';
import { TagsService } from './tags.service';
import { LeadPerformanceByUserIdsDto } from 'apps/order-api/src/dtos/lead-performance-by-user-ids.dto';
import FilterUtils from 'core/utils/FilterUtils';
import { CONFIRMED_STATES } from 'apps/analytics-api/src/constants/orders/lead-states.constant';
import {
  ACTUAL_REVENUE_STATUSES,
  RETURNED_SALES_STATUSES,
} from 'apps/analytics-api/src/constants/orders/order-statuses.constant';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';
import { CallCenterExtensionsFilter } from 'apps/order-api/src/filters/call-center-extensions.filter';
import { CALLCENTER } from 'core/cache/constants/prefix.constant';
import {
  CallHistoryStatus,
  CallHistoryType,
  convertCallHistoryStatusToEnum,
} from 'apps/order-api/src/enums/call-center.enum';
import SignatureUtils from 'core/utils/SignatureUtils';
import { ErrorCode } from 'apps/order-api/src/enums/error-code.enum';
import ExcelUtils from 'core/utils/ExcelUtils';
import Utils from 'core/utils/Utils';
import { AwsUtils } from 'core/utils/AwsUtils';
import { QUEUE_SETTING } from 'core/constants/constants';
import { Job } from 'bull';
import { JobDataCallCenter } from 'apps/order-api/src/modules/order/processors/callcenter.processor';
import { Sort } from 'core/enums/sort.enum';
import { RemovedDuplicateLeads } from 'apps/order-api/src/entities/removed-duplicate-leads.entity';
import StringUtils from 'core/utils/StringUtils';
import { PermissionUtils } from 'core/utils/PermissionUtils';
import { SalePermission } from 'core/enums/sale-permissions';
import { TelesalesPermission } from 'core/enums/sale-permissions/telesales-permission.enum';
import { PossibleDuplicateOrder } from 'apps/order-api/src/entities/possible-duplicate-order.entity';
import {
  GatherLeadsAfterSaleFilter,
  LeadsASAssignedProjectFilter,
  LeadsASFilterCommon,
  LeadsASUnassignProjectFilter,
} from 'apps/order-api/src/filters/leads-as.filter';
import { LeadType } from 'apps/order-api/src/enums/lead-type.enum';
import { DistributeProjectForLeadAsDto } from 'apps/order-api/src/dtos/distribute-project-for-lead-as.dto';
import { OrderProduct } from 'apps/order-api/src/entities/order-product.entity';
import { DistributeProjectForLeadAsType } from 'apps/order-api/src/enums/distribute-project-for-lead-as.enum';
import { LeadASDistributeConfigsService } from './lead-as-distribute-configs.service';
import { LeadASDistributeRuleType } from 'apps/order-api/src/enums/lead-as-distribute-rule-type.enum';
import { LeadASDistributeCondition } from 'apps/order-api/src/enums/lead-as-distribute-condition.enum';
import { LeadASDistributeRuleValue } from 'apps/order-api/src/read-entities/order/lead-as-distribute-rule-value.entity';
import { ImportLeadsAfterSaleDto } from 'apps/order-api/src/dtos/import-leads-after-sale.dto';
import { LocationsService } from './locations.service';
import { ProductVariant } from 'apps/order-api/src/read-entities/catalog/product-variant.entity';
import { Customer } from 'apps/order-api/src/entities/customer.entity';
import { OrderCarrier } from 'apps/order-api/src/entities/order-carrier.entity';
import { LeadASConfigType } from 'apps/order-api/src/enums/lead-as-config-type.enum';
import { ProjectStatus } from 'apps/identity-api/src/enums/project-status.enum';
import { LeadAfterSaleSource } from 'apps/order-api/src/entities/lead-after-sale-source.entity';
import { LeadAfterSaleSourceFilter } from 'apps/order-api/src/filters/lead-after-sale-source.filter';
import {
  CreateAfterSaleSourceDto,
  UpdateAfterSaleSourceDto,
} from 'apps/order-api/src/dtos/create-aftersales-source.dto';
import { CommonStatus } from 'core/enums/common-status.enum';
import { ReasonTab } from 'apps/order-api/src/enums/lead-aftersales-filter.enum';
import ExcelUtilsV2 from 'core/utils/ExcelUtilsV2';
import { FilterCollectionType } from 'core/enums/filter-collection-type.enum';
import { AfterSalesFilterCollectionFilter } from 'apps/order-api/src/filters/aftersales-filter-collection.filter';
import { LeadAftersalesDateRangeType } from 'apps/order-api/src/enums/lead-aftersales-date-range-type.enum';

export class LeadsASService implements OnModuleInit {
  private readonly logger = new Logger(LeadsASService.name);
  columnMultiLanguage = {
    vi: [
      'STT',
      'Mã đơn',
      'Thị trường',
      'Dự án',
      'Khách hàng',
      'Số điện thoại',
      'Địa chỉ',
      'Tỉnh/Thành phố',
      'Quận/Huyện',
      'Phường/Xã',
      'Mã bưu chính',
      'Biến thể',
      'Ghi chú in đơn',
      'Ghi chú cho kho',
      'Người phụ trách',
      'Cách thu thập dữ liệu',
      'ID nguồn',
      'Chăm sóc mới nhất',
      'Giá trị đơn',
      'Thẻ',
      'Chăm sóc cuối',
      'Thời gian tạo',
      'Trạng thái lead',
    ],
    en: [
      'No.',
      'OrderID',
      'Country',
      'Project',
      'Customer',
      'Phone number',
      'Address',
      'Province',
      'District',
      'Ward',
      'Post code',
      'Variant',
      'Print order note',
      'Warehouse note',
      'Sales rep',
      'Collect method',
      'Source ID',
      'Latest care reason',
      'COD',
      'Tag',
      'Lastest care time',
      'Creation time',
      'Lead status',
    ],
  };
  constructor(
    @InjectRepository(Lead, orderConnection)
    private leadsRepo: Repository<Lead>,
    @InjectRepository(RecordFile, orderConnection)
    private recordFileRepository: Repository<RecordFile>,
    @InjectRepository(LeadCare, orderConnection)
    private leadCaresRepo: Repository<LeadCare>,
    @InjectRepository(LeadCareItem, orderConnection)
    private leadCareItemsRepo: Repository<LeadCareItem>,
    @InjectRepository(DuplicateLead, orderConnection)
    private duplicateLeadsRepo: Repository<DuplicateLead>,
    @InjectRepository(CareReason, orderConnection)
    private careReasonRepo: Repository<CareReason>,
    @InjectRepository(Order, orderConnection)
    private orderRepo: Repository<Order>,
    @InjectRepository(AppointmentSchedule, orderConnection)
    private appointmentScheduleRepo: Repository<AppointmentSchedule>,
    @InjectRepository(FilterCollection, orderConnection)
    private filterRepo: Repository<FilterCollection>,
    @InjectRepository(SystemLog, orderConnection)
    private logsRepo: Repository<SystemLog>,
    @InjectRepository(OrderSource, orderConnection)
    private externalSourceRepo: TreeRepository<OrderSource>,
    @InjectRepository(Cdr, orderConnection)
    private cdrRepository: Repository<Cdr>,
    @InjectRepository(CallHistory, orderConnection)
    private callHistoryRepo: Repository<CallHistory>,
    @InjectRepository(CallCenter, orderConnection)
    private callCenterRepo: Repository<CallCenter>,
    @InjectRepository(CallCenterExtension, orderConnection)
    private callCenterExtensionRepo: Repository<CallCenterExtension>,
    @InjectRepository(LeadAfterSaleSource, orderConnection)
    private leadAfterSaleSourceRepo: Repository<LeadAfterSaleSource>,
    private landingService: LandingPagesService,
    private ordersService: OrdersService,
    private distributeConfigsService: LeadDistributeConfigsService,
    private leadAfterSaleDistributeConfigsService: LeadASDistributeConfigsService,
    private shiftsService: ShiftsService,
    private tagsService: TagsService,
    private amqpConnection: AmqpConnection,
    @InjectQueue('order')
    private orderQueue: Queue,
    @InjectQueue('callcenter')
    private callCenterQueue: Queue,
    @InjectRedis() private readonly redis: Redis,
    private locationService: LocationsService,
  ) {}

  async onModuleInit() {
    // if (process.env.CONSUMER == 'false') {
    await this.initCallCenterExts();
    // }
  }

  async initCallCenterExts() {
    const extensions = await this.callCenterExtensionRepo.find({
      where: { isAvailable: true },
      order: {
        createdAt: 'ASC',
      },
    });

    const grouped: Record<string, number[]> = extensions.reduce((acc, extension) => {
      const key = `-${CALLCENTER}-${extension.callCenterId}-${extension.countryId}-${extension.companyId}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(extension.id);
      return acc;
    }, {} as Record<string, number[]>);

    for (const [key, ids] of Object.entries(grouped)) {
      await this.redis.del(key);
      await this.redis.rpush(key, ...ids);
    }
  }

  async getLeadsASUnassignProject(
    pagination?: PaginationOptions,
    filter?: LeadsASUnassignProjectFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const leadsQb = await this.getLeadsAfterSaleUnassignProjectQueryBuilder(
      filter,
      pagination,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (isNil(leadsQb)) return [];
    const orderBy = filter.orderBy ? `lead.${filter.orderBy}` : 'lead.id';
    const sort = filter.sort ? filter.sort : Sort.DESC;
    leadsQb.select([
      'lead.id AS "id"',
      'lead.created_at AS "createdAt"',
      'lead.updated_at AS "updatedAt"',
      'lead.state AS "state"',
      'order.id AS "orderId"',
      'order.displayId AS "orderDisplayId"',
      'order.customerPhone AS "customerPhone"',
      'order.customerName AS "customerName"',
      'oo.totalPrice AS "totalPrice"',
      'oo.shippingFee AS "shippingFee"',
      'oo.surcharge AS "surcharge"',
      'oo.discount AS "discount"',
      'oo.projectId AS "projectId"',
      'order.note AS "orderNote"',
      'order.countryId AS "countryId"',
      'order.addressText AS "addressText"',
      'order.addressWard AS "addressWard"',
      'order.addressDistrict AS "addressDistrict"',
      'order.addressProvince AS "addressProvince"',
      'order.postCode AS "postCode"',
      'oo.displayId AS "sourceOrderDisplayId"',
      '(SELECT COUNT(*) AS "leadsDup" from duplicate_leads dl WHERE dl.lead_id = lead.id) AS "leadsDup"',
    ]);
    leadsQb.addSelect(subQ => {
      return subQ
        .select(
          `STRING_AGG((op.product_detail::json->'product'->>'name') || ' (' || (op.product_detail::json->>'sku') || ')', ',') AS "products"`,
        )
        .from(OrderProduct, 'op')
        .where('op.order_id = oo.id')
        .groupBy('op.order_id');
    }, 'products');
    leadsQb.orderBy(orderBy, sort);
    // leadsQb = await this.setCommonGetListLeadsQb(filter, leadsQb);
    const leads = await leadsQb.getRawMany();
    return leads.map(l => {
      return {
        ...l,
        hasLeadsDuplicate: +l.leadsDup > 0 ? true : false,
        state: CareStateAfterSales[l.state],
      };
    });
  }

  async countProductOfLeadsASUnassignProject(
    filter?: LeadsASUnassignProjectFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const leadsQb = await this.getLeadsAfterSaleUnassignProjectQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (isNil(leadsQb)) return [];
    leadsQb
      .select("op.product_detail->>'sku'", 'sku')
      .addSelect(
        `STRING_AGG(lead.id::TEXT, ',' ORDER BY ${
          filter.orderBy ? `lead.${filter.orderBy}` : 'lead.id'
        } ${filter.sort ? filter.sort : Sort.DESC}) AS "leadIds"`,
      )
      .leftJoin('oo.products', 'op')
      .groupBy("op.product_detail->>'sku'");
    const rawData: Array<{
      sku: string;
      leadIds: string; // '1,2,3' -> need to split into array
    }> = await leadsQb.getRawMany();

    return rawData.map(r => {
      return {
        ...r,
        leadIds: r.leadIds.split(',').map(l => +l),
      };
    });
  }

  async _getLeadIdsPerProduct(
    filter?: LeadsASUnassignProjectFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<
    Array<{
      sku: string;
      leadIds: string;
    }>
  > {
    const leadsQb = await this.getLeadsAfterSaleUnassignProjectQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (isNil(leadsQb)) return [];
    leadsQb
      .select("op.product_detail->>'sku'", 'sku')
      .addSelect(
        `STRING_AGG(lead.id::TEXT, ',' ORDER BY ${
          filter.orderBy ? `lead.${filter.orderBy}` : 'lead.id'
        } ${filter.sort ? filter.sort : Sort.DESC}) AS "leadIds"`,
      )
      .leftJoin('lead.order', 'no')
      // .leftJoin('orders', 'oo', 'oo.id = no.source_id')
      .leftJoin('oo.products', 'op')
      .groupBy("op.product_detail->>'sku'");
    return await leadsQb.getRawMany();
  }

  async getLeadsAfterSaleAssignedProjectQueryBuilder(
    filter?: LeadsASAssignedProjectFilter,
    pagination?: PaginationOptions,
    countryIds?: (number | string)[],
    companyId?: number,
    isGetFullTags = false,
  ): Promise<SelectQueryBuilder<Lead> | null> {
    const qb = this.leadsRepo
      .createQueryBuilder('lead')
      .leftJoin('lead.order', 'order')
      .leftJoinAndSelect('order.products', 'products')
      .leftJoinAndSelect('order.externalSource', 'es')
      .andWhere(`lead.lead_type = :leadType`, { leadType: LeadType.after_sale });

    if (companyId) qb.andWhere('order.companyId = :companyId', { companyId });
    if (pagination) qb.take(pagination.limit).skip(pagination.skip);

    const {
      from,
      to,
      projectIds,
      productIds,
      fromPrice,
      toPrice,
      state,
      ids,
      hasDuplicates,
      userIds,
      reasonIds,
      tagIds,
      numberOfRepeats,
      query,
      collectType,
      fromLastCare,
      toLastCare,
      numberOfCares,
      fromNumberOfCares,
      toNumberOfCares,
      sourceProductIds,
      marketerIds,
      excludeUserIds,
      originalProjectIds,
      fromUpdatedAt,
      toUpdatedAt,
      reasonTab,
      sourceIds,
    } = filter;

    if (fromUpdatedAt)
      qb.andWhere('lead.updated_at >= :fromUpdatedAt', { fromUpdatedAt: fromUpdatedAt });
    if (toUpdatedAt) qb.andWhere('lead.updated_at <= :toUpdatedAt', { toUpdatedAt: toUpdatedAt });
    if (!isEmpty(countryIds)) qb.andWhere('order.countryId IN (:...countryIds)', { countryIds });
    if (from) qb.andWhere('lead.created_at >= :from', { from });
    if (to) qb.andWhere('lead.created_at < :to', { to });
    if (!isEmpty(projectIds)) qb.andWhere('order.projectId IN (:...projectIds)', { projectIds });
    if (!isEmpty(productIds)) qb.andWhere(`products.productId IN (:...productIds)`, { productIds });
    if (!isEmpty(originalProjectIds)) {
      originalProjectIds.includes(-1)
        ? qb.andWhere(
            `(order.sourceProjectId is null OR order.sourceProjectId IN (:...originalProjectIds))`,
            { originalProjectIds },
          )
        : qb.andWhere(`order.sourceProjectId IN (:...originalProjectIds)`, { originalProjectIds });
    }
    if (fromPrice)
      qb.andWhere(
        '(COALESCE(order.total_price,0) + COALESCE(order.shipping_fee,0) + COALESCE(order.surcharge,0) - COALESCE(order.discount,0)) >= :fromPrice',
        { fromPrice },
      );
    if (toPrice)
      qb.andWhere(
        '(COALESCE(order.total_price,0) + COALESCE(order.shipping_fee,0) + COALESCE(order.surcharge,0) - COALESCE(order.discount,0)) <= :toPrice',
        { toPrice },
      );
    if (!isEmpty(state)) qb.andWhere('lead.state IN (:...state)', { state });
    if (!isEmpty(ids)) qb.andWhere('lead.id IN (:...ids)', { ids });
    if (hasDuplicates) {
      qb.leftJoin('lead.duplicateLeads', 'dupLead').addSelect([
        'dupLead.id',
        'dupLead.orderId',
        'dupLead.displayId',
        'dupLead.duplicateDisplayId',
      ]);
      qb.andWhere('dupLead.id IS NOT NULL AND lead.ignore_duplicate_warning = FALSE');
    }
    if (userIds) qb.andWhere('lead.user_id IN (:...userIds)', { userIds: userIds });
    if (excludeUserIds) qb.andWhere('lead.user_id NOT IN (:...excludeUserIds)', { excludeUserIds });
    if (!isEmpty(marketerIds))
      qb.andWhere('order.marketerId IN (:...marketerIds)', { marketerIds });
    if ((!reasonTab || (reasonTab && reasonTab === ReasonTab.ALL)) && !isEmpty(reasonIds)) {
      const noReasonId = remove(reasonIds, id => id === -1);
      qb.leftJoin(LeadCareItem, 'CI', 'CI.lead_id = lead.id')
        .andWhere(
          new Brackets(sqb => {
            if (!isEmpty(noReasonId)) sqb.where(`CI.reason_id IS NULL`);
            if (!isEmpty(reasonIds)) {
              const reasonsSubQb = this.careReasonRepo
                .createQueryBuilder('cr1')
                .innerJoin(
                  CareReason,
                  'cr2',
                  'cr1.reasonKey = cr2.reasonKey AND cr1.companyId = cr2.companyId',
                )
                .select('cr1.id')
                .where('cr2.id IN (:...reasonIds)');

              if (!isEmpty(countryIds))
                reasonsSubQb.andWhere(
                  '(cr1.countryId IS NULL OR cr1.countryId IN (:...countryIds))',
                );

              sqb.orWhere(`CI.reason_id IN (${reasonsSubQb.getQuery()})`);
            }
          }),
        )
        .andWhere(`(CI.note is null or CI.note != 'from system')`) // thêm điều kiện này để bỏ qua các lý do tự động từ hệ thống
        .setParameters({ reasonIds: reasonIds, countryIds });
    } else if (reasonTab === ReasonTab.LATEST && !isEmpty(reasonIds)) {
      qb.andWhere('lead.lastCareReasonId IN (:...reasonIds)', { reasonIds });
    }

    if (numberOfRepeats) {
      const subQb = this.leadCaresRepo
        .createQueryBuilder('lc')
        .select('lc.leadId', 'lead_id')
        .addSelect('COUNT (lc.id)', 'cares_count')
        .groupBy('lc.leadId');
      qb.leftJoin(
        `(${subQb.getQuery()})`,
        'lc',
        'lead.id = lc.lead_id',
      ).andWhere('lc.cares_count > :numberOfRepeats', { numberOfRepeats: numberOfRepeats });
    }
    if (query) {
      const phonesQuery = filter.query.match(/\S+/g) || [];
      qb.andWhere(
        new Brackets(qb => {
          qb.where('order.display_id ILIKE :query', { query: `%${query}%` })
            .orWhere('order.customer_name ILIKE :query', { query: `%${query}%` })
            .orWhere('order.address_ward ILIKE :query', { query: `%${query}%` })
            .orWhere('order.address_district ILIKE :query', { query: `%${query}%` })
            .orWhere('order.address_province ILIKE :query', { query: `%${query}%` });
          if (!isEmpty(phonesQuery)) {
            qb.orWhere('order.customer_phone IN (:...phonesQuery)', { phonesQuery });
          }
        }),
      );
    }
    if (tagIds && !isGetFullTags) {
      const subQb = getConnection(orderConnection)
        .createQueryBuilder()
        .from('order_tags', 'ot')
        .select('ot.order_id', 'order_id')
        .where('ot.tag_id IN (:...tagIds)', { tagIds: filter.tagIds })
        .groupBy('ot.order_id');
      if (filter.tagMethod == TagMethodType.Include) {
        if (filter.operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          qb.andWhere(`lead.orderId IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          qb.andWhere('order_tags.tag_id IN (:...tagIds)', {
            tagIds: filter.tagIds,
          });
        }
      }

      if (filter.tagMethod == TagMethodType.Exclude) {
        if (filter.operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          qb.andWhere(`lead.orderId NOT IN (${subQb.getQuery()})`).setParameters(
            subQb.getParameters(),
          );
        }
        if (filter.operator == TagOperatorType.Or) {
          qb.andWhere(`lead.orderId NOT IN (${subQb.getQuery()})`).setParameters(
            subQb.getParameters(),
          );
        }
      }
    }
    if (!isEmpty(collectType)) {
      qb.andWhere(
        new Brackets(sqb => {
          for (const type of collectType) {
            switch (type) {
              case LeadCollectType.convertFromFacebook:
                sqb.orWhere(
                  '(lead.form_captured_at IS NULL AND order.fb_scoped_user_id IS NOT NULL)',
                );
                break;
              case LeadCollectType.captureForm:
                sqb.orWhere('lead.form_captured_at IS NOT NULL');
                break;
              case LeadCollectType.manualKeying:
              default:
                sqb.orWhere('lead.form_captured_at IS NULL AND order.fb_scoped_user_id IS NULL');
                break;
            }
          }
        }),
      );
    }
    if (fromLastCare || toLastCare) {
      qb.leftJoin('lead.latestCareItem', 'lcii');
      if (fromLastCare)
        qb.andWhere('lcii.createdAt IS NOT NULL').andWhere('lcii.createdAt >= :fromLastCare', {
          fromLastCare,
        });

      if (toLastCare) {
        qb.andWhere('lcii.createdAt IS NOT NULL').andWhere('lcii.createdAt <= :toLastCare', {
          toLastCare,
        });
      }
    }

    if (!isNil(filter.numberOfCares)) {
      const subQuery = await this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin(Order, 'o', 'o.id = l.orderId')
        .leftJoin(LeadCareItem, 'lci', 'lci.leadId = l.id')
        .where(`(lci.note != 'from system' OR lci.note IS NULL)`)
        .select(['l.id'])
        .orderBy('l.id', 'DESC');
      if (from) subQuery.andWhere('l.createdAt >= :from', { from });
      if (to) subQuery.andWhere('l.createdAt <= :to', { to });
      if (filter.numberOfCares === 0) {
        subQuery.andWhere('lci.leadId IS NULL');
      } else {
        if (filter.numberOfCares === 1) subQuery.andWhere('lci.leadId IS NOT NULL');
        subQuery.groupBy('l.id').having('COUNT(*) = :numberOfCares', { numberOfCares });
      }
      qb.andWhere(`lead.id IN (${subQuery.getQuery()})`).setParameters(subQuery.getParameters());
    }
    if (filter.fromNumberOfCares) {
      const subQuery = await this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin(Order, 'o', 'o.id = l.orderId')
        .leftJoin(LeadCareItem, 'lci', 'lci.leadId = l.id')
        .where(`(lci.note != 'from system' OR lci.note IS NULL)`)
        .select(['l.id'])
        .orderBy('l.id', 'DESC');
      if (from) subQuery.andWhere('l.createdAt >= :from', { from });
      if (to) subQuery.andWhere('l.createdAt <= :to', { to });
      if (filter.fromNumberOfCares === 1) subQuery.andWhere('lci.leadId IS NOT NULL');
      subQuery.groupBy('l.id').having('COUNT(*) >= :fromNumberOfCares', { fromNumberOfCares });
      qb.andWhere(`lead.id IN (${subQuery.getQuery()})`).setParameters(subQuery.getParameters());
    }

    if (filter.toNumberOfCares) {
      const subQuery = await this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin(Order, 'o', 'o.id = l.orderId')
        .leftJoin(LeadCareItem, 'lci', 'lci.leadId = l.id')
        .where(`(lci.note != 'from system' OR lci.note IS NULL)`)
        .select(['l.id'])
        .orderBy('l.id', 'DESC');
      if (from) subQuery.andWhere('l.createdAt >= :from', { from });
      if (to) subQuery.andWhere('l.createdAt <= :to', { to });
      subQuery.groupBy('l.id').having('COUNT(*) <= :toNumberOfCares', { toNumberOfCares });
      qb.andWhere(`lead.id IN (${subQuery.getQuery()})`).setParameters(subQuery.getParameters());
    }

    if (!isEmpty(sourceProductIds)) {
      qb.leftJoin('orders', 'oo', 'oo.id = order.source_id')
        .leftJoin('order_products', 'ops', 'ops.order_id = oo.id')
        .andWhere(`ops.productId IN (:...sourceProductIds)`, { sourceProductIds });
    }

    if (!isEmpty(sourceIds)) {
      let newSourceIds = sourceIds;
      if (newSourceIds.includes('-1')) {
        if (newSourceIds.length === 1) {
          qb.andWhere('order.externalSourceId IS NULL');
        } else {
          newSourceIds = newSourceIds.filter(it => it !== '-1');
          const keyPairs = newSourceIds.map(it => {
            return `('aftersales', '${it}')`;
          });
          const extSources = await this.externalSourceRepo
            .createQueryBuilder('es')
            .where(`(es.entity, entity_id) IN (${keyPairs.join(', ')})`)
            .select(['es.id'])
            .getMany();
          if (isEmpty(extSources)) {
            qb.andWhere('order.externalSourceId IS NULL');
          } else {
            const extSrcIds = extSources.map(it => it.id);
            const dbConnection = getConnection(orderConnection);
            const ancestorIdQb = dbConnection
              .createQueryBuilder()
              .from('order_sources_closure', 'osc')
              .where('osc.id_descendant IN (:...extSrcIds)')
              .select(['osc.id_ancestor']);
            const descendantsQb = dbConnection
              .createQueryBuilder()
              .from('order_sources_closure', 'osc')
              .where(`osc.id_ancestor IN (${ancestorIdQb.getQuery()})`)
              .select('osc.id_descendant');
            qb.andWhere(
              new Brackets(sqb => {
                sqb.where('order.externalSourceId IS NULL');
                sqb.orWhere(`order.externalSourceId IN (${descendantsQb.getQuery()})`);
              }),
            ).setParameters({
              extSrcIds,
            });
          }
        }
      } else {
        const keyPairs = newSourceIds.map(it => {
          return `('aftersales', '${it}')`;
        });
        const extSources = await this.externalSourceRepo
          .createQueryBuilder('es')
          .where(`(es.entity, entity_id) IN (${keyPairs.join(', ')})`)
          .select(['es.id'])
          .getMany();
        if (isEmpty(extSources)) return null;

        const extSrcIds = extSources.map(it => it.id);
        const dbConnection = getConnection(orderConnection);
        const ancestorIdQb = dbConnection
          .createQueryBuilder()
          .from('order_sources_closure', 'osc')
          .where('osc.id_descendant IN (:...extSrcIds)')
          .select(['osc.id_ancestor']);
        const descendantsQb = dbConnection
          .createQueryBuilder()
          .from('order_sources_closure', 'osc')
          .where(`osc.id_ancestor IN (${ancestorIdQb.getQuery()})`)
          .select('osc.id_descendant');
        qb.andWhere(`order.externalSourceId IN (${descendantsQb.getQuery()})`).setParameters({
          extSrcIds,
        });
      }
    }
    return qb;
  }

  async getLeadsAfterSaleUnassignProjectQueryBuilder(
    filter?: LeadsASFilterCommon,
    pagination?: PaginationOptions,
    countryIds?: (number | string)[],
    companyId?: number,
  ): Promise<SelectQueryBuilder<Lead> | null> {
    const qb = this.leadsRepo
      .createQueryBuilder('lead')
      .leftJoin('lead.order', 'order')
      .leftJoin('orders', 'oo', 'oo.id = order.source_id')
      .andWhere(`lead.lead_type = :leadType`, { leadType: LeadType.after_sale })
      .andWhere('lead.project_id IS NULL');

    if (companyId) qb.andWhere('order.companyId = :companyId', { companyId });
    if (pagination) qb.limit(pagination.limit).offset(pagination.skip);

    const {
      from,
      to,
      projectIds,
      productIds,
      fromPrice,
      toPrice,
      state,
      ids,
      hasDuplicates,
    } = filter;

    if (!isEmpty(countryIds)) qb.andWhere('order.countryId IN (:...countryIds)', { countryIds });
    if (from) qb.andWhere('lead.created_at >= :from', { from });
    if (to) qb.andWhere('lead.created_at < :to', { to });
    if (!isEmpty(projectIds)) qb.andWhere('oo.projectId IN (:...projectIds)', { projectIds });
    if (!isEmpty(productIds)) {
      qb.leftJoin('order_products', 'ops', 'ops.order_id = oo.id');
      qb.andWhere(`ops.productId IN (:...productIds)`, { productIds });
    }
    if (fromPrice)
      qb.andWhere(
        '(COALESCE(oo.total_price,0) + COALESCE(oo.shipping_fee,0) + COALESCE(oo.surcharge,0) - COALESCE(oo.discount,0)) >= :fromPrice',
        {
          fromPrice,
        },
      );
    if (toPrice)
      qb.andWhere(
        '(COALESCE(oo.total_price,0) + COALESCE(oo.shipping_fee,0) + COALESCE(oo.surcharge,0) - COALESCE(oo.discount,0)) <= :toPrice',
        {
          toPrice,
        },
      );
    if (!isEmpty(state)) qb.andWhere('lead.state IN (:...state)', { state });
    if (!isEmpty(ids)) qb.andWhere('lead.id IN (:...ids)', { ids });
    if (!isNil(filter.isGetUnassignedProjectLead)) {
      if (filter.isGetUnassignedProjectLead) qb.andWhere('lead.project_id IS NULL');
      else qb.andWhere('lead.project_id IS NOT NULL');
    }
    if (hasDuplicates)
      qb.andWhere(
        '(select count(*) as total from duplicate_leads dl where dl.lead_id = lead.id) > 0',
      );

    return qb;
  }

  async setJoinDataLeadsAfterSaleAssignedProject(
    leadsQb: SelectQueryBuilder<Lead>,
  ): Promise<SelectQueryBuilder<Lead> | null> {
    leadsQb
      .leftJoin('lead.latestCareItem', 'lci')
      .addSelect(['lci.id', 'lci.reasonId', 'lci.timesRepeatReason', 'lci.createdAt'])
      .leftJoin('lci.reason', 're')
      .addSelect(['re.id', 're.name', 're.reasonKey'])
      .addSelect([
        'order.id',
        'order.displayId',
        'order.customerName',
        'order.customerPhone',
        'order.addressText',
        'order.addressProvince',
        'order.addressDistrict',
        'order.addressWard',
        'order.postCode',
        'order.note',
        'order.totalPrice',
        'order.shippingFee',
        'order.surcharge',
        'order.discount',
        'order.marketerId',
        'order.externalSourceId',
        'order.updatedAt',
        'order.projectId',
        'order.marketerId',
        'order.crossCare',
        'order.fbScopedUserId',
        'order.sourceId',
        'order.countryId',
        'order.projectId',
        'order.sourceProjectId',
        'order.paid',
      ])
      // .leftJoinAndSelect('order.products', 'products')
      .leftJoin('lead.duplicateLeads', 'dupLeads')
      .addSelect([
        'dupLeads.id',
        'dupLeads.orderId',
        'dupLeads.displayId',
        'dupLeads.duplicateDisplayId',
      ])
      .leftJoinAndSelect('order.carrier', 'carrier')
      .leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`)
      .addSelect(['tags.id', 'tags.name'])
      .leftJoin('lead.cares', 'cares')
      .addSelect(['cares.id', 'cares.userId'])
      .leftJoin('cares.careItems', 'ci')
      .addSelect(['ci.id', 'ci.createdAt', 'ci.reasonId', 'ci.note'])
      .leftJoin('ci.reason', 'r')
      .addSelect(['r.id', 'r.name'])
      .leftJoin('lead.currentCare', 'currentCare')
      .addSelect(['currentCare.id', 'currentCare.userId'])
      .addSelect(['currentCare.created_at', 'currentCare.createdAt'])
      .leftJoin('currentCare.careItems', 'careItems')
      .addSelect([
        'careItems.id',
        'careItems.reasonId',
        'careItems.timesRepeatReason',
        'careItems.note',
      ]);
    return leadsQb;
  }

  async countLeadsAfterSale(
    filter: LeadsASAssignedProjectFilter,
    groupBy: string[] = [],
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const countryIds = headers?.['country-ids'] ? headers?.['country-ids'].split(',') : [];
    const companyId = request?.user?.companyId;

    let subQb = null;
    if (filter.isAssignedProject) {
      subQb = await this.getLeadsAfterSaleAssignedProjectQueryBuilder(
        filter,
        undefined,
        countryIds,
        companyId,
      );
      switch (filter.dateRangeType) {
        case LeadAftersalesDateRangeType.createdTime: {
          if (filter.fromRange) {
            subQb.andWhere('lead.created_at >= :from', { from: filter.fromRange });
          }
          if (filter.toRange) {
            subQb.andWhere('lead.created_at <= :to', { to: filter.toRange });
          }
          break;
        }
        case LeadAftersalesDateRangeType.takeCareTime: {
          subQb.leftJoin('lead.latestCareItem', 'lcii');
          if (filter.fromRange) {
            subQb
              .andWhere('lcii.createdAt IS NOT NULL')
              .andWhere('lcii.createdAt >= :from', { from: filter.fromRange });
          }
          if (filter.toRange) {
            subQb
              .andWhere('lcii.createdAt IS NOT NULL')
              .andWhere('lcii.createdAt <= :to', { to: filter.toRange });
          }
          break;
        }
        case LeadAftersalesDateRangeType.distributeTime: {
          subQb.leftJoin('lead.currentCare', 'currentCare');
          if (filter.fromRange) {
            subQb.andWhere('currentCare.created_at >= :from', { from: filter.fromRange });
          }
          if (filter.toRange) {
            subQb.andWhere('currentCare.created_at <= :to', { to: filter.toRange });
          }
          break;
        }
      }
      if (filter.fromCurrentCare || filter.toCurrentCare) {
        subQb.leftJoin('lead.currentCare', 'currentCare');
        if (filter.fromCurrentCare) {
          subQb.andWhere('currentCare.created_at >= :fromCurrentCare', {
            fromCurrentCare: filter.fromCurrentCare,
          });
        }

        if (filter.toCurrentCare) {
          subQb.andWhere('currentCare.created_at <= :toCurrentCare', {
            toCurrentCare: filter.toCurrentCare,
          });
        }
      }
    } else {
      subQb = await this.getLeadsAfterSaleUnassignProjectQueryBuilder(
        filter,
        undefined,
        countryIds,
        companyId,
      );
    }
    if (isNil(subQb)) return [];
    subQb.leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`);
    subQb
      .distinctOn(['order.id'])
      .select([
        'order.id AS order_id',
        'order.total_price AS total_price',
        'order.surcharge AS surcharge',
        'order.shipping_fee AS shipping_fee',
        'order.discount AS discount',
      ])
      .orderBy('order.id', 'DESC');

    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('(' + subQb.getQuery() + ')', 'sub', 'l.order_id = sub.order_id')
      .select('COUNT(DISTINCT l.id)', 'count')
      .addSelect(
        'SUM(sub.total_price + COALESCE(sub.surcharge, 0) + COALESCE(sub.shipping_fee, 0) - COALESCE(sub.discount, 0))',
        'revenue',
      )
      .setParameters(subQb.getParameters());
    for (const group of groupBy) {
      qb.addGroupBy(`l.${group}`);
      qb.addSelect(`l.${group}`, group);
    }
    const data = await qb.getRawMany();
    for (const item of data) {
      if (!isNil(item.state)) {
        item.state = CareStateAfterSales[item.state];
      }
      item.revenue = Number(item.revenue);
      item.count = Number(item.count);
    }
    return data;
  }

  // async setQueryForAssignedProjectLead(
  //   filter: LeadsASAssignedProjectFilter,
  //   leadsQb: SelectQueryBuilder<Lead>,
  //   headers: Record<string, string>,
  // ): Promise<SelectQueryBuilder<Lead> | null> {
  //   const countryIds = headers?.['country-ids'] ? headers?.['country-ids'].split(',') : [];
  //   if (filter.userIds)
  //     leadsQb.andWhere('lead.user_id IN (:...userIds)', { userIds: filter.userIds });

  //   if (!isEmpty(filter.reasonIds)) {
  //     const noReasonId = remove(filter.reasonIds, id => id === -1);
  //     leadsQb
  //       .leftJoin(LeadCareItem, 'CI', 'CI.lead_id = lead.id')
  //       .andWhere(
  //         new Brackets(sqb => {
  //           if (!isEmpty(noReasonId)) sqb.where(`CI.reason_id IS NULL`);
  //           if (!isEmpty(filter.reasonIds)) {
  //             const reasonsSubQb = this.careReasonRepo
  //               .createQueryBuilder('cr1')
  //               .innerJoin(
  //                 CareReason,
  //                 'cr2',
  //                 'cr1.reasonKey = cr2.reasonKey AND cr1.companyId = cr2.companyId',
  //               )
  //               .select('cr1.id')
  //               .where('cr2.id IN (:...reasonIds)');

  //             if (!isEmpty(countryIds))
  //               reasonsSubQb.andWhere(
  //                 '(cr1.countryId IS NULL OR cr1.countryId IN (:...countryIds))',
  //               );

  //             sqb.orWhere(`CI.reason_id IN (${reasonsSubQb.getQuery()})`);
  //           }
  //         }),
  //       )
  //       .setParameters({ reasonIds: filter.reasonIds, countryIds });
  //   }

  //   if (filter.tagIds) {
  //     leadsQb.andWhere('order_tags.tag_id IN (:...tagIds)', {
  //       tagIds: filter.tagIds,
  //     });
  //   }

  //   if (filter.numberOfRepeats) {
  //     const subQb = this.leadCaresRepo
  //       .createQueryBuilder('lc')
  //       .select('lc.leadId', 'lead_id')
  //       .addSelect('COUNT (lc.id)', 'cares_count')
  //       .groupBy('lc.leadId');
  //     leadsQb
  //       .leftJoin(`(${subQb.getQuery()})`, 'lc', 'lead.id = lc.lead_id')
  //       .andWhere('lc.cares_count > :numberOfRepeats', { numberOfRepeats: filter.numberOfRepeats });
  //   }

  //   if (filter.query) {
  //     const phonesQuery = filter.query.match(/\S+/g).join('|');
  //     leadsQb.andWhere(
  //       new Brackets(qb => {
  //         qb.where('order.display_id ILIKE :query', { query: `%${filter.query}%` })
  //           .orWhere('order.customer_name ILIKE :query', { query: `%${filter.query}%` })
  //           .orWhere('order.customer_phone ILIKE :query', { query: `%${filter.query}%` })
  //           .orWhere('order.address_ward ILIKE :query', { query: `%${filter.query}%` })
  //           .orWhere('order.address_district ILIKE :query', { query: `%${filter.query}%` })
  //           .orWhere('order.address_province ILIKE :query', { query: `%${filter.query}%` })
  //           .orWhere('order.customer_phone ~* :phonesQuery', { phonesQuery });
  //       }),
  //     );
  //   }
  //   return leadsQb;
  // }

  async getLeadsASAssignedProject(
    pagination?: PaginationOptions,
    filter?: LeadsASAssignedProjectFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    let leadsQb = await this.getLeadsAfterSaleAssignedProjectQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
      true,
    );
    if (isNil(leadsQb)) return [];
    const subQuery = leadsQb.clone();
    subQuery.leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`);
    // filter by tag
    if (filter.tagIds) {
      const subQb = getConnection(orderConnection)
        .createQueryBuilder()
        .from('order_tags', 'ot')
        .select('ot.order_id', 'order_id')
        .where('ot.tag_id IN (:...tagIds)', { tagIds: filter.tagIds })
        .groupBy('ot.order_id');
      if (filter.tagMethod == TagMethodType.Include) {
        if (filter.operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          subQuery
            .andWhere(`lead.orderId IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          subQuery.andWhere('order_tags.tag_id IN (:...tagIds)', {
            tagIds: filter.tagIds,
          });
        }
      }

      if (filter.tagMethod == TagMethodType.Exclude) {
        if (filter.operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          subQuery
            .andWhere(`lead.orderId NOT IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          subQuery
            .andWhere(`lead.orderId NOT IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
      }
    }
    leadsQb = await this.setJoinDataLeadsAfterSaleAssignedProject(leadsQb);
    if (filter.getUpcomingAppointments) {
      leadsQb.leftJoinAndMapMany(
        'currentCare.upcomingAppointments',
        AppointmentSchedule,
        'a',
        'currentCare.id = a.leadCareId AND a.appointmentTime > NOW()',
      );
    }
    if (filter.getAllAppointments) {
      leadsQb.leftJoinAndMapMany(
        'lead.appointments',
        AppointmentSchedule,
        'appointments',
        'lead.id = cares.leadId AND cares.id = appointments.leadCareId',
      );
    }
    switch (filter.dateRangeType) {
      case LeadAftersalesDateRangeType.createdTime: {
        if (filter.fromRange) {
          subQuery.andWhere('lead.created_at >= :from', { from: filter.fromRange });
        }
        if (filter.toRange) {
          subQuery.andWhere('lead.created_at <= :to', { to: filter.toRange });
        }
        break;
      }
      case LeadAftersalesDateRangeType.takeCareTime: {
        subQuery.leftJoin('lead.latestCareItem', 'lcii');
        if (filter.fromRange) {
          subQuery
            .andWhere('lcii.createdAt IS NOT NULL')
            .andWhere('lcii.createdAt >= :from', { from: filter.fromRange });
        }
        if (filter.toRange) {
          subQuery
            .andWhere('lcii.createdAt IS NOT NULL')
            .andWhere('lcii.createdAt <= :to', { to: filter.toRange });
        }
        break;
      }
      case LeadAftersalesDateRangeType.distributeTime: {
        subQuery.leftJoin('lead.currentCare', 'currentCare');
        if (filter.fromRange) {
          subQuery.andWhere('currentCare.created_at >= :from', { from: filter.fromRange });
        }
        if (filter.toRange) {
          subQuery.andWhere('currentCare.created_at <= :to', { to: filter.toRange });
        }
        break;
      }
    }
    if (filter.fromCurrentCare) {
      leadsQb.andWhere('currentCare.created_at >= :fromCurrentCare', {
        fromCurrentCare: filter.fromCurrentCare,
      });
    }

    if (filter.toCurrentCare) {
      leadsQb.andWhere('currentCare.created_at <= :toCurrentCare', {
        toCurrentCare: filter.toCurrentCare,
      });
    }
    let orderBy = filter.orderBy ? `lead.${filter.orderBy}` : 'lead.id';
    if (filter.orderBy === 'createdAt') {
      orderBy = 'lead.createdAt';
    } else if (filter.orderBy === 'reasonUpdateTime') {
      orderBy = 'lci.createdAt';
      subQuery.leftJoin('lead.latestCareItem', 'lci');
    } else if (filter.orderBy === 'lastCareAt') {
      orderBy = 'lci.createdAt';
      subQuery
        .leftJoin('lead.latestCareItem', 'lci')
        .addSelect(['lci.id', 'lci.reasonId', 'lci.timesRepeatReason', 'lci.createdAt']);
    } else {
      orderBy = 'lead.id';
    }
    const sort = filter.sort ? filter.sort : Sort.DESC;

    leadsQb.orderBy(orderBy, sort);
    subQuery.orderBy(orderBy, sort);
    subQuery.select(['lead.id', 'lead.createdAt', 'lead.updatedAt', 'lead.lastUpdatedState']);
    if (filter.orderBy === 'reasonUpdateTime') {
      subQuery.addSelect(['lci.id', 'lci.reasonId', 'lci.timesRepeatReason', 'lci.createdAt']);
    } else if (filter.orderBy === 'lastCareAt') {
      subQuery.addSelect(['lci.createdAt']);
    }

    if (pagination) subQuery.take(pagination.limit).skip(pagination.skip);
    const leadIds = await subQuery.getMany();
    if (leadIds.length === 0) return [];
    leadsQb.andWhere(`lead.id IN (:...oIds)`, {
      oIds: leadIds?.map((it: any) => it?.id),
    });
    const leads = await leadsQb.getMany();
    if (filter.getExternalSource) {
      const extSourceIds = leads.map(lead => lead.order.externalSourceId);
      if (extSourceIds.length > 0) {
        const extSourcesQb = this.externalSourceRepo
          .createQueryBuilder('es')
          .leftJoin('order_sources_closure', 'osc', 'osc.id_descendant = es.id')
          .leftJoinAndMapMany('es.parents', OrderSource, 'oes', 'osc.id_ancestor = oes.id')
          .where('es.id IN (:...extSourceIds)', { extSourceIds });
        const extSources = await extSourcesQb.getMany();
        const extSourcesLookup = extSources?.reduce((prev, es) => {
          prev[String(es.id)] = es.parents;
          return prev;
        }, {});
        if (extSourcesLookup) {
          for (const lead of leads) {
            if (!lead.order.externalSourceId) continue;
            lead.order.externalSource = extSourcesLookup[lead.order.externalSourceId][0];
            lead.order.externalSources = extSourcesLookup[lead.order.externalSourceId];
          }
        }
      }
    }

    return leads.map(l => {
      return {
        ...l,
        state: CareStateAfterSales[l.state],
      };
    });
  }

  async distributeProjectForLeadAs(
    filter: LeadsASUnassignProjectFilter,
    headers: Record<string, string>,
    request: Record<string, any>,
    data: DistributeProjectForLeadAsDto,
  ) {
    const leadsRaw = await this._getLeadsToDistributeProject(filter, headers, request);
    if (leadsRaw.length === 0) throw new BadRequestException('No leads to distribute');
    if (leadsRaw.length < data.projectIds.length) throw new BadRequestException('Not enough leads');
    if (isNil(data.distributeType)) {
      await this._distributeEquallyByProduct(leadsRaw, data, request?.user?.id);
    } else {
      const leadIdsPerProduct = await this._getLeadIdsPerProduct(filter, headers, request);
      await this._distributeByProductRatio(leadsRaw, data, leadIdsPerProduct, request?.user?.id);
    }
    return true;
  }

  async _getLeadsToDistributeProject(
    filter: LeadsASUnassignProjectFilter,
    headers: Record<string, string>,
    request: Record<string, any>,
  ): Promise<
    Array<{
      leadId: number;
      orderId: number;
      productSku: string;
    }>
  > {
    const leadsQb = await this.getLeadsAfterSaleUnassignProjectQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (isNil(leadsQb)) return [];
    // if (!isNil(filter.isGetUnassignedProjectLead)) {
    //   if (filter.isGetUnassignedProjectLead) leadsQb.andWhere('lead.project_id IS NULL');
    //   else leadsQb.andWhere('lead.project_id IS NOT NULL');
    // }
    leadsQb.andWhere('lead.project_id IS NULL');
    if (isEmpty(filter.productIds)) {
      leadsQb.leftJoin('order_products', 'ops', 'ops.order_id = oo.id');
    }
    const orderBy = filter.orderBy ? `lead.${filter.orderBy}` : 'lead.id';
    const sort = filter.sort ? filter.sort : Sort.DESC;
    leadsQb.groupBy('lead.id');
    leadsQb.orderBy(orderBy, sort);
    // leadsQb = await this.setCommonGetListLeadsQb(filter, leadsQb);
    leadsQb.select([
      'lead.id AS "leadId"',
      'lead.order_id AS "orderId"',
      `STRING_AGG(ops.product_detail->>'sku'::TEXT, ',' ORDER BY ops.product_id ASC) AS "productSku"`,
    ]);
    const leadsRaw: Array<{
      leadId: number;
      orderId: number;
      productSku: string;
    }> = await leadsQb.getRawMany();
    return leadsRaw;
  }

  async _distributeEquallyByProduct(
    leadsRaw: Array<{
      leadId: number;
      orderId: number;
      productSku: string;
    }>,
    data: DistributeProjectForLeadAsDto,
    userId: number,
  ) {
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const combinationProductIds: Record<string, Array<number>> = leadsRaw.reduce((prev, next) => {
        if (prev[next.productSku] !== undefined) {
          prev[next.productSku].push(next.leadId);
        } else {
          prev[next.productSku] = [next.leadId];
        }
        return prev;
      }, {});
      const hashMapOrderIdOfLead: Record<string, any> = leadsRaw.reduce((prev, next) => {
        prev[next.leadId] = next.orderId;
        return prev;
      }, {});
      for (const productIds in combinationProductIds) {
        const limitLeadsPerProj =
          combinationProductIds[productIds].length >= data.projectIds.length
            ? Math.ceil(combinationProductIds[productIds].length / data.projectIds.length)
            : 1;
        for (const projId of data.projectIds) {
          const leadsSplit = combinationProductIds[productIds].splice(0, limitLeadsPerProj);
          if (leadsSplit.length <= 0) break;
          const leadIds = leadsSplit.map(it => it);
          const orderIds = leadsSplit.map(it => hashMapOrderIdOfLead[it.toString()]);
          await Promise.all([
            queryRunner.manager
              .getRepository(Lead)
              .createQueryBuilder()
              .update()
              .set({
                projectId: projId,
                updatedBy: userId,
              })
              .where('id IN (:...ids)', {
                ids: leadIds,
              })
              .execute(),
            queryRunner.manager
              .getRepository(Order)
              .createQueryBuilder()
              .update()
              .set({
                projectId: projId,
                lastUpdatedBy: userId,
              })
              .where('id IN (:...ids)', {
                ids: orderIds,
              })
              .execute(),
            ,
            orderIds.forEach(it => {
              this.amqpConnection.publish(
                'order-service',
                'scan-possible-duplicate-leads-after-sale',
                { orderId: it },
              );
            }),
          ]);
        }
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async _distributeByProductRatio(
    leadsRaw: Array<{
      leadId: number;
      orderId: number;
    }>,
    data: DistributeProjectForLeadAsDto,
    leadIdsPerProduct: Array<{
      sku: string;
      leadIds: string;
    }>,
    userId: number,
  ) {
    let leadsRemain = [];
    let count = 0;
    for (const value of data.values) {
      count++;
      const leadIdsNeedDistribute = leadIdsPerProduct
        .find(it => it.sku === value.productSku)
        .leadIds.split(',')
        .map(it => +it);
      const leadNeedDistribute = [];
      const hashmapLeadsOfProj: Record<string, any> = {};
      leadsRaw.forEach(l => {
        if (leadIdsNeedDistribute.includes(l.leadId)) leadNeedDistribute.push(l);
        else if (
          !leadIdsNeedDistribute.includes(l.leadId) &&
          leadsRemain.findIndex(it => it.leadId === l.leadId) === -1
        )
          leadsRemain.push(l);
      });
      leadsRaw = leadsRemain;
      if (count < data.values.length) {
        leadsRemain = [];
      }
      for (const projectSetting of value.values) {
        if (data.distributeType === DistributeProjectForLeadAsType.percentage) {
          hashmapLeadsOfProj[projectSetting.projectId] = leadNeedDistribute.splice(
            0,
            Math.ceil((projectSetting.value / 100) * leadNeedDistribute.length),
          );
        } else {
          hashmapLeadsOfProj[projectSetting.projectId] = leadNeedDistribute.splice(
            0,
            projectSetting.value,
          );
        }
      }
      const projectRest = difference(
        data.projectIds,
        value.values.map(it => it.projectId),
      );
      for (const projId of projectRest) {
        hashmapLeadsOfProj[projId] = leadNeedDistribute.splice(
          0,
          Math.ceil(leadNeedDistribute.length / projectRest.length),
        );
      }
      for (const proj in hashmapLeadsOfProj) {
        const leadsSplit = hashmapLeadsOfProj[proj];
        if (leadsSplit.length > 0) {
          const leadIds = leadsSplit.map(it => it.leadId);
          const orderIds = leadsSplit.map(it => it.orderId);
          await Promise.all([
            this.leadsRepo
              .createQueryBuilder()
              .update()
              .set({
                projectId: +proj,
                updatedBy: userId,
              })
              .where('id IN (:...ids)', {
                ids: leadIds,
              })
              .execute(),
            this.orderRepo
              .createQueryBuilder()
              .update()
              .set({
                projectId: +proj,
                lastUpdatedBy: userId,
              })
              .where('id IN (:...ids)', {
                ids: orderIds,
              })
              .execute(),
            orderIds.forEach(it => {
              this.amqpConnection.publish(
                'order-service',
                'scan-possible-duplicate-leads-after-sale',
                { orderId: it },
              );
            }),
          ]);
        }
      }
    }

    if (leadsRemain.length > 0) {
      await this._distributeEquallyByProduct(leadsRemain, data, userId);
    }
  }

  async manualDistribute(
    filter: ManualDistributeLeadsAfterSaleFilter,
    body: ManualDistributeLeadsAfterSaleDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const countryId = headers['country-ids']?.split(',')?.[0];
    let projectIds = headers['project-ids']?.split(',');
    if (!filter.projectIds || isEmpty(filter.projectIds))
      throw new BadRequestException(`ProjectIds required when manual distribute data.`);
    const companyId = request.user.companyId;
    const userId = request.user.id;

    // get projectIds of user
    let scopes = flatMap(request?.user?.profiles, p => p[4]);
    scopes = scopes.filter(s => s[0] == countryId);
    const projectIdsActive = scopes.map(s => String(s[1])); // get list Ids of project ['1', '2', '3', ...]
    projectIds = projectIdsActive.filter(item => projectIds.includes(item));
    filter.projectIds = projectIds.filter(item => filter.projectIds.includes(Number(item)));

    const { distributeType, specs } = body;
    const users: ShiftUser[] = await this.shiftsService.getWorkingUsers(
      { userIds: body.userIds },
      headers,
      request,
    );

    if (users.length === 0)
      throw new BadRequestException(`Không có nhân viên nào đang trong ca làm việc`);

    const { userIds, userShiftsLookup, userProjects } = users.reduce(
      (prev, user) => {
        if (!prev.userIds.includes(user.userId)) prev.userIds.push(user.userId);
        prev.userShiftsLookup[user.userId] = user.shiftId;
        prev.userProjects[user.userId] = user.projectIds;
        return prev;
      },
      { userIds: [], userShiftsLookup: {}, userProjects: {} },
    );

    // Get qualified leads to distribute
    const qb = await this.getLeadsToDistributeQueryBuilder(filter, [countryId], companyId, headers);

    let leads = isNil(qb) ? [] : await qb.getMany();
    const totalLeads = leads.length;
    if (totalLeads === 0) throw new HttpException(`No data to distribute`, HttpStatus.NO_CONTENT);

    const [config, procedureConfig] = await Promise.all([
      this.leadAfterSaleDistributeConfigsService.getLeadDistributeConfig(headers, request),
      this.leadAfterSaleDistributeConfigsService.getConfigByCompanyId(
        companyId,
        Number(countryId),
        LeadASConfigType.processing_procedure,
      ),
    ]);

    if (!config) throw new BadRequestException(`Chưa thiết lập cấu hình định mức`);
    const assignedRule = procedureConfig?.rules?.find(
      r => r.type === LeadASDistributeRuleType.assigned_leads,
    );
    const skipAssigned = (assignedRule?.value as LeadASDistributeRuleValue[])?.find(
      v => v.condition === LeadASDistributeCondition.changeCareStateToInProcessAutomatically,
    )?.value;

    const [usersLeadsCount, usersLeadsProcess]: [
      { userId: number; count: number }[],
      { userId: number; count: number }[],
    ] = await Promise.all([
      this.countLeadsAfterSale(
        {
          state: LEAD_AFTER_SALES_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED,
          userIds,
          isAssignedProject: true,
        },
        ['userId'],
        headers,
        request,
      ),
      this.countLeadsAfterSale(
        { state: [CareStateAfterSales.no_attempt], userIds, isAssignedProject: true },
        ['userId'],
        headers,
        request,
      ),
    ]);

    const usersLeadsCountLookup = usersLeadsCount.reduce((prev, next) => {
      prev[next.userId] = next.count;
      return prev;
    }, {});

    const usersLeadsCountNoAttempLookup = usersLeadsProcess.reduce((prev, next) => {
      prev[next.userId] = next.count;
      return prev;
    }, {});

    userIds.forEach(userId => {
      if (!usersLeadsCountLookup[userId]) usersLeadsCountLookup[userId] = 0;
      if (!usersLeadsCountNoAttempLookup[userId]) usersLeadsCountNoAttempLookup[userId] = 0;
    });

    const maxAssignedLeadsRule = config.rules.find(
      rule => rule.type === LeadASDistributeRuleType.assigned_leads,
    )?.value;
    const maxLeadToDistribute: number | undefined =
      maxAssignedLeadsRule.find(v => v.condition === LeadASDistributeCondition.maxLeads)?.value ||
      Infinity;

    const maxProcessingData =
      config.rules
        .find(r => r.type === LeadASDistributeRuleType.processing_leads)
        ?.value.find(v => v.condition === LeadASDistributeCondition.maxLeads).value || Infinity;

    let distributedLeads: Record<string, Lead[]> = {};
    if (isNil(distributeType)) {
      distributedLeads = this.distributeEqually(
        userIds,
        leads,
        usersLeadsCountLookup,
        maxLeadToDistribute,
        userProjects,
        usersLeadsCountNoAttempLookup,
        maxProcessingData,
        Boolean(skipAssigned),
      );
    } else {
      const maxLeads = skipAssigned ? maxProcessingData : maxLeadToDistribute;
      const leadsCountLookup = skipAssigned ? usersLeadsCountNoAttempLookup : usersLeadsCountLookup;

      switch (distributeType) {
        case AmountType.fixed: {
          for (const spec of specs) {
            const { userId, value } = spec;
            let remainingLeads = 0;
            if (skipAssigned) {
              const numCanReceive = maxLeadToDistribute - usersLeadsCountLookup[userId];
              const numProcessCanReceive =
                maxProcessingData - usersLeadsCountNoAttempLookup[userId];
              const realNumCanReceive = Math.min(numCanReceive, numProcessCanReceive);
              remainingLeads = Math.min(realNumCanReceive, value);
            } else {
              remainingLeads = Math.max(maxLeads - (leadsCountLookup[userId] || 0), 0);
            }
            const leadsToDistribute = Math.min(remainingLeads, value);
            const userProjectIds = userProjects[userId] || [];
            const userLeads = leads.filter(lead => userProjectIds.includes(lead?.order.projectId));
            distributedLeads[userId] = userLeads.splice(0, leadsToDistribute);
            leads = leads.filter(
              lead =>
                !distributedLeads[userId].some(distributedLead => distributedLead.id === lead.id),
            );
          }
          // Distribute remaining orders equally among the rest of the user IDs
          const remainingUsers = userIds.filter(userId => !distributedLeads[userId]);
          distributedLeads = {
            ...distributedLeads,
            ...this.distributeEqually(
              remainingUsers,
              leads,
              usersLeadsCountLookup,
              maxLeadToDistribute,
              userProjects,
              usersLeadsCountNoAttempLookup,
              maxProcessingData,
              Boolean(skipAssigned),
            ),
          };
          break;
        }
        case AmountType.percentage:
        default: {
          for (const spec of specs) {
            const { userId } = spec;
            const value = Math.ceil((totalLeads * spec.value) / 100);
            let remainingLeads = 0;
            if (skipAssigned) {
              const numCanReceive = maxLeadToDistribute - usersLeadsCountLookup[userId];
              const numProcessCanReceive =
                maxProcessingData - usersLeadsCountNoAttempLookup[userId];
              const realNumCanReceive = Math.min(numCanReceive, numProcessCanReceive);
              remainingLeads = Math.min(realNumCanReceive, value);
            } else {
              remainingLeads = Math.max(maxLeads - (leadsCountLookup[userId] || 0), 0);
            }
            const leadsToDistribute = Math.min(remainingLeads, value);
            const userProjectIds = userProjects[userId] || [];
            const userLeads = leads.filter(lead => userProjectIds.includes(lead?.order.projectId));
            distributedLeads[userId] = userLeads.splice(0, leadsToDistribute);
            leads = leads.filter(
              lead =>
                !distributedLeads[userId].some(distributedLead => distributedLead.id === lead.id),
            );
          }
          // Distribute remaining orders equally among the rest of the user IDs
          const remainingUsers = userIds.filter(userId => !distributedLeads[userId]);
          distributedLeads = {
            ...distributedLeads,
            ...this.distributeEqually(
              remainingUsers,
              leads,
              usersLeadsCountLookup,
              maxLeadToDistribute,
              userProjects,
              usersLeadsCountNoAttempLookup,
              maxProcessingData,
              Boolean(skipAssigned),
            ),
          };
          break;
        }
      }
    }

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const records = Object.keys(distributedLeads).reduce((prev: LeadCare[], key: string) => {
        const items = distributedLeads[key].map(lead => {
          return plainToInstance(LeadCare, {
            userId: key,
            leadId: lead.id,
            shiftId: userShiftsLookup[key],
            updatedBy: userId,
          });
        });
        prev.push(...items);
        return prev;
      }, []);
      const leadCares = await queryRunner.manager.save(records);
      const mLeads = leadCares.map(care => {
        return plainToInstance(Lead, {
          id: care.leadId,
          userId: care.userId,
          state: skipAssigned ? CareStateAfterSales.no_attempt : CareStateAfterSales.assigned,
          currentCareId: care.id,
          lastCareId: care.id,
          updatedBy: userId,
          updatedAt: care.createdAt,
        });
      });
      await queryRunner.manager.save(mLeads);
      await queryRunner.commitTransaction();
      await Promise.all(
        mLeads.flatMap(it => [
          this.onLeadUpdated(it),
          this.amqpConnection.publish(
            'order-service',
            'assign-duplicate-leads-according-to-original-lead-after-sale',
            { leadId: it.id, updatedBy: it.updatedBy, state: it.state, filter },
          ),
        ]),
      );
      return leadCares;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async onLeadUpdated(payload: Partial<Lead>, currentState?: CareStateAfterSales) {
    const { id, state, userId, currentCareId, updatedAt, updatedBy } = payload;
    if (!isNil(state) && !isNil(currentCareId)) {
      console.log(`lead aftersales state changed to ${PlainCareStateAfterSales[state]}`, payload);
      await Promise.all([
        this.amqpConnection.publish('order-service', 'after-lead-after-sale-state-updated', {
          leadId: id,
          state,
          updatedAt,
          currentCareId,
          currentState,
        }),
        this.amqpConnection.publish('order-service', 'schedule-gather-lead-after-sale', {
          leadId: id,
          state,
          updatedAt,
          currentCareId,
        }),
      ]);
    }
    if (userId === null || !isNil(userId)) {
      await this.amqpConnection.publish('order-service', 'after-lead-user-id-updated', {
        leadId: id,
        updatedAt,
        userId,
        updatedBy,
      });
    }
    await this.amqpConnection.publish('message-service', 'after-lead-updated', {
      leadId: id,
    });
    return true;
  }

  async getLeadsToDistributeQueryBuilder(
    filter: ManualDistributeLeadsAfterSaleFilter,
    countryIds: (number | string)[],
    companyId: number,
    headers?: Record<string, string>,
  ) {
    let projectId;
    if (headers) projectId = Number(headers['project-ids']);

    if (!projectId && isEmpty(filter.projectIds)) return null;

    const connection = getConnection(orderConnection);
    const subQb = await connection.createQueryBuilder();
    if (isEmpty(filter?.ids)) {
      subQb
        .select('MIN(l.id)', 'originId')
        .from('duplicate_leads', 'dl')
        .innerJoin('leads', 'l', 'dl.lead_id = l.id');
    } else {
      subQb
        .select('MIN(l.id)', 'originId')
        .from('leads', 'l')
        .leftJoin('duplicate_leads', 'dl', 'dl.lead_id = l.id');
    }
    subQb
      .innerJoin('orders', 'o', 'o.id = l.order_id')
      .where('l.state IN (:...states)', {
        states: [CareStateAfterSales.new, CareStateAfterSales.unassign_attempted],
      })
      .andWhere('l.user_id IS NULL')
      .andWhere('l.lead_type = :leadType', { leadType: LeadType.after_sale })
      .andWhere('o.company_id = :companyId', { companyId })
      .andWhere('o.customer_phone != :emptyPhone', { emptyPhone: '' })
      .groupBy('o.customer_phone')
      .addGroupBy('o.project_id');

    if (projectId) subQb.andWhere('o.project_id = :projectId', { projectId });
    if (!isEmpty(filter.projectIds))
      subQb.andWhere('o.project_id IN (:...projectIds)', { projectIds: filter.projectIds });
    if (!isEmpty(filter?.ids)) subQb.andWhere('l.id IN (:...ids)', { ids: filter.ids });
    if (filter?.from) subQb.andWhere('l.created_at >= :from', { from: filter.from });
    if (filter?.to) subQb.andWhere('l.created_at <= :to', { to: filter.to });
    const listIds = await subQb.execute();

    const leadsQb = await this.getLeadsAfterSaleAssignedProjectQueryBuilder(
      {
        ...filter,
        state: isEmpty(filter.state)
          ? [CareStateAfterSales.new, CareStateAfterSales.unassign_attempted]
          : filter.state,
      },
      undefined,
      countryIds,
      companyId,
    );

    return isNil(leadsQb)
      ? leadsQb
      : leadsQb
          .leftJoin('lead.duplicateLeads', 'pdl')
          .andWhere(
            new Brackets(subQuery => {
              subQuery.where('pdl.id IS NULL');
              if (!isEmpty(listIds)) {
                subQuery.orWhere('lead.id IN (:...ids)', {
                  ids: listIds.map(item => item.originId),
                });
              }
            }),
          )
          .distinct(true)
          .select(['lead.id', 'lead.state', 'order.projectId'])
          .orderBy('lead.id', 'ASC');
  }

  distributeEqually(
    userIds: number[],
    leads: Lead[],
    usersLeadsCount: Record<string, number>,
    maxLeadToDistribute: number,
    userProjects: Record<string, number[]>,
    usersLeadsCountNoAttempLookup: Record<string, number>,
    maxProcessingData?: number,
    skipAssigned?: boolean,
  ): Record<number, Lead[]> {
    if (isEmpty(userIds)) return {};

    // Sort user ids based on the number of orders they have already
    userIds.sort((a, b) => (usersLeadsCount[a] || 0) - (usersLeadsCount[b] || 0));

    if (maxLeadToDistribute !== Infinity && maxProcessingData === Infinity)
      maxProcessingData = maxLeadToDistribute;

    const distributedLeads = {};
    const numUsers = userIds.length;
    const numLeads = leads.length;
    const leadsCanReceive = {};

    if (skipAssigned) {
      Object.keys(usersLeadsCountNoAttempLookup).forEach(userId => {
        if (maxLeadToDistribute - usersLeadsCount[userId] <= 0) return;
        const numCanReceive = maxLeadToDistribute - usersLeadsCount[userId];
        const numProcessCanReceive = maxProcessingData - usersLeadsCountNoAttempLookup[userId];
        leadsCanReceive[userId] = Math.min(numCanReceive, numProcessCanReceive);
      });
    } else {
      Object.keys(usersLeadsCount).forEach(userId => {
        const count = usersLeadsCount[userId] || 0;
        if (maxLeadToDistribute - count > 0) leadsCanReceive[userId] = maxLeadToDistribute - count;
      });
    }

    leadsLoop: for (let i = 0; i < numLeads; i++) {
      // const userId = userIds[i % numUsers];
      const lead = leads[i];
      const projectId = lead?.order?.projectId;

      for (let j = 0; j < numUsers; j++) {
        const userId = userIds[(i + j) % numUsers];
        if (!distributedLeads[userId]) distributedLeads[userId] = [];
        if (!leadsCanReceive[userId]) continue;

        // Check if user is eligible for the project
        if (userProjects && userProjects[userId]?.includes(projectId)) {
          // Check if user has reached max lead limit
          if (distributedLeads[userId].length < leadsCanReceive[userId]) {
            distributedLeads[userId].push(lead);
            continue leadsLoop;
          }
        }
      }
    }

    return distributedLeads;
  }

  async getLeadAfterSaleDetail(
    lid: number,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const mQuery = this.leadsRepo
      .createQueryBuilder('leads')
      .where('leads.id = :lid', { lid })
      .leftJoinAndSelect('leads.cares', 'cares')
      .leftJoinAndSelect('cares.careItems', 'careItems')
      .leftJoin('leads.duplicateLeads', 'dupLeads')
      .addSelect([
        'dupLeads.id',
        'dupLeads.leadId',
        'dupLeads.duplicateLeadId',
        'dupLeads.orderId',
        'dupLeads.displayId',
        'dupLeads.duplicateDisplayId',
      ]);

    const data = await mQuery.getOne();
    let duplicateLeadIds = [];
    if (data) {
      duplicateLeadIds = data.duplicateLeads.map(dupLead => dupLead.duplicateLeadId);
      if (duplicateLeadIds.length === 0) return { ...data, state: CareStateAfterSales[data.state] };
      // Fetch lead.id, lead.orderId, and lead.state for duplicateLeadIds
      const leadOrders = await this.leadsRepo
        .createQueryBuilder('leads')
        .where('leads.id IN (:...duplicateLeadIds)', { duplicateLeadIds })
        .select(['leads.id', 'leads.orderId', 'leads.state', 'leads.formCapturedAt'])
        .getMany();

      // Map duplicateLeadId to orderId and state
      const leadOrderMap = new Map(
        leadOrders.map(item => [
          item.id,
          {
            orderId: item.orderId,
            state: CareStateAfterSales[item.state],
            formCapturedAt: item.formCapturedAt,
          },
        ]),
      );

      // Fetch orders using the collected orderIds
      const orderIds = [...new Set(leadOrders.map(item => item.orderId))]; // Ensure unique orderIds
      const orders = await this.orderRepo
        .createQueryBuilder('orders')
        .leftJoinAndSelect('orders.externalSource', 'es')
        .where('orders.id IN (:...orderIds)', { orderIds })
        .getMany();

      // Create a map for orders
      const orderMap = new Map(orders.map(order => [order.id, order]));

      // Enrich duplicateLeads with corresponding orders and states
      data.duplicateLeads = data.duplicateLeads.map(dupLead => {
        const leadDetails = leadOrderMap.get(dupLead.duplicateLeadId);
        const order = orderMap.get(leadDetails?.orderId);

        return {
          ...dupLead,
          order: order || null,
          state: CareStateAfterSales[leadDetails?.state || null], // Add the state here
          formCapturedAt: leadDetails?.formCapturedAt || null,
        };
      });
    }
    return {
      ...data,
      state: CareStateAfterSales[data.state],
    };
  }

  async importLeadsAfterSale(buffer: Buffer, request: Record<string, any>) {
    const userId = request?.user?.id;
    const data = await ExcelUtilsV2.read(buffer, 0, 'NO');

    const date = new Date();

    const items: { orderSourceId: string; body: ImportLeadsAfterSaleDto }[] = data.reduce(
      (prev: { orderSourceId: string; body: ImportLeadsAfterSaleDto }[], item) => {
        if (Object.keys(item).length !== 0) {
          const customerName = item['Customer *'] || item['Khách hàng *'];
          const customerPhone = item['Phone number *'] || item['Số điện thoại *'];
          const country = item['Country *'] || item['Thị trường * '] || item['Thị trường *'];
          const project = item['Project *'] || item['Dự án *'];
          const addressText = item['Address'] || item['Địa chỉ chi tiết'];
          const province = item['Province'] || item['Tỉnh/Thành phố'];
          const district = item['District'] || item['Quận/Huyện'];
          const ward = item['Ward'] || item['Phường/Xã'];
          const postCode = item['Post code'] || item['Mã bưu chính'];
          const productId = item['Product'] || item['Sản phẩm'];
          const variantSku = item['Variant'] || item['Biến thể'];
          const waybillNotes = item['Print order note'] || item['Ghi chú in đơn'];
          const note =
            item['Warehouse note'] || item['Ghi chú cho kho '] || item['Ghi chú cho kho'];
          const orderSourceId = item['Source ID '] || item['ID nguồn'] || item['Source ID'];
          const collectMethod = item['Collect method'] || item['Cách thu thập dữ liệu'];
          const marketerId = item['Marketer'] || item['Nhân viên MKT'];
          const body = plainToInstance(ImportLeadsAfterSaleDto, {
            customerName,
            customerPhone,
            country,
            project,
            addressText,
            province,
            district,
            ward,
            postCode,
            productId,
            variantSku,
            note,
            waybillNotes,
            orderSourceId,
            collectMethod,
            marketerId,
          });
          prev.push({
            orderSourceId,
            body,
          });
        }
        return prev;
      },
      [],
    );
    const results = [];
    for (const each of items) {
      try {
        const result = await this.createLeadsAfterSale(
          each.orderSourceId,
          each.body,
          userId,
          request,
        );
        results.push(result);
      } catch (error) {
        console.log(`error import landing page order`, error, each);
        results.push({
          message: 'Error when import landing page order',
          error: StringUtils.getString(error),
          data: each,
        });
      }
    }
    return results;
  }

  async createLeadsAfterSale(
    orderSourceId: string,
    body: ImportLeadsAfterSaleDto,
    userCreatedId: number,
    request: Record<string, any>,
  ) {
    const scopes = flatMap(request?.user?.profiles, p => p[4]);
    const countriesIdsActive = scopes.map(s => s[0]);
    const {
      customerPhone,
      customerName,
      country,
      project,
      addressText,
      province,
      district,
      ward,
      postCode,
      productId,
      variantSku,
      note,
      waybillNotes,
      collectMethod,
      marketerId,
    } = body;
    let metadata: Record<string, any> = {
      sourceType: SourceEntity.aftersales,
      repsId: '',
    };
    const metadataTagLog: Record<string, any> = {
      tags: [],
    };
    let metadataCarrierLog: Record<string, any> = {};
    if (!customerPhone)
      throw new BadRequestException({
        code: 'STD_0014',
        Message: 'Customer phone is required',
      });
    if (!customerName)
      throw new BadRequestException({
        code: 'ASL_0001',
        message: 'Customer name is required',
      });
    if (!project)
      throw new BadRequestException({
        code: 'ASL_0010',
        message: 'Project is required',
      });
    if (!country) {
      throw new BadRequestException({
        code: 'ASL_0009',
        message: 'Country is required',
      });
    }
    let user: User;
    try {
      const { data: mUser } = await this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'get-user',
        payload: { id: userCreatedId },
        timeout: 5000,
      });
      user = mUser as User;
    } catch (err) {
      console.log(`cannot fetch user`, err);
    }
    if (!user) throw new BadRequestException(`User id ${userCreatedId} not found`);

    const resCountries = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-countries',
      routingKey: 'get-countries',
      payload: {},
      timeout: 10000,
    });
    let countryId;
    if (resCountries?.data) {
      countryId = resCountries.data.find(c => c.name.toLowerCase() === country.toLowerCase())?.id;
      if (!countryId || !countriesIdsActive.includes(+countryId))
        throw new BadRequestException({
          code: 'ASL_0009',
          message: 'Country not found',
        });
    }

    const resProj: any = await this.amqpConnection.request({
      exchange: 'identity-service-projects',
      routingKey: 'get-project-by-short-names',
      payload: {
        names: [project],
        companyId: user.companyId,
      },
      timeout: 5000,
    });
    if (resProj?.data?.length === 0)
      throw new BadRequestException({
        code: 'ASL_0010',
        message: 'Project not found',
      });
    if (resProj.data[0]?.status === ProjectStatus[ProjectStatus.deactivated])
      throw new BadRequestException({
        code: 'ASL_0023',
        message: 'Project is deactivated',
      });
    const projectId = resProj.data[0].id;
    const projectIdsInCountry = scopes.filter(s => s[0] === +countryId).map(it => it[1]);
    if (!projectIdsInCountry.includes(+projectId))
      throw new BadRequestException({
        code: 'ASL_0021',
        message: 'Project not found',
      });

    if (orderSourceId) {
      const leadSource = await this.leadAfterSaleSourceRepo.findOne({
        id: orderSourceId,
        countryId: Number(countryId),
        projectId: Number(projectId),
      });
      if (!leadSource)
        throw new BadRequestException({
          code: 'ASL_0011',
          message: 'Order source not found',
        });
    }
    let mId;
    if (marketerId) {
      if (!StringUtils.validateEmail(marketerId))
        throw new BadRequestException({
          code: 'ASL_0025',
          message: 'Cannot find Marketer/Wrong format',
        });
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'get-users-by-filter',
        payload: {
          filter: {
            companyIds: [user.companyId],
            projectIds: [Number(projectId)],
            countryIds: [Number(countryId)],
            query: marketerId,
          },
        },
        timeout: 5000,
      });
      const users: any = data;
      if (users.length === 0) {
        throw new BadRequestException({
          code: 'ASL_0024',
          message: 'Marketer is not in the selected project',
        });
      }
      const userIsMkt = users.filter(x => {
        return (
          !x.isAdmin &&
          find(
            x?.profiles,
            p =>
              intersection(p?.role?.moduleInCharge || [], [ModuleInCharge[ModuleInCharge.marketer]])
                ?.length > 0 && p?.role?.status === CommonStatus[CommonStatus.activated],
          )
        );
      });
      if (userIsMkt.length === 0) {
        throw new BadRequestException({
          code: 'ASL_0024',
          message: 'Marketer is not in the selected project',
        });
      }
      mId = userIsMkt[0].id;
    }
    const order = plainToClass(Order, {
      customerPhone,
      customerName,
      note,
      companyId: user.companyId,
      creatorId: Number(user?.id),
      lastUpdatedBy: Number(user?.id),
      countryId: Number(countryId),
      projectId: Number(projectId),
      status: OrderStatus.Draft,
    });
    if (mId) {
      order.marketerId = mId;
      metadata = {
        ...metadata,
        mktId: mId,
      };
    }
    try {
      if (province) {
        const location = await this.locationService.getLocationByName(
          province,
          district,
          ward,
          countryId,
        );
        if (location) {
          if (location.wards_id) {
            order.addressWardId = location.wards_id;
            order.addressWard = location.wards_name;
          }
          order.addressDistrictId = location.districts_id;
          order.addressDistrict = location.districts_name;
          order.addressProvinceId = location.provinces_id;
          order.addressProvince = location.provinces_name;
          order.addressText = addressText;
          order.postCode = postCode;
        } else {
          throw new BadRequestException({
            code: 'ASL_0005',
            message: 'Address not found',
          });
        }
      }
    } catch (e) {
      throw new BadRequestException({
        code: 'ASL_0005',
        message: 'Address not found',
      });
    }
    order.addressText = addressText;
    metadata = {
      ...metadata,
      customer: {
        customerName,
        customerPhone,
        addressText,
        addressDistrictId: order.addressDistrictId || '',
        addressDistrict: order.addressDistrict || '',
        addressProvinceId: order.addressProvinceId || '',
        addressProvince: order.addressProvince || '',
        addressWardId: order.addressWardId || '',
        addressWard: order.addressWard || '',
        postCode: order?.postCode?.toString() || '',
      },
    };

    if (variantSku) {
      try {
        const variantsSku = variantSku.split(',').map(sku => sku.trim());
        const { data } = await this.amqpConnection.request({
          exchange: 'catalog-service-variants',
          routingKey: 'find-variant-by-list-sku',
          payload: {
            listSku: variantsSku,
            countryId: countryId,
            companyId: order.companyId,
            projectIds: [projectId],
          },
          timeout: 5000,
        });
        const variants = data as ProductVariant[];
        if (variants.length === 0)
          throw new BadRequestException({
            code: 'ASL_0003',
            message: 'Variant not found',
            data: variantsSku,
          });
        if (variants.length > 0 && variants.length !== variantsSku.length) {
          const vIds = variants.map(it => it.sku);
          throw new BadRequestException({
            code: 'ASL_0003',
            message: 'Variant not found',
            data: difference(variantsSku, vIds),
          });
        }

        order.products = map(variants, item => {
          const product = new OrderProduct();
          product.productId = item.id;
          product.productDetail = instanceToPlain(item);
          product.quantity = 1;
          product.price = item.priceByCountry || 0;
          return product;
        });
        order.totalPrice = sumBy(order.products, o => (o.editedPrice || o.price) * o.quantity);
        const metadataProduct = [];
        const totalWeight = sumBy(order.products, o => o?.productDetail?.weight * o.quantity);
        order.products.map(product => {
          metadataProduct.push({
            id: product.productId,
            sku: product?.productDetail?.sku,
            name: product?.productDetail?.product?.name,
            quantity: product.quantity,
            price: product.price,
            weight: product?.productDetail?.weight,
          });
        });
        metadata = {
          ...metadata,
          products: metadataProduct,
          totalWeight,
        };
      } catch (error) {
        throw error;
      }
    }
    metadata = {
      ...metadata,
      fee: {
        totalPrice: order?.totalPrice || 0,
        shippingFee: order?.shippingFee || 0,
        discount: order?.discount || 0,
        surcharge: order?.surcharge || 0,
        paid: order?.paid || 0,
        cod: Math.max(
          Number(order?.totalPrice || 0) +
            Number(order?.shippingFee || 0) +
            (order?.surcharge || 0) -
            (order?.discount || 0) -
            (order?.paid || 0),
          0,
        ),
      },
    };
    const mObjDisplayId = await this.ordersService.getOrderAfterSaleDisplayId(
      Number(projectId),
      order.companyId,
    );
    order.displayId = mObjDisplayId.displayId;
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      if (orderSourceId) {
        const externalSource = await this.externalSourceRepo
          .createQueryBuilder('oes')
          .andWhere('oes.entity = :entity')
          .andWhere('oes.entity_id = :entityId')
          .setParameters({
            entity: SourceEntity.aftersales,
            entityId: orderSourceId,
          })
          .getOne();
        if (externalSource) {
          order.externalSource = externalSource;
        } else {
          order.externalSource = await queryRunner.manager.save(
            plainToInstance(OrderSource, {
              entity: SourceEntity.aftersales,
              entityId: orderSourceId,
            }),
          );
        }
        metadata = {
          ...metadata,
          orderSourceType: SourceEntity.aftersales,
          orderSourceId: order?.externalSource?.id,
        };
      }
      const customerDb = await queryRunner.manager.getRepository(Customer).findOne({
        where: {
          phone: customerPhone,
          countryId: Number(countryId),
          companyId: user.companyId,
        },
      });
      let customer = plainToInstance(Customer, {
        name: body.customerName || body.customerPhone,
        phone: body.customerPhone,
        countryId: countryId,
        companyId: user.companyId,
      });
      if (body.addressText) customer.address = body.addressText;
      if (customerDb) customer.id = customerDb.id;
      customer = await queryRunner.manager.save(customer);
      metadataCarrierLog = {
        waybillNote: body.waybillNotes || waybillNotes || '',
        carrierCode: '',
        waybillNumber: '',
        customerEDD: null,
        carrierEDD: null,
      };
      // Insert new order
      order.metadata = JSON.stringify(metadata);
      order.metadataTagLog = JSON.stringify(metadataTagLog);
      order.metadataCarrierLog = JSON.stringify(metadataCarrierLog);
      const mOrder = await queryRunner.manager.save(order);

      if (body.waybillNotes || waybillNotes) {
        await queryRunner.manager.save(OrderCarrier, {
          orderId: mOrder.id,
          waybillNote: body.waybillNotes || waybillNotes,
        });
      }
      // Create new lead
      const newLead = plainToInstance(Lead, {
        orderId: mOrder.id,
        createdAt: mOrder.createdAt,
        leadType: LeadType.after_sale,
        projectId,
        countryId: countryId,
      });
      await queryRunner.manager.upsert(Lead, newLead, ['orderId']);
      await queryRunner.commitTransaction();
      if (mOrder) {
        // Scan for possible duplicate orders
        await this.amqpConnection.publish('order-service', 'scan-possible-duplicate-orders', {
          id: mOrder.id,
        });

        // Scan for possible duplicate leads
        await this.amqpConnection.publish(
          'order-service',
          'scan-possible-duplicate-leads-after-sale',
          {
            orderId: mOrder.id,
          },
        );
      }
      return order;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async manualGatherLeads(
    filter: GatherLeadsAfterSaleFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    filter.state = !isEmpty(filter.state)
      ? intersection(filter.state, GATHERABLE_STATES_AFTER_SALES)
      : GATHERABLE_STATES_AFTER_SALES;
    const leadsQb = await this.getLeadsAfterSaleAssignedProjectQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (isNil(leadsQb)) return [];
    leadsQb.select(['lead.id']);
    const leads = await leadsQb.getMany();
    if (isEmpty(leads)) throw new BadRequestException('No lead to gather!');
    const leadIds = leads.map(lead => lead.id);

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const result = await queryRunner.manager
        .createQueryBuilder(Lead, 'l')
        .update()
        .set({
          state: CareStateAfterSales.unassign_attempted,
          userId: null,
          currentCareId: null,
          updatedBy: request.user.id,
        })
        .where({ id: In(leadIds) })
        .returning(['id'])
        .execute();
      console.log(`manual gather result`, result);

      const affectedLeadIds = result.raw.map(r => r.id);

      await queryRunner.manager
        .createQueryBuilder(Order, 'o')
        .update()
        .where(
          `id IN (SELECT order_id FROM leads WHERE leads.id IN (${affectedLeadIds.join(',')}))`,
        )
        .set({
          saleId: null,
          lastUpdatedBy: request.user.id,
        })
        .execute();

      await queryRunner.commitTransaction();
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async createLeadAfterSale(
    body: CreateLeadAfterSaleDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const countryId = headers['country-ids'];
    const companyId = request?.user?.companyId;
    const userId = request?.user?.id;
    const projectId = Number(headers['project-ids']);
    let leadDup;
    if (body.isCreateDuplicate && body.leadIdDuplicate) {
      leadDup = await this.leadsRepo.findOne({ id: body.leadIdDuplicate });
      if (!leadDup) throw new NotFoundException('Lead Duplicate Not Found');
    }

    if (body.collectType === 'convertFromFacebook') {
      body.customerFbScopedUserId = `${StringUtils.generateRandomFacebookId()}_${StringUtils.generateSecureUniqueString()}`;
    }

    const order = await this.ordersService.createOrderForLeadAfterSale(
      { ...body },
      Number(countryId),
      companyId,
      userId,
      projectId,
    );

    const lead = plainToInstance(Lead, {
      orderId: order.id,
      createdAt: order.createdAt,
      leadType: LeadType.after_sale,
      projectId,
      countryId: Number(countryId),
    });
    switch (body.collectType) {
      case 'convertFromFacebook':
      case 'manualKeying':
        lead.formCapturedAt = null;
        break;
      case 'captureForm':
        lead.formCapturedAt = order.createdAt;
        break;
      default:
        break;
    }

    await this.leadsRepo.upsert(lead, ['orderId']);

    // Logic for duplicate lead
    if (body.isCreateDuplicate && body.leadIdDuplicate) {
      const [updatedLead] = await Promise.all([
        // insert logs activitiy TLS-00009
        this.leadsRepo.findOne({
          where: { orderId: lead.orderId },
          select: ['id'], // Select only the `id` if needed
        }),
        // this.shiftsService.getUserCurrentShiftByUserId({ userId: leadDup.userId }),
      ]);

      const orderDup = await this.leadsRepo
        .createQueryBuilder('leads')
        .where('leads.id = :id', { id: body.leadIdDuplicate })
        .leftJoin('orders', 'orders', 'orders.id = leads.orderId')
        .select('orders.displayId', 'displayId')
        .getRawOne();

      const logs = [
        plainToInstance(SystemLog, {
          tableName: 'leads',
          action: 'DUPLICATE',
          parentId: body.leadIdDuplicate,
          recordId: updatedLead.id,
          changes: ['lead_id', body.leadIdDuplicate, orderDup?.displayId],
          creatorId: userId,
        }),
        plainToInstance(SystemLog, {
          tableName: 'orders',
          action: 'UPDATE',
          recordId: updatedLead.id,
          changes: ['sale_id', leadDup.userId],
          beforeChanges: ['sale_id', null],
        }),
      ];
      await this.logsRepo.save(logs);
    }
    return lead;
  }

  async takeCareLeads(
    userId: number,
    companyId: number,
    countryId: string | number,
    projectIds: (string | number)[],
    request: Record<string, any>,
  ) {
    if (!countryId) throw new BadRequestException('country-ids is required');

    const timezone = 'Asia/Ho_Chi_Minh';
    const current = moment();

    // get projectIds of user
    const scopes = flatMap(request?.user?.profiles, p => p[4]);
    const projectIdsActive = scopes.map(s => String(s[1])); // get list Ids of project ['1', '2', '3', ...]
    projectIds = projectIdsActive.filter(item => projectIds.includes(item));

    const { data: user } = await this.amqpConnection.request<{ data: User }>({
      exchange: 'identity-service-roles',
      routingKey: 'get-user',
      payload: { id: userId },
      timeout: 5000,
    });
    if (!user.isOnline) throw new BadRequestException('Bạn đang offline!');

    const [dConfig, rConfig] = await Promise.all([
      this.leadAfterSaleDistributeConfigsService.getConfigByCompanyId(
        companyId,
        Number(countryId),
        LeadASConfigType.distribute,
      ),
      this.leadAfterSaleDistributeConfigsService.getConfigByCompanyId(
        companyId,
        Number(countryId),
        LeadASConfigType.revoke,
      ),
    ]);
    if (!dConfig || !rConfig)
      throw new BadRequestException('Thị trường chưa thiết lập quy định phân phối data');

    // const leadsCount = await this.countLeads(
    //   { state: LEAD_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED, userIds: [userId] },
    //   ['state'],
    //   headers,
    //   request,
    // );
    const qb = await this.getLeadsAfterSaleAssignedProjectQueryBuilder(
      {
        state: LEAD_AFTER_SALES_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED,
        userIds: [userId],
        projectIds,
      },
      undefined,
      [countryId],
      companyId,
    );

    const leadsCount = isNil(qb)
      ? []
      : await qb
          .select('COUNT(DISTINCT lead.id)', 'count')
          .addGroupBy(`lead.state`)
          .addSelect(`lead.state`, 'state')
          .getRawMany();
    const { assignedCount, noAttemptsCount } = leadsCount.reduce(
      (prev, it) => {
        if (it.state === CareStateAfterSales.no_attempt) prev.noAttemptsCount += Number(it.count);
        prev.assignedCount += Number(it.count);
        return prev;
      },
      { assignedCount: 0, noAttemptsCount: 0 },
    );

    // Get maximum amount of data a user can have
    const maxAssignedLeads =
      rConfig.rules
        .find(rule => rule.type === LeadASDistributeRuleType.assigned_leads)
        ?.value.find(v => v.condition === LeadASDistributeCondition.maxLeads)?.value || Infinity;

    const assignedAvailable = Math.max(maxAssignedLeads - assignedCount, 0);
    if (!assignedAvailable)
      throw new BadRequestException(
        'You have reached the maximum amount of lead assignments a user can have',
      );

    const maxProcessingData =
      rConfig.rules
        .find(r => r.type === LeadASDistributeRuleType.processing_leads)
        ?.value.find(v => v.condition === LeadASDistributeCondition.maxLeads).value || Infinity;

    const processingAvailable = Math.max(maxProcessingData - noAttemptsCount, 0);

    const maxLimitData = Math.min(assignedAvailable, processingAvailable);

    const avgProceed = dConfig.rules.find(r => r.type === LeadASDistributeRuleType.average_proceed)
      ?.numberOfTimes;
    const minLimitData = dConfig.rules.find(r => r.type === LeadASDistributeRuleType.min_limit_data)
      ?.numberOfTimes;
    const coefficientNew = dConfig.rules.find(
      r => r.type === LeadASDistributeRuleType.coefficient_new,
    )?.numberOfTimes;
    if (!avgProceed || !minLimitData || !coefficientNew)
      throw new BadRequestException(`Cấu hình phân phối data không hợp lệ. Vui lòng kiểm tra lại.`);

    // Thời gian còn lại ca làm việc (hr)
    let tShift = 1;

    const shifts = await this.shiftsService.getUserCurrentShiftByUserId({ userId });
    const shift = shifts?.[0];
    if (shift?.endAt) {
      const [hour, minute, second] = String(shift.endAt).split(':');

      tShift = current
        .clone()
        .tz(timezone)
        .set({
          hour: Number(hour),
          minute: Number(minute),
          second: Number(second),
        })
        .diff(current, 'hours', true);
    }

    // Số lần get care trong ngày
    const reqCount = await this.leadsRepo.query(`SELECT daily_care_sequences($1) as no`, [userId]);
    const noReq = reqCount?.[0]?.no;

    // Số data có thể xử lý
    const processable = (tShift * 60) / avgProceed;
    const percentNew = noReq * coefficientNew;
    const totalEligible = Math.floor(Math.min(Math.max(processable, minLimitData), maxLimitData));
    const newEligible = Math.ceil(totalEligible * Math.min(percentNew, 1));
    const revokedEligible = Math.max(totalEligible - newEligible, 0);

    const leadsFilter = plainToInstance(LeadsASFilterCommon, { projectIds });

    // eslint-disable-next-line prefer-const
    let [newLeads, revokedLeads] = await Promise.all([
      newEligible
        ? this.handleGetCares(
            Number(countryId),
            companyId,
            userId,
            newEligible,
            CareStateAfterSales.new,
            shift?.id,
            leadsFilter,
          )
        : 0,
      revokedEligible
        ? this.handleGetCares(
            Number(countryId),
            companyId,
            userId,
            revokedEligible,
            CareStateAfterSales.unassign_attempted,
            shift?.id,
            leadsFilter,
          )
        : 0,
    ]);

    const newMissing = newEligible - newLeads;
    const revokedMissing = revokedEligible - revokedLeads;
    if (newMissing && !revokedMissing) {
      revokedLeads += await this.handleGetCares(
        Number(countryId),
        companyId,
        userId,
        newMissing,
        CareStateAfterSales.unassign_attempted,
        shift?.id,
        leadsFilter,
      );
    }

    return {
      noReq,
      tShift,
      maxLimitData,
      minLimitData,
      processable,
      totalEligible,
      newEligible,
      revokedEligible,
      newLeads,
      revokedLeads,
    };
  }

  async handleGetCares(
    countryId: number,
    companyId: number,
    userId: number,
    limit: number,
    state: CareStateAfterSales = CareStateAfterSales.new,
    shiftId?: number,
    leadsFilter?: ManualDistributeLeadsAfterSaleFilter,
  ) {
    if (![CareStateAfterSales.new, CareStateAfterSales.unassign_attempted].includes(state))
      throw new BadRequestException();
    const qbNewLead = await this.getLeadsToDistributeQueryBuilder(
      {
        ...leadsFilter,
        state: [state],
      },
      [countryId],
      companyId,
    );

    const qbOldLead = await this.getLeadsToDistributeQueryBuilder(
      {
        ...leadsFilter,
        state: [state],
      },
      [countryId],
      companyId,
    );
    const startOfDay = moment()
      .startOf('day')
      .toISOString();
    const endOfDay = moment()
      .endOf('day')
      .toISOString();
    const newLeads = isNil(qbNewLead)
      ? []
      : await qbNewLead
          .select('lead.id', 'id')
          .addSelect('lead.last_care_reason_id', 'last_care_reason_id')
          .addSelect('lead.state', 'state')
          .addSelect('lead.created_at', 'created_at')
          .addSelect(
            `
                  CASE
                    WHEN lead.form_captured_at IS NOT NULL THEN 'B Capture Form'
                    WHEN lead.form_captured_at IS NULL AND order.fb_scoped_user_id IS NOT NULL THEN 'C Facebook Conversion'
                    ELSE 'A Manual Keying'
                  END AS collectType
                `,
          )
          .andWhere('lead.created_at >= :startOfDay AND lead.created_at <= :endOfDay', {
            startOfDay,
            endOfDay,
          })
          .limit(limit)
          .orderBy('collectType', 'ASC')
          // .addOrderBy('lead.last_care_reason_id', 'ASC', 'NULLS FIRST')
          .addOrderBy('lead.created_at', 'DESC')
          .getRawMany();
    let oldLeads = [];
    if (newLeads.length < limit) {
      oldLeads = isNil(qbOldLead)
        ? []
        : await qbOldLead
            .select('lead.id', 'id')
            .addSelect('lead.last_care_reason_id', 'last_care_reason_id')
            .addSelect('lead.state', 'state')
            .addSelect('lead.created_at', 'created_at')
            .addSelect(
              `
                      CASE
                        WHEN lead.form_captured_at IS NOT NULL THEN 'B Capture Form'
                        WHEN lead.form_captured_at IS NULL AND order.fb_scoped_user_id IS NOT NULL THEN 'C Facebook Conversion'
                        ELSE 'A Manual Keying'
                      END AS collectType
                    `,
            )
            .andWhere('lead.created_at <= :startOfDay', {
              startOfDay,
            })
            .limit(limit - newLeads.length)
            .orderBy('collectType', 'ASC')
            // .addOrderBy('lead.last_care_reason_id', 'ASC', 'NULLS FIRST')
            .addOrderBy('lead.created_at', 'DESC')
            .getRawMany();
    }
    const newResults = await Promise.all(
      newLeads.map(lead => this.handleAssign(lead.id, userId, state, shiftId, leadsFilter)),
    );

    const oldResults = await Promise.all(
      oldLeads.map(lead => this.handleAssign(lead.id, userId, state, shiftId, leadsFilter)),
    );
    const newCount = newResults.reduce((prev, result) => {
      if (!result?.affected) return prev;
      return (prev += result.affected);
    }, 0);
    const oldCount = oldResults.reduce((prev, result) => {
      if (!result?.affected) return prev;
      return (prev += result.affected);
    }, 0);

    return (newCount | 0) + (oldCount | 0);
  }

  async handleAssign(
    leadId: number,
    userId: number,
    currentState: CareStateAfterSales,
    shiftId?: number,
    leadsFilter?: ManualDistributeLeadsAfterSaleFilter,
  ): Promise<UpdateResult | undefined> {
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const [lead, care] = await Promise.all([
        queryRunner.manager.findOne(Lead, { id: leadId }),
        queryRunner.manager.save(
          LeadCare,
          plainToInstance(LeadCare, {
            userId,
            leadId,
            shiftId,
            updatedBy: userId,
          }),
        ),
      ]);
      if (!lead) {
        console.log('handleAssign() Error: Không tìm thấy lead');
        return;
      }
      const order = await queryRunner.manager.findOne(Order, { id: lead.orderId });
      let metadata = JSON.parse(order.metadata);
      if (!metadata || (typeof metadata === 'object' && Object.keys.length === 0)) metadata = {};
      metadata = {
        ...metadata,
        repsId: userId,
      };
      const updatedAt = care.createdAt;

      const [result, updateOrder] = await Promise.all([
        queryRunner.manager.update(
          Lead,
          { id: leadId, userId: IsNull(), state: currentState },
          {
            userId,
            currentCareId: care.id,
            lastCareId: care.id,
            state: CareState.no_attempt,
            updatedBy: userId,
            updatedAt,
          },
        ),
        queryRunner.manager.update(
          Order,
          { id: lead.orderId },
          {
            saleId: userId,
            metadata: JSON.stringify(metadata),
            lastUpdatedBy: userId,
          },
        ),
      ]);
      console.log(`handleAssign result`, leadId, result, updateOrder);
      if (!result.affected || !updateOrder.affected) {
        await queryRunner.rollbackTransaction();
        return result;
      }
      await queryRunner.commitTransaction();

      await Promise.all([
        this.onLeadUpdated({
          id: leadId,
          state: CareStateAfterSales.no_attempt,
          userId,
          currentCareId: care.id,
          updatedBy: userId,
          updatedAt,
        }),
        this.amqpConnection.publish(
          'order-service',
          'assign-duplicate-leads-according-to-original-lead-after-sale',
          { leadId, updatedBy: userId, state: CareStateAfterSales.no_attempt, filter: leadsFilter },
        ),
      ]);
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log(`handle lead assignment error`, error?.driverError?.detail || error);
    } finally {
      await queryRunner.release();
    }
    return;
  }

  async getExternalSource(
    pagination?: PaginationOptions,
    filter?: LeadAfterSaleSourceFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;
    const { ids, countryIds, projectIds, userIds, query } = filter;
    const countryId = headers['country-ids'];
    const qb = await this.leadAfterSaleSourceRepo
      .createQueryBuilder('ls')
      .where('ls.company_id = :companyId', { companyId });

    if (countryId) qb.andWhere('ls.countryId IN (:...countryIds)', { countryIds: [countryId] });
    if (projectIds) qb.andWhere('ls.projectId IN (:...projectIds)', { projectIds });
    if (query) qb.andWhere('ls.name ~* :query', { query });
    if (ids) qb.andWhereInIds(ids);
    qb.orderBy('ls.createdAt', 'DESC');
    const [landingPages, count] = await qb.getManyAndCount();
    return [landingPages, count];
  }

  async createSource(
    body: CreateAfterSaleSourceDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;

    const source = plainToInstance(LeadAfterSaleSource, {
      ...body,
      companyId,
      creatorId: request.user.id,
    });
    return this.leadAfterSaleSourceRepo.save(source);
  }

  async getSource(id: string, headers?: Record<string, string>, request?: Record<string, any>) {
    const companyId = request?.user?.companyId;
    const qb = this.leadAfterSaleSourceRepo
      .createQueryBuilder('p')
      .where('p.id = :id')
      .andWhere('p.companyId = :companyId')
      .setParameters({ id, companyId });
    const landingPage = await qb.getOne();
    return landingPage;
  }

  async updateSource(
    id: string,
    body: UpdateAfterSaleSourceDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;
    const oldRecord = await this.getSource(id, headers, request);
    if (!oldRecord) throw new NotFoundException('Not found source');
    if (oldRecord.companyId !== companyId) throw new ForbiddenException();

    const landingPage = plainToInstance(LeadAfterSaleSource, {
      ...oldRecord,
      ...body,
      productId:
        isNil(oldRecord.productId) && body?.productId ? body?.productId : oldRecord.productId,
      updatedBy: request.user.id,
    });

    return this.leadAfterSaleSourceRepo.save(landingPage);
  }

  async deleteSource(id: string, headers?: Record<string, string>, request?: Record<string, any>) {
    const companyId = request?.user?.companyId;
    const landingPage = await this.getSource(id, headers, request);
    if (!landingPage) throw new NotFoundException('Not found landing page');
    if (landingPage.companyId !== companyId) throw new ForbiddenException();

    return this.leadAfterSaleSourceRepo.delete(id);
  }

  async takeAssignedLead(
    leadId: number,
    headers: Record<string, string>,
    request: Record<string, any>,
  ): Promise<Lead> {
    const qb = this.leadsRepo
      .createQueryBuilder('leads')
      .where('leads.id = :leadId', { leadId })
      .leftJoin('leads.order', 'order')
      .addSelect(['order.id', 'order.companyId', 'order.countryId']);
    const lead = await qb.getOne();
    if (!lead) throw new NotFoundException('Không tìm thấy lead');
    if (lead.userId !== request.user.id)
      throw new ForbiddenException('Đơn hàng không ở trạng thái đã được chia cho bạn');
    if (lead.state !== CareStateAfterSales.assigned) return lead;

    const companyId = lead.order.companyId;
    const countryId = lead.order.countryId;
    const config = await this.leadAfterSaleDistributeConfigsService.getConfigByCompanyId(
      companyId,
      countryId,
    );
    if (config) {
      const maxProcessingData =
        config.rules
          .find(r => r.type === LeadASDistributeRuleType.processing_leads)
          ?.value.find(v => v.condition === LeadASDistributeCondition.maxLeads).value || Infinity;
      const processingLeads = await this.countLeadsAfterSale(
        {
          state: [CareStateAfterSales.no_attempt],
          userIds: [request.user.id],
          isAssignedProject: true,
        },
        ['userId'],
        headers,
        request,
      );
      const count = processingLeads.length === 0 ? 0 : processingLeads[0].count;
      if (maxProcessingData - count <= 0)
        throw new BadRequestException({
          code: ErrorCode.TLS_0003,
          message: 'Không thể nhận thêm lead vì đã đạt giới hạn lead Đang Xử Lý (Chưa chăm sóc)',
        });
    }

    const result = await this.leadsRepo.save({
      id: leadId,
      state: CareStateAfterSales.no_attempt,
      updatedBy: request.user.id,
    });
    console.log(`take assigned lead result`, result);
    await this.onLeadUpdated({
      id: leadId,
      state: result.state,
      currentCareId: lead.currentCareId,
      updatedBy: request.user.id,
      updatedAt: result.updatedAt,
    });
    return lead;
  }

  async createCareItem(
    body: CreateLeadCareItemDto,
    request?: Record<string, any>,
    headers?: Record<string, any>,
    force?: boolean,
  ): Promise<LeadCareItem> {
    const redisKey = `updating-ag-sale-lead.${body.leadId}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 60, 'NX')
      .incr(redisKey)
      .exec();
    if (isUpdating > 1) {
      throw new BadRequestException({
        isUpdating,
        message: `This lead is updating by another request. Please try again later.`,
      });
    }

    try {
      const leadItem = plainToInstance(LeadCareItem, {
        leadId: body.leadId,
        leadCareId: body?.leadCareId,
        reasonId: body?.reasonId,
        note: body?.note,
        creatorId: request.user.id,
      });
      if (body.tagIds) leadItem.tags = await this.tagsService.findByIds(body.tagIds);

      const leadCare = await this.leadCaresRepo
        .createQueryBuilder('lc')
        .where('lc.id = :id')
        .select(['lc.id', 'lc.state', 'lc.userId'])
        .leftJoin('lc.lead', 'l')
        .addSelect(['l.id', 'l.currentCareId', 'l.state', 'l.leadType'])
        .leftJoin('l.order', 'o')
        .addSelect(['o.id', 'o.customerName', 'o.customerPhone'])
        .setParameters({ id: body.leadCareId })
        .getOne();

      if (!leadCare) throw new NotFoundException('Lead care not exists!');
      if (leadCare.userId !== request.user.id && !force) throw new ForbiddenException();

      const lead = leadCare.lead;
      if (lead.currentCareId !== leadCare.id) throw new ForbiddenException();

      const order = lead.order;
      if (!order) throw new NotFoundException('Not found order id' + order.id);

      const currentState = lead?.state;

      const reason = await this.careReasonRepo.findOne(
        { id: body?.reasonId },
        { select: ['id', 'reasonKey', 'state'] },
      );
      if (!reason) throw new BadRequestException('Not found reason');
      let nextState;
      if (
        [CareStateAfterSales.failed, CareStateAfterSales.potential].includes(
          currentState as CareStateAfterSales,
        )
      ) {
        const attempedState = [CareStateAfterSales.not_connected, CareStateAfterSales.connected];
        if (attempedState.includes(reason.state as CareStateAfterSales)) {
          nextState = currentState;
        } else {
          nextState = reason.state;
        }
      } else {
        nextState =
          reason.state === CareStateAfterSales.awaiting_stock && !isNil(body.appointmentAt)
            ? CareStateAfterSales.temp
            : reason.state;
      }

      if (!NON_COUNT_REPEAT_TIMES_CARE_STATES.includes(reason.state as CareState)) {
        const countReason = await this.leadCareItemsRepo
          .createQueryBuilder('careItem')
          .leftJoin('careItem.leadCare', 'leadCare')
          .andWhere('leadCare.lead_id = :leadId', { leadId: body?.leadId })
          .andWhere('careItem.reason_id = :reasonId', { reasonId: body?.reasonId })
          .getCount();

        leadItem.timesRepeatReason = countReason + 1;
      }

      if (!NEXT_CARE_STATES_AFTER_SALES[currentState]?.includes(nextState) && !force)
        throw new ForbiddenException('The selected reason is not allowed to update');

      let updateOrderData: UpdateOrderDto = {};
      if (CONFIRM_LEAD_STATES.includes(nextState as CareState)) {
        if (!isEmpty(body.orderData)) {
          for (const key of Object.keys(body.orderData)) {
            updateOrderData[key] = body.orderData[key];
          }
        }
        if (nextState !== CareState.temp) {
          updateOrderData.status = OrderStatus.Confirmed;
        }
        if (nextState === CareState.temp) updateOrderData.status = OrderStatus.New;
      } else if (CANCEL_LEAD_STATES.includes(nextState as CareState)) {
        updateOrderData.status = OrderStatus.Canceled;
        updateOrderData.cancelReasonText = `Order canceled due to lead cancellation.`;
      }
      if (body.tagIds) updateOrderData.additionalTagIds = body.tagIds;
      if (body.customerEDD) {
        const orderCarrierData = new OrderCarrierDto();
        orderCarrierData.customerEDD = body.customerEDD;
        updateOrderData.carrier = orderCarrierData;
      }

      /**
       * Workaround to handle case create FFM order fails
       * Firstly, check validations and save order as [New]
       * Next, update order status to [Confirmed] and sync order to FFM after create lead care item
       */
      if (!isEmpty(updateOrderData)) {
        // If the update status is [Confirm], we need to check validations and save order as [New] first
        if (updateOrderData.status && updateOrderData.status === OrderStatus.Confirmed) {
          await this.ordersService.updateOrder(
            body?.orderId,
            { ...updateOrderData, status: OrderStatus.New },
            headers,
            request,
          );
          // Then delete all update order props except status so we can confirm the order later at the end of the process.
          updateOrderData = { status: OrderStatus.Confirmed };
        } else {
          // Update order normally
          await this.ordersService.updateOrder(body?.orderId, updateOrderData, headers, request);
          updateOrderData = undefined;
        }
      }

      const appointment = new AppointmentSchedule();
      if (body.appointmentAt) appointment.appointmentTime = body.appointmentAt;
      if (body.appointmentContent) appointment.content = body.appointmentContent;

      let result: LeadCareItem;

      const connection = getConnection(orderConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        result = await queryRunner.manager.save(leadItem);
        await queryRunner.manager.update(
          LeadCare,
          { id: leadCare.id },
          {
            state: nextState,
            updatedBy: request.user.id,
            updatedAt: result.createdAt,
          },
        );

        const updateResult = await queryRunner.manager.update(
          Lead,
          {
            id: lead.id,
            state: Not(CareState.confirmed),
          },
          {
            state: nextState,
            updatedBy: request.user.id,
            updatedAt: result.createdAt,
            lastCareItemId: result.id,
            lastCareReasonId: reason.id,
          },
        );

        if (!updateResult.affected) throw new BadRequestException();

        // const newLead = await queryRunner.manager.findOne(Lead, { id: lead.id });

        if (!isEmpty(appointment)) {
          appointment.leadCareId = leadCare.id;
          appointment.customerName = order.customerName;
          appointment.customerPhone = order.customerPhone;
          appointment.updatedBy = request.user.id;
          await queryRunner.manager.save(appointment);
        }

        await queryRunner.commitTransaction();

        lead.state = nextState;
        lead.updatedAt = result.createdAt;
        await this.onLeadUpdated(
          {
            id: lead.id,
            state: lead.state,
            currentCareId: leadCare.id,
            userId: request.user.id,
            updatedBy: request.user.id,
            updatedAt: lead.updatedAt,
          },
          currentState as CareStateAfterSales,
        );

        result.reason = reason;
        leadCare.lead = lead;
        leadCare.state = nextState;
        leadCare.updatedBy = request.user.id;
        result.leadCare = leadCare;
      } catch (error) {
        await queryRunner.rollbackTransaction();
        if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
        throw error;
      } finally {
        await queryRunner.release();
      }

      if (!isEmpty(updateOrderData)) {
        // Confirm order
        const order = await this.ordersService.updateOrder(
          body?.orderId,
          updateOrderData,
          headers,
          request,
        );
        console.log(`order ${order.id} ${order.displayId} update`, updateOrderData);
      }
      return result;
    } catch (error) {
      throw error;
    } finally {
      await this.redis.del(redisKey);
    }
  }

  async getLeadsManualFilter(
    headers?: Record<string, string>,
    request?: Record<string, any>,
    filter?: AfterSalesFilterCollectionFilter,
  ): Promise<FilterCollection[]> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);
    const qb = this.filterRepo
      .createQueryBuilder('f')
      .where('f.countryId = :countryId')
      .andWhere('f.companyId = :companyId');
    if (filter?.type)
      qb.andWhere('f.type IN (:...type)', {
        type: filter?.type,
      });
    qb.setParameters({ companyId, countryId });
    return qb.getMany();
  }

  async createLeadsManualFilter(
    { raw, ...body }: CreateOrdersFilterDto,
    user: AuthUser,
    headers?: Record<string, string>,
  ): Promise<FilterCollection> {
    const { id: userId, companyId } = user;
    const countryId = headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);
    const filter = plainToInstance(FilterCollection, {
      ...body,
      raw,
      companyId,
      countryId,
      userId,
      type: body?.type || FilterCollectionType.FilterAfterSalesLeadToDistributeProject,
    });
    const result = await this.filterRepo.save(filter).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });
    return result;
  }

  async deleteLeadsManualFilter(id: number): Promise<boolean> {
    const filter = await this.filterRepo.findOne({ id });
    if (!filter) throw new NotFoundException('Không tìm thấy bộ lọc yêu cầu');

    const result = await this.filterRepo.softDelete(filter.id);
    return result.affected > 0;
  }
}
