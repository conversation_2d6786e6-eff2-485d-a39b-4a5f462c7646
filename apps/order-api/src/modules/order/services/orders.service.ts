import {
  Amqp<PERSON>onnection,
  default<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  NotFoundException,
  OnModuleInit,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CANCEL_LEAD_STATES } from 'apps/order-api/src/constants/care-states.constant';
import { AG_ORDER_STATUSES_CAN_BE_SYNC_TO_FFM } from 'apps/order-api/src/constants/ffm-order-status-mapping.constant';
import {
  AG_TO_FFM_SYNCHRONIZE_PROPS,
  ALLOWED_STATUSES_TO_UPDATE_WHEN_USING_FFM,
  NEXT_STATUS_BY_USER,
  NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES,
  ORDER_STATUSES_CAN_BE_AUTO_UPDATE_TO_WAIT_TO_PACK,
  PlainOrderStatus,
  UPDATABLE_PROPS,
} from 'apps/order-api/src/constants/order-statuses.constant';
import {
  EXPORT_EXCEL_ORDER_COLUMNS,
  EXPORT_EXCEL_ORDER_COLUMNS_DEFAULT,
  NEW_ORDER_REQUIRED_PROPS,
} from 'apps/order-api/src/constants/order.constant';
import { CreateLandingPageOrderDto } from 'apps/order-api/src/dtos/create-landing-page-order.dto';
import { CreateOrderDto } from 'apps/order-api/src/dtos/create-order.dto';
import { FbSendMessagesResponse } from 'apps/order-api/src/dtos/facebook.dto';
import { OrderExportAdvanceDto } from 'apps/order-api/src/dtos/order-export-advanced.dto';
import { OrderProductDto } from 'apps/order-api/src/dtos/order-product.dto';
import { UpdateMultipleOrdersDto } from 'apps/order-api/src/dtos/update-multiple-orders.dto';
import { UpdateOrderDto } from 'apps/order-api/src/dtos/update-order.dto';
import { Customer } from 'apps/order-api/src/entities/customer.entity';
import { District } from 'apps/order-api/src/entities/district.entity';
import { Lead } from 'apps/order-api/src/entities/lead.entity';
import { OrderCarrier } from 'apps/order-api/src/entities/order-carrier.entity';
import { OrderHistory } from 'apps/order-api/src/entities/order-history.entity';
import { OrderProduct } from 'apps/order-api/src/entities/order-product.entity';
import { OrderSource } from 'apps/order-api/src/entities/order-source.entity';
import { Order } from 'apps/order-api/src/entities/order.entity';
import { PossibleDuplicateOrder } from 'apps/order-api/src/entities/possible-duplicate-order.entity';
import { ProjectOrdersCount } from 'apps/order-api/src/entities/project-orders-count.entity';
import { Province } from 'apps/order-api/src/entities/province.entity';
import { Source } from 'apps/order-api/src/entities/source.entity';
import { Ward } from 'apps/order-api/src/entities/ward.entity';
import { CancelReasonStatus } from 'apps/order-api/src/enums/cancel-reason.enum';
import { TeamInCharge } from 'apps/order-api/src/enums/team-in-charge.enum';
import { SourceEntity } from 'apps/order-api/src/enums/source-entity.enum';
import { TagStatus, TagType } from 'apps/order-api/src/enums/tag.enum';
import {
  CountOrdersFilter,
  OrdersFilter,
  OrdersQuery,
  TagMethodType,
  TagOperatorType,
} from 'apps/order-api/src/filters/orders.filter';
import { ProductVariant } from 'apps/order-api/src/read-entities/catalog/product-variant.entity';
import { ConversationReferral } from 'apps/order-api/src/read-entities/facebook-bot/conversation-referral.entity';
import { FanPage } from 'apps/order-api/src/read-entities/facebook-bot/fanpage.entity';
import { FfmOrder } from 'apps/order-api/src/read-entities/ffm-order/ffm-order.entity';
import { Market } from 'apps/order-api/src/read-entities/identity/market.entity';
import { Project } from 'apps/order-api/src/read-entities/identity/project.entity';
import { User } from 'apps/order-api/src/read-entities/identity/user.entity';
import axios from 'axios';
import { Queue } from 'bull';
import { instanceToPlain, plainToClass, plainToInstance } from 'class-transformer';
import { ICountry } from 'core/cache/interfaces/country.interface';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { OrderStatus } from 'core/enums/order-status.enum';
import { IRmqMessage } from 'core/interfaces';
import { RawResponse } from 'core/raw/raw-response';
import ExcelUtils from 'core/utils/ExcelUtils';
import StringUtils from 'core/utils/StringUtils';
import { Redis } from 'ioredis';
import {
  chunk,
  difference,
  findIndex,
  identity,
  intersection,
  isArray,
  isEmpty,
  isEqual,
  isNil,
  isNumber,
  isUndefined,
  map,
  omit,
  pick,
  pickBy,
  reduce,
  remove,
  sumBy,
  take,
  uniq,
  update,
} from 'lodash';
import * as moment from 'moment-timezone';
import xlsx from 'node-xlsx';
import {
  Between,
  Brackets,
  FindCondition,
  FindConditions,
  getConnection,
  In,
  IsNull,
  Not,
  Repository,
  SelectQueryBuilder,
  TreeRepository,
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { rmqErrorsHandler } from '../../../../../../core/handlers/rmq-errors.handler';
import { CancelReasonsService } from './cancel-reasons.service';
import { FfmOrdersService } from './ffm-orders.service';
import { LandingPagesService } from './landing-pages.service';
import { LocationsService } from './locations.service';
import { OrderSyncService } from './order-sync.service';
import { PrintNotesService } from './print-notes.service';
import { TagsService } from './tags.service';
import { PUB_SUB } from 'core/pubsub/pub-sub.module';
import { PubSub } from 'graphql-subscriptions';
import { OrderStatusHistory } from 'apps/order-api/src/entities/order-status-history.entity';
import { ErrorCode } from 'apps/order-api/src/enums/error-code.enum';
import { SystemLog } from '../../../entities/system-log.entity';
import { UncensorLog, UncensorType } from 'apps/order-api/src/entities/uncensored-log.entity';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import { UncensorLogsFilter } from 'apps/order-api/src/filters/uncensor-logs.filter';
import { SalePermission } from 'core/enums/sale-permissions';
import { OrderPermission } from 'core/enums/sale-permissions/order-permission.enum';
import { RemovedDuplicateOrders } from 'apps/order-api/src/entities/removed-duplicate-orders.entity';
import { LeadType } from 'apps/order-api/src/enums/lead-type.enum';
import { CreateLeadAfterSaleDto } from 'apps/order-api/src/dtos/lead.dto';
import { ClickhouseService } from '../../clickhouse/clickhouse.service';
import Utils from 'core/utils/Utils';
import { LandingPage } from 'apps/order-api/src/entities/landing-page.entity';
import { OrderType } from 'apps/order-api/src/enums/order-type.enum';

@Injectable()
export class OrdersService implements OnModuleInit {
  constructor(
    @InjectRepository(Order, orderConnection)
    private orderRepository: Repository<Order>,
    @InjectRepository(OrderProduct, orderConnection)
    private orderProductsRepo: Repository<OrderProduct>,
    @InjectRepository(PossibleDuplicateOrder, orderConnection)
    private possibleDupOrdersRepo: Repository<PossibleDuplicateOrder>,
    @InjectRepository(Customer, orderConnection)
    private customerRepo: Repository<Customer>,
    @InjectRepository(OrderHistory, orderConnection)
    private orderHistoryRepo: Repository<OrderHistory>,
    @InjectRepository(Source, orderConnection)
    private sourceRepository: Repository<Source>,
    @InjectRepository(ProjectOrdersCount, orderConnection)
    private prjOrdersCountRepo: Repository<ProjectOrdersCount>,
    @InjectRepository(OrderSource, orderConnection)
    private externalSourceRepo: TreeRepository<OrderSource>,
    @InjectRepository(Lead, orderConnection)
    private leadsRepo: Repository<Lead>,
    @InjectRepository(SystemLog, orderConnection)
    private systemLogRepo: Repository<SystemLog>,
    private locationService: LocationsService,
    private tagService: TagsService,
    private reasonsService: CancelReasonsService,
    private printNotesService: PrintNotesService,
    private amqpConnection: AmqpConnection,
    private orderSyncService: OrderSyncService,
    private ffmOrdersService: FfmOrdersService,
    private landingPagesService: LandingPagesService,
    @InjectRedis() private redis: Redis,
    @InjectQueue('order')
    private orderQueue: Queue,
    @Inject(PUB_SUB)
    private pubSub: PubSub,
    @InjectRepository(OrderStatusHistory, orderConnection)
    private oshRepository: Repository<OrderStatusHistory>,
    private clickhouseService: ClickhouseService,
  ) {}

  onModuleInit() {
    this.scheduleSendFacebookPurchaseEventDaily();
    this.scheduleScanPackingTimeJob();
    this.scheduleResetOrdersCounterJob();
    this.scheduleSyncOrderStatusJob();
  }

  async scheduleSyncOrderStatusJob() {
    const jobName = 'backup-manual-sync-order-status';
    try {
      const repeatable = await this.orderQueue.getRepeatableJobs();
      for (const job1 of repeatable) {
        if (job1.id !== jobName) {
          continue;
        }
        await this.orderQueue.removeRepeatableByKey(job1.key);
      }
      const queue = await this.orderQueue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            cron: '*/6 * * * *',
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: true,
        },
      );
      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm:ss'),
      );
    } catch (e) {
      console.log(e);
    }
  }

  async syncOrderStatus() {
    console.log('syncOrderStatus() STARTED');
    try {
      const result = await this.clickhouseService.query(`
        SELECT * FROM ${process.env.CLICKHOUSE_TABLE_SYNC}
      `);
      if (result.length === 0) {
        console.log('syncOrderStatus(): No orders to sync');
      } else {
        const listSOCode = result.map(item => item?.so_code);
        const chunks = Utils.chunkArray(listSOCode, 200);
        await Promise.all(
          chunks.map(chunk =>
            this.amqpConnection.publish('ffm-order', 'backup-manual-ffm-to-agsale', {
              listSOCode: chunk,
            }),
          ),
        );

        const listNullSOCode = result.filter(item => !item?.so_code);
        console.log('syncOrderStatus(): listNullSOCode', listNullSOCode);

        if (listNullSOCode.length > 0) {
          const listDisplayId = listNullSOCode.map(item => item?.display_id);
          const lookUpSOCodeByOrderId = listNullSOCode.reduce((acc, item) => {
            acc[item?.display_id] = item?.ffm_display_id;
            return acc;
          }, {});
          const orders = await this.orderRepository.find({
            where: {
              displayId: In(listDisplayId),
            },
          });

          const { validOrders, invalidOrders, markets } = await this.validateOrdersWithMarkets(
            orders,
          );

          if (invalidOrders.length > 0) {
            console.log('syncOrderStatus(): Invalid orders:', invalidOrders);
          }
          if (validOrders.length > 0) {
            const queryRunner = getConnection(orderConnection).createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
              const updateResults = await Promise.allSettled(
                validOrders.map(async order => {
                  try {
                    await queryRunner.manager.update(
                      Order,
                      { id: order.id },
                      {
                        ffmCompanyId:
                          markets[`${order.companyId}_${order.projectId}_${order.countryId}`]
                            ?.fulfillmentPartnerId,
                        ffmPartnerClientId:
                          markets[`${order.companyId}_${order.projectId}_${order.countryId}`]
                            ?.fulfillmentPartnerClientId,
                        ffmDisplayId: lookUpSOCodeByOrderId[order.displayId],
                        updatedAt: new Date(),
                      },
                    );
                    return {
                      success: true,
                      orderId: order.id,
                      SOCode: lookUpSOCodeByOrderId[order.displayId],
                    };
                  } catch (error) {
                    return {
                      success: false,
                      orderId: order.id,
                      error: error.message,
                    };
                  }
                }),
              );
              console.log('syncOrderStatus(): updateResults', updateResults);
              // Log results
              const successfulUpdates = updateResults.filter(
                (
                  result,
                ): result is PromiseFulfilledResult<{
                  success: boolean;
                  orderId: number;
                  SOCode: string;
                }> => result.status === 'fulfilled' && result?.value?.success,
              );
              const listSOCode = successfulUpdates.map(item => item?.value?.SOCode);

              // Commit successful updates
              await queryRunner.commitTransaction();
              console.log('syncOrderStatus() with null SOcode, listSOCode: ', listSOCode);
              await this.amqpConnection.publish('ffm-order', 'backup-manual-ffm-to-agsale', {
                listSOCode,
              });
            } catch (err) {
              await queryRunner.rollbackTransaction();
              console.error('Transaction failed:', err);
            } finally {
              await queryRunner.release();
            }
          }
        }
      }
      console.log('syncOrderStatus() FINISHED');
    } catch (error) {
      console.error('syncOrderStatus() ERROR:', error);
    }
  }

  async getMarketsForOrders(orders: Order[]): Promise<{ [key: string]: Market }> {
    // Create unique combinations to avoid duplicate requests
    const uniqueMarketKeys = new Set(
      orders.map(order => `${order.companyId}_${order.projectId}_${order.countryId}`),
    );

    // Create array of promises for unique market requests
    const marketPromises = Array.from(uniqueMarketKeys).map(async key => {
      const [companyId, projectId, countryId] = key.split('_').map(Number);

      const { data: market } = await this.amqpConnection.request<{ data: Market }>({
        exchange: 'identity-service-projects',
        routingKey: 'find-market',
        payload: {
          companyId,
          projectId,
          countryId,
        } as FindCondition<Market>,
        timeout: 10000,
      });

      return { key, market };
    });

    // Execute all promises in parallel
    const results = await Promise.all(marketPromises);

    // Convert array to object with keys
    return results.reduce((acc, { key, market }) => {
      acc[key] = market;
      return acc;
    }, {} as { [key: string]: Market });
  }

  async validateOrdersWithMarkets(
    orders: Order[],
  ): Promise<{
    validOrders: Order[];
    invalidOrders: Order[];
    markets: { [key: string]: Market };
  }> {
    const marketsByKey = await this.getMarketsForOrders(orders);

    const validOrders: Order[] = [];
    const invalidOrders: Order[] = [];

    orders.forEach(order => {
      const marketKey = `${order.companyId}_${order.projectId}_${order.countryId}`;
      const market = marketsByKey[marketKey];
      order.market = market;

      if (
        order.status === OrderStatus.AwaitingStock &&
        (!market?.fulfillmentPartnerClientId || !market?.fulfillmentPartnerId)
      ) {
        invalidOrders.push(order);
      } else {
        validOrders.push(order);
      }
    });

    return {
      validOrders,
      invalidOrders,
      markets: marketsByKey,
    };
  }

  async findById(
    id: number,
    relations?: (keyof Order)[],
    select?: (keyof Order)[],
  ): Promise<Order> {
    return this.orderRepository.findOne(id, { select, relations });
  }

  async getOrdersByIds(orderIds: number[], request?: Record<string, any>): Promise<Order[]> {
    const companyId = request?.user?.companyId;
    return this.orderRepository.findByIds(orderIds, { where: { companyId } });
  }

  async getDisplayId(projectId: number, companyId: number) {
    const raw = await this.leadsRepo.query(
      `SELECT *
       FROM daily_order_display_id_sequences($1) AS seq(project_id INT, date DATE, count INT)`,
      [projectId],
    );
    const date = raw?.[0].date;
    const odCount = raw?.[0].count;
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-projects',
        routingKey: 'get-project-by-id',
        payload: { id: projectId },
        timeout: 5000,
      });
      const project = data as Project;
      if (!project) throw new BadRequestException();

      const shortName = project.shortName;
      const mDate = moment(date)
        .tz('Asia/Ho_Chi_Minh')
        .format('YYYYMMDD');
      return {
        displayId: `${shortName}-${companyId}${mDate}${odCount}`,
        count: odCount,
      };
    } catch (error) {
      console.log(`get display id error`, error);
      throw new BadRequestException(error.detail);
    }
  }

  async getOrderAfterSaleDisplayId(projectId: number, companyId: number) {
    const raw = await this.leadsRepo.query(
      `SELECT *
       FROM daily_order_display_id_sequences($1) AS seq(project_id INT, date DATE, count INT)`,
      [projectId],
    );
    const date = raw?.[0].date;
    const odCount = raw?.[0].count;
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-projects',
        routingKey: 'get-project-by-id',
        payload: { id: projectId },
        timeout: 5000,
      });
      const project = data as Project;
      if (!project) throw new BadRequestException();
      const mDate = moment(date)
        .tz('Asia/Ho_Chi_Minh')
        .format('YYYYMMDD');
      return {
        displayId: `ASL-${companyId}${project?.id}${mDate}${odCount}`,
        count: odCount,
      };
    } catch (error) {
      console.log(`get display id error`, error);
      throw new BadRequestException(error.detail);
    }
  }

  async getOrders(
    pagination?: PaginationOptions,
    filters?: OrdersFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<[Order[], number]> {
    const qb = await this.getOrderQueryBuilder(filters, pagination, headers, request);
    if (isNil(qb)) return [[], 0];
    const { sort, hasPossibleDuplicates } = filters;
    const countQb = qb.clone();
    let orderBy = '';
    if (!filters.orderBy) orderBy = 'order.id';
    else if (filters.orderBy === 'createAt') orderBy = 'order.createdAt';
    else if (filters.orderBy === 'createdAt') orderBy = 'order.createdAt';
    else if (filters.orderBy === 'lastUpdateStatus') orderBy = 'order.lastUpdateStatus';
    else if (filters.orderBy === 'updatedAt') orderBy = 'order.updatedAt';
    else if (filters.orderBy === 'expect_delivery_at') orderBy = 'carrier.customerEDD';
    else if (filters.orderBy === 'confirmation_time') orderBy = 'order.confirmedAt';
    qb.addOrderBy(orderBy, sort || 'DESC', 'NULLS LAST');
    if (hasPossibleDuplicates) qb.addOrderBy('order.customerPhone', 'DESC');
    const esQb = qb.clone();
    const oshQb = qb.clone().select('order.id', 'id');
    if (filters.orderBy === 'expect_delivery_at') {
      esQb.leftJoinAndSelect('order.carrier', 'carrier');
      oshQb.leftJoinAndSelect('order.carrier', 'carrier');
    }

    const inTransitLogsQb = getConnection(orderConnection)
      .createQueryBuilder(OrderStatusHistory, 'osh')
      .addCommonTableExpression(() => oshQb, 'orders_cte')
      .innerJoin('orders_cte', 'orders_cte', 'orders_cte.id = osh.order_id')
      .where(`osh.status = :oshStatus`, { oshStatus: OrderStatus.InTransit })
      .select('osh.order_id')
      .addSelect('MIN(osh.created_at)', 'in_transit_at')
      .groupBy('osh.order_id');
    // .getRawMany();

    qb.leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`)
      .addSelect(['tags.id', 'tags.name'])
      .leftJoinAndSelect('order.products', 'products')
      .leftJoinAndSelect('order.carrier', 'carrier')
      .leftJoin('order.externalSource', 'es')
      .addSelect(['es.id', 'es.entity', 'es.entityId'])
      .leftJoin('order.possibleDuplicateOrders', 'possibleDuplicateOrders')
      .unScope('possibleDuplicateOrders')
      .addSelect([
        'possibleDuplicateOrders.id',
        'possibleDuplicateOrders.possibleDuplicateOrderDisplayId',
        'possibleDuplicateOrders.possibleDuplicateOrderId',
      ])
      .leftJoin(
        'conversations',
        'con',
        `con.page_id = order.page_id AND con.scoped_user_id = order.fb_scoped_user_id AND con.feed_id = ''`,
      )
      .addSelect('con.created_at', 'conversation_created_at')
      .leftJoin('order.cancelReasons', 'cancelReasons', 'cancelReasons.status = :cStatus', {
        cStatus: CancelReasonStatus.active,
      })
      .addSelect(['cancelReasons.id', 'cancelReasons.name'])
      .leftJoin('order.printNotes', 'printNotes')
      .addSelect(['printNotes.id', 'printNotes.name']);

    const extSourcesQb = this.externalSourceRepo
      .createQueryBuilder('es')
      .innerJoin(
        `(${esQb
          .select(`order.external_source_id AS "od_ext_src_id"`)
          .addOrderBy(orderBy, sort || 'DESC', 'NULLS LAST')
          .getQuery()})`,
        'e2',
        'es.id = e2.od_ext_src_id',
      )
      .leftJoin('order_sources_closure', 'osc', 'osc.id_descendant = es.id')
      .leftJoinAndMapMany(
        'es.parents',
        OrderSource,
        'oes',
        'osc.id_ancestor = oes.id AND oes.entity IN (:...entities)',
      )
      .setParameters({
        ...esQb.getParameters(),
        entities: [SourceEntity.fb_page, SourceEntity.landing_page],
      });

    const [extSources, orders, count, inTransitLogs] = await Promise.all([
      extSourcesQb.getMany(),
      qb.getMany(),
      countQb.getCount(),
      inTransitLogsQb.getRawMany(),
    ]);
    const extSourcesLookup = extSources.reduce((prev, es) => {
      prev[String(es.id)] = es.parents;
      return prev;
    }, {});

    const inTransitsLookup = reduce(
      inTransitLogs,
      (prev, item) => {
        prev[item.order_id] = item.in_transit_at;
        return prev;
      },
      {},
    );

    const dupOrderIdsTotal = [];

    for (const order of orders) {
      if (order.externalSourceId) {
        order.externalSources = extSourcesLookup[order.externalSourceId];
      }
      if (inTransitsLookup[order.id]) {
        order.inTransitAt = inTransitsLookup[order.id];
      }
      const dupOrderIds = order.possibleDuplicateOrders.map(it => it.possibleDuplicateOrderId);
      if (dupOrderIds.length > 0) {
        dupOrderIdsTotal.push(...dupOrderIds);
      }
    }
    if (dupOrderIdsTotal.length > 0) {
      const dupOrders = await this.orderRepository
        .createQueryBuilder('o')
        .where('o.id IN (:...dupOrderIds)', { dupOrderIds: dupOrderIdsTotal })
        .select(['o.id', 'o.status'])
        .getMany();
      const dupOrdersMap = dupOrders.reduce((prev, next) => {
        prev[next.id] = next['status'];
        return prev;
      }, {});
      for (const order of orders) {
        order.possibleDuplicateOrders = order.possibleDuplicateOrders.map(it => {
          it.possibleDuplicateOrderStatus = OrderStatus[dupOrdersMap[it.possibleDuplicateOrderId]];
          return it;
        });
      }
    }
    return [orders, count];
  }

  async countOrder(
    filter: CountOrdersFilter,
    groupBy: string[] = [],
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    let qb;
    if (filter.countAllOrderOfCustomer) {
      qb = await this.getOrderQueryBuilder(filter, undefined, headers, request, true);
    } else {
      qb = await this.getOrderQueryBuilder(filter, undefined, headers, request, false);
    }
    if (isNil(qb)) return [];

    qb.select('COUNT(DISTINCT order.id)', 'count');
    const { getRevenue } = filter;
    if (getRevenue) {
      qb.addSelect(
        `SUM (COALESCE(order.total_price, 0) + COALESCE(order.surcharge, 0) + COALESCE(order.shipping_fee, 0) - COALESCE(order.discount, 0))`,
        'revenue',
      );
    }
    for (const group of groupBy) {
      qb.addGroupBy(`order.${group}`);
      qb.addSelect(`order.${group}`, group);
    }
    const data = await qb.getRawMany();

    for (const item of data) {
      if (!isNil(item.status)) {
        item.status = OrderStatus[item.status];
      }
      item.count = Number(item.count);
    }
    return data;
  }

  async getOrderQueryBuilder(
    filter: OrdersFilter,
    pagination?: PaginationOptions,
    headers?: Record<string, string>,
    request?: Record<string, any>,
    isSkipProjectsInHeaders: boolean = false,
  ): Promise<SelectQueryBuilder<Order> | null> {
    const companyId = request?.user?.companyId;
    const queryBuilder = this.orderRepository.createQueryBuilder('order');
    if (pagination) {
      queryBuilder.take(pagination.limit).skip(pagination.skip);
    }
    const {
      dateRangeType,
      from,
      to,
      confirmedFrom,
      confirmedTo,
      status,
      excludesStatus,
      ids,
      displayIds,
      saleIds,
      carePageIds,
      userInChargeIds,
      marketerIds,
      sourceType,
      sourceIds,
      tagIds,
      tagMethod,
      tagOperator,
      productIds,
      isCheckedAfterReturn,
      teamsInCharge,
      fbId,
      pageIds,
      exceptDraft,
      scopedUserId,
      hasPossibleDuplicates,
      leadState,
      excludesLeadState,
      customerPhone,
      passedStatuses,
      selectedTabStatus,
    } = filter;

    if (dateRangeType && (from || to)) {
      switch (dateRangeType) {
        case 'creationTime': {
          if (from) {
            queryBuilder.andWhere(`order.created_at >= :from`, { from });
          }
          if (to) {
            queryBuilder.andWhere(`order.created_at < :to`, { to });
          }
          break;
        }
        case 'lastUpdateStatusTime': {
          if (!status) {
            if (from) {
              queryBuilder.andWhere(`order.last_update_status >= :from`, { from });
            }
            if (to) {
              queryBuilder.andWhere(`order.last_update_status < :to`, { to });
            }
          } else {
            const subQ = this.oshRepository
              .createQueryBuilder('osh')
              .select('osh.orderId', 'id')
              .where('osh.status IN (:...status)', { status })
              .groupBy('osh.orderId');
            if (from) {
              subQ.andWhere('osh.createdAt >= :from', { from });
            }
            if (to) {
              subQ.andWhere('osh.createdAt < :to', { to });
            }
            queryBuilder.innerJoin(
              `(${subQ.getQuery()})`,
              'osh_h',
              'osh_h.id = order.id',
              subQ.getParameters(),
            );
          }
          break;
        }
        case 'lastUpdateTime': {
          if (from) {
            queryBuilder.andWhere(`order.updated_at >= :from`, { from });
          }
          if (to) {
            queryBuilder.andWhere(`order.updated_at < :to`, { to });
          }
          break;
        }
        default:
          break;
      }
    }

    if (!isEmpty(fbId) || !isEmpty(scopedUserId)) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          if (fbId) qb.orWhere('order.fb_global_id = :fbId', { fbId });
          if (scopedUserId) {
            const [, psid] = scopedUserId.split('_');
            qb.orWhere('order.fb_scoped_user_id = :scopedUserId', {
              scopedUserId: psid || scopedUserId,
            });
          }
        }),
      );
    }
    if (confirmedFrom) {
      queryBuilder.andWhere('order.confirmed_at >= :confirmedFrom', {
        confirmedFrom,
      });
    }
    if (confirmedTo) {
      queryBuilder.andWhere('order.confirmed_at < :confirmedTo', { confirmedTo });
    }
    if (dateRangeType !== 'lastUpdateStatusTime') {
      if (!isNil(selectedTabStatus)) {
        if (status && intersection(status, [selectedTabStatus]).length === 0) {
          queryBuilder.andWhere('order.status = -1');
        } else {
          const statusIntersection = intersection(status || [selectedTabStatus], [
            selectedTabStatus,
          ]);
          queryBuilder.andWhere('order.status IN (:...statusIntersection)', {
            statusIntersection,
          });
          if (statusIntersection.includes(OrderStatus.Canceled)) {
            queryBuilder.leftJoin(
              'order_status_histories',
              'oh',
              `oh.order_id = order.id AND oh.status = ${OrderStatus.Canceled} AND oh.before_status = ${OrderStatus.Draft}`,
            );
            queryBuilder.andWhere(
              '(order.cancel_reason_text is NULL AND (order.page_id IS NULL OR (order.page_id IS NOT NULL AND oh.id IS NULL)))',
            );
          } else if (statusIntersection.includes(OrderStatus.Draft)) {
            queryBuilder.leftJoin('leads', 'lo', 'lo.order_id = order.id');
            if (!isEmpty(fbId) || !isEmpty(scopedUserId)) {
              queryBuilder.andWhere('(lo.id is NULL)');
            } else {
              queryBuilder.andWhere(
                '((lo.id is NULL AND order.page_id IS NULL) OR (lo.id is NULL AND order.page_id IS NOT NULL AND order.creator_id is NOT NULL))',
              );
            }
          }
        }
      } else {
        const statusNeedAdvancedQuery = [OrderStatus.Draft, OrderStatus.Canceled];
        if (intersection(status, statusNeedAdvancedQuery).length === 0) {
          if (status) {
            queryBuilder.andWhere('order.status IN (:...status)', {
              status,
            });
          } else {
            queryBuilder.leftJoin('leads', 'lo', 'lo.order_id = order.id');
            queryBuilder.leftJoin(
              'order_status_histories',
              'oh',
              `oh.order_id = order.id AND oh.status = ${OrderStatus.Canceled} AND oh.before_status = ${OrderStatus.Draft}`,
            );
            if (!isEmpty(fbId) || !isEmpty(scopedUserId)) {
              queryBuilder.andWhere(
                '(order.status IN (:...fullStatus) OR (order.cancel_reason_text is NULL AND order.status = :cancelStatus) OR (order.status = :draftStatus AND (lo.id IS NULL OR order.cross_care=true)))',
              );
            } else {
              queryBuilder.andWhere(
                '(order.status IN (:...fullStatus) OR (order.cancel_reason_text is NULL AND order.status = :cancelStatus AND (order.page_id IS NULL OR (order.page_id IS NOT NULL AND oh.id IS NULL))) OR (order.status = :draftStatus AND ((lo.id is NULL AND order.page_id IS NULL) OR (lo.id is NULL AND order.page_id IS NOT NULL AND order.creator_id is NOT NULL))))',
              );
            }
            queryBuilder.setParameters({
              fullStatus: [
                OrderStatus.New,
                OrderStatus.AwaitingStock,
                OrderStatus.Reconfirm,
                OrderStatus.Confirmed,
                OrderStatus.Preparing,
                OrderStatus.HandlingOver,
                OrderStatus.InTransit,
                OrderStatus.InDelivery,
                OrderStatus.Delivered,
                OrderStatus.DeliveredCompleted,
                OrderStatus.FailedDelivery,
                OrderStatus.AwaitingReturn,
                OrderStatus.InReturn,
                OrderStatus.ReturnedStocked,
                OrderStatus.ReturnedCompleted,
                OrderStatus.Damaged,
                OrderStatus.DamagedCompleted,
                OrderStatus.Lost,
                OrderStatus.LostCompleted,
              ],
              cancelStatus: OrderStatus.Canceled,
              draftStatus: OrderStatus.Draft,
            });
          }
        } else {
          const statusExceptCanceledAndDraft = status.filter(
            it => it !== OrderStatus.Canceled && it !== OrderStatus.Draft,
          );

          if (statusExceptCanceledAndDraft.length > 0) {
            queryBuilder.andWhere(
              new Brackets(qb => {
                qb.andWhere('order.status IN (:...statusExceptCanceledAndDraft)', {
                  statusExceptCanceledAndDraft,
                });
                if (status.includes(OrderStatus.Canceled)) {
                  queryBuilder.leftJoin(
                    'order_status_histories',
                    'oh',
                    `oh.order_id = order.id AND oh.status = ${OrderStatus.Canceled} AND oh.before_status = ${OrderStatus.Draft}`,
                  );
                  qb.orWhere(
                    '(order.status = :cancelStatus AND order.cancel_reason_text is NULL AND (order.page_id IS NULL OR (order.page_id IS NOT NULL AND oh.id IS NULL)))',
                    {
                      cancelStatus: OrderStatus.Canceled,
                    },
                  );
                }
                if (status.includes(OrderStatus.Draft)) {
                  queryBuilder.leftJoin('leads', 'lo', 'lo.order_id = order.id');
                  if (!isEmpty(fbId) || !isEmpty(scopedUserId)) {
                    qb.orWhere('(order.status = :draftStatus AND lo.id is NULL)', {
                      draftStatus: OrderStatus.Draft,
                    });
                  } else {
                    qb.orWhere(
                      '(order.status = :draftStatus AND ((lo.id is NULL AND order.page_id IS NULL) OR (lo.id is NULL AND order.page_id IS NOT NULL AND order.creator_id is NOT NULL)))',
                      {
                        draftStatus: OrderStatus.Draft,
                      },
                    );
                  }
                }
              }),
            );
          } else {
            if (status.length === 2) {
              queryBuilder.leftJoin('leads', 'lo', 'lo.order_id = order.id');
              queryBuilder.leftJoin(
                'order_status_histories',
                'oh',
                `oh.order_id = order.id AND oh.status = ${OrderStatus.Canceled} AND oh.before_status = ${OrderStatus.Draft}`,
              );
              queryBuilder.andWhere(
                '((order.cancel_reason_text is NULL AND order.status = :cancelStatus AND (order.page_id IS NULL OR (order.page_id IS NOT NULL AND oh.id IS NULL))) OR (order.status = :draftStatus AND ((lo.id is NULL AND order.page_id IS NULL) OR (lo.id is NULL AND order.page_id IS NOT NULL AND order.creator_id is NOT NULL))))',
                {
                  cancelStatus: OrderStatus.Canceled,
                  draftStatus: OrderStatus.Draft,
                },
              );
            } else {
              if (status.includes(OrderStatus.Canceled)) {
                queryBuilder.leftJoin(
                  'order_status_histories',
                  'oh',
                  `oh.order_id = order.id AND oh.status = ${OrderStatus.Canceled} AND oh.before_status = ${OrderStatus.Draft}`,
                );
                queryBuilder.andWhere(
                  '(order.status = :cancelStatus AND order.cancel_reason_text is NULL AND (order.page_id IS NULL OR (order.page_id IS NOT NULL AND oh.id IS NULL)))',
                  {
                    cancelStatus: OrderStatus.Canceled,
                  },
                );
              }
              if (status.includes(OrderStatus.Draft)) {
                queryBuilder.leftJoin('leads', 'lo', 'lo.order_id = order.id');
                if (!isEmpty(fbId) || !isEmpty(scopedUserId)) {
                  queryBuilder.andWhere('(order.status = :draftStatus AND lo.id is NULL)', {
                    draftStatus: OrderStatus.Draft,
                  });
                } else {
                  queryBuilder.andWhere(
                    '(order.status = :draftStatus AND ((lo.id is NULL AND order.page_id IS NULL) OR (lo.id is NULL AND order.page_id IS NOT NULL AND order.creator_id is NOT NULL)))',
                    {
                      draftStatus: OrderStatus.Draft,
                    },
                  );
                }
              }
            }
          }
        }
      }
    } else {
      if (!isNil(selectedTabStatus)) {
        queryBuilder.andWhere('order.status = :selectedTabStatus', {
          selectedTabStatus,
        });
      }
    }
    if (customerPhone)
      queryBuilder.andWhere('order.customerPhone = :customerPhone', { customerPhone });
    if (excludesStatus)
      queryBuilder.andWhere('order.status NOT IN (:...excludesStatus)', { excludesStatus });
    if (teamsInCharge) {
      queryBuilder.andWhere('order.teamInCharge IN (:...teamsInCharge)', {
        teamsInCharge,
      });
    }
    if (!isNil(isCheckedAfterReturn))
      queryBuilder.andWhere('order.isCheckedAfterReturn = :isCheckedAfterReturn', {
        isCheckedAfterReturn,
      });
    if (ids) {
      queryBuilder.andWhere('order.id IN (:...ids)', { ids });
    }
    if (displayIds) {
      queryBuilder.andWhere('order.displayId IN (:...displayIds)', { displayIds });
    }
    if (saleIds) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          qb.where('order.sale_id IN (:...saleIds)', { saleIds });
          if (saleIds.includes(-1)) qb.orWhere('order.saleId IS NULL');
        }),
      );
    }
    if (carePageIds) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          qb.where('order.care_page_id IN (:...carePageIds)', { carePageIds });
          if (carePageIds.includes(-1)) qb.orWhere('order.care_page_id IS NULL');
        }),
      );
    }
    if (userInChargeIds) {
      queryBuilder.andWhere(
        '(order.sale_id IN (:...userInChargeIds) OR order.care_page_id IN (:...userInChargeIds))',
        { userInChargeIds },
      );
    }
    if (marketerIds) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          qb.where('order.marketer_id IN (:...marketerIds)', { marketerIds });
          if (marketerIds.includes(-1)) qb.orWhere('order.marketer_id IS NULL');
        }),
      );
    }
    if (sourceType && sourceIds) {
      const keyPairs = sourceIds.map(it => {
        return `('${sourceType}', '${it}')`;
      });
      const extSources = await this.externalSourceRepo
        .createQueryBuilder('es')
        .where(`(es.entity, entity_id) IN (${keyPairs.join(', ')})`)
        .select(['es.id'])
        .getMany();
      if (isEmpty(extSources)) return null;

      const extSrcIds = extSources.map(it => it.id);
      const dbConnection = getConnection(orderConnection);
      const ancestorIdQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where('osc.id_descendant IN (:...extSrcIds)')
        .select(['osc.id_ancestor']);
      const descendantsQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where(`osc.id_ancestor IN (${ancestorIdQb.getQuery()})`)
        .select('osc.id_descendant');
      queryBuilder
        .andWhere(`order.externalSourceId IN (${descendantsQb.getQuery()})`)
        .setParameters({ extSrcIds });
    }
    if (pageIds) {
      queryBuilder.andWhere('order.pageId IN (:...pageIds)', { pageIds });
    }

    if (filter.query) {
      const query = filter.query.match(/\S+/g).join('|');
      queryBuilder.leftJoin(OrderCarrier, 'o_carrier', 'o_carrier.order_id = order.id').andWhere(
        new Brackets(qb => {
          qb.where('cast(order.id as text) SIMILAR TO :query', {
            query: `(${query})`,
          })
            .orWhere('order.ffm_display_id SIMILAR TO :query')
            .orWhere('order.display_id SIMILAR TO :query')
            .orWhere('o_carrier.waybill_number SIMILAR TO :query')
            .orWhere('order.last_waybill_number SIMILAR TO :query')
            .orWhere('order.customer_name ~* :query')
            .orWhere('order.customer_phone ~* :query');
        }),
      );
    }

    let projectIds: (string | number)[] = [];

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (headers['project-ids'] && !isSkipProjectsInHeaders)
        projectIds = headers['project-ids']?.split(',');
      if (!isEmpty(countryIds))
        queryBuilder.andWhere('order.country_id IN (:...countryIds)', { countryIds });
    }
    if (filter?.projectIds) projectIds = filter?.projectIds;

    if (!isEmpty(projectIds))
      queryBuilder.andWhere('order.project_id IN (:...projectIds)', { projectIds });

    if (tagIds) {
      const subQb = getConnection(orderConnection)
        .createQueryBuilder()
        .from('order_tags', 'ot')
        .select('ot.order_id', 'order_id')
        .where('ot.tag_id IN (:...tagIds)', { tagIds })
        .groupBy('ot.order_id');

      if (tagMethod === TagMethodType.Include) {
        if (tagOperator === TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${tagIds?.length}`);
          queryBuilder.andWhere(`order.id IN (${subQb.getQuery()})`);
        }
        if (tagOperator === TagOperatorType.Or)
          queryBuilder.andWhere(`order.id IN (${subQb.getQuery()})`);
      }

      if (tagMethod === TagMethodType.Exclude) {
        if (tagOperator === TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${tagIds?.length}`);
          queryBuilder.andWhere(`order.id NOT IN (${subQb.getQuery()})`);
        }
        if (tagOperator === TagOperatorType.Or)
          queryBuilder.andWhere(`order.id NOT IN (${subQb.getQuery()})`);
      }
      queryBuilder.setParameters(subQb.getParameters());
    }

    if (productIds) {
      queryBuilder.innerJoin(
        'order_products',
        'oProducts',
        'order.id = oProducts.order_id AND oProducts.productId IN (:...productIds)',
        { productIds },
      );
    }

    // if (companyId) mQuery.andWhere('order.companyId = :companyId', { companyId });

    if (hasPossibleDuplicates) {
      queryBuilder.innerJoin(
        PossibleDuplicateOrder,
        'pdo',
        'order.id = pdo.order_id AND order.ignore_duplicate_warning = FALSE',
      );
    }

    if (leadState) {
      queryBuilder.andWhere('(order.lead_state IS NULL OR order.lead_state IN (:...leadState))', {
        leadState,
      });
    }

    if (excludesLeadState) {
      queryBuilder.andWhere('order.lead_state NOT IN (:...excludesLeadState)', {
        excludesLeadState,
      });
    }

    if (passedStatuses) {
      const oshSubQb = getConnection(orderConnection)
        .createQueryBuilder(OrderStatusHistory, 'osh')
        .where('osh.status IN (:...passedStatuses)', { passedStatuses })
        .select('osh.order_id');

      queryBuilder
        .andWhere(`order.id IN (${oshSubQb.getQuery()})`)
        .setParameters(oshSubQb.getParameters());
    }

    if (filter.isCountCancelDueToCustomer) {
      queryBuilder.leftJoinAndSelect('order.cancelReasons', 'cancelReasonsQuery');
      queryBuilder.andWhere(
        '(cancelReasonsQuery.id IS NULL OR cancelReasonsQuery.due_to_customer = :dueToCustomer)',
        {
          dueToCustomer: true,
        },
      );
    }

    return queryBuilder;
  }

  async importLandingPageOrderFromExcel(buffer: Buffer, userId: number) {
    const data = ExcelUtils.read(buffer, 0, 'NO');

    const date = new Date();

    const items: { landingId: string; body: CreateLandingPageOrderDto }[] = data.reduce(
      (
        prev: { landingId: string; body: CreateLandingPageOrderDto; index?: number }[],
        item,
        index,
      ) => {
        if (!item['No.'] || (item['No.'] && item['No.'] !== index + 1)) return prev;
        const landingId = item['SourceID*'];
        const customerName = item['Customer*'];
        const customerPhone = item['Phone*'];
        const addressText = item['Address'];
        const postCode = item['Postcode'];
        const sku = item['SKU'];
        // const skuQuantity = item['Quantity'];
        const note = item['Warehouse notes*'] || '';
        const waybillNotes = item['Waybill notes'];
        const collectMethod = item['Collect Method'];
        if (!landingId || !customerName || !customerPhone || !note) return prev;

        const body = plainToInstance(CreateLandingPageOrderDto, {
          customerName,
          customerPhone,
          addressText,
          note,
          postCode,
          sku,
          waybillNotes,
          collectMethod,
        });
        if (collectMethod && collectMethod === 'Capture Form') {
          body.status_send = 'Capture Form';
          body.message_time = date;
        }
        prev.push({
          landingId,
          body,
          index: item['No.'],
        });
        return prev;
      },
      [],
    );

    const results = [];
    for (const each of items) {
      try {
        const result = await this.createLandingPageOrder({
          landingId: each.landingId,
          body: each.body,
          userCreatedId: userId,
        });
        results.push(result);
      } catch (error) {
        console.log(`error import landing page order`, error, each);
        results.push({
          message: 'Error when import landing page order',
          error: StringUtils.getString(error),
          data: each,
        });
      }
    }
    return results;
  }

  async onLandingPageWebhook(landingId: string, body: CreateLandingPageOrderDto) {
    body.customerName = body.customerName || body.name;
    body.customerPhone = body.customerPhone || body.phone;
    body.message_time = new Date();
    await this.amqpConnection.publish('order-service', 'landing-page-order', { landingId, body });
    return new RawResponse(instanceToPlain({ landingId, data: body }));
    // return this.createLandingPageOrder({ landingId, body });
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'landing-page-order',
    queue: 'agbiz-process-landing-page-order',
    errorHandler: rmqErrorsHandler,
  })
  async createLandingPageOrder(payload: {
    landingId: string;
    body: CreateLandingPageOrderDto;
    userCreatedId?: number;
  }): Promise<Order> {
    let redisKey: string;

    const { landingId, body, userCreatedId } = payload;
    let metadata: Record<string, any> = {
      sourceType:
        body.collectMethod && body.collectMethod.toLowerCase() === 'facebook conversion'
          ? SourceEntity.fb_page
          : SourceEntity.landing_page,
      repsId: '',
    };
    const metadataTagLog: Record<string, any> = {
      tags: [],
    };
    let metadataCarrierLog: Record<string, any> = {};
    let landingPage = null;
    if (body.collectMethod && body.collectMethod.toLowerCase() === 'facebook conversion') {
      const { data: page } = await this.amqpConnection.request<{ data: FanPage }>({
        exchange: 'facebook-bot',
        routingKey: 'find-fanpage-by-id',
        payload: landingId,
        timeout: 10000,
      });
      landingPage = page;
    } else {
      landingPage = await this.landingPagesService.findById(landingId);
    }
    if (!landingPage || Object.keys(landingPage).length <= 0)
      throw new NotFoundException('Not found source ID: ' + landingId);

    const { userId, countryId, projectId } = landingPage;
    let user: User;
    let userIdGetDetail;
    if (userCreatedId) {
      userIdGetDetail = userCreatedId;
    } else {
      userIdGetDetail = userId;
    }
    try {
      const { data: mUser } = await this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'get-user',
        payload: { id: userIdGetDetail },
        timeout: 5000,
      });
      user = mUser as User;
    } catch (err) {
      console.log(`cannot fetch user`, err);
    }
    if (!user) throw new BadRequestException(`User id ${userIdGetDetail} not found`);

    const {
      sku,
      skuQuantity,
      message_time,
      status_send,
      utm_source,
      utm_medium,
      utm_campaign,
      utm_term,
      message_id,
      link,
      ...rest
    } = body;

    let creatorId;
    if (userCreatedId) {
      creatorId = userCreatedId;
    } else {
      creatorId = userId;
    }

    const order = plainToClass(Order, {
      ...rest,
      companyId: user.companyId,
      creatorId: Number(creatorId),
      lastUpdatedBy: Number(creatorId),
      countryId: Number(countryId),
      projectId: Number(projectId),
      status: OrderStatus.Draft,
    });
    if (userId) {
      order.marketerId = Number(userId);
      metadata = {
        ...metadata,
        mktId: Number(userId),
      };
    }
    if (body.collectMethod && body.collectMethod.toLowerCase() === 'facebook conversion') {
      order.fbScopedUserId = StringUtils.generateSecureUniqueString();
    }
    try {
      if (body.state) {
        const regex = /^([0-9])+(: )+/;
        const stateStr = body.state.replace(regex, '');
        const districtStr = body.district.replace(regex, '');
        const wardStr = body.state.replace(regex, '');
        const location = await this.locationService.getLocationByName2(
          stateStr,
          districtStr,
          wardStr,
        );
        if (location) {
          if (location.wards_id) {
            order.addressWardId = location.wards_id;
            order.addressWard = location.wards_name;
          }
          order.addressDistrictId = location.districts_id;
          order.addressDistrict = location.districts_name;
          order.addressProvinceId = location.provinces_id;
          order.addressProvince = location.provinces_name;
        } else {
          order.addressText = [order.addressText, stateStr, districtStr, wardStr]
            .filter(identity)
            .join(', ');
        }
      }
    } catch (e) {
      console.log('handle location error', e);
    }
    if (body.message_time) order.createdAt = body.message_time;
    if (body.message_id) {
      const checkExist = await this.leadsRepo
        .createQueryBuilder('l')
        .where('l.request_landing_id=:requestId', {
          requestId: body.message_id,
        })
        .getOne();

      if (checkExist) {
        console.log(
          `Leads with  request_landing_id is ${body.message_id} already exist!`,
        );
        throw new BadRequestException(
          `Leads with  request_landing_id is ${body.message_id} already exist!`,
        );
      }
    }

    if (body.postCode)
      order.note = isEmpty(order.note)
        ? String(body.postCode)
        : order.note.concat('\n' + String(body.postCode));

    const extraFields = ['extra', 'extra1', 'extra2', 'extra3', 'extra4', 'extra5'];
    for (const field of extraFields) {
      if (body[field])
        order.note = isEmpty(order.note)
          ? String(body[field])
          : order.note.concat('\n' + String(body[field]));
    }

    if (!order.customerName) order.customerName = order.customerPhone;
    metadata = {
      ...metadata,
      customer: {
        customerName: order.customerName,
        customerPhone: order.customerPhone,
        addressText: order.addressText,
        addressDistrictId: order.addressDistrictId || '',
        addressDistrict: order.addressDistrict || '',
        addressProvinceId: order.addressProvinceId || '',
        addressProvince: order.addressProvince || '',
        addressWardId: order.addressWardId || '',
        addressWard: order.addressWard || '',
        postCode: order?.postCode?.toString() || '',
      },
    };

    // if (!sku && !isEmpty(order.note)) {
    //   try {
    //     const { data } = await this.amqpConnection.request({
    //       exchange: 'catalog-service-variants',
    //       routingKey: 'find-variants-by-sku-query',
    //       payload: { query: order.note, projectId, countryId, companyId: order.companyId },
    //       timeout: 5000,
    //     });
    //     const variants = data as ProductVariant[];

    //     order.products = map(variants, item => {
    //       const product = new OrderProduct();
    //       product.productId = item.id;
    //       product.productDetail = instanceToPlain(item);
    //       product.quantity = 1;
    //       product.price = item.priceByCountry || 0;
    //       return product;
    //     });
    //   } catch (error) {
    //     // throw new BadRequestException();
    //   }
    // }

    if (sku) {
      try {
        const { data } = await this.amqpConnection.request({
          exchange: 'catalog-service-variants',
          routingKey: 'find-variant-by-list-sku',
          payload: { listSku: [sku], countryId, companyId: order.companyId },
          timeout: 5000,
        });
        const variants = data as ProductVariant[];

        order.products = map(variants, item => {
          const product = new OrderProduct();
          product.productId = item.id;
          product.productDetail = instanceToPlain(item);
          product.quantity = skuQuantity || 1;
          product.price = item.priceByCountry || 0;
          return product;
        });
        const metadataProduct = [];
        const totalWeight = sumBy(order.products, o => o?.productDetail?.weight * o.quantity);
        order.products.map(product => {
          metadataProduct.push({
            id: product.productId,
            sku: product?.productDetail?.sku,
            name: product?.productDetail?.product?.name,
            quantity: product.quantity,
            price: product.price,
            weight: product?.productDetail?.weight,
          });
        });
        metadata = {
          ...metadata,
          products: metadataProduct,
          totalWeight,
        };
      } catch (error) {
        // throw new BadRequestException();
      }
    }
    metadata = {
      ...metadata,
      fee: {
        totalPrice: order?.totalPrice || 0,
        shippingFee: order?.shippingFee || 0,
        discount: order?.discount || 0,
        surcharge: order?.surcharge || 0,
        paid: order?.paid || 0,
        cod: Math.max(
          Number(order?.totalPrice || 0) +
            Number(order?.shippingFee || 0) +
            (order?.surcharge || 0) -
            (order?.discount || 0) -
            (order?.paid || 0),
          0,
        ),
      },
    };

    if (link) {
      redisKey = `processing-ladipage-data.${order.customerPhone}.${
        ['Capture Form', 'Draft form'].includes(status_send) ? 'capture' : 'manual'
      }.link.${link}`;
      const [, [, isProcessing]] = await this.redis
        .multi()
        .set(redisKey, 0, 'EX', 60, 'NX')
        .incr(redisKey)
        .exec();
      if (isProcessing > 1) {
        console.log(`Duplicate request from ladipage`, isProcessing, body);
        return order;
      }

      const existingQb = this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin('l.order', `o`, `l.order_id = o.id`)
        .addSelect('o.id')
        .where(`DATE(o.created_at) = DATE('${order.createdAt}')`)
        .andWhere(`o.customerPhone IS NOT NULL`)
        .andWhere(`o.customerPhone <> ''`)
        .andWhere(`o.customerPhone = :phone`)
        .andWhere(`l.link = :link`)
        .setParameters({ phone: order.customerPhone, link });
      if (['Capture Form', 'Draft form'].includes(status_send))
        existingQb.andWhere(`l.form_captured_at IS NOT NULL`);
      else existingQb.andWhere(`l.form_captured_at IS NULL`);

      const existingLead = await existingQb.getOne();
      if (existingLead) return existingLead.order;
    }

    const mObjDisplayId = await this.getDisplayId(Number(projectId), order.companyId);
    order.displayId = mObjDisplayId.displayId;

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const externalSource = await this.externalSourceRepo
        .createQueryBuilder('oes')
        .andWhere('oes.entity = :entity')
        .andWhere('oes.entity_id = :entityId')
        .setParameters({
          entity:
            body.collectMethod && body.collectMethod.toLowerCase() === 'facebook conversion'
              ? SourceEntity.fb_page
              : SourceEntity.landing_page,
          entityId: landingId,
        })
        .getOne();
      if (externalSource) {
        order.externalSource = externalSource;
      } else {
        order.externalSource = await queryRunner.manager.save(
          plainToInstance(OrderSource, {
            entity:
              body.collectMethod && body.collectMethod.toLowerCase() === 'facebook conversion'
                ? SourceEntity.fb_page
                : SourceEntity.landing_page,
            entityId: landingId,
          }),
        );
      }
      metadata = {
        ...metadata,
        orderSourceType:
          body.collectMethod && body.collectMethod.toLowerCase() === 'facebook conversion'
            ? SourceEntity.fb_page
            : SourceEntity.landing_page,
        orderSourceId: order?.externalSource?.id,
      };

      let customer = plainToInstance(Customer, {
        name: body.customerName || body.customerPhone,
        phone: body.customerPhone,
        countryId: Number(countryId),
      });
      if (body.addressText) customer.address = body.addressText;
      customer = await queryRunner.manager.save(customer);
      metadataCarrierLog = body.waybillNotes
        ? {
            waybillNote: body.waybillNotes,
            carrierCode: '',
            waybillNumber: '',
            customerEDD: null,
            carrierEDD: null,
          }
        : {
            waybillNote: '',
            carrierCode: '',
            waybillNumber: '',
            customerEDD: null,
            carrierEDD: null,
          };
      // Insert new order
      order.metadata = JSON.stringify(metadata);
      order.metadataTagLog = JSON.stringify(metadataTagLog);
      order.metadataCarrierLog = JSON.stringify(metadataCarrierLog);
      const mOrder = await queryRunner.manager.save(order);
      if (body.waybillNotes) {
        await queryRunner.manager.save(OrderCarrier, {
          orderId: mOrder.id,
          waybillNote: body.waybillNotes,
        });
      }

      // Create new lead
      const newLead = plainToInstance(Lead, { orderId: mOrder.id, createdAt: mOrder.createdAt });
      
      if (!isEmpty(message_id)) newLead.requestLandingId = message_id;
      if (!isEmpty(utm_source)) newLead.utmSource = utm_source;
      if (!isEmpty(utm_medium)) newLead.utmMedium = utm_medium;
      if (!isEmpty(utm_campaign)) newLead.utmCampaign = utm_campaign;
      if (!isEmpty(utm_term)) newLead.utmTerm = utm_term;
      if (!isEmpty(link)) newLead.link = link;
      if (['Capture Form', 'Draft form'].includes(status_send) && message_time)
        newLead.formCapturedAt = message_time;
      const upsertLeadResult = await queryRunner.manager.upsert(Lead, newLead, ['orderId']);
      console.log(`upsert lead result after created order from landing page`, upsertLeadResult);
      await queryRunner.commitTransaction();

      if (mOrder) {
        // Scan for possible duplicate orders
        await this.amqpConnection.publish('order-service', 'scan-possible-duplicate-orders', {
          id: mOrder.id,
        });

        // Scan for possible duplicate leads
        await this.amqpConnection.publish('order-service', 'scan-possible-duplicate-leads', {
          orderId: mOrder.id,
        });
      }

      if (upsertLeadResult) {
        const leadId = upsertLeadResult?.identifiers?.[0]?.id;
        if (leadId && newLead.utmSource === 'facebook')
          await this.amqpConnection.sendMessage(
            'order-service',
            undefined,
            { leadId },
            { routingKey: 'save-external-source-after-created-order' },
          );
      }

      return order;
    } catch (e) {
      console.log(`createLandingPageOrder ERROR:`, e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
      if (redisKey) await this.redis.del(redisKey);
    }
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'create-facebook-order',
    queue: 'order-service-create-facebook-order',
    errorHandler: rmqErrorsHandler,
  })
  async createBotOrder({
    data,
    countryId,
    companyId,
    userId,
    projectId,
  }: {
    countryId: number;
    companyId: number;
    userId: number;
    projectId: number;
    data: CreateOrderDto;
  }) {
    const { customerFbScopedUserId } = data;
    if (!customerFbScopedUserId) {
      return new Nack();
    }
    /* const redisKey = `create-facebook-order-${customerFbScopedUserId}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 20, 'NX')
      .incr(redisKey)
      .exec();
    if (isUpdating > 1) return new Nack(); */
    const [pageId, psid] = customerFbScopedUserId.split('_');
    const draftOrder = await this.orderRepository.findOne({
      where: {
        fbScopedUserId: psid,
        pageId,
        status: OrderStatus.Draft,
      },
    });
    if (draftOrder) {
      return draftOrder;
    }
    console.log('create bot order', data, countryId, companyId, userId, projectId);
    return await this.createOrder(data, countryId, companyId, userId, projectId);
  }

  async createOrder(
    data: CreateOrderDto,
    countryId: number,
    companyId: number,
    userId?: number,
    projectId?: number,
  ): Promise<Order> {
    const {
      customerName,
      customerPhone,
      addressText,
      addressNote,
      products,
      addressWardId,
      addressDistrictId,
      addressProvinceId,
      customerFbGlobalId,
      customerFbScopedUserId,
      pageId,
      isImport,
    } = data;
    let metadata: Record<string, any> = {
      sourceType: data.sourceType || '',
    };
    let metadataTagLog: Record<string, any> = {};
    let metadataCarrierLog: Record<string, any> = {};
    const postCode: string =
      !isUndefined(data.postCode) && Array.isArray(data.postCode) && data.postCode?.[0]
        ? data.postCode[0]
        : (data.postCode as string);

    const order = plainToClass(Order, data);
    order.postCode = postCode;
    order.companyId = companyId;
    order.creatorId = userId;
    order.lastUpdatedBy = userId;
    order.fbGlobalId = customerFbGlobalId;
    order.fbScopedUserId = customerFbScopedUserId;
    order.countryId = typeof countryId === 'number' ? countryId : Number(countryId);
    order.pageId = pageId;

    if (customerFbScopedUserId) {
      const [pageId, psid] = customerFbScopedUserId.split('_');
      if (psid) {
        order.pageId = pageId;
        order.fbScopedUserId = psid;
        const { data: pData } = await this.amqpConnection.request({
          exchange: 'facebook-bot',
          routingKey: 'get-fanpages-by-ids',
          payload: { ids: [pageId] },
          timeout: 10000,
        });
        const pages = pData as FanPage[];
        order.type = pages[0]?.orderType || OrderType.normal;
      }
    }

    if (!isEmpty(products)) {
      order.products = await this.getOrderProducts(products, Number(countryId), undefined, userId);
      const projectIds = order.products.reduce((prev, it) => {
        if (prev.includes(it.productDetail.product?.projectId)) return prev;
        prev.push(it.productDetail.product?.projectId);
        return prev;
      }, []);
      if (projectIds.length > 1)
        throw new BadRequestException(`Tất cả các sản phẩm trong đơn hàng phải cùng 1 dự án`);

      order.projectId = projectIds[0];
      order.totalPrice = sumBy(order.products, o => (o.editedPrice || o.price) * o.quantity);
      if (order.discount) order.discount = Math.min(order.discount, order.totalPrice);
      const metadataProduct = [];
      const totalWeight = sumBy(order.products, o => o?.productDetail?.weight * o.quantity);
      order.products.map(product => {
        metadataProduct.push({
          id: product.productId,
          sku: product?.productDetail?.sku,
          name: product?.productDetail?.product?.name,
          quantity: product.quantity,
          price: product.price,
          weight: product?.productDetail?.weight,
        });
      });
      metadata = {
        ...metadata,
        products: metadataProduct,
        totalWeight,
      };
    }

    metadata = {
      ...metadata,
      fee: {
        totalPrice: order?.totalPrice || 0,
        shippingFee: order?.shippingFee || 0,
        discount: order?.discount || 0,
        surcharge: order?.surcharge || 0,
        paid: order?.paid || 0,
        cod: Math.max(
          Number(order?.totalPrice || 0) +
            Number(order?.shippingFee || 0) +
            (order?.surcharge || 0) -
            (order?.discount || 0) -
            (order?.paid || 0),
          0,
        ),
      },
    };

    if (order.pageId) {
      try {
        const { data: page } = await this.amqpConnection.request<{ data: FanPage }>({
          exchange: 'facebook-bot',
          routingKey: 'find-fanpage-by-id',
          payload: order.pageId,
          timeout: 10000,
        });
        if (page?.marketerId) order.marketerId = page.marketerId;
        if (!order.projectId && page.projectId) order.projectId = page.projectId;
      } catch (error) {
        console.log(`find fb page error: `, error);
      }
    }

    if (!order.projectId && !isNil(projectId)) order.projectId = projectId;
    if (!order.projectId) throw new BadRequestException(`Project id is required`);

    const mObjDisplayId = await this.getDisplayId(Number(order.projectId), companyId);
    order.displayId = mObjDisplayId.displayId;

    if (data.carePageId) order.teamInCharge = TeamInCharge.CarePage;

    if (!isNil(data.tagIds)) {
      const mTags = await this.tagService.findByIds(data.tagIds, TagType.order);
      order.tags = mTags;
      const tags = [];
      mTags.forEach(tag => tags.push(tag?.id));
      metadataTagLog = { ...metadataTagLog, tags };
    }
    if (!isNil(data.printNoteIds)) {
      const notes = await this.printNotesService.findByIds(data.printNoteIds);
      order.printNotes = notes;
    }

    if (data.status !== OrderStatus.Draft) {
      let ward: Ward | undefined, district: District | undefined, province: Province | undefined;
      if (isImport) {
        const location = await this.locationService.getLocationByName(
          addressProvinceId,
          addressDistrictId,
          addressWardId,
        );
        if (!location) {
          const errorLocations = [addressProvinceId, addressDistrictId];
          if (addressWardId) errorLocations.push(addressWardId);
          throw new NotFoundException(
            `Địa chỉ không hợp lệ. Không tìm thấy địa chỉ ${errorLocations.join(', ')}`,
          );
        }

        if (location.wards_id) {
          order.addressWardId = location.wards_id;
          order.addressWard = location.wards_name;
          ward = plainToInstance(Ward, {
            id: location.wards_id,
            name: location.wards_name,
            nameEn: location.wards_name_en,
          });
        }
        order.addressDistrictId = location.districts_id;
        order.addressDistrict = location.districts_name;
        order.addressProvinceId = location.provinces_id;
        order.addressProvince = location.provinces_name;

        district = plainToInstance(District, {
          id: location.districts_id,
          name: location.districts_name,
          nameEn: location.districts_name_en,
        });
        province = plainToInstance(Province, {
          id: location.provinces_id,
          name: location.provinces_name,
          nameEn: location.provinces_name_en,
        });
      } else {
        if (!data.isCreatedByAI) {
          ward = await this.locationService.getWard(addressWardId, {
            where: { districtId: addressDistrictId },
          });
          district = await this.locationService.getDistrict(addressDistrictId);
          province = await this.locationService.getProvince(addressProvinceId);
          if ((addressWardId && !ward) || !district || !province)
            throw new NotFoundException('Địa chỉ không hợp lệ');

          if (ward) {
            order.addressWard = ward.name;
            order.addressWardId = ward.id;
          }
          order.addressDistrict = district.name;
          order.addressProvince = province.name;
          order.addressDistrictId = district.id;
          order.addressProvinceId = province.id;
        }
      }

      await this.updateCustomer(
        customerName,
        customerPhone,
        addressText,
        addressNote,
        ward,
        district,
        province,
        Number(countryId),
        userId,
        postCode,
        order.fbGlobalId,
        order.fbScopedUserId,
        companyId,
      );
    }

    if (addressProvinceId) {
      const province = await this.locationService.getProvince(addressProvinceId);
      order.addressProvince = province?.name;
    }

    if (addressDistrictId) {
      const district = await this.locationService.getDistrict(addressDistrictId);
      order.addressDistrict = district?.name;
    }

    if (addressWardId) {
      const ward = await this.locationService.getWard(addressWardId, {
        where: { districtId: addressDistrictId },
      });
      order.addressWard = ward?.name;
    }

    if (data?.carrier) {
      order.carrier = plainToInstance(OrderCarrier, {
        ...data?.carrier,
      });
    }
    metadataCarrierLog = {
      waybillNote: order.carrier?.waybillNote || '',
      carrierCode: order.carrier?.carrierCode || '',
      waybillNumber: order.carrier?.waybillNumber || '',
      customerEDD: order.carrier?.customerEDD || null,
      carrierEDD: order.carrier?.carrierEDD || null,
    };

    if (data.crossCare) {
      order.teamInCharge = TeamInCharge.Sale;
      order.saleId = data.saleId || data.carePageId;
      order.carePageId = null;
    } else if (data.sourceType === SourceEntity.fb_page) {
      order.teamInCharge = TeamInCharge.CarePage;
      order.carePageId = data.saleId || data.carePageId;
      order.saleId = null;
    }
    // customer & general
    metadata = {
      ...metadata,
      customer: {
        customerName,
        customerPhone,
        addressText,
        addressDistrictId: order.addressDistrictId || '',
        addressDistrict: order.addressDistrict || '',
        addressProvinceId: order.addressProvinceId || '',
        addressProvince: order.addressProvince || '',
        addressWardId: order.addressWardId || '',
        addressWard: order.addressWard || '',
        postCode: order?.postCode?.toString() || '',
      },
      repsId: data.saleId || data.carePageId || '',
      mktId: data?.marketerId || '',
    };
    if (isImport) {
      metadata = { ...metadata, isCreatedByBulkAction: true };
    }
    order.metadata = JSON.stringify(metadata);
    order.metadataTagLog = JSON.stringify(metadataTagLog);
    order.metadataCarrierLog = JSON.stringify(metadataCarrierLog);

    const queryRunner = getConnection(orderConnection).createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      if (!isNil(data.sourceType) && data.sourceId) {
        let externalSource = await queryRunner.manager
          .createQueryBuilder(OrderSource, 'oes')
          .andWhere('oes.entity = :entity')
          .andWhere('oes.entity_id = :entityId')
          .setParameters({
            entity: data.sourceType,
            entityId: String(data.sourceId),
          })
          .getOne();
        if (!externalSource) {
          externalSource = await queryRunner.manager.save(
            plainToInstance(OrderSource, {
              entity: data.sourceType,
              entityId: String(data.sourceId),
            }),
          );
        }
        if (data.sourceType === SourceEntity.fb_page) {
          const { data: pData } = await this.amqpConnection.request({
            exchange: 'facebook-bot',
            routingKey: 'get-fanpages-by-ids',
            payload: { ids: [data.sourceId] },
            timeout: 10000,
          });
          const pages = pData as FanPage[];
          order.type = pages[0]?.orderType || OrderType.normal;
        }
        if (data.sourceType === SourceEntity.landing_page) {
          const landingPage = await queryRunner.manager.getRepository(LandingPage).findOne({
            where: { id: data.sourceId },
          });
          order.type = landingPage?.orderType || OrderType.normal;
        }
        if (!externalSource?.id) throw new BadRequestException('Nguồn đơn không hợp lệ');
        order.externalSourceId = externalSource.id;
        metadata = {
          ...metadata,
          orderSourceType: data.sourceType,
          orderSourceId: externalSource.id,
        };
      }

      if (customerPhone && data.status === OrderStatus.Draft) {
        const customer = await queryRunner.manager.getRepository(Customer).findOne({
          where: {
            phone: customerPhone,
            countryId: Number(countryId),
            companyId,
          },
        });
        const customerUpdate = plainToInstance(Customer, {
          name: customerName || customerPhone,
          phone: customerPhone,
          countryId: Number(countryId),
          companyId,
          creatorId: userId,
        });
        if (addressText) customerUpdate.address = addressText;
        if (customer) customerUpdate.id = customer.id;
        await queryRunner.manager.save(customerUpdate);
      }

      const mOrder = await queryRunner.manager.save(order);

      // Create new lead
      if (mOrder.crossCare) {
        await queryRunner.manager.upsert(
          Lead,
          plainToInstance(Lead, {
            orderId: mOrder.id,
            createdAt: mOrder.createdAt,
          }),
          ['orderId'],
        );
      }

      await queryRunner.commitTransaction();

      if (mOrder.pageId && (mOrder.fbGlobalId || mOrder.fbScopedUserId)) {
        // Sync new created order to facebook-bot service
        await this.amqpConnection.publish('order-service', 'sync-new-order-to-auto-inbox', {
          orderId: mOrder.id,
        });
      }

      if (data.status !== OrderStatus.Draft) {
        // Scan for possible duplicate orders
        await this.amqpConnection.publish('order-service', 'scan-possible-duplicate-orders', {
          id: mOrder.id,
        });
      }

      if (mOrder.crossCare) {
        // Scan for possible duplicate leads
        await this.amqpConnection.publish('order-service', 'scan-possible-duplicate-leads', {
          orderId: mOrder.id,
          userId: data.saleId, // handle case tạo lead từ duplicate và chia cho user được chọn
        });
      }

      // Update external source
      await this.amqpConnection.sendMessage(
        'order-service',
        null,
        { orderId: mOrder.id },
        { routingKey: 'save-external-source-after-created-order' },
      );

      // Send facebook initiate checkout event
      if (order.fbScopedUserId && order.status === OrderStatus.New) {
        await this.amqpConnection.publish(
          'facebook-bot',
          'send-custom-event-when-fb-order-created',
          { id: mOrder.id },
        );
      }

      return mOrder;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log(`create order error`, error, StringUtils.getString(error));
      const mError = error?.response?.data ?? error?.detail ?? error;
      throw new BadRequestException(mError);
    } finally {
      await queryRunner.release();
    }
  }

  async createOrderForLeadAfterSale(
    data: CreateLeadAfterSaleDto,
    countryId: number,
    companyId: number,
    userId?: number,
    projectId?: number,
  ): Promise<Order> {
    const {
      customerName,
      customerPhone,
      addressText,
      addressNote,
      products,
      addressWardId,
      addressDistrictId,
      addressProvinceId,
      customerFbGlobalId,
      customerFbScopedUserId,
      pageId,
      isImport,
    } = data;
    let metadata: Record<string, any> = {
      sourceType: data.sourceType || '',
    };
    let metadataTagLog: Record<string, any> = {};
    let metadataCarrierLog: Record<string, any> = {};
    const postCode: string =
      !isUndefined(data.postCode) && Array.isArray(data.postCode) && data.postCode?.[0]
        ? data.postCode[0]
        : (data.postCode as string);

    const order = plainToClass(Order, data);
    order.postCode = postCode;
    order.companyId = companyId;
    order.creatorId = userId;
    order.lastUpdatedBy = userId;
    order.fbGlobalId = customerFbGlobalId;
    order.fbScopedUserId = customerFbScopedUserId;
    order.countryId = typeof countryId === 'number' ? countryId : Number(countryId);
    order.pageId = pageId;

    if (customerFbScopedUserId) {
      const [pageId, psid] = customerFbScopedUserId.split('_');
      if (psid) {
        order.pageId = pageId;
        order.fbScopedUserId = psid;
      }
    }

    if (!isEmpty(products)) {
      order.products = await this.getOrderProducts(products, Number(countryId), undefined, userId);
      const projectIds = order.products.reduce((prev, it) => {
        if (prev.includes(it.productDetail.product?.projectId)) return prev;
        prev.push(it.productDetail.product?.projectId);
        return prev;
      }, []);
      if (projectIds.length > 1)
        throw new BadRequestException(`Tất cả các sản phẩm trong đơn hàng phải cùng 1 dự án`);

      order.projectId = projectIds[0];
      order.totalPrice = sumBy(order.products, o => (o.editedPrice || o.price) * o.quantity);
      if (order.discount) order.discount = Math.min(order.discount, order.totalPrice);

      const metadataProduct = [];
      const totalWeight = sumBy(order.products, o => o?.productDetail?.weight * o.quantity);
      order.products.map(product => {
        metadataProduct.push({
          id: product.productId,
          sku: product?.productDetail?.sku,
          name: product?.productDetail?.product?.name,
          quantity: product.quantity,
          price: product.price,
          weight: product?.productDetail?.weight,
        });
      });
      metadata = {
        ...metadata,
        products: metadataProduct,
        totalWeight,
      };
    }
    metadata = {
      ...metadata,
      fee: {
        totalPrice: order?.totalPrice || 0,
        shippingFee: order?.shippingFee || 0,
        discount: order?.discount || 0,
        surcharge: order?.surcharge || 0,
        paid: order?.paid || 0,
        cod: Math.max(
          Number(order?.totalPrice || 0) +
            Number(order?.shippingFee || 0) +
            (order?.surcharge || 0) -
            (order?.discount || 0) -
            (order?.paid || 0),
          0,
        ),
      },
    };

    // if (order.pageId) {
    //   try {
    //     const { data: page } = await this.amqpConnection.request<{ data: FanPage }>({
    //       exchange: 'facebook-bot',
    //       routingKey: 'find-fanpage-by-id',
    //       payload: order.pageId,
    //       timeout: 10000,
    //     });
    //     if (page?.marketerId) order.marketerId = page.marketerId;
    //     if (!order.projectId && page.projectId) order.projectId = page.projectId;
    //   } catch (error) {
    //     console.log(`find fb page error: `, error);
    //   }
    // }

    if (!order.projectId && !isNil(projectId)) order.projectId = projectId;
    if (!order.projectId) throw new BadRequestException(`Project id is required`);

    let mObjDisplayId = await this.getOrderAfterSaleDisplayId(Number(order.projectId), companyId);
    let retryGetOrderDisplayId = 0;
    while (retryGetOrderDisplayId < 5) {
      const checkOrder = await this.orderRepository.findOne({
        where: { displayId: mObjDisplayId.displayId },
      });
      if (checkOrder) {
        retryGetOrderDisplayId++;
        mObjDisplayId = await this.getOrderAfterSaleDisplayId(Number(order.projectId), companyId);
      } else {
        break;
      }
    }
    order.displayId = mObjDisplayId.displayId;

    if (data.carePageId) order.teamInCharge = TeamInCharge.CarePage;

    if (!isNil(data.tagIds)) {
      const mTags = await this.tagService.findByIds(data.tagIds, TagType.order);
      order.tags = mTags;
      metadataTagLog = { ...metadataTagLog, tags: mTags.map(t => t.id) };
    }
    if (!isNil(data.printNoteIds)) {
      const notes = await this.printNotesService.findByIds(data.printNoteIds);
      order.printNotes = notes;
    }

    if (data.status !== OrderStatus.Draft) {
      let ward: Ward | undefined, district: District | undefined, province: Province | undefined;
      if (isImport) {
        const location = await this.locationService.getLocationByName(
          addressProvinceId,
          addressDistrictId,
          addressWardId,
        );
        if (!location) {
          const errorLocations = [addressProvinceId, addressDistrictId];
          if (addressWardId) errorLocations.push(addressWardId);
          throw new NotFoundException(
            `Địa chỉ không hợp lệ. Không tìm thấy địa chỉ ${errorLocations.join(', ')}`,
          );
        }

        if (location.wards_id) {
          order.addressWardId = location.wards_id;
          order.addressWard = location.wards_name;
          ward = plainToInstance(Ward, {
            id: location.wards_id,
            name: location.wards_name,
            nameEn: location.wards_name_en,
          });
        }
        order.addressDistrictId = location.districts_id;
        order.addressDistrict = location.districts_name;
        order.addressProvinceId = location.provinces_id;
        order.addressProvince = location.provinces_name;

        district = plainToInstance(District, {
          id: location.districts_id,
          name: location.districts_name,
          nameEn: location.districts_name_en,
        });
        province = plainToInstance(Province, {
          id: location.provinces_id,
          name: location.provinces_name,
          nameEn: location.provinces_name_en,
        });
      } else {
        ward = await this.locationService.getWard(addressWardId, {
          where: { districtId: addressDistrictId },
        });
        district = await this.locationService.getDistrict(addressDistrictId);
        province = await this.locationService.getProvince(addressProvinceId);
        if ((addressWardId && !ward) || !district || !province)
          throw new NotFoundException('Địa chỉ không hợp lệ');

        if (ward) {
          order.addressWard = ward.name;
          order.addressWardId = ward.id;
        }
        order.addressDistrict = district.name;
        order.addressProvince = province.name;
        order.addressDistrictId = district.id;
        order.addressProvinceId = province.id;
      }

      await this.updateCustomer(
        customerName,
        customerPhone,
        addressText,
        addressNote,
        ward,
        district,
        province,
        Number(countryId),
        userId,
        postCode,
        order.fbGlobalId,
        order.fbScopedUserId,
        companyId,
      );
    }

    if (data?.carrier) {
      order.carrier = plainToInstance(OrderCarrier, {
        ...data?.carrier,
      });
    }
    metadataCarrierLog = {
      waybillNote: order.carrier?.waybillNote || '',
      carrierCode: order.carrier?.carrierCode || '',
      waybillNumber: order.carrier?.waybillNumber || '',
      customerEDD: order.carrier?.customerEDD || null,
      carrierEDD: order.carrier?.carrierEDD || null,
    };

    if (data.crossCare) {
      order.teamInCharge = TeamInCharge.Sale;
      order.saleId = data.saleId || data.carePageId;
      order.carePageId = null;
    } else if (data.sourceType === SourceEntity.fb_page) {
      order.teamInCharge = TeamInCharge.CarePage;
      order.carePageId = data.saleId || data.carePageId;
      order.saleId = null;
    }
    // customer & general
    metadata = {
      ...metadata,
      customer: {
        customerName,
        customerPhone,
        addressText,
        addressDistrictId: order.addressDistrictId || '',
        addressDistrict: order.addressDistrict || '',
        addressProvinceId: order.addressProvinceId || '',
        addressProvince: order.addressProvince || '',
        addressWardId: order.addressWardId || '',
        addressWard: order.addressWard || '',
        postCode: order?.postCode?.toString() || '',
      },
      repsId: data.saleId || data.carePageId || '',
      mktId: data?.marketerId || '',
    };

    const queryRunner = getConnection(orderConnection).createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      if (!isNil(data.sourceType) && data.sourceId) {
        let externalSource = await queryRunner.manager
          .createQueryBuilder(OrderSource, 'oes')
          .andWhere('oes.entity = :entity')
          .andWhere('oes.entity_id = :entityId')
          .setParameters({
            entity: SourceEntity.aftersales,
            entityId: String(data.sourceId),
          })
          .getOne();
        if (!externalSource) {
          externalSource = await queryRunner.manager.save(
            plainToInstance(OrderSource, {
              entity: SourceEntity.aftersales,
              entityId: String(data.sourceId),
            }),
          );
        }
        if (!externalSource?.id) throw new BadRequestException('Nguồn đơn không hợp lệ');
        order.externalSourceId = externalSource.id;
        metadata = {
          ...metadata,
          orderSourceType: data.sourceType,
          orderSourceId: externalSource.id,
        };
      }
      if (customerPhone) {
        const customer = await queryRunner.manager.getRepository(Customer).findOne({
          where: {
            phone: customerPhone,
            countryId: Number(countryId),
            companyId,
          },
        });
        const customerUpdate = plainToInstance(Customer, {
          name: customerName || customerPhone,
          phone: customerPhone,
          countryId: Number(countryId),
          companyId,
          creatorId: userId,
        });
        if (addressText) customerUpdate.address = addressText;
        if (customer) customerUpdate.id = customer.id;
        await queryRunner.manager.save(customerUpdate);
      }

      if (isImport) {
        metadata = { ...metadata, isCreatedByBulkAction: true };
      }
      order.metadata = JSON.stringify(metadata);
      order.metadataTagLog = JSON.stringify(metadataTagLog);
      order.metadataCarrierLog = JSON.stringify(metadataCarrierLog);
      const mOrder = await queryRunner.manager.save(order);

      // Create new lead
      if (mOrder.crossCare) {
        await queryRunner.manager.upsert(
          Lead,
          plainToInstance(Lead, {
            orderId: mOrder.id,
            createdAt: mOrder.createdAt,
            leadType: LeadType.after_sale,
            projectId,
            countryId,
          }),
          ['orderId'],
        );
      }

      await queryRunner.commitTransaction();

      if (mOrder.pageId && (mOrder.fbGlobalId || mOrder.fbScopedUserId)) {
        // Sync new created order to facebook-bot service
        await this.amqpConnection.publish('order-service', 'sync-new-order-to-auto-inbox', {
          orderId: mOrder.id,
        });
      }

      if (data.status !== OrderStatus.Draft) {
        // Scan for possible duplicate orders
        await this.amqpConnection.publish('order-service', 'scan-possible-duplicate-orders', {
          id: mOrder.id,
        });
      }

      if (mOrder.crossCare) {
        // Scan for possible duplicate leads
        await this.amqpConnection.publish(
          'order-service',
          'scan-possible-duplicate-leads-after-sale',
          {
            orderId: mOrder.id,
            userId: data.saleId, // handle case tạo lead từ duplicate và chia cho user được chọn
          },
        );
      }

      // Update external source
      if (data.collectType !== 'convertFromFacebook') {
        await this.amqpConnection.sendMessage(
          'order-service',
          null,
          { orderId: mOrder.id },
          { routingKey: 'save-external-source-after-created-order' },
        );
      }

      // Send facebook initiate checkout event
      if (order.fbScopedUserId && order.status === OrderStatus.New) {
        await this.amqpConnection.publish(
          'facebook-bot',
          'send-custom-event-when-fb-order-created',
          { id: mOrder.id },
        );
      }

      return mOrder;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log(`create order error`, error, StringUtils.getString(error));
      const mError = error?.response?.data ?? error?.detail ?? error;
      throw new BadRequestException(mError);
    } finally {
      await queryRunner.release();
    }
  }

  async updateProjectOrdersCount(projectId: number, count: number) {
    return;
    return await this.prjOrdersCountRepo.update({ projectId }, { count }).then(async result => {
      if (!result.affected)
        await this.prjOrdersCountRepo.save(
          plainToInstance(ProjectOrdersCount, {
            projectId,
            count,
          }),
        );
    });
  }

  async getOrderProducts(
    products: OrderProductDto[],
    countryId: number,
    orderId?: number,
    userId?: number,
  ): Promise<OrderProduct[]> {
    const variantIds = products.map(item => item.productId);
    const reducedVariants: Record<number, OrderProductDto> = reduce(
      products,
      (prev, item) => {
        prev[item.productId] = item;
        return prev;
      },
      {},
    );

    let mVariants: ProductVariant[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'catalog-service-variants',
        routingKey: 'find-variant-by-ids',
        payload: { ids: variantIds, countryId },
        timeout: 5000,
      });
      mVariants = data as ProductVariant[];
      // console.log(`mVariants`, mVariants);
    } catch (error) {
      console.log(`error at find variant by ids`, StringUtils.getString(error));
      throw new BadRequestException(error);
    }

    const mProducts = map(mVariants, item => {
      const product = new OrderProduct();
      if (orderId) product.orderId = orderId;
      product.productId = item.id;
      product.productDetail = instanceToPlain(item);
      product.quantity = reducedVariants[item.id].quantity;
      product.price = item.priceByCountry || 0;
      product.editedPrice =
        reducedVariants[item.id].price && item.priceByCountry !== reducedVariants[item.id].price
          ? reducedVariants[item.id].price
          : undefined;
      product.priceEditedBy = product.editedPrice ? userId : undefined;
      return product;
    });
    // console.log(`order products`, mProducts);

    return mProducts;
  }

  async updateCustomer(
    name: string,
    phone: string,
    address: string,
    addressNote: string,
    ward: Ward,
    district: District,
    province: Province,
    countryId: number,
    creatorId?: number,
    postCode?: string,
    customerFbGlobalId?: string,
    customerFbScopedUserId?: string,
    companyId?: number,
  ) {
    const customer = plainToInstance(Customer, {
      name,
      phone,
      address,
      addressNote,
      fbGlobalId: customerFbGlobalId,
      fbScopedUserId: customerFbScopedUserId,
      postCode,
      districtId: district.id,
      provinceId: province.id,
      countryId,
      creatorId,
      companyId,
    });
    customer.fullAddress = `${address}`;
    if (ward) {
      customer.wardId = ward.id;
      customer.fullAddress += `, ${ward.name}`;
    }
    customer.fullAddress += `, ${district.name}, ${province.name}`;
    if (postCode) customer.fullAddress += `, ${postCode}`;

    const criteria: FindConditions<Customer> = { phone, countryId, companyId };
    if (customerFbGlobalId) criteria.fbGlobalId = customerFbGlobalId;
    const uResult = await this.customerRepo.update(
      criteria,
      pickBy(customer, v => v !== undefined),
    );
    if (!uResult.affected) {
      this.customerRepo.save(customer).catch(reason => {
        console.log(`save customer error`, reason);
        return;
      });
    }
  }

  async getOrderMessages(
    id: number,
    currentCount: number,
    request: Record<string, any>,
  ): Promise<Record<string, any>> {
    const order = await this.orderRepository.findOne(
      { id },
      { select: ['pancakeConversationId', 'countryId'] },
    );
    if (!order?.pancakeConversationId) {
      throw new NotFoundException('Cuộc trò chuyện không tồn tại');
    }
    const [{ data: country }, { data: access_token }] = await Promise.all([
      this.amqpConnection.request<{ data: ICountry }>({
        exchange: 'identity-service-countries',
        routingKey: 'get-country-by-id',
        timeout: 3000,
        payload: {
          id: order.countryId,
          companyId: request.user.companyId,
        },
      }),
      this.amqpConnection.request({
        exchange: 'crawl-service',
        routingKey: 'get-pancake-token',
        timeout: 10000,
      }),
    ]);
    const res = await axios.get(`https://pages.fm/api/v1/shops/${country.pancakeShopId}/messages`, {
      params: {
        order_id: order.pancakeConversationId,
        current_count: currentCount,
        access_token,
      },
    });
    return res.data;
  }

  async getOrder(id: string, request?: Record<string, any>) {
    const companyId = request?.user?.companyId;
    const qb = this.orderRepository
      .createQueryBuilder('o')
      .andWhere('(o.id::TEXT = :id OR o.displayId = :id OR o.ffmDisplayId = :id)', { id })
      .andWhere('o.companyId = :companyId', { companyId })
      .leftJoinAndSelect('o.products', 'products')
      .leftJoinAndSelect('o.printNotes', 'printNotes')
      .leftJoinAndSelect('o.carrier', 'carrier')
      .leftJoinAndSelect('o.externalSource', 'es')
      .leftJoinAndSelect('o.tags', 'tags', 'tags.status = :status', {
        status: TagStatus.active,
      })
      .leftJoinAndSelect('o.cancelReasons', 'cancelReasons', 'cancelReasons.status = :status', {
        status: CancelReasonStatus.active,
      })
      .leftJoin('o.possibleDuplicateOrders', 'possibleDuplicateOrders')
      .addSelect([
        'possibleDuplicateOrders.id',
        'possibleDuplicateOrders.possibleDuplicateOrderDisplayId',
        'possibleDuplicateOrders.possibleDuplicateOrderId',
      ])
      .leftJoin('order_sources_closure', 'osc', 'osc.id_descendant = o.external_source_id')
      .leftJoinAndMapMany('o.externalSources', OrderSource, 'oes', 'osc.id_ancestor = oes.id');
    qb.leftJoin(
      'conversations',
      'con',
      `con.page_id = o.page_id AND con.scoped_user_id = o.fb_scoped_user_id AND con.feed_id = ''`,
    ).addSelect('con.created_at', 'conversation_created_at');
    const order = await qb.getOne();
    let orderSource: Order = null;
    if (order.sourceId) {
      orderSource = await this.orderRepository.findOne({ where: { id: order.sourceId } });
      order.sourceDisplayId = orderSource?.displayId;
    }
    let adId = '';
    const orderSourceAd = order.externalSources.find(it => it.entity === SourceEntity.fb_ad);
    if (orderSourceAd) adId = orderSourceAd.entityId;
    else if (!orderSourceAd && order.fbScopedUserId) {
      const fbScopedUSerSource = await this.externalSourceRepo.findOne({
        entity: SourceEntity.fb_scoped_user,
        entityId: order.fbScopedUserId,
      });
      if (fbScopedUSerSource) {
        const fbAdSource = await this.externalSourceRepo.findOne({
          id: fbScopedUSerSource.parentId,
          entity: SourceEntity.fb_ad,
        });
        if (fbAdSource) adId = fbAdSource.entityId;
      }
    }
    order.adId = adId;
    const dupOrderIds = order.possibleDuplicateOrders.map(it => it.possibleDuplicateOrderId);
    let dupOrdersMap: Record<string, any> = {};
    if (dupOrderIds.length > 0) {
      const dupOrders = await this.orderRepository
        .createQueryBuilder('o')
        .where('o.id IN (:...dupOrderIds)', { dupOrderIds })
        .select(['o.id', 'o.status'])
        .getMany();
      dupOrdersMap = dupOrders.reduce((prev, next) => {
        prev[next.id] = next['status'];
        return prev;
      }, {});
    }
    order.possibleDuplicateOrders = order.possibleDuplicateOrders.map(it => {
      it.possibleDuplicateOrderStatus = OrderStatus[dupOrdersMap[it.possibleDuplicateOrderId]];
      return it;
    });
    return order;
  }

  async getDuplicateLeadsById(orderId, request?: Record<string, any>) {
    const order: Order = await this.orderRepository
      .createQueryBuilder('o')
      .where('o.id = :orderId')
      .leftJoinAndSelect('o.products', 'products')
      .setParameters({ orderId })
      .getOne();
    if (!order) throw new NotFoundException(`getDuplicateLeadsById not found order id ${orderId}`);
    if (isEmpty(order.products)) return { order, possibleDuplicateOrders: null };

    const productIds = order.products.map(it => it.productId);
    const qb = this.orderRepository
      .createQueryBuilder('o')
      .leftJoinAndSelect('o.products', 'products')
      .where('o.id != :id')
      .andWhere('o.status NOT IN (:...statuses)')
      .andWhere('o.companyId = :companyId')
      .andWhere('o.projectId = :projectId')
      .andWhere('o.countryId = :countryId')
      .andWhere('o.customerPhone = :phone')
      .andWhere('products.productId IN (:...productIds)')
      .setParameters({
        id: order.id,
        statuses: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES,
        companyId: order.companyId,
        projectId: order.projectId,
        countryId: order.countryId,
        phone: order.customerPhone,
        productIds,
      })
      .select(['o.id']);
    const raw = await qb.getMany();
    const ids = raw.map(i => i.id);
    if (ids.length === 0) return { order, possibleDuplicateOrders: null };
    const possibleDuplicateOrders = await this.orderRepository
      .createQueryBuilder('o')
      .leftJoinAndSelect('o.products', 'products')
      .where('o.id IN (:...ids)')
      .setParameters({ ids })
      .getMany();
    return { order, possibleDuplicateOrders };
  }

  async updateOrder(
    orderId: number,
    updateData: UpdateOrderDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Order> {
    const redisKey = `updating-ag-sale-order.${orderId}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 60, 'NX')
      .incr(redisKey)
      .exec();
    if (isUpdating > 1)
      throw new BadRequestException(
        { isUpdating },
        `This order is updating by another request. Please try again later.`,
      );

    let result: Order,
      isNeedToScanForDuplicates = false,
      isNeedToSyncStatusToFfm = false,
      syncOrderToFfmError: Record<string, any>;
    const changes: Partial<Order> = {};

    const queryRunner = getConnection(orderConnection).createQueryRunner('master');
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const countryId = headers['country-ids'];
      const companyId = request?.user?.companyId;
      const updatedByUserId = request?.user?.id;
      const oldOrder: Order = await queryRunner.manager
        .createQueryBuilder(Order, 'o')
        .where('o.id = :orderId')
        .andWhere('o.companyId = :companyId')
        .leftJoinAndSelect('o.products', 'products')
        .leftJoinAndSelect('o.tags', 'tags')
        .leftJoinAndSelect('o.cancelReasons', 'cancelReasons')
        .leftJoinAndSelect('o.carrier', 'carrier')
        .leftJoinAndSelect('o.printNotes', 'printNotes')
        .leftJoin('o.possibleDuplicateOrders', 'possibleDuplicateOrders')
        .addSelect([
          'possibleDuplicateOrders.possibleDuplicateOrderId',
          'possibleDuplicateOrders.possibleDuplicateOrderDisplayId',
        ])
        .leftJoin('order_sources_closure', 'osc', 'osc.id_descendant = o.external_source_id')
        .leftJoinAndMapMany('o.externalSources', OrderSource, 'oes', 'osc.id_ancestor = oes.id')
        .setParameters({ orderId, companyId })
        .getOne();

      if (!oldOrder) throw new NotFoundException(`Not found order id ${orderId}`);
      let metadata = JSON.parse(oldOrder.metadata);
      let metadataTagLog = JSON.parse(oldOrder.metadataTagLog);
      let metadataCarrierLog = JSON.parse(oldOrder.metadataCarrierLog);
      if (!metadata || (typeof metadata === 'object' && Object.keys.length === 0)) metadata = {};
      if (!metadataTagLog || (typeof metadataTagLog === 'object' && Object.keys.length === 0))
        metadataTagLog = {};

      // Check if the order has possible duplicates, then only allow updating status to canceled
      const hasPossibleDuplicates =
        !isEmpty(oldOrder.possibleDuplicateOrders) && oldOrder.ignoreDuplicateWarning === false;

      if (hasPossibleDuplicates) {
        // // Check if there are possible duplicates
        // const {
        //   status,
        //   cancelReasonIds,
        //   cancelReasonText,
        //   ignoreDuplicateWarning,
        //   tagIds,
        //   additionalTagIds,
        //   ...rest
        // } = updateData;
        // // Omit the 'status', 'cancelReasonIds', 'tagIds', 'additionalTagIds' and 'ignoreDuplicateWarning' properties from the 'updateData' object
        // // and assign the remaining properties to the 'dataToCheck' object
        // // Check if the 'rest' object is not empty
        // const isRestUpdateDataEmpty = Object.keys(rest).length === 0;
        // // Check if the 'status' is not null and 'Canceled'
        // const isUpdateAllowed =
        //   isNil(status) || (!isNil(status) && status === OrderStatus.Canceled);
        // if (!isRestUpdateDataEmpty || !isUpdateAllowed) {
        //   // If either of the conditions are not true, then throw a ForbiddenException
        //   throw new ForbiddenException({
        //     code: ErrorCode.ORD_0003,
        //     message: 'Not allowed to update this order until it has been marked as non-duplicated.',
        //   });
        // }
      } else {
        delete updateData.ignoreDuplicateWarning;
      }
      const projectIdUpdate = updateData.projectId;
      const updatableProps = UPDATABLE_PROPS[oldOrder.status];
      const updateProps = Object.keys(updateData) as (keyof UpdateOrderDto)[];
      for (const prop of updateProps) {
        if (!updatableProps.includes(prop)) delete updateData[prop];
      }
      if (isEmpty(updateData)) return oldOrder;

      if (
        !isNil(updateData.status) &&
        (oldOrder.status !== updateData.status || // normal case
          !oldOrder.ffmDisplayId) // case when order is confirmed -> awaiting stock but not yet synced to ffm
      ) {
        if ([OrderStatus.New, OrderStatus.Confirmed].includes(updateData.status)) {
          for (const prop of NEW_ORDER_REQUIRED_PROPS) {
            // Handle when the required props are an array, then one of each prop item value must be not empty
            if (isArray(prop)) {
              let requiredPropValue;
              for (const p of prop) {
                if (!isNil(oldOrder[p]) && (isNumber(oldOrder[p]) || !isEmpty(oldOrder[p])))
                  requiredPropValue = oldOrder[p];
                else if (
                  !isNil(updateData[p]) &&
                  (isNumber(updateData[p]) || !isEmpty(updateData[p]))
                )
                  requiredPropValue = updateData[p];
                if (requiredPropValue) break;
              }

              if (isNil(requiredPropValue))
                throw new BadRequestException(`One of ${prop.join(' or ')} should not be empty`);
            } else {
              let requiredPropValue;
              if (!isNil(oldOrder[prop]) && (isNumber(oldOrder[prop]) || !isEmpty(oldOrder[prop])))
                requiredPropValue = oldOrder[prop];
              else if (
                !isNil(updateData[prop]) &&
                (isNumber(updateData[prop]) || !isEmpty(updateData[prop]))
              )
                requiredPropValue = updateData[prop];

              if (isNil(requiredPropValue))
                throw new BadRequestException(`${prop} should not be empty`);
            }
          }
        }
        if (
          Boolean(oldOrder.ffmDisplayId) &&
          !ALLOWED_STATUSES_TO_UPDATE_WHEN_USING_FFM[oldOrder.status]?.includes(updateData.status)
        ) {
          throw new ForbiddenException({
            code: ErrorCode.ORD_0004,
            message: `Không thể chuyển trạng thái đơn từ "${
              PlainOrderStatus[oldOrder.status]
            }" -> "${PlainOrderStatus[updateData.status]}" của dự án đã kết nối Fulfillment`,
          });
        }

        const nextStatuses = NEXT_STATUS_BY_USER[oldOrder.status];
        if (!nextStatuses || !nextStatuses.includes(updateData.status)) {
          throw new ForbiddenException(
            `Cập nhật trạng thái đơn không hợp lệ. Không thể chuyển trạng thái đơn từ "${
              PlainOrderStatus[oldOrder.status]
            }" -> "${PlainOrderStatus[updateData.status]}"`,
          );
        }

        if (
          [OrderStatus.Canceled, OrderStatus.FailedDelivery].includes(updateData.status) &&
          isEmpty(updateData.cancelReasonIds) &&
          isEmpty(updateData.cancelReasonText)
        ) {
          throw new BadRequestException(
            `Bạn chưa chọn lý do ${PlainOrderStatus[updateData.status]}`,
          );
        }
      }

      const newOrder = plainToClass(Order, {
        ...oldOrder,
        ...omit(updateData, ['products']),
      });
      if (newOrder.marketerId) {
        metadata = { ...metadata, mktId: newOrder.marketerId };
      }

      const { sourceType, sourceId } = updateData;

      const postCode: string =
        !isUndefined(updateData.postCode) &&
        Array.isArray(updateData.postCode) &&
        updateData.postCode?.[0]
          ? updateData.postCode[0]
          : (updateData.postCode as string);
      if (postCode && postCode !== newOrder.postCode) newOrder.postCode = postCode;
      if (updateData.saleId || updateData.carePageId) {
        const teamInCharge = newOrder.teamInCharge;
        const oldSaleRep = oldOrder.saleId || oldOrder.carePageId;
        const newSaleRep = updateData.saleId || updateData.carePageId;
        if (newSaleRep !== oldSaleRep) {
          if (teamInCharge === TeamInCharge.CarePage) {
            newOrder.saleId = null;
            newOrder.carePageId = newSaleRep;
          } else {
            newOrder.saleId = newSaleRep;
            newOrder.carePageId = null;
          }
          metadata = {
            ...metadata,
            repsId: newSaleRep,
          };
        }
      }

      if (!isNil(sourceType) && sourceId) {
        metadata = {
          ...metadata,
          sourceType,
        };
        const insertExtSource = await this.externalSourceRepo
          .createQueryBuilder()
          .insert()
          .into(OrderSource)
          .values({ entity: sourceType, entityId: String(sourceId) })
          .orUpdate(['updated_at'], ['entity', 'entity_id'])
          .execute();
        const extSourceId = insertExtSource?.identifiers?.[0]?.id;

        if (!extSourceId) throw new BadRequestException(`Nguồn đơn không hợp lệ`);
        if (sourceType === SourceEntity.landing_page) {
          const landingPage = await queryRunner.manager.getRepository(LandingPage).findOne({
            where: { id: String(sourceId) },
          });
          newOrder.type = landingPage?.orderType || OrderType.normal;
        }
        if (sourceType === SourceEntity.fb_page) {
          const { data: pData } = await this.amqpConnection.request({
            exchange: 'facebook-bot',
            routingKey: 'get-fanpages-by-ids',
            payload: { ids: [String(sourceId)] },
            timeout: 10000,
          });
          const pages = pData as FanPage[];
          newOrder.type = pages[0]?.orderType || OrderType.normal;
        }
        if (isNil(newOrder.externalSourceId) || newOrder.externalSourceId !== extSourceId) {
          newOrder.externalSourceId = extSourceId;
          newOrder.externalSources = await this.externalSourceRepo
            .createQueryBuilder('es')
            .innerJoin(
              'order_sources_closure',
              'osc',
              'osc.id_ancestor = es.id AND osc.id_descendant = :esId',
            )
            .setParameters({ esId: extSourceId })
            .getMany();
        }
        metadata = {
          ...metadata,
          orderSourceType: sourceType,
          orderSourceId: extSourceId,
        };
      }

      if (!isNil(updateData.customerPhone) && oldOrder.customerPhone !== updateData.customerPhone) {
        isNeedToScanForDuplicates = true;
      }

      const provinceId = updateData.addressProvinceId || oldOrder.addressProvinceId;
      const districtId = updateData.addressDistrictId || oldOrder.addressDistrictId;
      const wardId = updateData.addressWardId || oldOrder.addressWardId;

      if (
        districtId &&
        provinceId &&
        (!oldOrder.addressDistrict ||
          !oldOrder.addressProvince ||
          oldOrder.addressDistrictId !== districtId ||
          oldOrder.addressProvinceId !== provinceId)
      ) {
        const [ward, district, province] = await Promise.all([
          this.locationService.getWard(wardId, { where: { districtId } }),
          this.locationService.getDistrict(districtId),
          this.locationService.getProvince(provinceId),
        ]);
        if ((wardId && !ward) || !district || !province)
          throw new NotFoundException('Địa chỉ không hợp lệ');

        if (ward) newOrder.addressWard = ward.name;
        newOrder.addressDistrict = district.name;
        newOrder.addressProvince = province.name;

        await this.updateCustomer(
          newOrder.customerName,
          newOrder.customerPhone,
          newOrder.addressText,
          newOrder.addressNote,
          ward,
          district,
          province,
          Number(countryId),
          updatedByUserId,
          postCode,
          undefined,
          undefined,
          companyId,
        );
      }
      if (provinceId && oldOrder.addressProvinceId !== provinceId) {
        const province = await this.locationService.getProvince(provinceId);
        newOrder.addressProvince = province?.name;
      }
      if (districtId && oldOrder.addressDistrictId !== districtId) {
        const district = await this.locationService.getDistrict(districtId);
        newOrder.addressDistrict = district?.name;
      }
      if (wardId && oldOrder.addressWardId !== wardId) {
        const ward = await this.locationService.getWard(wardId, { where: { districtId } });
        newOrder.addressWard = ward?.name;
      }
      metadata = {
        ...metadata,
        customer: {
          customerName: newOrder.customerName,
          customerPhone: newOrder.customerPhone,
          addressText: newOrder.addressText,
          addressDistrictId: newOrder.addressDistrictId || '',
          addressDistrict: newOrder.addressDistrict || '',
          addressProvinceId: newOrder.addressProvinceId || '',
          addressProvince: newOrder.addressProvince || '',
          addressWardId: newOrder.addressWardId || '',
          addressWard: newOrder.addressWard || '',
          postCode: newOrder?.postCode?.toString() || '',
        },
      };

      if (!isNil(updateData.tagIds)) {
        const mTags = await this.tagService.findByIds(updateData.tagIds, TagType.order);
        newOrder.tags = mTags;
        const tags = [];

        for (const tag of updateData.tagIds) {
          const isExist = mTags.filter(it => it.id === tag);
          if (isExist.length > 0) tags.push(tag);
        }
        newOrder.metadataTagLog = JSON.stringify({ ...metadataTagLog, tags });
      }

      if (!isNil(updateData.additionalTagIds)) {
        const oldTagIds = newOrder.tags.map(it => it.id);
        const updateTagIds = uniq([...oldTagIds, ...updateData.additionalTagIds]);
        const mTags = await this.tagService.findByIds(updateTagIds, TagType.order);
        newOrder.tags = mTags;
      }
      if (!isNil(updateData.removeTagIds)) {
        const oldTagIds = newOrder.tags.map(it => it.id);
        const updateTagIds = difference(oldTagIds, updateData.removeTagIds);
        if (oldTagIds.length === updateTagIds.length) {
          throw new BadRequestException('order_not_has_tags');
        }
        const mTags = await this.tagService.findByIds(updateTagIds, TagType.order);
        newOrder.tags = mTags;
      }

      if (!isNil(updateData.cancelReasonIds)) {
        const mReasons = await this.reasonsService.findByIds(updateData.cancelReasonIds);
        newOrder.cancelReasons = mReasons;
      }

      if (!isNil(updateData.printNoteIds)) {
        const notes = await this.printNotesService.findByIds(updateData.printNoteIds);
        newOrder.printNotes = notes;
      }

      let removeProducts: OrderProduct[] = [];
      if (!isNil(updateData.products)) {
        const oldProductIds: number[] = [];
        newOrder.products = newOrder.products.map(product => {
          oldProductIds.push(product.productId);
          const idx = updateData.products.findIndex(it => it.productId === product.productId);
          if (idx === -1) return product;

          const item = updateData.products[idx];
          if (item.price) {
            product.editedPrice = item.price;
            product.priceEditedBy = updatedByUserId;
          }
          if (item.quantity) product.quantity = item.quantity;
          return product;
        });

        const addProducts = updateData.products.filter(it => !oldProductIds.includes(it.productId));
        const newProducts = await this.getOrderProducts(
          addProducts,
          Number(countryId),
          newOrder.id,
          updatedByUserId,
        );

        removeProducts = remove(newOrder.products, item => {
          const index = findIndex(updateData.products, obj => obj.productId === item.productId);
          return index === -1;
        });
        // console.log(`removeProducts`, removeProducts);
        // console.log(`mOrder.products`, mOrder.products);

        const uProducts = [...newOrder.products, ...newProducts];

        const metadataProduct = [];
        uProducts.map(product => {
          metadataProduct.push({
            id: product.productId,
            sku: product?.productDetail?.sku,
            name: product?.productDetail?.product?.name,
            quantity: product.quantity,
            price: product.price,
            weight: product?.productDetail?.weight,
          });
        });

        newOrder.products = uProducts;

        newOrder.totalPrice = sumBy(uProducts, o => (o.editedPrice || o.price) * o.quantity);
        const totalWeight = sumBy(uProducts, o => o?.productDetail?.weight * o.quantity);
        if (newOrder.discount) newOrder.discount = Math.min(newOrder.discount, newOrder.totalPrice);
        // this.orderProductsRepo.save(uProducts);
        newOrder.metadata = JSON.stringify({ ...metadata, products: metadataProduct });
        metadata = {
          ...metadata,
          products: metadataProduct,
          totalWeight,
          fee: {
            totalPrice: newOrder?.totalPrice || 0,
            shippingFee: newOrder?.shippingFee || 0,
            discount: newOrder?.discount || 0,
            surcharge: newOrder?.surcharge || 0,
            paid: newOrder?.paid || 0,
            cod: Math.max(
              Number(newOrder?.totalPrice || 0) +
                Number(newOrder?.shippingFee || 0) +
                (newOrder?.surcharge || 0) -
                (newOrder?.discount || 0) -
                (newOrder?.paid || 0),
              0,
            ),
          },
        };
        if (!isEmpty(addProducts) || !isEmpty(removeProducts)) isNeedToScanForDuplicates = true;
      }

      if (updateData?.carrier) {
        newOrder.carrier = plainToInstance(OrderCarrier, {
          ...updateData?.carrier,
          orderId,
        });
        const metadataCarrier = {
          waybillNote: updateData?.carrier?.waybillNote || '',
          carrierCode: updateData?.carrier?.carrierCode || '',
          waybillNumber: updateData?.carrier?.waybillNumber || '',
          customerEDD: updateData?.carrier?.customerEDD || null,
          carrierEDD: updateData?.carrier?.carrierEDD || null,
        };
        newOrder.metadataCarrierLog = JSON.stringify(metadataCarrier);
        metadataCarrierLog = metadataCarrier;
      }

      newOrder.lastUpdatedBy = updatedByUserId;

      if (!isNil(updateData.status) && oldOrder.status !== newOrder.status) {
        if (oldOrder.status === OrderStatus.Draft && newOrder.status === OrderStatus.Confirmed) {
          // user want to change status draft ======> confirmed
          // then auto update sequently draft => new => confirmed instead
          newOrder.status = OrderStatus.New;
        }
        changes.status = newOrder.status;

        // Get market of the order to know whether it uses fulfillment partner
        const { data: market } = await this.amqpConnection.request({
          exchange: 'identity-service-projects',
          routingKey: 'find-market',
          payload: {
            companyId: oldOrder.companyId,
            projectId: oldOrder.projectId,
            countryId: oldOrder.countryId,
          } as FindCondition<Market>,
          timeout: 10000,
        });
        newOrder.market = market as Market;

        if (
          oldOrder.status === OrderStatus.AwaitingStock &&
          newOrder.status === OrderStatus.Confirmed
        ) {
          if (
            !newOrder?.market?.fulfillmentPartnerClientId ||
            !newOrder.market?.fulfillmentPartnerId
          ) {
            throw new BadRequestException({
              code: ErrorCode.ORD_0013,
              message: 'Sync đơn sang AGFFM nhưng dự án chưa được kết nối với AGFFM',
            });
          }
        }

        if (newOrder.status === OrderStatus.Confirmed) {
          if (isNil(newOrder.confirmedAt)) newOrder.confirmedAt = new Date();
          if (
            !newOrder.market?.fulfillmentPartnerClientId &&
            !newOrder.market?.fulfillmentPartnerId
          ) {
            newOrder.status = OrderStatus.AwaitingStock;
          } else if (!oldOrder.ffmDisplayId) {
            // Sync order to FFM
            const rawResponse = await this.ffmOrdersService.syncOrder(newOrder);
            const { response, error } = rawResponse;
            // console.log(`rawResponse`, rawResponse);
            if (error || !response?.data) {
              const mError = error?.response?.data ?? error?.detail ?? error;
              // throw new BadRequestException(mError);
              syncOrderToFfmError = mError;
            }

            const syncedFfmOrder = response?.data as FfmOrder;
            console.log(`synced ffm order`, syncedFfmOrder);
            // mOrder.ffmDisplayId = syncedFfmOrder.so;
            // mOrder.ffmCompanyId = mOrder.market.fulfillmentPartnerId;
            // mOrder.ffmPartnerClientId = mOrder.market.fulfillmentPartnerClientId;
            delete newOrder.status;
          }
        }

        isNeedToSyncStatusToFfm =
          Boolean(oldOrder.ffmDisplayId) &&
          AG_ORDER_STATUSES_CAN_BE_SYNC_TO_FFM.includes(updateData.status);
      }

      if (!isEmpty(removeProducts)) {
        await queryRunner.manager.remove(removeProducts);
      }
      if (
        newOrder.ignoreDuplicateWarning ||
        (updateData.ignoreDuplicateIds && updateData.ignoreDuplicateIds.length > 0)
      ) {
        const orderDisplayIds = oldOrder?.possibleDuplicateOrders
          ? oldOrder?.possibleDuplicateOrders.map(order => {
              return {
                orderId: order.possibleDuplicateOrderId,
                displayId: order.possibleDuplicateOrderDisplayId,
              };
            })
          : [];

        await Promise.all([
          queryRunner.manager.delete(PossibleDuplicateOrder, { orderId: newOrder.id }),
          queryRunner.manager.delete(PossibleDuplicateOrder, {
            possibleDuplicateOrderId: newOrder.id,
          }),
          ...(orderDisplayIds.length > 0
            ? [
                queryRunner.manager.save([
                  plainToInstance(SystemLog, {
                    tableName: 'orders',
                    action: 'REMOVE_DUPLICATE',
                    recordId: orderId,
                    changes: [
                      'order_id',
                      oldOrder.displayId,
                      'order_duplicate_display_ids',
                      orderDisplayIds.map(i => i.displayId).join(','),
                    ],
                    creatorId: request?.user?.id,
                  }),
                  // thêm logs remove duplicate order ở chiều còn lại
                  ...orderDisplayIds.map(order =>
                    plainToInstance(SystemLog, {
                      tableName: 'orders',
                      action: 'REMOVE_DUPLICATE',
                      recordId: order.orderId,
                      changes: [
                        'order_id',
                        order.displayId,
                        'order_duplicate_display_ids',
                        oldOrder.displayId,
                      ],
                      creatorId: request?.user?.id,
                    }),
                  ),
                  // thêm record vào bảng possible_duplicate_orders cả 2 chiều
                  ...orderDisplayIds.map(order =>
                    plainToInstance(RemovedDuplicateOrders, {
                      orderId: newOrder.id,
                      duplicateOrderId: order.orderId,
                    }),
                  ),
                  ...orderDisplayIds.map(order =>
                    plainToInstance(RemovedDuplicateOrders, {
                      orderId: order.orderId,
                      duplicateOrderId: newOrder.id,
                    }),
                  ),
                ]),
              ]
            : []),
        ]);
      }

      newOrder.updatedAt = new Date();
      delete newOrder.possibleDuplicateOrders;
      // add updated time of lead
      const lead = await queryRunner.manager.findOne(Lead, { orderId: newOrder.id });
      if (lead) {
        lead.updatedAt = newOrder.updatedAt;
        // update project when update lead aftersales
        if (
          projectIdUpdate &&
          projectIdUpdate !== oldOrder.projectId &&
          lead.leadType === LeadType.after_sale
        ) {
          newOrder.projectId = projectIdUpdate;
          lead.projectId = projectIdUpdate;
        }
        await queryRunner.manager.save(lead);
      }
      newOrder.metadata = JSON.stringify(metadata);
      result = await queryRunner.manager.save(newOrder);

      // if order has already been created in FFM partner, then it must be sync to FFM again with new data.
      if (
        Boolean(oldOrder.ffmDisplayId) &&
        !isEmpty(pick(updateData, AG_TO_FFM_SYNCHRONIZE_PROPS[oldOrder.status]))
      ) {
        if (!newOrder.market) {
          const { data: market } = await this.amqpConnection.request({
            exchange: 'identity-service-projects',
            routingKey: 'find-market',
            payload: {
              companyId: oldOrder.companyId,
              projectId: oldOrder.projectId,
              countryId: oldOrder.countryId,
            } as FindCondition<Market>,
            timeout: 10000,
          });
          newOrder.market = market as Market;
        }
        const raw = await this.ffmOrdersService.syncOrder(newOrder);
        const { response, error } = raw;
        if (error || !response?.data) {
          const mError = error?.response?.data ?? error?.detail ?? error;
          throw new BadRequestException({ ...mError, description: 'Sync order to FFM error' });
        }
        console.log(`sync order to ffm response`, response?.data);
      }

      if (isNeedToSyncStatusToFfm) {
        const raw = await this.ffmOrdersService.updateFfmOrderStatus(newOrder);
        const { response, error } = raw;
        if (error || !response?.data) {
          const mError = error?.response?.data ?? error?.detail ?? error;
          throw new BadRequestException({
            ...mError,
            description: 'Sync order status to FFM error',
          });
        }
        console.log(`sync order status to ffm response`, response?.data);
      }

      await queryRunner.commitTransaction();

      await this.amqpConnection.publish('order-service', 'after-order-update', {
        id: result.id,
        updatedAt: result.updatedAt,
        updatedBy: result.lastUpdatedBy,
        changes,
      });

      // Send message to warehouses services
      await this.amqpConnection.sendMessage('OrderService.Orders.StatusChanged', null, {
        orderId,
        orderDisplayId: newOrder.displayId,
        products: newOrder.products,
        preOrderStatus: newOrder.status,
        orderStatus: newOrder.status,
        warehouseId: newOrder.countryId,
        companyId: newOrder.companyId,
        ffmDisplayId: newOrder.ffmDisplayId,
      });

      const isNeedToSendFbReceipt =
        !oldOrder.confirmedAt &&
        newOrder.confirmedAt &&
        oldOrder.fbScopedUserId &&
        !oldOrder.fbReceiptMessageId;

      if (isNeedToSendFbReceipt) {
        await Promise.all([
          this.amqpConnection.publish('facebook-bot', 'order-confirmed', { id: orderId }),
          this.amqpConnection.publish('facebook-bot', 'send-custom-event-when-fb-order-confirmed', {
            id: orderId,
          }),
          this.amqpConnection.publish('facebook-bot', 'send-pixel-event-when-fb-order-confirmed', {
            id: orderId,
          }),
        ]);
      }

      // Send facebook initiate checkout event
      if (newOrder.fbScopedUserId && !isNil(changes.status) && changes.status === OrderStatus.New) {
        await this.amqpConnection.publish(
          'facebook-bot',
          'send-custom-event-when-fb-order-created',
          { id: newOrder.id },
        );
      }

      if (newOrder.ignoreDuplicateWarning) {
        await Promise.all([
          this.amqpConnection.publish('order-service', 'ignore-duplicate-orders-warning', {
            id: newOrder.id,
          }),
          this.amqpConnection.publish('order-service', 'ignore-duplicate-leads-warning', {
            orderId: newOrder.id,
            updatedAt: newOrder.updatedAt,
          }),
        ]);
      }

      if (newOrder.ignoreDuplicateWarning === false && isNeedToScanForDuplicates) {
        if (lead && lead.leadType === LeadType.after_sale) {
          await Promise.all([
            this.amqpConnection.publish('order-service', 'scan-possible-duplicate-orders', {
              id: newOrder.id,
            }),
            this.amqpConnection.publish(
              'order-service',
              'scan-possible-duplicate-leads-after-sale',
              {
                orderId: newOrder.id,
              },
            ),
          ]);
        } else {
          await Promise.all([
            this.amqpConnection.publish('order-service', 'scan-possible-duplicate-orders', {
              id: newOrder.id,
            }),
            this.amqpConnection.publish('order-service', 'scan-possible-duplicate-leads', {
              orderId: newOrder.id,
            }),
          ]);
        }
      }

      // Check possible duplicate orders when change status to New
      if (
        !isNil(updateData.status) &&
        oldOrder.status !== newOrder.status &&
        newOrder.status === OrderStatus.New
      ) {
        // Scan for possible duplicate orders
        await this.amqpConnection.publish('order-service', 'scan-possible-duplicate-orders', {
          id: newOrder.id,
        });
      }
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log(`update order ${orderId} raw error`, error);
      console.log(`update order ${orderId} error`, StringUtils.getString(error));

      if (error instanceof HttpException) throw error;

      const mError = error?.response?.data ?? error?.detail ?? error;
      throw new BadRequestException(mError);
    } finally {
      await queryRunner.release();
      await this.redis.del(redisKey);
    }

    if (syncOrderToFfmError) throw new BadRequestException(syncOrderToFfmError);

    if (result.status === OrderStatus.New && updateData.status === OrderStatus.Confirmed) {
      result = await this.updateOrder(orderId, { status: OrderStatus.Confirmed }, headers, request);
    }

    return result;
  }

  async getOrderHistoriesV2(orderId: number) {
    const logsRaw = await this.systemLogRepo.find({
      where: {
        tableName: 'orders',
        recordId: orderId,
      },
      order: { id: 'DESC' },
    });
    return logsRaw.map(it => {
      const newLog: Record<string, any> = { ...it };
      if (it.action === 'STATUS' && !it.creatorId) {
        const actor = this._getStatusChangeActorFromLogs(it, logsRaw);
        const reason = this._getStatusChangeReason(it, logsRaw);
        newLog['statusChangeActor'] = actor;
        newLog['statusChangeReason'] = reason;
      }
      if (it.action === 'UPDATE' && it.changes.includes('metadata_tag_log') && !it.creatorId) {
        const actor = this._getStatusChangeActorFromLogs(it, logsRaw);
        newLog['statusChangeActor'] = actor;
      }
      if (it.action === 'UPDATE' && it.changes.includes('metadata_carrier_log') && !it.creatorId) {
        const actor = this._getStatusChangeActorFromLogs(it, logsRaw);
        newLog['statusChangeActor'] = actor;
      }
      if (it.action === 'UPDATE' && it.changes.includes('metadata') && !it.creatorId) {
        const actor = this._getStatusChangeActorFromLogs(it, logsRaw);
        newLog['statusChangeActor'] = actor;
      }
      return newLog;
    });
  }

  _getStatusChangeReason(log: SystemLog, logs: SystemLog[]) {
    let reason = '';
    const indexOfLog = logs.findIndex(l => l.id === log.id);
    const limit = indexOfLog - 2 < 0 ? 0 : indexOfLog - 2;
    for (let i = indexOfLog; i >= limit; i--) {
      if (logs[i].action === 'UPDATE') {
        const idx = logs[i].changes.findIndex(l => l === 'status_change_reason');
        if (idx !== -1) {
          reason = logs[i].changes[idx + 1];
          break;
        }
      }
    }
    return reason;
  }

  _getStatusChangeActorFromLogs(log: SystemLog, logs: SystemLog[]) {
    let actor = '';
    const indexOfLog = logs.findIndex(l => l.id === log.id);
    for (let i = indexOfLog; i >= 0; i--) {
      if (logs[i].action === 'UPDATE') {
        const idx = logs[i].changes.findIndex(l => l === 'status_change_actor');
        if (idx !== -1) {
          actor = logs[i].changes[idx + 1];
          break;
        }
      }
    }
    if (!actor) {
      for (let i = indexOfLog + 1; i < logs.length; i++) {
        if (logs[i].action === 'UPDATE') {
          const idx = logs[i].changes.findIndex(l => l === 'status_change_actor');
          if (idx !== -1) {
            actor = logs[i].changes[idx + 1];
            break;
          }
        }
      }
    }
    return actor;
  }

  async getOrderHistories(orderId: number) {
    const mQuery = this.orderHistoryRepo.createQueryBuilder('o');
    mQuery.andWhere('o.order_id = :orderId', { orderId }).orderBy('id', 'ASC');
    const orderHistories = await mQuery.getMany();

    let logs = await this.systemLogRepo.find({
      where: {
        tableName: 'orders',
        recordId: orderId,
      },
    });
    logs = logs.filter(log => log.action === 'REMOVE_DUPLICATE');

    let histories = [...orderHistories, ...logs];
    histories = histories.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
    return histories;
  }

  @RabbitRPC({
    exchange: 'CatalogService.Orders.StatusChanged',
    routingKey: '',
    queue: 'order-service-order-status-changed',
    errorHandler: rmqErrorsHandler,
  })
  async onStockMovedMessage(payload) {
    console.log(`onStockMovedMessage`, payload);
    const { orderId, orderStatus } = payload || {};
    if (!orderId || !orderStatus) return new Nack(false);

    const order = await this.orderRepository.findOne(orderId, {
      relations: ['products'],
    });
    if (!order || order.ffmDisplayId || order.status === orderStatus) return new Nack(false);

    const result = await this.orderRepository.save({
      id: order.id,
      status: orderStatus,
      lastUpdateStatus: new Date(),
      lastUpdatedBy: null,
    });

    await this.amqpConnection.publish('order-service', 'after-order-update', {
      id: result.id,
      updatedAt: result.updatedAt,
      updatedBy: result.lastUpdatedBy,
      changes: { status: orderStatus },
    });

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'CatalogService.Orders.StockFilled',
    routingKey: '',
    queue: 'order-service-order-stock-filled',
    errorHandler: rmqErrorsHandler,
  })
  async onStockFilled(payload) {
    console.log(`onStockFilled`, payload);
    const { productIds, warehouseId } = payload || {};
    if (isEmpty(productIds) || !warehouseId) return new Nack(false);

    const qb = await this.getOrderQueryBuilder({
      productIds,
      status: [OrderStatus.AwaitingStock],
      countryIds: [warehouseId],
    });
    if (isNil(qb)) return new Nack(false);

    const orders = await qb.getMany();

    await Promise.all(
      orders.map(order =>
        this.amqpConnection.sendMessage('OrderService.Orders.StatusChanged', null, {
          orderId: order.id,
          products: order.products,
          preOrderStatus: order.status,
          orderStatus: OrderStatus.Confirmed,
          warehouseId: order.countryId,
          companyId: order.companyId,
          ffmDisplayId: order.ffmDisplayId,
        }),
      ),
    );

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'CatalogService.Orders.ReturnSheetCreated',
    routingKey: '',
    queue: 'order-service-order-return-sheet-created',
    errorHandler: rmqErrorsHandler,
  })
  async onOrderReturnedSheetCreated(payload) {
    const { orderId, returnSheetId } = payload;
    const products: OrderProduct[] = payload.products;

    const order = await this.orderRepository.findOne(orderId, {
      relations: ['products'],
    });
    if (!order || !isEmpty(order.products)) return new Nack(false);

    if (isEqual(order.products, products)) {
      const uResult = await this.orderRepository.update(
        { id: orderId },
        { isCheckedAfterReturn: true },
      );
      if (uResult.affected > 0) {
        // send message to catalog
        await this.amqpConnection.sendMessage('OrderService.Orders.ReturnSheetValidated', null, {
          returnSheetId,
        });
      }

      return new Nack(false);
    }

    return new Nack(false);
  }

  nextStatuses() {
    return NEXT_STATUS_BY_USER;
  }

  async scheduleSendFacebookPurchaseEventDaily() {
    const jobName = 'send-facebook-purchase-event';
    try {
      const repeatable = await this.orderQueue.getRepeatableJobs();
      for (const job1 of repeatable) {
        if (job1.id !== jobName) continue;
        await this.orderQueue.removeRepeatableByKey(job1.key);
      }
      await this.orderQueue.removeJobs(jobName);

      // const queue = await this.orderQueue.add(
      //   jobName,
      //   {},
      //   {
      //     attempts: 3,
      //     repeat: {
      //       cron: '0 0 * * *',
      //       tz: 'Asia/Ho_Chi_Minh',
      //     },
      //     jobId: jobName,
      //     removeOnComplete: true,
      //     removeOnFail: false,
      //   },
      // );
      // console.log(
      //   jobName +
      //     ' will run at ' +
      //     moment(queue.timestamp + queue.opts.delay)
      //       .tz('Asia/Ho_Chi_Minh')
      //       .format('DD/MM/yyyy HH:mm'),
      // );
    } catch (error) {
      console.log(`schedule send facebook purchase event err`, error);
    }
  }

  async scheduleScanPackingTimeJob() {
    const jobName = 'scan-packing-time-set-orders';
    try {
      const repeatable = await this.orderQueue.getRepeatableJobs();
      for (const job1 of repeatable) {
        if (job1.id !== jobName) {
          continue;
        }
        await this.orderQueue.removeRepeatableByKey(job1.key);
      }
      await this.orderQueue.removeJobs(jobName);
      // const queue = await this.orderQueue.add(
      //   jobName,
      //   {},
      //   {
      //     attempts: 3,
      //     repeat: {
      //       cron: '0 0 * * * *',
      //       tz: 'Asia/Ho_Chi_Minh',
      //     },
      //     jobId: jobName,
      //     removeOnComplete: true,
      //     removeOnFail: false,
      //   },
      // );
      // console.log(
      //   jobName +
      //     ' will run at ' +
      //     moment(queue.timestamp + queue.opts.delay)
      //       .tz('Asia/Ho_Chi_Minh')
      //       .format('DD/MM/yyyy HH:mm'),
      // );
    } catch (e) {
      console.log(e);
    }
  }

  async scheduleResetOrdersCounterJob() {
    const jobName = 'reset-orders-counter';
    try {
      const repeatable = await this.orderQueue.getRepeatableJobs();
      for (const job1 of repeatable) {
        if (job1.id !== jobName) {
          continue;
        }
        await this.orderQueue.removeRepeatableByKey(job1.key);
      }
      await this.orderQueue.removeJobs(jobName);
      // const queue = await this.orderQueue.add(
      //   jobName,
      //   {},
      //   {
      //     attempts: 3,
      //     repeat: {
      //       cron: '0 0 * * *',
      //       tz: 'Asia/Ho_Chi_Minh',
      //     },
      //     jobId: jobName,
      //     removeOnComplete: true,
      //     removeOnFail: false,
      //   },
      // );
      // console.log(
      //   jobName +
      //     ' will run at ' +
      //     moment(queue.timestamp + queue.opts.delay)
      //       .tz('Asia/Ho_Chi_Minh')
      //       .format('DD/MM/yyyy HH:mm'),
      // );
    } catch (e) {
      console.log(e);
    }
  }

  async scanPackingTimeSetOrders() {
    const mMoment = moment().tz('Asia/Ho_Chi_Minh');
    const from = mMoment.startOf('day').toDate();
    const to = mMoment.endOf('day').toDate();

    const updateData: QueryDeepPartialEntity<Order> = {
      status: OrderStatus.Preparing,
      lastUpdatedBy: null,
    };

    const results = await this.orderRepository.update(
      {
        packingAt: Between(from, to),
        status: In(ORDER_STATUSES_CAN_BE_AUTO_UPDATE_TO_WAIT_TO_PACK),
      },
      updateData,
    );

    console.log(`orders to be updated: `, results.affected);
  }

  async resetOrdersCounter() {
    // const results = await this.prjOrdersCountRepo.update({}, { count: 0 });
    // console.log(`orders counter has been reset`, results.affected);
  }

  async importExcel(buffer: Buffer) {
    const mapCol = {
      uniq: 'STT',
      id: 'Mã đơn hàng',
      customerName: 'Khách hàng',
      customerPhone: 'SĐT',
      addressText: 'Số nhà, ngõ/ngách, hẻm',
      addressWard: 'Phường/Xã',
      addressDistrict: 'Quận/Huyện',
      addressProvince: 'Tỉnh thành phố',
    };

    const data = ExcelUtils.read(buffer, 0, 'STT');

    const records = [];

    for (const item of take(data, 2)) {
      const order = new Order();
      order.id = item[mapCol.id];
      order.customerName = item[mapCol.customerName];
      order.customerPhone = item[mapCol.customerPhone];
      order.addressText = item[mapCol.addressText];
      order.addressWard = item[mapCol.addressWard];
      order.addressDistrict = item[mapCol.addressDistrict];
      order.addressProvince = item[mapCol.addressProvince];

      const address = await this.locationService.getLocationByName(
        order.addressWard,
        order.addressDistrict,
        order.addressProvince,
      );
      console.log(`address`, address);

      records.push({
        ...order,
        address,
      });
    }
    return records;
  }

  async updateMultipleOrders(
    data: UpdateMultipleOrdersDto,
    header: Record<string, string>,
    request: Record<string, any>,
  ) {
    if (isEmpty(data)) throw new BadRequestException('Thông tin cập nhật đơn hàng không hợp lệ');

    const { orderIds, ...rest } = data;

    const orders = await this.orderRepository.find({
      where: { id: In(orderIds) },
    });

    const validOrders = [];
    const invalidOrders = [];
    const orderGroups = chunk(orders, 5);

    for (const group of orderGroups) {
      await Promise.all(
        group.map(async order => {
          try {
            const updateData = plainToInstance(UpdateOrderDto, { ...rest });
            if (updateData.sourceId) updateData.sourceType = SourceEntity.other;
            const updatedOrder = await this.updateOrder(order.id, updateData, header, request);
            if (updatedOrder) validOrders.push({ orderId: order.displayId });
          } catch (error) {
            const reason = error.response?.response?.message
              ? isArray(error.response?.response?.message)
                ? error.response?.response?.message.join(', ')
                : error.response?.response?.message
              : error.message;
            const code = error.response?.code || error.response?.response?.code;
            invalidOrders.push({
              orderId: order.displayId,
              reason,
              code,
            });
          }
        }),
      );
    }

    return {
      validOrders,
      invalidOrders,
    };
  }

  canDownloadFullInfo(profiles: any[]): boolean {
    // Loop through each profile and check for the specific permissions
    for (const profile of profiles) {
      // Retrieve the permissions from index 3 of each profile
      const permission = profile[3];
      const idx = SalePermission.order;

      const hasPerm = permission[idx] & OrderPermission.exportOrdersFullInfo;
      if (hasPerm) return true;
    }
    return false;
  }

  async exportExcel(
    filters: OrdersFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
    dataBody?: OrderExportAdvanceDto,
  ): Promise<Buffer> {
    let orders: Order[] = [];
    const timezone = headers?.['timezone'] ? Number(headers?.['timezone']) : -7;

    const isDownloadFullInfo = this.canDownloadFullInfo(request?.user?.profiles);

    const qb = await this.getOrderQueryBuilder(filters, undefined, headers, request);
    if (!isNil(qb)) {
      const { orderBy, sort } = filters;
      qb.leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`)
        .addSelect(['tags.id', 'tags.name'])
        .leftJoinAndSelect('order.products', 'products')
        .leftJoinAndSelect('order.carrier', 'carrier')
        .leftJoin('order.externalSource', 'es')
        .addSelect(['es.id', 'es.entity', 'es.entityId'])
        .leftJoin('order.possibleDuplicateOrders', 'possibleDuplicateOrders')
        .unScope('possibleDuplicateOrders')
        .addSelect([
          'possibleDuplicateOrders.id',
          'possibleDuplicateOrders.possibleDuplicateOrderDisplayId',
        ])
        .leftJoin(
          'conversations',
          'con',
          `con.page_id = order.page_id AND con.scoped_user_id = order.fb_scoped_user_id AND con.feed_id = ''`,
        )
        .addSelect('con.created_at', 'conversation_created_at')
        .leftJoin('order.cancelReasons', 'cancelReasons', 'cancelReasons.status = :cStatus', {
          cStatus: CancelReasonStatus.active,
        })
        .leftJoinAndMapOne(
          'order.history',
          OrderStatusHistory,
          'history',
          'history.order_id = order.id AND history.before_status =:orderStatusHistoryBefore AND history.status =:orderStatusHistoryAfter',
          {
            orderStatusHistoryBefore: OrderStatus.Draft,
            orderStatusHistoryAfter: OrderStatus.New,
          },
        )
        .addSelect('history.createdAt', 'history_createdAt')
        .addSelect(['cancelReasons.id', 'cancelReasons.name'])
        .leftJoin('order.printNotes', 'printNotes')
        .addSelect(['printNotes.id', 'printNotes.name'])
        .orderBy(`order.${orderBy || `id`}`, sort || 'DESC');
      orders = await qb.getMany();
    }

    let columns = EXPORT_EXCEL_ORDER_COLUMNS_DEFAULT,
      [columnNames, columnKeys] = EXPORT_EXCEL_ORDER_COLUMNS_DEFAULT.reduce(
        (prev, c) => {
          prev[0].push(c.title);
          prev[1].push(c.key);
          return prev;
        },
        [[], []],
      );
    if (dataBody.columns) {
      const colsLookup = EXPORT_EXCEL_ORDER_COLUMNS.reduce((prev, col) => {
        prev[col.key] = col;
        return prev;
      }, {});

      const [cols, names, keys] = dataBody.columns.reduce(
        (prev, colKey) => {
          const col = colsLookup[colKey];
          prev[0].push(col);
          prev[1].push(col.title);
          prev[2].push(col.key);
          return prev;
        },
        [[], [], []],
      );
      columns = cols;
      columnNames = names;
      columnKeys = keys;
    }

    const pageIds = [],
      userIds = [],
      sourceIds = [],
      orderIds = [],
      projectIds = [];
    for (const order of orders) {
      if (order.id && !orderIds.includes(order.id)) orderIds.push(order.id);
      if (order.pageId && !pageIds.includes(order.pageId)) pageIds.push(order.pageId);
      if (order.marketerId && !userIds.includes(order.marketerId)) userIds.push(order.marketerId);
      if (order.carePageId && !userIds.includes(order.carePageId)) userIds.push(order.carePageId);
      if (order.saleId && !userIds.includes(order.saleId)) userIds.push(order.saleId);
      if (order.sourceId && !sourceIds.includes(order.sourceId)) sourceIds.push(order.sourceId);
      if (order.projectId && !projectIds.includes(order.projectId))
        projectIds.push(order.projectId);
      if (order.sourceProjectId && !projectIds.includes(order.sourceProjectId))
        projectIds.push(order.sourceProjectId);
    }
    // console.log(`pageIds`, pageIds);
    // console.log(`userIds`, userIds);
    // console.log(`sourceIds`, sourceIds);

    let pageLookup: Record<string, FanPage>,
      userLookup: Record<string, User>,
      sourceLookup: Record<string, Source>,
      inTransitsLookup: Record<string, string>,
      projectLookup: Record<string, Project>;

    await Promise.all([
      (async () => {
        try {
          if (columnKeys.includes('pageId')) {
            const { data: pData } = await this.amqpConnection.request({
              exchange: 'facebook-bot',
              routingKey: 'get-fanpages-by-ids',
              payload: { ids: pageIds },
              timeout: 5000,
            });

            const pages = pData as FanPage[];
            pageLookup = reduce(
              pages,
              (prev, item) => {
                prev[item.id] = item;
                return prev;
              },
              {},
            );
          }
        } catch (error) {
          console.log(`get fanpages err`, error);
        }
      })(),
      (async () => {
        try {
          const { data: uData } = await this.amqpConnection.request({
            exchange: 'identity-service-roles',
            routingKey: 'get-users-by-ids',
            payload: { ids: userIds, select: ['id', 'name', 'email'] },
            timeout: 5000,
          });

          const users = uData as User[];
          userLookup = reduce(
            users,
            (prev, item) => {
              prev[item.id] = item;
              return prev;
            },
            {},
          );
        } catch (error) {
          console.log(`get users err`, error);
        }
      })(),
      (async () => {
        try {
          if (columnKeys.includes('sourceId')) {
            const sources = await this.sourceRepository.findByIds(sourceIds, {
              select: ['id', 'name'],
            });

            sourceLookup = reduce(
              sources,
              (prev, item) => {
                prev[item.id] = item;
                return prev;
              },
              {},
            );
          }
        } catch (error) {
          console.log(`get od sources err`, error);
        }
        return [];
      })(),
      (async () => {
        try {
          if (columnKeys.includes('inTransitAt')) {
            const inTransitLogs = await getConnection(orderConnection)
              .createQueryBuilder(OrderStatusHistory, 'osh')
              .where(`osh.status = :status`, { status: OrderStatus.InTransit })
              .andWhere('osh.order_id IN (:...orderIds)', { orderIds })
              .select('osh.order_id')
              .addSelect('MIN(osh.created_at)', 'in_transit_at')
              .groupBy('osh.order_id')
              .getRawMany();

            inTransitsLookup = reduce(
              inTransitLogs,
              (prev, item) => {
                prev[item.order_id] = item.in_transit_at;
                return prev;
              },
              {},
            );
          }
        } catch (error) {
          console.log(`get od in transit time err`, error);
        }
        return [];
      })(),
      (async () => {
        try {
          if (columnKeys.includes('project') || columnKeys.includes('originalProject')) {
            const { data: pData } = await this.amqpConnection.request({
              exchange: 'identity-service-projects',
              routingKey: 'get-project-by-ids',
              payload: { ids: projectIds, companyId: request.user.companyId },
              timeout: 10000,
            });
            const projects = pData as Project[];

            projectLookup = reduce(
              projects,
              (prev, item) => {
                prev[item.id] = item;
                return prev;
              },
              {},
            );
          }
        } catch (error) {
          console.log(`get od projects err`, error);
        }
        return [];
      })(),
    ]);

    const data: unknown[][] = [columnNames];

    for (let index = 0; index < orders.length; index++) {
      const order = orders[index];
      const products = order.products;
      let numberOfRows = products.length || 1;
      if (dataBody.isShowProductsInSingleRow) {
        numberOfRows = 1;
      } else {
        numberOfRows = products.length || 1;
      }
      const rows = [...Array(numberOfRows + 1)].map(o => Array(columns.length).fill(null));

      const userInChargeId = order.carePageId || order.saleId;

      try {
        if (dataBody.isShowProductsInSingleRow) {
          for (const [columnIndex, column] of Object.entries(columns)) {
            switch (column.key) {
              case 'index':
                rows[0][columnIndex] = index + 1;
                break;
              case 'status':
                rows[0][columnIndex] = PlainOrderStatus[order.status];
                break;
              case 'project':
                rows[0][columnIndex] = order.projectId
                  ? projectLookup?.[order.projectId]?.name
                  : null;
                break;
              case 'originalProject':
                rows[0][columnIndex] = order.sourceProjectId
                  ? projectLookup?.[order.sourceProjectId]?.name
                  : null;
                break;
              case 'waybillNumber':
                rows[0][columnIndex] = order.carrier?.waybillNumber || '';
                break;
              case 'createdAt':
                rows[0][columnIndex] = moment(order.createdAt)
                  .utcOffset(timezone * -60)
                  .format('DD/MM/YYYY HH:mm:ss');
                break;
              case 'confirmedAt': {
                rows[0][columnIndex] = order.confirmedAt
                  ? moment(order.confirmedAt)
                      .utcOffset(timezone * -60)
                      .format('DD/MM/YYYY HH:mm:ss')
                  : null;
                break;
              }
              case 'inTransitAt': {
                rows[0][columnIndex] = inTransitsLookup?.[order.id]
                  ? moment(inTransitsLookup[order.id])
                      .utcOffset(timezone * -60)
                      .format('DD/MM/YYYY HH:mm:ss')
                  : null;
                break;
              }
              case 'conversationCreatedAt': {
                rows[0][columnIndex] = order.conversationCreatedAt
                  ? moment(order.conversationCreatedAt)
                      .utcOffset(timezone * -60)
                      .format('DD/MM/YYYY HH:mm:ss')
                  : null;
                break;
              }
              case 'sourceId':
                rows[0][columnIndex] = order.sourceId ? sourceLookup?.[order.sourceId]?.name : null;
                break;
              case 'pageId':
                rows[0][columnIndex] = order.pageId ? pageLookup?.[order.pageId]?.name : null;
                break;
              case 'marketerId':
              case 'carePageId':
                rows[0][columnIndex] = order.marketerId
                  ? userLookup?.[order?.[column.key]]?.name
                  : null;
                break;
              case 'marketerEmail':
              case 'carePageEmail':
                rows[0][columnIndex] = order.marketerId
                  ? userLookup?.[order?.[column.key]]?.email
                  : null;
                break;
              case 'saleId':
                rows[0][columnIndex] = userInChargeId ? userLookup?.[userInChargeId]?.name : null;
                break;
              case 'saleEmail':
                rows[0][columnIndex] = userInChargeId ? userLookup?.[userInChargeId]?.email : null;
                break;
              case 'shippingFee':
              case 'discount':
              case 'surcharge':
              case 'paid':
                rows[0][columnIndex] = order?.[column.key];
                break;
              case 'cod':
                rows[0][columnIndex] = Math.max(
                  order.totalPrice +
                    (order.shippingFee || 0) +
                    (order.surcharge || 0) -
                    (order.discount || 0) -
                    (order.paid || 0),
                  0,
                );
                break;
              case 'revenue':
                rows[0][columnIndex] = Math.max(
                  order.totalPrice +
                    (order.shippingFee || 0) -
                    (order.discount || 0) +
                    (order.surcharge || 0),
                  0,
                );
                break;
              case 'productName':
                rows[0][columnIndex] = products
                  ?.map(it => it?.productDetail?.product?.name)
                  .join(', ');
                break;
              case 'productSKU':
                rows[0][columnIndex] = products
                  ?.map(it => it?.productDetail?.product?.sku)
                  .join(', ');
                break;
              case 'variantSKU':
                const sku = [];
                for (const product of products) {
                  if (product?.productDetail?.product?.isCombo) {
                    for (const prodOfCombo of product?.productDetail?.product?.combo) {
                      sku.push(`${prodOfCombo?.qty}x${prodOfCombo?.variant?.sku}`);
                    }
                  } else {
                    sku.push(`${product.quantity}x${product?.productDetail?.sku}`);
                  }
                }
                rows[0][columnIndex] = sku.join(', ');
                // rows[0][columnIndex] = product?.productDetail?.product?.isCombo
                //   ? product?.productDetail?.product?.combo?.map(x => x?.variant?.sku).join(', ')
                //   : product?.productDetail?.sku;
                break;
              case 'quantity':
                rows[0][columnIndex] = products.reduce((sum, prod) => sum + prod.quantity, 0);
                break;
              case 'price':
                rows[0][columnIndex] = products?.map(it => it.price).join(', ');
                break;
              case 'statusChangeReason': {
                rows[0][columnIndex] = [order.statusChangeReason, order.statusChangeDescription]
                  .filter(identity)
                  .join(' | ');
                break;
              }
              case 'customerPhone':
                if (isDownloadFullInfo) {
                  rows[0][columnIndex] = order?.[column.key] ? order?.[column.key] : '';
                } else {
                  rows[0][columnIndex] = order?.[column.key]
                    ? StringUtils.maskPhoneNumber(order?.[column.key])
                    : '';
                }
                break;
              case 'addressText':
              case 'addressProvince':
              case 'addressDistrict':
              case 'addressWard': {
                if (isDownloadFullInfo) {
                  rows[0][columnIndex] = order?.[column.key] ? order?.[column.key] : '';
                } else {
                  rows[0][columnIndex] = order?.[column.key]
                    ? StringUtils.maskTextV2(order?.[column.key])
                    : '';
                }
                break;
              }
              case 'carrierNote':
                rows[0][columnIndex] = order.carrier?.waybillNote || '';
                break;
              case 'carrier':
                rows[0][columnIndex] = order.carrier?.carrierCode || '';
                break;
              case 'customerEDD':
                rows[0][columnIndex] = order.carrier?.customerEDD
                  ? moment(order.carrier?.customerEDD)
                      .utcOffset(timezone * -60)
                      .format('DD/MM/YYYY HH:mm:ss')
                  : null;
                break;
              case 'tags':
                rows[0][columnIndex] = order.tags?.map(it => it.name).join(', ');
                break;
              case 'createNewAt': {
                rows[0][columnIndex] = order.history?.updatedAt
                  ? moment(order.history?.updatedAt)
                      .utcOffset(timezone * -60)
                      .format('DD/MM/YYYY HH:mm:ss')
                  : null;
                break;
              }
              default:
                rows[0][columnIndex] = order?.[column.key];
                break;
            }
          }
        } else {
          for (let pIndex = 0; pIndex < numberOfRows; pIndex++) {
            const product = products[pIndex];

            for (const [columnIndex, column] of Object.entries(columns)) {
              switch (column.key) {
                case 'index':
                  rows[pIndex][columnIndex] = index + 1;
                  break;
                case 'status':
                  rows[pIndex][columnIndex] = PlainOrderStatus[order.status];
                  break;
                case 'project':
                  rows[pIndex][columnIndex] = order.projectId
                    ? projectLookup?.[order.projectId]?.name
                    : null;
                  break;
                case 'originalProject':
                  rows[pIndex][columnIndex] = order.sourceProjectId
                    ? projectLookup?.[order.sourceProjectId]?.name
                    : null;
                  break;
                case 'waybillNumber':
                  rows[pIndex][columnIndex] = order.carrier?.waybillNumber || '';
                  break;
                case 'createdAt':
                  rows[pIndex][columnIndex] = moment(order.createdAt)
                    .utcOffset(timezone * -60)
                    .format('DD/MM/YYYY HH:mm:ss');
                  break;
                case 'confirmedAt': {
                  rows[pIndex][columnIndex] = order.confirmedAt
                    ? moment(order.confirmedAt)
                        .utcOffset(timezone * -60)
                        .format('DD/MM/YYYY HH:mm:ss')
                    : null;
                  break;
                }
                case 'inTransitAt': {
                  rows[pIndex][columnIndex] = inTransitsLookup?.[order.id]
                    ? moment(inTransitsLookup[order.id])
                        .utcOffset(timezone * -60)
                        .format('DD/MM/YYYY HH:mm:ss')
                    : null;
                  break;
                }
                case 'conversationCreatedAt': {
                  rows[pIndex][columnIndex] = order.conversationCreatedAt
                    ? moment(order.conversationCreatedAt)
                        .utcOffset(timezone * -60)
                        .format('DD/MM/YYYY HH:mm:ss')
                    : null;
                  break;
                }
                case 'sourceId':
                  rows[pIndex][columnIndex] = order.sourceId
                    ? sourceLookup?.[order.sourceId]?.name
                    : null;
                  break;
                case 'pageId':
                  rows[pIndex][columnIndex] = order.pageId
                    ? pageLookup?.[order.pageId]?.name
                    : null;
                  break;
                case 'marketerId':
                case 'carePageId':
                  rows[pIndex][columnIndex] = order.marketerId
                    ? userLookup?.[order?.[column.key]]?.name
                    : null;
                  break;
                case 'marketerEmail':
                case 'carePageEmail':
                  rows[pIndex][columnIndex] = order.marketerId
                    ? userLookup?.[order?.[column.key]]?.email
                    : null;
                  break;
                case 'saleId':
                  rows[pIndex][columnIndex] = userInChargeId
                    ? userLookup?.[userInChargeId]?.name
                    : null;
                  break;
                case 'saleEmail':
                  rows[pIndex][columnIndex] = userInChargeId
                    ? userLookup?.[userInChargeId]?.email
                    : null;
                  break;
                case 'shippingFee':
                case 'discount':
                case 'surcharge':
                case 'paid':
                  rows[pIndex][columnIndex] = pIndex === 0 ? order?.[column.key] : 0;
                  break;
                case 'cod':
                  rows[pIndex][columnIndex] =
                    pIndex === 0
                      ? Math.max(
                          order.totalPrice +
                            (order.shippingFee || 0) +
                            (order.surcharge || 0) -
                            (order.discount || 0) -
                            (order.paid || 0),
                          0,
                        )
                      : 0;
                  break;
                case 'revenue':
                  rows[pIndex][columnIndex] =
                    pIndex === 0
                      ? Math.max(
                          order.totalPrice +
                            (order.shippingFee || 0) -
                            (order.discount || 0) +
                            (order.surcharge || 0),
                          0,
                        )
                      : 0;
                  break;
                case 'productName':
                  rows[pIndex][columnIndex] = product?.productDetail?.product?.name;
                  break;
                case 'productSKU':
                  rows[pIndex][columnIndex] = product?.productDetail?.product?.sku;
                  break;
                case 'variantSKU':
                  rows[pIndex][columnIndex] = product?.productDetail?.product?.isCombo
                    ? product?.productDetail?.product?.combo?.map(x => x?.variant?.sku).join(', ')
                    : product?.productDetail?.sku;
                  break;
                case 'quantity':
                  rows[pIndex][columnIndex] = product?.quantity;
                  break;
                case 'price':
                  rows[pIndex][columnIndex] = product?.editedPrice || product?.price;
                  break;
                case 'statusChangeReason': {
                  rows[pIndex][columnIndex] = [
                    order.statusChangeReason,
                    order.statusChangeDescription,
                  ]
                    .filter(identity)
                    .join(' | ');
                  break;
                }
                case 'customerPhone':
                  if (isDownloadFullInfo) {
                    rows[pIndex][columnIndex] = order?.[column.key] ? order?.[column.key] : '';
                  } else {
                    rows[pIndex][columnIndex] = order?.[column.key]
                      ? StringUtils.maskPhoneNumber(order?.[column.key])
                      : '';
                  }
                  break;
                case 'addressText':
                case 'addressProvince':
                case 'addressDistrict':
                case 'addressWard': {
                  if (isDownloadFullInfo) {
                    rows[pIndex][columnIndex] = order?.[column.key] ? order?.[column.key] : '';
                  } else {
                    rows[pIndex][columnIndex] = order?.[column.key]
                      ? StringUtils.maskTextV2(order?.[column.key])
                      : '';
                  }
                  break;
                }
                case 'carrierNote':
                  rows[pIndex][columnIndex] = order.carrier?.waybillNote || '';
                  break;
                case 'carrier':
                  rows[pIndex][columnIndex] = order.carrier?.carrierCode || '';
                  break;
                case 'customerEDD':
                  rows[pIndex][columnIndex] = order.carrier?.customerEDD
                    ? moment(order.carrier?.customerEDD)
                        .utcOffset(timezone * -60)
                        .format('DD/MM/YYYY HH:mm:ss')
                    : null;
                  break;
                case 'tags':
                  rows[pIndex][columnIndex] = order.tags?.map(it => it.name).join(', ');
                  break;
                case 'createNewAt': {
                  rows[pIndex][columnIndex] = order.history?.updatedAt
                    ? moment(order.history?.updatedAt)
                        .utcOffset(timezone * -60)
                        .format('DD/MM/YYYY HH:mm:ss')
                    : null;
                  break;
                }
                default:
                  rows[pIndex][columnIndex] = order?.[column.key];
                  break;
              }
            }
          }
        }

        data.push(...rows);
      } catch (error) {
        console.log(`error at`, index, order.ffmDisplayId);
        console.log(`error reason`, error);
      }
    }

    const sheetOptions = {
      '!cols': [
        { wch: 6 },
        { wch: 10 },
        { wch: 10 },
        { wch: 10 },
        { wch: 20 },
        { wch: 20 },
        { wch: 30 },
        { wch: 20 },
        { wch: 20 },
        { wch: 20 },
        { wch: 20 },
        { wch: 20 },
        { wch: 15 },
        { wch: 15 },
        { wch: 10 },
        { wch: 15 },
        { wch: 15 },
        { wch: 15 },
        { wch: 20 },
        { wch: 15 },
        { wch: 15 },
        { wch: 20 },
        { wch: 15 },
      ],
    };
    const buffer = xlsx.build([{ name: 'Danh sách đơn hàng', data, options: {} }], {
      sheetOptions,
    }); // Returns a buffer
    return buffer;
  }

  async getOrderDisplayId(query: OrdersQuery): Promise<Record<string, any>[]> {
    const { orderIds } = query;
    if (!orderIds) throw new BadRequestException('OrderIds is required');
    const mQuery = this.orderRepository
      .createQueryBuilder('o')
      .where('o.id IN (:...orderIds)', { orderIds: orderIds })
      .select(['o.displayId', 'o.id']);
    return mQuery.getMany();
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'save-external-source-after-created-order',
    queue: 'order-service-save-external-source-after-created-order',
    errorHandler: rmqErrorsHandler,
  })
  async updateOrderExternalSource(payload: IRmqMessage) {
    const { orderId, leadId } = payload.message || {};
    if (!orderId && !leadId) return new Nack(false);

    if (!isNil(leadId)) return this.updateLeadExternalSource(leadId);

    const oldOrder: Order = await this.orderRepository
      .createQueryBuilder('o')
      .where('o.id = :orderId')
      // .andWhere('o.externalSourceId IS NULL')
      .select([
        'o.id',
        'o.displayId',
        'o.createdAt',
        'o.pageId',
        'o.fbScopedUserId',
        'o.externalSourceId',
      ])
      .setParameters({ orderId })
      .getOne();
    if (!oldOrder) {
      console.log(`Not found order id ${orderId}`, payload.message);
      return new Nack(false);
    }

    if (!oldOrder.fbScopedUserId && !oldOrder.pageId) {
      console.log(`No need to update external source for order `, orderId);
      return new Nack(false);
    }

    const customerFbScopedUserId = oldOrder.fbScopedUserId;
    const pageId = oldOrder.pageId;
    let extSourceId: number | undefined = oldOrder.externalSourceId;
    let adsId: string | undefined = oldOrder.fbAdsId;

    let fbPageSource: OrderSource;
    if (pageId) {
      // Find fb page source and save new if not exist
      fbPageSource = await this.externalSourceRepo.findOne({
        entity: SourceEntity.fb_page,
        entityId: pageId,
      });
      if (!fbPageSource)
        fbPageSource = await this.externalSourceRepo.save(
          plainToInstance(OrderSource, {
            entity: SourceEntity.fb_page,
            entityId: pageId,
          }),
        );
      if (!extSourceId) extSourceId = fbPageSource.id;
    }

    // Find fb scoped user source and save new if not exist
    let fbScopedUserSource: OrderSource;
    if (customerFbScopedUserId) {
      fbScopedUserSource = await this.externalSourceRepo.findOne(
        {
          entity: SourceEntity.fb_scoped_user,
          entityId: customerFbScopedUserId,
        },
        { relations: ['parent'] },
      );
      if (!fbScopedUserSource) {
        fbScopedUserSource = plainToInstance(OrderSource, {
          entity: SourceEntity.fb_scoped_user,
          entityId: customerFbScopedUserId,
        });
        if (!fbScopedUserSource.parentId && fbPageSource?.id) {
          fbScopedUserSource.parentId = fbPageSource.id;
          fbScopedUserSource.parent = fbPageSource;
        }
        fbScopedUserSource = await this.externalSourceRepo.save(fbScopedUserSource);
      }
    }

    if (customerFbScopedUserId) {
      try {
        const { data } = await this.amqpConnection.request({
          exchange: 'facebook-bot',
          routingKey: 'find-conversation-source-by-scoped-user-id',
          payload: { scopedUserId: customerFbScopedUserId },
          timeout: 5000,
        });
        const conversationReferral = data as ConversationReferral;
        if (!isEmpty(conversationReferral)) {
          switch (conversationReferral.source) {
            case 'ADS': {
              let fbPostSource: OrderSource;
              if (conversationReferral.postId) {
                // Find fb post source and save new if not exist
                fbPostSource = await this.externalSourceRepo.findOne({
                  entity: SourceEntity.fb_post,
                  entityId: conversationReferral.postId,
                });
                if (
                  !fbPostSource ||
                  !fbPostSource.parentId ||
                  fbPostSource.parentId !== fbPageSource.id
                )
                  fbPostSource = await this.externalSourceRepo.save(
                    plainToInstance(OrderSource, {
                      id: fbPostSource?.id,
                      entity: SourceEntity.fb_post,
                      entityId: conversationReferral.postId,
                      parentId: fbPageSource?.id,
                      parent: fbPageSource,
                    }),
                  );
              }

              const adParent = fbPostSource ?? fbPageSource;
              adsId = conversationReferral.adId;

              // Find fb ad source and save new if not exist
              let fbAdSource = await this.externalSourceRepo.findOne({
                entity: SourceEntity.fb_ad,
                entityId: conversationReferral.adId,
              });
              if (!fbAdSource || !fbAdSource.parentId || fbAdSource.parentId !== adParent.id)
                fbAdSource = await this.externalSourceRepo.save(
                  plainToInstance(OrderSource, {
                    id: fbAdSource?.id,
                    entity: SourceEntity.fb_ad,
                    entityId: conversationReferral.adId,
                    parentId: adParent?.id,
                    parent: adParent,
                  }),
                );
              if (fbAdSource?.id && fbScopedUserSource.parentId !== fbAdSource.id) {
                fbScopedUserSource.parentId = fbAdSource.id;
                fbScopedUserSource.parent = fbAdSource;
              }
              fbScopedUserSource = await this.externalSourceRepo.save(fbScopedUserSource);
              break;
            }
            case 'CUSTOMER_CHAT_PLUGIN':
            case 'SHORTLINK':
            default:
              break;
          }
        }
      } catch (error) {
        console.log(`error`, error, customerFbScopedUserId);
      }
    }

    console.log(
      `update source for order ID ${orderId} ${oldOrder.displayId} createdAt ${oldOrder.createdAt}`,
    );
    console.log(`fbScopedUserSource`, fbScopedUserSource);
    if (fbScopedUserSource?.id) extSourceId = fbScopedUserSource.id;

    if (oldOrder.externalSourceId !== extSourceId || oldOrder.fbAdsId !== adsId) {
      const result = await this.orderRepository
        .createQueryBuilder()
        .update()
        .set({ externalSourceId: extSourceId, fbAdsId: adsId })
        .where({ id: oldOrder.id })
        .andWhere('(externalSourceId IS NULL OR externalSourceId != :extSourceId)')
        .setParameters({ extSourceId })
        .execute();
      console.log(`update order ext src result`, result);
    }

    return new Nack(false);
  }

  async updateLeadExternalSource(leadId: number) {
    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')
      .innerJoinAndSelect('o.externalSource', 'es', 'es.entity = :entity')
      .where('l.id = :leadId')
      .addSelect(['o.id', 'o.externalSourceId'])
      .setParameters({ leadId, entity: SourceEntity.landing_page })
      .getOne();

    const order = lead?.order;

    if (!order) {
      console.log(`Not found order of lead id ${leadId}`);
      return new Nack(false);
    }

    if (lead?.utmSource !== 'facebook') {
      console.log(`No need to find and update external source of this lead`, leadId);
      return new Nack(false);
    }

    const landingPageSource = order.externalSource;
    let extSourceId: number | undefined = order.externalSourceId;
    let adsId: string | undefined = order.fbAdsId;

    if (lead.utmMedium) {
      let fbAdSource = await this.externalSourceRepo.findOne({
        entity: SourceEntity.fb_ad,
        entityId: lead.utmMedium,
      });
      if (!fbAdSource || !fbAdSource.parentId) adsId = lead.utmMedium;
      fbAdSource = await this.externalSourceRepo.save(
        plainToInstance(OrderSource, {
          id: fbAdSource?.id,
          entity: SourceEntity.fb_ad,
          entityId: lead.utmMedium,
          parentId: landingPageSource?.id,
          parent: landingPageSource,
        }),
      );

      if (fbAdSource.id) extSourceId = fbAdSource.id;
    }

    if (order.externalSourceId !== extSourceId) {
      const result = await this.orderRepository
        .createQueryBuilder()
        .update()
        .set({ externalSourceId: extSourceId, fbAdsId: adsId })
        .where({ id: order.id })
        .andWhere('(externalSourceId IS NULL OR externalSourceId != :extSourceId)')
        .setParameters({ extSourceId })
        .execute();
      console.log(`update order ext src result`, result);

      return result;
    }

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'get-orders-confirmed-at-by-ids',
    queue: 'order-service-get-orders-confirmed-at-by-ids',
    errorHandler: rmqErrorsHandler,
  })
  async getOrdersConfirmedAtByIds({ ids }) {
    if (!ids) return [];
    return await this.orderRepository
      .createQueryBuilder('o')
      .whereInIds(ids)
      .select(['o.id', 'o.confirmedAt'])
      .getMany();
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'get-orders-created-at-by-ids',
    queue: 'order-service-get-orders-created-at-by-ids',
    errorHandler: rmqErrorsHandler,
  })
  async getOrdersCreatedAtByIds({ ids }) {
    if (!ids) return [];
    return await this.orderRepository
      .createQueryBuilder('o')
      .whereInIds(ids)
      .select(['o.id', 'o.createdAt', 'o.creatorId'])
      .getMany();
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'get-orders-status-by-ids',
    queue: 'order-service-get-orders-status-by-ids',
    errorHandler: rmqErrorsHandler,
  })
  async getOrdersStatusByIds({ ids }) {
    if (!ids) return [];
    return await this.orderRepository
      .createQueryBuilder('o')
      .whereInIds(ids)
      .select('o.id', 'id')
      .addSelect('o.status', 'status')
      .addSelect('o.lastUpdateStatus', 'lastUpdateStatus')
      .getRawMany();
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'get-orders-country-project-at-by-ids',
    queue: 'order-service-get-orders-country-project-at-by-ids',
    errorHandler: rmqErrorsHandler,
  })
  async getOrdersCountryProjectByIds({ ids }) {
    if (!ids) return [];
    return await this.orderRepository
      .createQueryBuilder('o')
      .whereInIds(ids)
      .select(['o.id', 'o.countryId', 'o.projectId'])
      .getMany();
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'sync-orders-confirmed-at',
    queue: 'order-service-sync-orders-confirmed-at',
    errorHandler: rmqErrorsHandler,
  })
  async syncOrdersConfirmedAt({ data }) {
    if (isEmpty(data)) return new Nack(false);
    const ids = data.map(it => it.orderId);

    const orders = await this.orderRepository
      .createQueryBuilder('o')
      .whereInIds(ids)
      .andWhere('o.confirmedAt IS NULL')
      .select(['o.id', 'o.confirmedAt'])
      .getMany();

    if (isEmpty(orders)) return new Nack(false);

    const ordersLookup = data.reduce((prev, order) => {
      prev[order.orderId] = order;
      return prev;
    }, {});

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      for (const item of orders) {
        if (!ordersLookup[item.id]?.confirmedAt) continue;
        const confirmedAt = ordersLookup[item.id].confirmedAt;
        const result = await queryRunner.manager.update(
          Order,
          { id: item.id, confirmedAt: IsNull() },
          { confirmedAt },
        );
        console.log(`update confirmedAt of order ${item.id} to ${confirmedAt} result`, result);
        if (result.affected) item.confirmedAt = confirmedAt;
      }
      await queryRunner.commitTransaction();
      return new Nack(false);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'upsert-lead-after-upsert-order',
    queue: 'order-service-upsert-lead-after-upsert-order',
    errorHandler: rmqErrorsHandler,
  })
  async upsertLeadAfterUpsertOrder({ id }) {
    if (!id) return new Nack(false);
    const order = await this.orderRepository
      .createQueryBuilder('o')
      .where('o.id = :id', { id })
      .leftJoin('o.externalSource', 'es')
      .select(['o.id', 'o.companyId', 'o.externalSourceId'])
      .addSelect(['es.id', 'es.entity', 'es.entityId'])
      .getOne();

    if (!order || order.externalSource?.entity !== SourceEntity.landing_page)
      return new Nack(false);

    const lead = plainToInstance(Lead, { orderId: id, createdAt: order.createdAt });
    return await this.leadsRepo.upsert(lead, ['orderId']);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'scan-possible-duplicate-orders',
    queue: 'order-service-scan-possible-duplicate-orders',
    errorHandler: rmqErrorsHandler,
  })
  async scanPossibleDuplicateOrders({ id }) {
    if (!id) return new Nack(false);

    const order = await this.orderRepository
      .createQueryBuilder('o')
      .select([
        'o.id',
        'o.displayId',
        'o.customerPhone',
        'o.countryId',
        'o.companyId',
        'o.projectId',
      ])
      .leftJoin('o.products', 'p')
      .leftJoin('o.possibleDuplicateOrders', 'dupOrders')
      .addSelect(['p.id', 'p.orderId', 'p.productId'])
      .addSelect([
        'dupOrders.id',
        'dupOrders.possibleDuplicateOrderId',
        'dupOrders.possibleDuplicateOrderDisplayId',
      ])
      .where('(o.id::TEXT = :id OR o.displayId = :id OR o.ffmDisplayId = :id)', { id })
      .getOne();

    if (
      !order ||
      isEmpty(order.products) ||
      NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES.includes(order.status)
    )
      return new Nack(false);

    const productIds = order.products.map(it => it.productId);
    const qb = this.orderRepository
      .createQueryBuilder('o')
      .leftJoin('o.products', 'p')
      .leftJoin('o.possibleDuplicateOrders', 'dupOrders')
      .where('o.id != :id')
      .andWhere('o.status NOT IN (:...statuses)')
      .andWhere('o.companyId = :companyId')
      // .andWhere('o.projectId = :projectId')
      .andWhere('o.countryId = :countryId')
      .andWhere('o.ignoreDuplicateWarning = FALSE')
      .andWhere('o.customerPhone = :phone')
      .andWhere('p.productId IN (:...productIds)')
      .andWhere(
        'o.id NOT IN (SELECT rdo.duplicate_order_id FROM removed_duplicate_orders rdo WHERE rdo.order_id = :orderId)',
        { orderId: id },
      )
      .select(['o.id', 'o.displayId', 'o.customerPhone', 'o.companyId', 'o.projectId'])
      .addSelect(['p.id', 'p.productId'])
      .addSelect([
        'dupOrders.id',
        'dupOrders.possibleDuplicateOrderId',
        'dupOrders.possibleDuplicateOrderDisplayId',
      ])
      .setParameters({
        id: order.id,
        statuses: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES,
        companyId: order.companyId,
        // projectId: order.projectId,
        countryId: order.countryId,
        phone: order.customerPhone,
        productIds,
      });
    // console.log(`qb`, qb.getQueryAndParameters());
    const possibleDuplicateOrders = await qb.getMany();

    const dupRecords = possibleDuplicateOrders.reduce((prev, dupOrder) => {
      // Check and add possible duplicates to current scanning order
      const index = order.possibleDuplicateOrders.findIndex(
        it => it.possibleDuplicateOrderId === dupOrder.id,
      );
      if (index === -1) {
        prev.push(
          plainToInstance(PossibleDuplicateOrder, {
            orderId: order.id,
            possibleDuplicateOrderId: dupOrder.id,
            possibleDuplicateOrderDisplayId: dupOrder.displayId,
          }),
        );
      }

      // Check and add current scanning order to other possible duplicates
      const idx = dupOrder.possibleDuplicateOrders.findIndex(
        it => it.possibleDuplicateOrderId === order.id,
      );
      if (idx !== -1) return prev;
      prev.push(
        plainToInstance(PossibleDuplicateOrder, {
          orderId: dupOrder.id,
          possibleDuplicateOrderId: order.id,
          possibleDuplicateOrderDisplayId: order.displayId,
        }),
      );
      return prev;
    }, []);

    console.log(`dup records of order ID ${id}`, dupRecords);

    // return dupRecords;
    return await this.possibleDupOrdersRepo.upsert(dupRecords, [
      'orderId',
      'possibleDuplicateOrderId',
      'possibleDuplicateOrderDisplayId',
    ]);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'ignore-duplicate-orders-warning',
    queue: 'order-service-handling-ignore-duplicate-orders-warning',
    errorHandler: rmqErrorsHandler,
  })
  async handleIgnorePossibleDuplicateWarning({ id }) {
    if (!id) return new Nack();

    const [orderOrigin, orderDups] = await Promise.all([
      this.orderRepository.findOne({
        where: { id },
        select: ['id', 'displayId'],
      }),
      this.possibleDupOrdersRepo
        .createQueryBuilder('pdo')
        .innerJoin('orders', 'o', 'o.id = pdo.possibleDuplicateOrderId')
        .andWhere('pdo.orderId = :id', { id })
        .andWhere('o.status NOT IN (:...status)', {
          status: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES,
        })
        .select(['pdo.possibleDuplicateOrderId', 'pdo.possibleDuplicateOrderDisplayId'])
        .getMany(),
    ]);

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Delete possible duplicates orders
      await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from(PossibleDuplicateOrder)
        .where('possibleDuplicateOrderId = :id', { id })
        .orWhere('orderId = :id', { id })
        .execute();

      if (orderDups.length > 0) {
        // Add log remove duplicates orders
        const removedDupOrders = [
          // log for origin order
          plainToInstance(SystemLog, {
            tableName: 'orders',
            action: 'REMOVE_DUPLICATE',
            recordId: orderOrigin.id,
            changes: [
              'order_id',
              orderOrigin?.displayId,
              'order_duplicate_display_ids',
              orderDups.map(it => it.possibleDuplicateOrderDisplayId).join(', '),
            ],
            creatorId: null,
          }),
          // log for duplicates order
          ...orderDups.map(order =>
            plainToInstance(SystemLog, {
              tableName: 'orders',
              action: 'REMOVE_DUPLICATE',
              recordId: order.possibleDuplicateOrderId,
              changes: [
                'order_id',
                order.possibleDuplicateOrderDisplayId,
                'order_duplicate_display_ids',
                orderOrigin?.displayId,
              ],
              creatorId: null,
            }),
          ),
        ];
        await queryRunner.manager.save(removedDupOrders);
      }

      await queryRunner.commitTransaction();
    } catch (e) {
      await queryRunner.rollbackTransaction();
      console.log('handleIgnorePossibleDuplicateWarning error: ', e);
      throw e;
    } finally {
      await queryRunner.release();
    }

    console.log('orderOrigin', orderOrigin.id, orderOrigin.displayId);
    console.log(
      `possible duplicates`,
      orderDups.map(it => it.possibleDuplicateOrderDisplayId).join(','),
    );
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'after-lead-user-id-updated',
    queue: 'order-process-after-lead-user-id-updated',
    errorHandler: rmqErrorsHandler,
  })
  async afterLeadUserIdUpdated({
    leadId,
    orderId,
    updatedAt,
    userId,
    updatedBy,
  }: {
    leadId: number;
    orderId: number;
    updatedAt: number;
    userId?: number | null;
    updatedBy?: number | null;
  }) {
    if ((!leadId && !orderId) || !updatedAt) return new Nack(false);

    const qb = this.orderRepository
      .createQueryBuilder('o')
      .andWhere('(o.lastSyncedLeadUserIdAt IS NULL OR o.lastSyncedLeadUserIdAt < :updatedAt)', {
        updatedAt,
      });
    if (orderId) qb.andWhere('o.id = :orderId', { orderId });
    else if (leadId) qb.innerJoin(Lead, 'l', 'l.orderId = o.id AND l.id = :leadId', { leadId });
    const order = await qb.getOne();
    if (!order) {
      console.log(`Cannot find order to sync sale id`, qb.getQueryAndParameters());
      return new Nack(false);
    }
    let metadata = JSON.parse(order.metadata);
    if (!metadata || (typeof metadata === 'object' && Object.keys.length === 0)) metadata = {};
    metadata = {
      ...metadata,
      repsId: +userId,
    };
    order.saleId = userId;
    order.metadata = JSON.stringify(metadata);
    order.lastUpdatedBy = updatedBy;
    const result = await this.orderRepository.save(order);
    // console.log(`sync order sale id when lead user id changed result`, result);

    await this.amqpConnection.publish('order-service', 'after-order-update', {
      id: result.id,
      updatedAt: result.updatedAt,
      updatedBy: result.lastUpdatedBy,
    });

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'after-care-page-of-scoped-user-updated',
    queue: 'order-process-after-care-page-of-scoped-user-updated',
    errorHandler: rmqErrorsHandler,
  })
  async afterCarePageOfScopedUserUpdated({
    scopedUserId,
    updatedAt,
    careUserId,
  }: {
    scopedUserId: string;
    updatedAt: number;
    careUserId: number | null;
  }) {
    if (!scopedUserId || !updatedAt || careUserId === undefined) return new Nack(false);

    const [pageId, psId] = scopedUserId.split('_');

    const qb = this.orderRepository
      .createQueryBuilder('o')
      .where('o.pageId = :pageId')
      .andWhere('o.fbScopedUserId = :psId')
      .andWhere('o.status IN (:...statuses)')
      .andWhere('o.updatedAt < :updatedAt')
      .andWhere('o.confirmedAt IS NOT NULL')
      .andWhere({ carePageId: careUserId ? IsNull() : Not(IsNull()) })
      .andWhere('o.crossCare = FALSE')
      .setParameters({ pageId, psId, statuses: [OrderStatus.Draft, OrderStatus.New], updatedAt });

    const orders = await qb.getMany();

    const now = new Date();

    await Promise.all(
      orders.map(async order => {
        const result = await this.orderRepository
          .createQueryBuilder()
          .update()
          .where('id = :id')
          .andWhere('status IN (:...statuses)')
          .andWhere('updated_at < :updatedAt')
          .andWhere('confirmed_at IS NOT NULL')
          .andWhere({ carePageId: careUserId ? IsNull() : Not(IsNull()) })
          .andWhere('o.crossCare = FALSE')
          .set({ carePageId: careUserId, updatedAt, lastUpdatedBy: null })
          .setParameters({
            id: order.id,
            statuses: [OrderStatus.Draft, OrderStatus.New],
            updatedAt,
          })
          .execute();
        console.log(
          `sync order care page id when scoped user person in charge changed result`,
          result,
        );
        if (result.affected) {
          await this.amqpConnection.publish('order-service', 'after-order-update', {
            id: order.id,
            updatedAt: now,
            updatedBy: null,
          });
        }
      }),
    );

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'find-orders-by-ids',
    queue: 'order-find-orders-by-ids',
    errorHandler: defaultNackErrorHandler,
  })
  async fetchOrdersByIds({ ids }: { ids: number[] }) {
    if (!ids) return [];
    return this.orderRepository.findByIds(ids);
  }

  async deleteIncorrectPossibleDuplicates() {
    const qb = this.orderRepository
      .createQueryBuilder('o')
      .leftJoin('o.possibleDuplicateOrders', 'dupOrders')
      .where('(o.ignoreDuplicateWarning = TRUE OR o.status IN (:...status))', {
        status: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES,
      })
      .andWhere('dupOrders.id IS NOT NULL')
      .select(['o.id', 'o.status', 'dupOrders.id']);

    const orders = await qb.getMany();
    for (const order of orders) {
      await this.amqpConnection.publish('order-service', 'ignore-duplicate-orders-warning', {
        id: order.id,
      });
    }
    return orders;
  }

  async scanOrdersNeedToBeCanceledDueLeadCancellation() {
    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .leftJoin('l.order', 'o')
      .where('l.state IN (:...cancelStates) AND o.status != :cancelStatus')
      .setParameters({ cancelStates: CANCEL_LEAD_STATES, cancelStatus: OrderStatus.Canceled })
      .select('o.id', 'id');
    const raw = await qb.getRawMany();
    const ids: number[] = raw.map(item => item.id);

    const result = await this.orderRepository
      .update(
        { id: In(ids) },
        {
          status: OrderStatus.Canceled,
          cancelReasonText: `Order canceled due to lead cancellation.`,
        },
      )
      .catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
    return new RawResponse(result);
  }

  async scanOrdersNeedToBeSyncStatusWithLeads(
    filter: OrdersFilter,
    pagination?: PaginationOptions,
  ) {
    const { from, to, ids, companyId } = filter;
    const sqb = this.orderRepository
      .createQueryBuilder('o')
      .innerJoin(Lead, 'l', 'l.order_id = o.id')
      .innerJoin('order_status_histories', 'osh', 'osh.order_id = o.id')
      .select('MAX (osh.created_at)', 'max_created_at')
      .addSelect('osh.order_id', 'order_id')
      .where(
        `h.status IN (${[
          OrderStatus.AwaitingStock,
          OrderStatus.Reconfirm,
          OrderStatus.Confirmed,
          OrderStatus.Canceled,
        ].join(',')})`,
      )
      .where('o.company_id = :companyId', { companyId })
      .groupBy('osh.order_id');

    if (pagination) sqb.limit(pagination.limit);
    if (!isEmpty(ids)) sqb.andWhere('o.id IN (:...ids)', { ids });
    else {
      if (from) sqb.andWhere('o.created_at >= :from', { from });
      if (to) sqb.andWhere('o.created_at < :to', { to });
    }

    const qb = getConnection(orderConnection)
      .createQueryBuilder()
      .from(`(${sqb.getQuery()})`, 'sub')
      .innerJoin(
        'order_status_histories',
        'h',
        'h.created_at = sub.max_created_at AND h.order_id = sub.order_id',
      )
      .where('h.status > 0')
      .select('h.order_id')
      .addSelect('h.status', 'status')
      .addSelect('h.created_at', 'updated_at')
      .addSelect('h.creator_id', 'updated_by')
      .setParameters(sqb.getParameters());

    const data = await qb.getRawMany();

    const chunks = chunk(data, 5);
    for (const item of chunks) {
      await Promise.all(
        item.map(async it => {
          await this.amqpConnection.publish('order-service', 'after-order-status-updated', {
            orderId: it.order_id,
            updatedAt: it.updated_at,
            updatedBy: it.updated_by,
            status: it.status,
            force: true,
          });
        }),
      );
    }

    return data;
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'get-order-by-id',
    queue: 'order-process-get-order-by-id',
    errorHandler: defaultNackErrorHandler,
  })
  async getOrderById({ id, relations }: { id: number; relations?: (keyof Order)[] }) {
    const order = await this.findById(id, relations);
    if (!order) return new Nack(false);
    return order;
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'on-fb-receipt-sent',
    queue: 'order-process-on-fb-receipt-sent',
    errorHandler: defaultNackErrorHandler,
  })
  async onFbOrderReceiptSent({ id, receipt }: { id: number; receipt: FbSendMessagesResponse }) {
    if (!id || !receipt) return new Nack(false);

    const fbReceiptMessageId = receipt.message_id;
    const result = await this.orderRepository.update(
      { id, fbReceiptMessageId: IsNull() },
      { fbReceiptMessageId },
    );
    console.log(`result of update fbReceiptMessageId to order ${id}`, result);
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'create-cross-care-tls-order',
    queue: 'order-process-create-cross-care-tls-order',
    errorHandler: rmqErrorsHandler,
  })
  async createCrossCareTlsOrder({
    companyId,
    countryId,
    projectId,
    name,
    phone,
    scopedUserId,
    pageId,
    fbGlobalId,
    note,
  }) {
    if (!projectId) return new Nack(false);

    const data = plainToInstance(CreateOrderDto, {
      pageId,
      customerFbScopedUserId: scopedUserId,
      customerName: name,
      customerPhone: phone,
      status: OrderStatus.Draft,
      crossCare: true,
      note,
    });
    if (fbGlobalId) data.customerFbGlobalId = fbGlobalId;

    return this.createOrder(data, countryId, companyId, undefined, projectId);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'after-order-update',
    queue: 'order-process-after-order-update',
    errorHandler: rmqErrorsHandler,
  })
  async afterOrderUpdate({ id, updatedAt, updatedBy, beforeChanges, changes }) {
    await this.amqpConnection.publish('facebook-bot', 'order-edited', {
      id,
      updatedAt,
      updatedBy,
    });

    if (!isNil(changes?.status) && beforeChanges?.status !== changes?.status) {
      await Promise.all([
        this.amqpConnection.publish('order-service', 'after-order-status-updated', {
          orderId: id,
          updatedAt,
          updatedBy,
          status: changes.status,
        }),
        this.amqpConnection.publish('facebook-bot', 'sync-order-status-from-sale', {
          id,
          updatedAt,
          updatedBy,
          status: changes.status,
        }),
        this.amqpConnection.publish('facebook-bot', 'keyword-trigger-on-order-status-changed', {
          id,
          updatedAt,
          updatedBy,
          status: changes.status,
        }),
      ]);

      if (NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES.includes(changes.status))
        await Promise.all([
          this.amqpConnection.publish('order-service', 'ignore-duplicate-orders-warning', {
            id,
          }),
          this.amqpConnection.publish('order-service', 'ignore-duplicate-leads-warning', {
            orderId: id,
            updatedAt,
          }),
        ]);
    }

    const updateOrder = await this.orderRepository.findOne({
      where: { id },
      relations: ['products', 'tags', 'cancelReasons', 'carrier'],
    });

    if (updateOrder) {
      if (beforeChanges)
        await this.pubSub.publish('onOrderUpdated', {
          after: updateOrder,
          before: beforeChanges,
        });

      // Remove logs order history
      const orderHistory = new OrderHistory();
      orderHistory.createdAt = updatedAt;
      orderHistory.updatedAt = updatedAt;
      orderHistory.orderId = updateOrder.id;
      orderHistory.orderData = updateOrder;
      orderHistory.updatedBy = updatedBy;

      await this.orderHistoryRepo.save(orderHistory);
    }

    return new Nack(false);
  }

  async scanAndSendFacebookPurchaseEventDaily() {
    return true;

    const timezone = 'Asia/Ho_Chi_Minh';
    const date = moment()
      .subtract(1, 'day')
      .toDate();
    const orders = await this.orderRepository
      .createQueryBuilder('o')
      .where('o.confirmed_at IS NOT NULL')
      .andWhere(`o.confirmed_at >= :date`, { date })
      .andWhere('o.fbScopedUserId IS NOT NULL')
      .select('o.id')
      .getMany();

    await Promise.all(
      orders.map(o =>
        this.amqpConnection.publish('facebook-bot', 'send-custom-event-when-fb-order-created', {
          id: o.id,
        }),
      ),
    );

    return orders;
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'save-log-call-action',
    queue: 'order-service-save-log-call-action',
    errorHandler: rmqErrorsHandler,
  })
  async saveCallActionLog({
    userId,
    leadId,
    phoneNumber,
  }: {
    userId: number;
    leadId: number;
    phoneNumber: string;
  }) {
    await this.systemLogRepo.save(
      plainToInstance(SystemLog, {
        action: 'CALL',
        tableName: 'leads',
        creatorId: userId,
        recordId: leadId,
        changes: [phoneNumber],
      }),
    );
    return new Nack(false);
  }

  async uncensorOrder(
    id: number | string,
    type: UncensorType,
    user: AuthUser,
  ): Promise<Partial<Order>> {
    const qb = this.orderRepository
      .createQueryBuilder('o')
      .where('(o.id::TEXT = :id OR o.displayId = :id)', { id })
      .select(['o.id', 'o.pageId', 'o.fbScopedUserId']);
    if (type === UncensorType.address) {
      qb.addSelect([
        'o.addressText',
        'o.addressWard',
        'o.addressDistrict',
        'o.addressProvince',
        'o.addressNote',
      ]);
    } else {
      qb.addSelect(['o.customerPhone']);
    }
    const order = await qb.getOne();
    if (!order) {
      throw new NotFoundException('Order is not exists');
    }

    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .where('l.orderId = :orderId', { orderId: order.id })
      .select(['l.id'])
      .getOne();

    const result = await getConnection(orderConnection)
      .createQueryBuilder()
      .insert()
      .into(UncensorLog)
      .values({
        orderId: order.id,
        leadId: lead?.id,
        pageId: order.pageId,
        scopedUserId: order.fbScopedUserId,
        userId: user.id,
        type,
      })
      .execute();

    console.log(`insert uncensor order log result`, result);

    return pickBy(order, identity);
  }

  async fetchUncensorOrderLogs(filter: UncensorLogsFilter, pagination?: PaginationOptions) {
    const qb = getConnection(orderConnection).createQueryBuilder(UncensorLog, 'l');

    if (pagination) qb.take(pagination?.limit).skip(pagination?.skip);

    const { orderId, leadId, pageId, scopedUserId, userId, type } = filter;
    if (orderId) {
      qb.andWhere('l.orderId = :orderId', { orderId });
    }
    if (leadId) {
      qb.andWhere('l.leadId = :leadId', { leadId });
    }
    if (pageId) {
      qb.andWhere('l.pageId = :pageId', { orderId });
    }
    if (scopedUserId) {
      qb.andWhere('l.scopedUserId = :scopedUserId', { scopedUserId });
    }
    if (userId) {
      qb.andWhere('l.userId = :userId', { userId });
    }
    if (type) {
      qb.andWhere('l.type = :type', { type });
    }
    return qb.orderBy('l.createdAt', 'DESC').getManyAndCount();
  }

  async getDisplayIdsOfOrders(query: OrdersFilter) {
    const orders = await this.orderRepository
      .createQueryBuilder('o')
      .where('o.id IN (:...ids)', { ids: query.ids })
      .getMany();
    return orders;
  }
}
