import { Inject } from '@nestjs/common';
import { InjectConnection, InjectRepository } from '@nestjs/typeorm';
import { OrderCustomerService } from 'apps/order-api/src/entities/order-cs.entity';
import { OrderHistory } from 'apps/order-api/src/entities/order-history.entity';
import { Order } from 'apps/order-api/src/entities/order.entity';
import { OrderCsStatus } from 'apps/order-api/src/enums/order-cs.enum';
import { orderConnection } from 'core/constants/database-connection.constant';
import { OrderStatus } from 'core/enums/order-status.enum';
import { PubSub } from 'graphql-subscriptions';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  Repository,
  UpdateEvent,
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { PUB_SUB } from '../../../../../../core/pubsub/pub-sub.module';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';

@EventSubscriber()
export class OrderSubscriber implements EntitySubscriberInterface<Order> {
  constructor(
    @InjectConnection(orderConnection)
    private connection: Connection,
    @Inject(PUB_SUB)
    private readonly pubSub: PubSub,
    @InjectRepository(OrderCustomerService, orderConnection)
    private csRepository: Repository<OrderCustomerService>,
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    // private readonly pancakeApiService: PancakeApiService,
    // private readonly posOrderService: PosOrdersService,
    private amqpConnection: AmqpConnection,
  ) {
    this.connection.subscribers.push(this);
  }

  listenTo() {
    return Order;
  }

  /**
   * Called after entity is loaded.
   */
  afterLoad(entity: Order) {
    // console.log(`AFTER ENTITY LOADED: `, entity);
  }

  /**
   * Called before post insertion.
   */
  beforeInsert(event: InsertEvent<Order>) {
    // console.log(`BEFORE POST INSERTED: `, event.entity);
  }

  /**
   * Called after entity insertion.
   */
  async afterInsert(event: InsertEvent<Order>) {
    // console.log(`AFTER ENTITY INSERTED: `, event.entity);

    const order = event.entity;
    const entityManager = event.manager;
    if (!order.id || !entityManager) return;

    // Remove logs order history
    const orderHistory = new OrderHistory();
    orderHistory.orderId = order.id;
    orderHistory.orderData = order;
    orderHistory.updatedBy = order.creatorId;

    await entityManager.save(orderHistory);

    await this.pubSub.publish('onOrderCreated', {
      orderCreated: order,
    });

    await this.pubSub.publish('onOrderUpdated', {
      after: order,
      before: null,
    });

    // if (order?.saleId) {
    //   await this.setStatusCs(order?.id);
    //   let cs = new OrderCustomerService();
    //   cs.orderId = order?.id;
    //   cs.saleId = order?.saleId;
    //   this.csRepository.save(cs);
    // }
  }

  beforeUpdate(event: UpdateEvent<Order>) {
    // console.log(`BEFORE ENTITY UPDATE: `, event.entity);
    // console.log('Database entity', event.databaseEntity);
  }

  /**
   * Called after entity update.
   */
  async afterUpdate(event: UpdateEvent<Order>) {
    // console.log('AFTER ORDER UPDATED: ' + event.databaseEntity?.id, event.entity);
    // console.log('Database entity', event.databaseEntity);

    const oldOrder = event.databaseEntity;
    const entityManager = event.manager;

    if (!oldOrder?.id || !entityManager) return;

    const updateOrder = await entityManager.findOne(Order, oldOrder.id, {
      relations: ['products', 'tags', 'cancelReasons', 'carrier'],
    });
    // console.log(`updateOrder`, updateOrder)

    await this.pubSub.publish('onOrderUpdated', {
      after: updateOrder,
      before: oldOrder,
    });
  }
}
