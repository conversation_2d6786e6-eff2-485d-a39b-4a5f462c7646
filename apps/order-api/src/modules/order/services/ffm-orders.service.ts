import {
  AmqpConnection,
  defaultN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Project } from 'apps/identity-api/src/entities/projects.entity';
import { FFM_API } from 'apps/order-api/src/constants/ffm-api-endpoints.constant';
import {
  AG_TO_FFM_ORDER_STATUS_MAPPING,
  FFM_TO_AG_ORDER_STATUS_MAPPING,
} from 'apps/order-api/src/constants/ffm-order-status-mapping.constant';
import { PlainOrderStatus } from 'apps/order-api/src/constants/order-statuses.constant';
import { AgToFfmOrderStatusDto } from 'apps/order-api/src/dtos/ag-to-ffm-order-status.dto';
import { FfmCustomerDto } from 'apps/order-api/src/dtos/ffm-customer.dto';
import { FfmOrderDto } from 'apps/order-api/src/dtos/ffm-order.dto';
import { FfmProductComboItemDto, FfmProductDto } from 'apps/order-api/src/dtos/ffm-product.dto';
import { FfmTagDto } from 'apps/order-api/src/dtos/ffm-tag.dto';
import {
  FfmToAgOrderCarrierDto,
  FfmToAgOrderInfoDto,
  FfmToAgOrderStatusDto,
  FfmToAgOrderTagsDto,
  ValidateSyncOrderStatusFromFFM,
} from 'apps/order-api/src/dtos/ffm-to-ag-order.dto';
import { OrderCarrier } from 'apps/order-api/src/entities/order-carrier.entity';
import { OrderProduct } from 'apps/order-api/src/entities/order-product.entity';
import { Order } from 'apps/order-api/src/entities/order.entity';
import { Tag } from 'apps/order-api/src/entities/tag.entity';
import { FfmOrderStatus } from 'apps/order-api/src/enums/ffm-order-status.enum';
import { TagType } from 'apps/order-api/src/enums/tag.enum';
import { FfmOrder } from 'apps/order-api/src/read-entities/ffm-order/ffm-order.entity';
import { Company } from 'apps/order-api/src/read-entities/identity/company.entity';
import { Market } from 'apps/order-api/src/read-entities/identity/market.entity';
import { User } from 'apps/order-api/src/read-entities/identity/user.entity';
import axios from 'axios';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { OrderStatus } from 'core/enums/order-status.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import SignatureUtils from 'core/utils/SignatureUtils';
import { Redis } from 'ioredis';
import { forEach, isEmpty, isNil, sumBy, unionBy, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import {
  Brackets,
  FindCondition,
  getConnection,
  IsNull,
  LessThanOrEqual,
  Repository,
} from 'typeorm';
import { LocationsService } from './locations.service';
import { TagsService } from './tags.service';

@Injectable()
export class FfmOrdersService {
  private logger = new Logger('HTTP');

  constructor(
    @InjectRepository(Order, orderConnection)
    private ordersRepo: Repository<Order>,
    private tagsService: TagsService,
    @InjectRepository(Tag, orderConnection)
    private tagsRepo: Repository<Tag>,
    private locationService: LocationsService,
    private amqpConnection: AmqpConnection,
    @InjectRedis() private redis: Redis,
  ) {}

  // Sync order from AG Sale ====> FFM
  async syncOrder(data: Order) {
    let userInCharge: User, project: Project, compDetail: { domainName: string };
    const userId = data.saleId || data.carePageId;
    if (userId) {
      try {
        const [{ data: user }, { data: proj }, { data: company }] = await Promise.all([
          this.amqpConnection.request<{ data: User }>({
            exchange: 'identity-service-roles',
            routingKey: 'get-user',
            payload: { id: userId },
            timeout: 5000,
          }),
          this.amqpConnection.request<{ data: Project }>({
            exchange: 'identity-service-projects',
            routingKey: 'get-project-by-id',
            payload: { id: data.projectId },
            timeout: 5000,
          }),
          this.amqpConnection.request<{ data: { domainName: string } }>({
            exchange: 'identity-service-companies',
            routingKey: 'get-company-by-id',
            payload: { id: data.companyId },
            timeout: 10000,
          }),
        ]);
        if (user) userInCharge = user;
        if (proj) project = proj;
        if (company) compDetail = company;
      } catch (err) {
        console.log(`get user in charge err`, err);
      }
    }

    const customer = plainToInstance(FfmCustomerDto, {
      name: data.customerName,
      phone: data.customerPhone,
      address: data.addressText,
      ward: data.addressWardId,
      district: data.addressDistrictId,
      province: data.addressProvinceId,
      postCode: data.postCode ?? undefined,
    });

    const products = data.products.map(it => {
      const pr = plainToInstance(FfmProductDto, {
        sku: it.productDetail.sku,
        quantity: it.quantity,
        price: it.editedPrice ?? it.price,
        weight: it.productDetail.weight,
        isCombo: it.productDetail.product.isCombo,
      });
      if (it.productDetail?.product?.isCombo && it.productDetail?.product?.combo) {
        pr.comboItems = it.productDetail?.product?.combo?.reduce((prev, c) => {
          if (c.status > 0) {
            prev.push(
              plainToInstance(FfmProductComboItemDto, {
                sku: c.variant.sku,
                quantity: c.qty,
              }),
            );
          }

          return prev;
        }, []);
      }
      return pr;
    });

    const notes: string[] = [];
    if (data.note) notes.push(data.note);
    if (data.printNotes) notes.push(...data.printNotes.map(it => it.name));
    if (data.printNoteText) notes.push(data.printNoteText);
    // if (!isEmpty(data.carrier?.waybillNote)) notes.push(data.carrier.waybillNote);

    const clientId =
      data.market.fulfillmentPartnerClient?.clientId || data.market.fulfillmentPartner?.clientId;
    const apiKey =
      data.market.fulfillmentPartnerClient?.apiKey || data.market.fulfillmentPartner?.apiKey;

    const body: FfmOrderDto = {
      products,
      customer,
      externalId: data.displayId,
      clientId,
      countryId: String(data.countryId),
      discount: data.discount ?? 0,
      surcharge: data.surcharge ?? 0,
      shippingFee: data.shippingFee ?? 0,
      paid: data.paid ?? 0,
      subTotal: data.totalPrice,
      internalNote: isEmpty(notes) ? '' : notes.join(', '),
      companyId: data.companyId,
      typeOrder: data.type,
    };
    if (project) {
      body.projectName = project.name;
      body.projectId = project.id;
    }
    if (data.carrier?.customerEDD) body.customerEDD = moment(data.carrier.customerEDD).valueOf();
    if (data.ffmDisplayId) body.displayId = data.ffmDisplayId;
    if (data.tags) body.tags = data.tags.map(it => plainToInstance(FfmTagDto, { name: it.name }));
    if (userInCharge?.name) body.seller = userInCharge?.name;
    if (!isEmpty(data.carrier?.waybillNote)) body.waybillNote = data.carrier.waybillNote;

    const requestBody = JSON.stringify({ ...body, domainName: compDetail.domainName });
    const signature = SignatureUtils.gen(apiKey, requestBody);

    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json;charset=UTF-8',
      'ag-signature': signature,
    };

    try {
      const rawResponse = await axios.request({
        baseURL: process.env.FFM_URL_API,
        url: Boolean(data.ffmDisplayId) ? FFM_API.UPDATE_ORDER : FFM_API.CREATE_ORDER,
        method: 'POST',
        headers,
        data: requestBody,
      });

      console.log(`syncOrder() data: `, rawResponse?.data);
      const response = rawResponse.data;
      const syncedFfmOrder = response.data as FfmOrder;

      if (Boolean(data.ffmDisplayId)) return { response };

      const result = await this.ordersRepo.update(
        { id: data.id, ffmDisplayId: IsNull() },
        {
          ffmDisplayId: syncedFfmOrder.so,
          ffmCompanyId: data.market.fulfillmentPartnerId,
          ffmPartnerClientId: data.market.fulfillmentPartnerClientId,
        },
      );
      if (result.affected > 0) return { response };
      // await this.amqpConnection.publish(
      //   'order-service',
      //   'cancel-newly-created-ffm-order-when-sync-fail',
      //   {
      //     result,
      //     orderId: data.id,
      //     ffmDisplayId: syncedFfmOrder.so,
      //   },
      // );
      const oldOrder = await this.ordersRepo.findOne(data.id);
      return { response: { data: { so: oldOrder.ffmDisplayId } } };
    } catch (error) {
      console.log('syncOrder() ERROR: ', error);
      if (error?.response?.data?.message) {
        const mError = { ...error };
        const eMessage = this.parseSyncFfmErrorMessage(mError);
        if (eMessage) {
          mError.response.data.message = eMessage;
          return { error: mError };
        }
      }
      return { error };
    }
  }

  // Sync order status from AG Sale ====> FFM
  async updateFfmOrderStatus(data: Order) {
    const updateStatus = FfmOrderStatus[
      AG_TO_FFM_ORDER_STATUS_MAPPING[data.status]
    ] as keyof typeof FfmOrderStatus;
    const body: AgToFfmOrderStatusDto = {
      status: updateStatus,
      so: data.ffmDisplayId,
      external: data.displayId,
    };

    const apiKey =
      data.market.fulfillmentPartnerClient?.apiKey || data.market.fulfillmentPartner?.apiKey;
    const requestBody = JSON.stringify(body);
    const signature = SignatureUtils.gen(apiKey, requestBody);

    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json;charset=UTF-8',
      'ag-signature': signature,
    };

    try {
      const rawResponse = await axios.request({
        baseURL: process.env.FFM_URL_API,
        url: FFM_API.SYNC_ORDER_STATUS,
        method: 'POST',
        headers,
        data: requestBody,
      });

      // console.log(`rawResponse`, rawResponse)
      const response = rawResponse.data;
      return { response };
    } catch (error) {
      return { error };
    }
  }

  async scanMissingFfmDisplayIdOrders(filter, headers, request) {
    const orders = await this.ordersRepo
      .createQueryBuilder('o')
      .where('o.ffm_display_id IS NULL')
      .andWhere('o.status > 0')
      .select('o.id')
      .addSelect('o.displayId')
      .getMany();

    const ids = orders.map(it => it.displayId);

    const response = await this.amqpConnection.request({
      exchange: 'ffm-order',
      routingKey: 'get-display-ids-by-external-ids',
      payload: { ids },
      timeout: 10000,
    });

    const ffmOrders = response.data as any[];
    const ffmIdsLookup = ffmOrders.reduce((prev, it) => {
      prev[it.externalId] = it;
      return prev;
    }, {});
    const mOrders = orders.reduce((prev, it) => {
      if (ffmIdsLookup[it.displayId]) {
        prev.push({
          id: it.id,
          ffmDisplayId: ffmIdsLookup[it.displayId].displayId,
          ffmCompanyId: ffmIdsLookup[it.displayId].companyId,
        });
      }
      return prev;
    }, []);

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.save(plainToInstance(Order, mOrders));
      await queryRunner.commitTransaction();
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }

    return mOrders;
  }

  // Bulkd sync order status from FFM ====> AG Sale
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'bulk-sync-status-from-ffm',
    queue: 'order-service-bulk-sync-status-from-ffm',
    errorHandler: rmqErrorsHandler,
  })
  async bulkSyncStatus(data: { orders: FfmToAgOrderStatusDto[]; force?: false }) {
    console.log(`START ====== bulkSyncStatus ======`);
    await new Promise(resolve => setTimeout(resolve, 5000)); // Sleep for 5 seconds
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();

    const errors: FfmToAgOrderStatusDto[] = [];
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      const chunkSize = 200;

      const ordersToUpdate = [];
      const originalOrder = {};

      for (let i = 0; i < data.orders.length; i += chunkSize) {
        const chunk = data.orders.slice(i, i + chunkSize);

        const promises = chunk.map(async item => {
          const updateStatus = FFM_TO_AG_ORDER_STATUS_MAPPING[item.status];
          if (!updateStatus) {
            errors.push(item);
            return;
          }

          try {
            let order: any = await queryRunner.manager
              .createQueryBuilder()
              .select('o')
              .from('orders', 'o')
              .where('o.ffmDisplayId = :ffmDisplayId', { ffmDisplayId: item.displayId })
              .leftJoinAndSelect('o.products', 'products')
              .getOne();

            if (
              !order &&
              [FfmOrderStatus.Confirmed, FfmOrderStatus.AwaitingStock].includes(item.status)
            ) {
              // Find markets and try fetching the order
              const { data: markets } = await this.amqpConnection.request<{ data: Market[] }>({
                exchange: 'identity-service-projects',
                routingKey: 'find-markets-list',
                payload: {
                  fulfillmentPartnerClient: { clientId: item.clientId },
                } as FindCondition<Market>,
                timeout: 10000,
              });

              const clients = plainToInstance(Market, markets);
              for (const client of clients) {
                order = await queryRunner.manager
                  .createQueryBuilder()
                  .select('o')
                  .from('orders', 'o')
                  .where('o.displayId = :displayId')
                  .andWhere('o.status = :status')
                  .andWhere('o.projectId = :projectId')
                  .andWhere('o.countryId = :countryId')
                  .leftJoinAndSelect('o.products', 'products')
                  .setParameters({
                    displayId: item.externalId,
                    status: OrderStatus.New,
                    projectId: client.projectId,
                    countryId: client.countryId,
                  })
                  .getOne();

                if (order) break;
              }
            }

            if (!order) {
              errors.push(item);
              return;
            }
            originalOrder[order.id] = { ...order };

            const updatedAt = new Date(item.updatedAt);
            if (
              order.lastSyncedFfmStatusAt &&
              order.lastSyncedFfmStatusAt > updatedAt &&
              !data.force
            ) {
              console.log(order.lastSyncedFfmStatusAt, updatedAt);
              console.log(
                `no need to update order ${order.id} - ffm id ${item.displayId} because the updated at timestamp is older than current last synced timestamp`,
              );
              errors.push(item);
              return;
            }

            const actors = [];
            const response = await this.amqpConnection.request({
              exchange: 'identity-service-companies',
              routingKey: 'get-company-name-by-id',
              payload: { id: order.ffmCompanyId },
              timeout: 5000,
            });
            const ffmCompany = response.data as Company;
            if (ffmCompany) actors.push(ffmCompany.name);
            if (item.actor) actors.push(`(${item.actor})`);
            const statusChangeActor = actors.join(' ');

            // Prepare order for bulk insert/update
            ordersToUpdate.push({
              id: order.id,
              preStatus: order.status,
              status: updateStatus,
              statusChangeReason: item.reason,
              statusChangeDescription: item.description,
              lastSyncedFfmStatusAt: updatedAt,
              lastUpdateStatus: updatedAt,
              lastUpdatedBy: null,
              ffmDisplayId: order.ffmDisplayId,
              updatedAt,
              statusChangeActor,
            });
          } catch (error) {
            errors.push(item);
          }
        });
        await Promise.all(promises);
      }

      // Perform bulk update with all ordersToUpdate
      if (ordersToUpdate.length > 0) {
        // Updating orders instead of inserting
        const updatePromises = ordersToUpdate.map(order => {
          return queryRunner.manager
            .createQueryBuilder()
            .update('orders')
            .set({
              status: order.status,
              statusChangeReason: order.statusChangeReason,
              statusChangeDescription: order.statusChangeDescription,
              lastSyncedFfmStatusAt: order.lastSyncedFfmStatusAt,
              lastUpdateStatus: order.lastUpdateStatus,
              lastUpdatedBy: order.lastUpdatedBy,
              ffmDisplayId: order.ffmDisplayId,
              statusChangeActor: order.statusChangeActor,
            })
            .where('id = :id', { id: order.id }) // Update only the specific order by id
            .execute();
        });
        // Wait for all update promises to complete
        await Promise.all(updatePromises);
        await queryRunner.commitTransaction();
        ordersToUpdate.forEach(async order => {
          const orderToCatalogMessage = {
            orderId: order.id,
            orderDisplayId: originalOrder[order.id]?.displayId,
            products: originalOrder[order.id]?.products,
            preOrderStatus: order?.preStatus,
            orderStatus: order.status,
            warehouseId: originalOrder[order.id]?.countryId,
            companyId: originalOrder[order.id]?.companyId,
            ffmDisplayId: originalOrder[order.id]?.ffmDisplayId,
          };
          // Send message to warehouses services
          await this.amqpConnection.sendMessage(
            'OrderService.Orders.StatusChanged',
            null,
            orderToCatalogMessage,
          );

          await this.amqpConnection.publish('order-service', 'after-order-update', {
            id: order.id,
            updatedAt: order.updatedAt,
            updatedBy: null,
            beforeChanges: originalOrder[order.id],
            changes: { status: order.status },
          });
        });
      }
    } catch (error) {
      this.logger.error(`bulkSyncStatus ERROR: `, JSON.stringify(error));
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await this.amqpConnection.publish('order-service', 'push-sync-order-ffm-log', {
        orders: data.orders,
        errors: errors,
        force: data.force,
      });
      await queryRunner.release();
    }
    console.log(`END ====== bulkSyncStatus ======`);
    return new Nack(false);
  }

  // validate sync order status from ffm
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'validate-sync-order-status-from-ffm',
    queue: 'order-service-validate-sync-order-status-from-ffm',
    errorHandler: rmqErrorsHandler,
  })
  async validateSyncOrderStatusFromFFM(data: {
    orders: ValidateSyncOrderStatusFromFFM[];
    isRequest: boolean;
  }) {
    const connection = getConnection(orderConnection);
    const hashMapOrderFFMStatus: Record<any, any> = {};
    const hashMapOrderFFMLastUpdateStatusTime: Record<any, any> = {};
    const orderAgSaleIds = [];
    data.orders.forEach(o => {
      orderAgSaleIds.push(o.externalId);
      hashMapOrderFFMStatus[o.externalId] = o.status;
      hashMapOrderFFMLastUpdateStatusTime[o.externalId] = o.lastUpdateStatus;
    });
    const ordersAg: Array<any> = await connection
      .createQueryBuilder()
      .select('o')
      .from('orders', 'o')
      .where('o.displayId IN (:...orderAgSaleIds)', { orderAgSaleIds })
      .orderBy('o.countryId', 'ASC')
      .getMany();
    const ordersSyncError: Array<{
      displayId: string;
      externalId?: string;
      ffmStatus: any;
      agSaleStatus?: any;
    }> = [];
    for (const order of ordersAg) {
      if (
        order.status !== FFM_TO_AG_ORDER_STATUS_MAPPING[hashMapOrderFFMStatus[order.displayId]] &&
        new Date(order.lastUpdateStatus).getTime() <
          new Date(hashMapOrderFFMLastUpdateStatusTime[order.displayId]).getTime()
      ) {
        ordersSyncError.push({
          displayId: order.ffmDisplayId,
          externalId: order.displayId,
          ffmStatus: hashMapOrderFFMStatus[order.displayId],
          agSaleStatus: order.status,
        });
      }
    }
    if (ordersAg.length < data.orders.length) {
      data.orders.forEach(o => {
        if (ordersAg.findIndex(oA => oA.displayId === o.externalId) === -1) {
          ordersSyncError.push({
            displayId: o.displayId,
            externalId: '',
            ffmStatus: hashMapOrderFFMStatus[o.externalId],
            agSaleStatus: '',
          });
        }
      });
    }
    console.log(
      'List orders have invalid status: ',
      JSON.stringify({
        data: ordersSyncError,
        count: ordersSyncError.length,
      }),
    );
    if (data.isRequest) {
      return {
        data: ordersSyncError,
        count: ordersSyncError.length,
      };
    }
    return new Nack(false);
  }

  // Sync order status from FFM ====> AG Sale
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'sync-status-from-ffm',
    queue: 'order-service-sync-status-from-ffm',
    errorHandler: rmqErrorsHandler,
  })
  async syncStatus(data: FfmToAgOrderStatusDto) {
    console.log(`syncStatus from FFM ====> AG Sale payload`, data);
    await new Promise(resolve => setTimeout(resolve, 5000)); // Sleep for 5 seconds

    const updateStatus = FFM_TO_AG_ORDER_STATUS_MAPPING[data.status];
    if (!updateStatus) {
      console.log(`Cannot mapping FFM order status ${data.status} to AG Sale order status`);
      return new Nack(false);
    }

    const redisKey = `syncing-order-status-from-ffm.${data.displayId}.${data.externalId}.${data.clientId}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 20, 'NX')
      .incr(redisKey)
      .exec();

    if (isUpdating > 1) {
      throw new BadRequestException({
        isUpdating,
        data,
        message: `Another payload of order ${data.externalId} is processing. Please try again later.`,
      });
    }

    try {
      let order = await this.ordersRepo
        .createQueryBuilder('o')
        .where('o.ffmDisplayId = :ffmDisplayId', { ffmDisplayId: data.displayId })
        .leftJoinAndSelect('o.products', 'products')
        .getOne();
      if (
        !order &&
        [FfmOrderStatus.Confirmed, FfmOrderStatus.AwaitingStock].includes(data.status)
      ) {
        const { data: markets } = await this.amqpConnection.request<{ data: Market[] }>({
          exchange: 'identity-service-projects',
          routingKey: 'find-markets-list',
          payload: {
            fulfillmentPartnerClient: {
              clientId: data.clientId,
            },
          } as FindCondition<Market>,
          timeout: 10000,
        });
        const clients = plainToInstance(Market, markets);
        for (const client of clients) {
          if (client) {
            order = await this.ordersRepo
              .createQueryBuilder('o')
              .where('o.displayId = :displayId')
              .andWhere('o.status = :status')
              .andWhere('o.projectId = :projectId')
              .andWhere('o.countryId = :countryId')
              .leftJoinAndSelect('o.products', 'products')
              .setParameters({
                displayId: data.externalId,
                status: OrderStatus.New,
                projectId: client.projectId,
                countryId: client.countryId,
              })
              .getOne();
          }
          if (order) {
            break;
          }
        }
      }

      if (!order) {
        throw new BadRequestException(`Cannot find order with ffm display id is ${data.displayId}`);
      }
      if (
        !order.ffmDisplayId &&
        [FfmOrderStatus.Confirmed, FfmOrderStatus.AwaitingStock].includes(data.status)
      ) {
        order.ffmDisplayId = data.displayId;
      } else if (order.ffmDisplayId !== data.displayId) {
        throw new BadRequestException('External ID is not match');
      }
      const updatedAt = new Date(data.updatedAt);
      if (order.lastSyncedFfmStatusAt && order.lastSyncedFfmStatusAt > updatedAt) {
        console.log(
          `no need to update order ${order.id} - ffm id ${data.displayId} because the updated at timestamp is older than current last synced timestamp`,
        );
        return new Nack(false);
      }

      // const nextStatuses = NEXT_STATUS_BY_SYSTEM[order.status];
      // if (!nextStatuses || !nextStatuses.includes(updateStatus)) {
      //   throw new BadRequestException(
      //     `Invalid update status of order ${order.displayId} ${
      //       OrderStatus[order.status]
      //     } => ${OrderStatus[updateStatus]}`,
      //   );
      // }
      const actors = [];
      const response = await this.amqpConnection.request({
        exchange: 'identity-service-companies',
        routingKey: 'get-company-name-by-id',
        payload: { id: order.ffmCompanyId },
        timeout: 5000,
      });
      const ffmCompany = response.data as Company;
      if (ffmCompany) actors.push(ffmCompany.name);
      if (data.actor) actors.push(`(${data.actor})`);
      const statusChangeActor = actors.join(' ');
      // console.log(order.lastSyncedFfmStatusAt);
      const updateResult = await this.ordersRepo
        .createQueryBuilder()
        .update()
        .set({
          id: order.id,
          status: updateStatus,
          statusChangeReason: data.reason,
          statusChangeDescription: data.description,
          statusChangeActor,
          lastSyncedFfmStatusAt: updatedAt,
          lastUpdateStatus: updatedAt,
          lastUpdatedBy: null,
          ffmDisplayId: order.ffmDisplayId,
        })
        .where(
          'id = :id AND (last_synced_ffm_status_at IS NULL OR last_synced_ffm_status_at <= :updatedAt)',
          {
            updatedAt,
            id: order.id,
          },
        )
        .execute();

      console.log(
        `update status of order ${order.displayId} ${OrderStatus[order.status]} => ${
          OrderStatus[updateStatus]
        } succeed`,
        updateResult,
      );

      if (updateResult.affected) {
        const orderToCatalogMessage = {
          orderId: order.id,
          orderDisplayId: order.displayId,
          products: order.products,
          preOrderStatus: order.status,
          orderStatus: updateStatus,
          warehouseId: order.countryId,
          companyId: order.companyId,
          ffmDisplayId: data.displayId,
        };

        // Send message to warehouses services
        await this.amqpConnection.sendMessage(
          'OrderService.Orders.StatusChanged',
          null,
          orderToCatalogMessage,
        );

        await this.amqpConnection.publish('order-service', 'after-order-update', {
          id: order.id,
          updatedAt,
          updatedBy: null,
          beforeChanges: order,
          changes: { status: updateStatus },
        });
      }
    } catch (error) {
      console.error(`sync order status from FFM => AG Sale error`, error, data);
      throw error;
    } finally {
      await this.redis.del(redisKey);
    }

    return new Nack(false);
  }

  // Sync order status from FFM ====> AG Sale
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'sync-tags-from-ffm',
    queue: 'order-service-sync-tags-from-ffm',
    errorHandler: rmqErrorsHandler,
  })
  async syncTags(payload: FfmToAgOrderTagsDto) {
    try {
      const order = await this.ordersRepo
        .createQueryBuilder('o')
        .where('o.ffmDisplayId = :ffmDisplayId', { ffmDisplayId: payload.displayId })
        .leftJoinAndSelect('o.tags', 'tags')
        .getOne();

      if (!order)
        throw new BadRequestException(
          `Cannot find order with ffm display id is ${payload.displayId}`,
        );

      if (isEmpty(payload.tags) && isEmpty(order.tags)) return new Nack(false);

      const updatedAt = new Date(payload.updatedAt);
      if (order.lastSyncedFfmTagsAt && order.lastSyncedFfmTagsAt > updatedAt) {
        console.log(
          `no need to update tags for order ${order.id} - ffm id ${payload.displayId} because the updated at timestamp is older than current last synced timestamp`,
        );
        return new Nack(false);
      }

      const ffmTagNames = uniq(payload.tags.map(t => t.content));
      let existingTags: Tag[] = [];
      if (!isEmpty(ffmTagNames))
        existingTags = await this.tagsService.findByNames(ffmTagNames, order.companyId);

      const existingNames = existingTags.map(t => t.name);

      let newTags: Tag[] = [];
      for (const ffmTag of payload.tags) {
        if (existingNames.includes(ffmTag.content)) continue;
        newTags.push(
          plainToInstance(Tag, {
            name: ffmTag.content,
            type: TagType.order,
            companyId: order.companyId,
          }),
        );
      }

      newTags = await this.tagsRepo.save(newTags);
      const allSyncedTags = [...existingTags, ...newTags];
      order.tags = allSyncedTags;

      const actors = [];
      const response = await this.amqpConnection.request({
        exchange: 'identity-service-companies',
        routingKey: 'get-company-name-by-id',
        payload: { id: order.ffmCompanyId },
        timeout: 5000,
      });
      const ffmCompany = response.data as Company;
      if (ffmCompany) actors.push(ffmCompany.name);
      if (payload.actor) actors.push(`(${payload.actor})`);
      const statusChangeActor = actors.join(' ');
      let metadataTagLog;
      metadataTagLog = order.metadataTagLog || '{}';
      metadataTagLog = JSON.parse(metadataTagLog);
      metadataTagLog = { ...metadataTagLog, tags: order.tags.map(t => t.id) };
      const mOrder = await this.ordersRepo.save({
        id: order.id,
        tags: order.tags,
        statusChangeActor,
        lastSyncedFfmTagsAt: updatedAt,
        lastUpdatedBy: null,
        metadataTagLog: JSON.stringify(metadataTagLog),
      });

      console.log(`update tags of order ${order.displayId} succeed`, mOrder);

      await this.amqpConnection.publish('order-service', 'after-order-update', {
        id: order.id,
        updatedAt,
        updatedBy: null,
      });
    } catch (error) {
      console.error(`sync order tags from FFM => AG Sale error`, error, payload);
      throw error;
    }

    return new Nack(false);
  }

  // Sync order carrier from FFM ====> AG Sale
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'sync-carrier-from-ffm',
    queue: 'order-service-sync-carrier-from-ffm',
    errorHandler: rmqErrorsHandler,
  })
  async syncCarrier(data: FfmToAgOrderCarrierDto) {
    console.log(`syncCarrier from FFM ====> AG Sale payload`, data);
    try {
      const keys = Object.keys(data) as (keyof FfmToAgOrderCarrierDto)[];
      for (const key of keys) {
        if (!data[key] && !['carrierCode', 'waybillNumber'].includes(key))
          throw new BadRequestException(
            `syncCarrier from FFM ====> AG Sale fail because not enough information`,
          );
        continue;
      }

      const order = await this.ordersRepo.findOne({
        where: [{ ffmDisplayId: data.displayId }],
        relations: ['carrier'],
      });

      if (!order) {
        throw new BadRequestException(`Cannot find order with ffm display id is ${data.displayId}`);
      }

      const carrier = new OrderCarrier();

      if (!isNil(data.waybillNumber)) carrier.waybillNumber = data.waybillNumber || '';
      if (!isNil(data.carrierCode)) carrier.carrierCode = data.carrierCode || '';

      const actors = [];
      const response = await this.amqpConnection.request({
        exchange: 'identity-service-companies',
        routingKey: 'get-company-name-by-id',
        payload: { id: order.ffmCompanyId },
        timeout: 5000,
      });
      const ffmCompany = response.data as Company;
      if (ffmCompany) actors.push(ffmCompany.name);
      if (data.actor) actors.push(`(${data.actor})`);
      const statusChangeActor = actors.join(' ');
      let metadataCarrierLog = JSON.parse(order.metadataCarrierLog);

      metadataCarrierLog = {
        ...metadataCarrierLog,
        carrierCode: carrier?.carrierCode || '',
        waybillNumber: carrier?.waybillNumber || '',
      };
      const mOrder = await this.ordersRepo.save({
        id: order.id,
        statusChangeActor,
        carrier,
        lastUpdatedBy: null,
        metadataCarrierLog: JSON.stringify(metadataCarrierLog),
        lastWaybillNumber:
          !carrier?.waybillNumber || carrier?.waybillNumber === ''
            ? order.carrier.waybillNumber
            : '',
      });

      console.log(`update carrier of order ${order.displayId} succeed`, mOrder);

      await this.amqpConnection.publish('order-service', 'after-order-update', {
        id: order.id,
        updatedAt: mOrder.updatedAt,
        updatedBy: null,
      });
    } catch (error) {
      console.error(`sync order carrier from FFM => AG Sale error`, error, data);
      throw error;
    }
    return new Nack(false);
  }

  // Sync order other information from FFM ====> AG Sale
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'sync-info-order-from-ffm',
    queue: 'order-service-sync-other-information-from-ffm',
    errorHandler: defaultNackErrorHandler,
  })
  async syncOtherInformation(payload: FfmToAgOrderInfoDto) {
    const redisKey = `syncing-info-order-from-ffm.${payload.displayId}.${payload.externalId}.${payload.clientId}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 20, 'NX')
      .incr(redisKey)
      .exec();

    if (isUpdating > 1) {
      throw new BadRequestException({
        isUpdating,
        payload,
        message: `Another sync info ffm order payload of ${payload.externalId} is processing. Please try again later.`,
      });
    }

    try {
      const requiredProps: (keyof FfmToAgOrderInfoDto)[] = ['displayId', 'updatedAt'];
      const missingProps = [];
      for (const key of requiredProps) if (!payload[key]) missingProps.push(key);
      if (!isEmpty(missingProps)) {
        console.log(
          `syncOtherInformation fail because not enough information`,
          `Missing ${missingProps.join(', ')}`,
        );
        return new Nack(false);
      }
      const order = await this.ordersRepo
        .createQueryBuilder('o')
        .where('o.ffmDisplayId = :ffmDisplayId', { ffmDisplayId: payload.displayId })
        .leftJoinAndSelect('o.products', 'products')
        .getOne();

      if (!order) {
        console.log(`Cannot find order with ffm display id is ${payload.displayId}`);
        return new Nack(false);
      }
      if (
        ![
          OrderStatus.AwaitingStock,
          OrderStatus.Reconfirm,
          OrderStatus.Confirmed,
          OrderStatus.Preparing,
          OrderStatus.HandlingOver,
          OrderStatus.InTransit,
          OrderStatus.InDelivery,
          OrderStatus.FailedDelivery,
        ].includes(order.status)
      ) {
        console.log(
          `Cannot update order ${payload.displayId} information when status is `,
          PlainOrderStatus[order.status],
        );
        return new Nack(false);
      }
      const updatedAt = new Date(payload.updatedAt);
      if (order.lastSyncedFfmInfoAt && order.lastSyncedFfmInfoAt > updatedAt) {
        console.log(
          `no need to update order ${order.id} - ffm id ${payload.displayId} because the updated at timestamp is older than current last synced timestamp`,
        );
        return new Nack(false);
      }

      const updateData: Partial<Order> = {};
      let metadata;
      metadata = order.metadata || '{}';
      metadata = JSON.parse(metadata);
      if (payload.recipientName) {
        updateData.customerName = payload.recipientName;
        metadata = {
          ...metadata,
          customer: {
            ...metadata.customer,
            customerName: payload.recipientName,
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (payload.recipientPhone) {
        updateData.customerPhone = payload.recipientPhone;
        metadata = {
          ...metadata,
          customer: {
            ...metadata.customer,
            customerPhone: payload.recipientPhone,
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (payload.recipientAddress) {
        updateData.addressText = payload.recipientAddress;
        metadata = {
          ...metadata,
          customer: {
            ...metadata.customer,
            addressText: payload.recipientAddress,
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (payload.recipientAddressNote) updateData.addressNote = payload.recipientAddressNote;
      if (payload.recipientWardId) {
        const ward = await this.locationService.getWard(payload.recipientWardId, {
          where: { districtId: payload.recipientDistrictId },
        });
        if (ward) {
          updateData.addressWardId = payload.recipientWardId;
          updateData.addressWard = ward?.name;
          metadata = {
            ...metadata,
            customer: {
              ...metadata.customer,
              addressWard: ward?.name,
              addressWardId: payload.recipientWardId,
            },
          };
          updateData.metadata = JSON.stringify(metadata);
        }
      }
      if (payload.recipientDistrictId) {
        const district = await this.locationService.getDistrict(payload.recipientDistrictId);
        updateData.addressDistrictId = payload.recipientDistrictId;
        updateData.addressDistrict = district?.name;
        metadata = {
          ...metadata,
          customer: {
            ...metadata.customer,
            addressDistrict: district?.name,
            addressDistrictId: payload.recipientDistrictId,
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (payload.recipientProvinceId) {
        const province = await this.locationService.getProvince(payload.recipientProvinceId);
        updateData.addressProvinceId = payload.recipientProvinceId;
        updateData.addressProvince = province?.name;
        metadata = {
          ...metadata,
          customer: {
            ...metadata.customer,
            addressProvince: province?.name,
            addressProvinceId: payload.recipientProvinceId,
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (payload.recipientPostCode) {
        updateData.postCode = payload.recipientPostCode;
        metadata = {
          ...metadata,
          customer: {
            ...metadata.customer,
            postCode: payload?.recipientPostCode?.toString(),
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (!isNil(payload.discount)) {
        updateData.discount = payload.discount;
        metadata = {
          ...metadata,
          fee: {
            ...metadata.fee,
            discount: payload.discount,
            cod: Math.max(
              Number(metadata?.fee?.totalPrice) +
                Number(metadata?.fee?.shippingFee) +
                metadata?.fee?.surcharge -
                payload?.discount -
                metadata?.fee?.paid,
              0,
            ),
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (!isNil(payload.surcharge)) {
        updateData.surcharge = payload.surcharge;
        metadata = {
          ...metadata,
          fee: {
            ...metadata.fee,
            surcharge: payload.surcharge,
            cod: Math.max(
              Number(metadata?.fee?.totalPrice) +
                Number(metadata?.fee?.shippingFee) +
                payload?.surcharge -
                metadata?.fee?.discount -
                metadata?.fee?.paid,
              0,
            ),
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (!isNil(payload.shippingFee)) {
        updateData.shippingFee = payload.shippingFee;
        metadata = {
          ...metadata,
          fee: {
            ...metadata.fee,
            shippingFee: payload.shippingFee,
            cod: Math.max(
              Number(metadata?.fee?.totalPrice) +
                payload?.shippingFee +
                metadata?.fee?.surcharge -
                metadata?.fee?.discount -
                metadata?.fee?.paid,
              0,
            ),
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (!isNil(payload.paid)) {
        updateData.paid = payload.paid;
        metadata = {
          ...metadata,
          fee: {
            ...metadata.fee,
            paid: payload.paid,
            cod: Math.max(
              Number(metadata?.fee?.totalPrice) +
                Number(metadata?.fee?.shippingFee) +
                metadata?.fee?.surcharge -
                metadata?.fee?.discount -
                payload?.paid,
              0,
            ),
          },
        };
        updateData.metadata = JSON.stringify(metadata);
      }
      if (!isEmpty(payload.products)) {
        const mProducts: any[] = metadata.products || [];
        if (mProducts.length > 0) {
          let flag = false;
          const ffmProductsLookup = payload.products.reduce((prev, next) => {
            prev[next.productSKU] = next;
            return prev;
          }, {});
          for (const orderItem of order.products) {
            const ffmProduct = ffmProductsLookup[orderItem.productDetail.sku];
            if (!ffmProduct) continue;

            if (!isNil(ffmProduct.weight) && !isNil(ffmProduct.quantity)) {
              for (const product of mProducts) {
                if (
                  product.sku === orderItem.productDetail.sku &&
                  product.weight !== ffmProduct.weight / ffmProduct.quantity
                ) {
                  flag = true;
                  product.weight = ffmProduct.weight / ffmProduct.quantity;
                  break;
                }
              }
            }
          }
          if (flag) {
            metadata.products = mProducts;
            const totalWeight = sumBy(mProducts, o => o.weight * o.quantity);
            metadata.totalWeight = totalWeight;
            updateData.metadata = JSON.stringify(metadata);
          }
        }
      }

      const actors = [];
      const response = await this.amqpConnection.request({
        exchange: 'identity-service-companies',
        routingKey: 'get-company-name-by-id',
        payload: { id: order.ffmCompanyId },
        timeout: 5000,
      });
      const ffmCompany = response.data as Company;
      if (ffmCompany) actors.push(ffmCompany.name);
      if (payload.creator) actors.push(`(${payload.creator})`);
      const changeActor = actors.join(' ');

      const connection = getConnection(orderConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const result = await queryRunner.manager
          .createQueryBuilder(Order, 'o')
          .update()
          .where({ id: order.id })
          .andWhere(
            new Brackets(sqb => {
              sqb
                .where({ lastSyncedFfmInfoAt: IsNull() })
                .orWhere({ lastSyncedFfmInfoAt: LessThanOrEqual(updatedAt) });
            }),
          )
          .set({
            ...updateData,
            statusChangeActor: changeActor,
            lastSyncedFfmInfoAt: updatedAt,
            lastUpdatedBy: null,
          })
          .execute();
        if (result.affected) {
          if (!isEmpty(payload.products)) {
            const ffmProductsLookup = payload.products.reduce((prev, next) => {
              prev[next.productSKU] = next;
              return prev;
            }, {});

            for (const orderItem of order.products) {
              const ffmProduct = ffmProductsLookup[orderItem.productDetail.sku];
              if (!ffmProduct) continue;

              const itemUpdates: Partial<OrderProduct> = {};
              if (!isNil(ffmProduct.quantity) && ffmProduct.quantity !== orderItem.quantity) {
                itemUpdates.quantity = ffmProduct.quantity;
              }
              if (!isNil(ffmProduct.weight) && !isNil(ffmProduct.quantity)) {
                itemUpdates.productDetail = orderItem.productDetail;
                itemUpdates.productDetail.weight = ffmProduct?.weight / ffmProduct?.quantity;
              }

              if (!isEmpty(itemUpdates)) {
                const updateOrderItemResult = await queryRunner.manager.update(
                  OrderProduct,
                  { id: orderItem.id, orderId: order.id },
                  itemUpdates,
                );

                console.log(`updateOrderItemResult`, updateOrderItemResult);
              }
            }
          }

          if (payload.waybillNote) {
            const carrier = new OrderCarrier();
            carrier.orderId = order.id;
            carrier.waybillNote = payload.waybillNote;
            let metadataCarrierLog = JSON.parse(order.metadataCarrierLog);

            const waybillSyncResult = await queryRunner.manager
              .createQueryBuilder()
              .insert()
              .into(OrderCarrier)
              .values(carrier)
              .orUpdate(['waybill_note'], ['order_id'])
              .execute();
            if (waybillSyncResult) {
              metadataCarrierLog = {
                ...metadataCarrierLog,
                waybillNote: payload.waybillNote,
              };
              await queryRunner.manager.update(
                Order,
                { id: order.id },
                {
                  metadataCarrierLog: JSON.stringify(metadataCarrierLog),
                },
              );
            }
            console.log(`waybillSyncResult`, waybillSyncResult);
          }
        }
        await queryRunner.commitTransaction();

        await this.amqpConnection.publish('order-service', 'after-order-update', {
          id: order.id,
          updatedAt,
          updatedBy: null,
        });
      } catch (e) {
        await queryRunner.rollbackTransaction();
        throw e;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      console.error(`sync order other information from FFM => AG Sale error`, error, payload);
      throw error;
    } finally {
      await this.redis.del(redisKey);
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'cancel-newly-created-ffm-order-when-sync-fail',
    queue: 'order-process-cancel-newly-created-ffm-order-when-sync-fail',
    errorHandler: rmqErrorsHandler,
  })
  async cancelNewlyCreatedFfmOrderIfSyncFailed({ orderId, ffmDisplayId }) {
    const order = await this.ordersRepo
      .createQueryBuilder('o')
      .where('o.id = :orderId')
      .setParameters({ orderId })
      .getOne();

    const { data: market } = await this.amqpConnection.request<{ data: Market }>({
      exchange: 'identity-service-projects',
      routingKey: 'find-market',
      payload: {
        projectId: order.projectId,
        countryId: order.countryId,
      } as FindCondition<Market>,
      timeout: 10000,
    });
    if (!market) return new Nack(false);

    const body: AgToFfmOrderStatusDto = {
      status: FfmOrderStatus[FfmOrderStatus.Canceled] as keyof typeof FfmOrderStatus,
      so: ffmDisplayId,
      external: order.displayId,
    };

    // const clientId = market.fulfillmentPartnerClient?.clientId || market.fulfillmentPartner?.clientId;
    const apiKey = market.fulfillmentPartnerClient?.apiKey || market.fulfillmentPartner?.apiKey;

    const requestBody = JSON.stringify(body);
    const signature = SignatureUtils.gen(apiKey, requestBody);

    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json;charset=UTF-8',
      'ag-signature': signature,
    };

    try {
      const rawResponse = await axios.request({
        baseURL: process.env.FFM_URL_API,
        url: FFM_API.SYNC_ORDER_STATUS,
        method: 'POST',
        headers,
        data: requestBody,
      });

      // console.log(`rawResponse`, rawResponse)
      const response = rawResponse.data;
      console.log(`cancel newly created ffm order ${ffmDisplayId} result`, response.data);
    } catch (error) {
      console.error(`cancel newly created ffm order ${ffmDisplayId} error`, error);
      throw error;
    }
    return new Nack(false);
  }

  parseSyncFfmErrorMessage(error: any): string | undefined {
    switch (error?.response?.data?.message) {
      case 'Client invalid':
        return `Client does not exist on FFM, please check again.`;
      case 'Country invalid':
        return `Client is not available in this market on FFM, please check again.`;
      case 'Province invalid':
        return `Province/city is not valid on FFM, please check again.`;
      case 'District invalid':
        return `District is not valid on FFM, please check again.`;
      case 'PostCode cannot be empty':
        return `Postcode does not exist in FFM, please check again.`;
      case 'Ward invalid':
        return `Ward/commune is not valid in FFM, please check again.`;
      case 'Product invalid':
        return `Product does not exist at FFM, please check again.`;
      default:
        return;
    }
  }
}
