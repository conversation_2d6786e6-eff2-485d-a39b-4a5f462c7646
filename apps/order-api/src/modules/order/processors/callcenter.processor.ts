import { OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { LeadsService } from '../services/leads.service';
import { forwardRef, Inject, Logger } from '@nestjs/common';
import { YcallRequestWebhookDto } from 'apps/order-api/src/dtos/callcenter.dto';
import { EtelecomService } from '../services/etelecom.service';

export interface JobDataCallCenter {
  callHistoryId?: number;
  extensionId?: number;
  leadId?: number;
  body?: YcallRequestWebhookDto; // Add body with specific type
}

@Processor('callcenter')
export class CallCenterProcessor {
  private readonly logger = new Logger('PROCESSER-CALLCENTER');
  constructor(
    @Inject(forwardRef(() => LeadsService)) private leadsService: LeadsService,
    @Inject(forwardRef(() => EtelecomService))
    private etelecomService: EtelecomService,
  ) {}

  @OnQueueActive()
  onActive(job: Job) {
    this.logger.log(
      `Processor:@OnQueueActive - Processing job ${job.id} of type ${job.queue.name}.`,
    );
  }

  @OnQueueCompleted()
  onComplete(job: Job) {
    this.logger.log(
      `Processor:@OnQueueCompleted - Completed job ${job.id} of type ${job.queue.name}.`,
    );
  }

  @OnQueueFailed()
  onError(job: Job<any>, error) {
    console.log(error);
    this.logger.log(
      `Processor:@OnQueueFailed - Failed job ${job.id} of type ${job.queue.name}: ${error.message}`,
      error.stack,
    );
  }

  @Process({ name: 'process-webhook', concurrency: 5 })
  async processWebhookYcall(job: Job) {
    return this.leadsService.processWebhookYcall(job);
  }

  @Process({ name: 'call-history-crawl-job' })
  async processCallHistoryCrawl() {
    return this.etelecomService.processCallHistoryCrawl();
  }

  @Process({ name: 'call-history-process-job' })
  async processCallHistories() {
    return this.etelecomService.processCallHistories();
  }
}
