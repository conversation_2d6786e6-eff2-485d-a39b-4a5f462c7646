import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { orderConnection } from 'core/constants/database-connection.constant';
import { DateScalar } from 'core/graphql/date-scalar';
import { PubSubModule } from '../../../../../core/pubsub/pub-sub.module';
import { CancelReason } from '../../entities/cancel-reasons.entity';
import { CareReason } from '../../entities/care-reason.entity';
import { Channel } from '../../entities/channel.entity';
import { Currency } from '../../entities/currency.entity';
import { Customer } from '../../entities/customer.entity';
import { District } from '../../entities/district.entity';
import { LandingPage } from '../../entities/landing-page.entity';
import { LeadDistributeConfig } from '../../entities/lead-distribute-config.entity';
import { SystemLog } from '../../entities/system-log.entity';
import { OrderCustomerServiceConfig } from '../../entities/order-cs-config.entity';
import { OrderCustomerServiceCount } from '../../entities/order-cs-count.entity';
import { OrderCustomerServiceHistory } from '../../entities/order-cs-history.entity';
import { OrderCustomerService } from '../../entities/order-cs.entity';
import { OrderExportTemplate } from '../../entities/order-export-template.entity';
import { OrderHistory } from '../../entities/order-history.entity';
import { OrderProduct } from '../../entities/order-product.entity';
import { OrderSource } from '../../entities/order-source.entity';
import { Order } from '../../entities/order.entity';
import { Page } from '../../entities/page.entity';
import { PfgOrdersScanLog } from '../../entities/pfg-orders-scan-log.entity';
import { PossibleDuplicateOrder } from '../../entities/possible-duplicate-order.entity';
import { PrintNote } from '../../entities/print-notes.entity';
import { ProjectOrdersCount } from '../../entities/project-orders-count.entity';
import { Province } from '../../entities/province.entity';
import { Source } from '../../entities/source.entity';
import { TagGroup } from '../../entities/tag-group.entity';
import { Tag } from '../../entities/tag.entity';
import { Ward } from '../../entities/ward.entity';
import { CallGroup } from '../../entities/call-group.entity';
import { CallGroupUser } from '../../entities/call-group-user.entity';
import { ApiModule } from '../api/api.module';
import { ShiftModule } from '../shift/shift.module';
import { CancelReasonsController } from './controllers/cancel-reasons.controller';
import { CareReasonsController } from './controllers/care-reasons.controller';
import { ChannelsController } from './controllers/channels.controller';
import { CurrenciesController } from './controllers/currencies.controller';
import { CustomersController } from './controllers/customers.controller';
import { ExchangeController } from './controllers/exchange.controller';
import { EtelecomController } from './controllers/etelecom.controller';
import { LandingPagesController } from './controllers/landing-pages.controller';
import { LeadDistributeConfigsController } from './controllers/lead-distribute-configs.controller';
import { LocationsController } from './controllers/locations.controller';
import { OrdersController } from './controllers/orders.controller';
import { PagesController } from './controllers/pages.controller';
import { PancakeController } from './controllers/pancake.controller';
import { PrintNotesController } from './controllers/print-notes.controller';
import { SourcesController } from './controllers/sources.controller';
import { TagGroupsController } from './controllers/tag-groups.controller';
import { TagsController } from './controllers/tags.controller';
import { OrdersProcessor } from './processors/orders.processor';
import { OrdersResolver } from './resolvers/orders.resolver';
import { CancelReasonsService } from './services/cancel-reasons.service';
import { CareReasonsService } from './services/care-reasons.service';
import { ChannelsService } from './services/channels.service';
import { CurrenciesService } from './services/currencies.service';
import { CustomerLogsService } from './services/customer-logs.service';
import { CustomersService } from './services/customers.service';
import { FfmOrdersService } from './services/ffm-orders.service';
import { LandingPagesService } from './services/landing-pages.service';
import { LeadDistributeConfigsService } from './services/lead-distribute-configs.service';
import { LocationsService } from './services/locations.service';
import { OrderExportTemplateService } from './services/order-export-template.service';
import { OrderLogsService } from './services/order-logs.service';
import { OrderSyncService } from './services/order-sync.service';
import { OrderSubscriber } from './services/order.subscriber';
import { OrdersService } from './services/orders.service';
import { PagesService } from './services/pages.service';
import { PosOrdersService } from './services/pos-orders.service';
import { PrintNotesService } from './services/print-notes.service';
import { SourcesService } from './services/sources.service';
import { TagGroupsService } from './services/tag-groups.service';
import { TagsService } from './services/tags.service';
import { Lead } from '../../entities/lead.entity';
import { LeadCareItem } from '../../entities/lead-care-item.entity';
import { LeadsController } from './controllers/leads.controller';
import { LeadsService } from './services/leads.service';
import { LeadCare } from '../../entities/lead-care.entity';
import { FilterCollection } from '../../entities/filter-collection.entity';
import { AppointmentSchedule } from '../../entities/appointment-schedule.entity';
import { TelesaleGroup } from '../../entities/telesale-group.entity';
import { TelesaleGroupsController } from './controllers/telesale-groups.controller';
import { TelesaleGroupsService } from './services/telesale-groups.service';
import { LeadCareItemSubscriber } from './services/lead-care-item.subscriber';
import { LeadSubscriber } from './services/lead.subscriber';
import { AppointmentsService } from './services/appointments.service';
import { Conversation } from '../../entities/conversation.entity';
import { FacebookOrdersService } from './services/facebook-conversations.service';
import { DuplicateLead } from '../../entities/duplicate-lead.entity';
import { RecordFile } from '../../entities/record-file.entity';
import { Cdr } from '../../entities/cdr.entity';
import { MarketplaceIntegration } from '../../entities/marketplace-integration.entity';
import { MarketplacesController } from './controllers/marketplace-integrations.controller';
import { MarketplaceIntegrationsService } from './services/marketplace-integrations.service';
import { ShopifyController } from './controllers/shopify.controller';
import { ShopifyService } from './services/shopify.service';
import { ShopifyOrdersService } from './services/shopify-orders.service';
import { Extension } from '../../entities/extension.entity';
import { Tenant } from '../../entities/tenant.entity';
import { CallController } from './controllers/call.controller';
import { CallService } from './services/call.service';
import { OrderTrackingController } from './controllers/order-tracking.controller';
import { CallHistory } from '../../entities/call-history.entity';
import { CallHistoryController } from './controllers/call-history.controller';
import { CallHistoryService } from './services/call-histories.service';
import { AppRelease } from '../../entities/app-release.entity';
import { AppReleasesController } from './controllers/app-releases.controller';
import { AppReleasesService } from './services/app-releases.service';
import { CallCenter, CallCenterExtension } from 'apps/order-api/src/entities';
import { CallCenterProcessor } from 'apps/order-api/src/modules/order/processors/callcenter.processor';
import { RemovedDuplicateLeads } from 'apps/order-api/src/entities/removed-duplicate-leads.entity';
import { CustomerNotes } from '../../entities/customer-notes.entity';
import { LeadsASController } from './controllers/leads-as.controller';
import { LeadsASService } from './services/leads-as.service';
import { LeadASDistributeConfig } from '../../entities/lead-as-distribute-config.entity';
import { LeadASDistributeConfigsController } from './controllers/lead-as-distribute-configs.controller';
import { LeadASDistributeConfigsService } from './services/lead-as-distribute-configs.service';
import { OrderStatusHistory } from '../../entities/order-status-history.entity';
import { LeadAfterSaleSource } from '../../entities/lead-after-sale-source.entity';
import { OneSignalController } from './controllers/one-signal.controller';
import { OrderBackupSyncStatusProcessor } from './processors/order-backup-sync-status.processor';
import { ClickhouseModule } from '../clickhouse/clickhouse.module';
import { EtelecomService } from './services/etelecom.service';
import { EtelecomRawLogs } from '../../entities/etelecom-raw-logs.entity';

@Module({
  imports: [
    RabbitMQModule.externallyConfigured(RabbitMQModule, 0),
    TypeOrmModule.forFeature(
      [
        Order,
        OrderProduct,
        Source,
        Currency,
        Customer,
        Ward,
        District,
        Province,
        OrderHistory,
        Tag,
        TagGroup,
        CancelReason,
        Page,
        OrderCustomerService,
        OrderCustomerServiceHistory,
        OrderCustomerServiceConfig,
        OrderCustomerServiceCount,
        ProjectOrdersCount,
        CallGroup,
        CallGroupUser,
        EtelecomRawLogs,
        PfgOrdersScanLog,
        PrintNote,
        SystemLog,
        OrderExportTemplate,
        PossibleDuplicateOrder,
        LandingPage,
        OrderSource,
        Channel,
        CareReason,
        Lead,
        LeadCare,
        LeadCareItem,
        LeadDistributeConfig,
        FilterCollection,
        AppointmentSchedule,
        TelesaleGroup,
        Conversation,
        DuplicateLead,
        RecordFile,
        Cdr,
        MarketplaceIntegration,
        Extension,
        Tenant,
        CallHistory,
        AppRelease,
        CallCenter,
        CallCenterExtension,
        RemovedDuplicateLeads,
        CustomerNotes,
        LeadASDistributeConfig,
        OrderStatusHistory,
        LeadAfterSaleSource,
      ],
      orderConnection,
    ),
    BullModule.registerQueue({ name: 'order' }),
    BullModule.registerQueue({ name: 'upsale' }),
    BullModule.registerQueue({ name: 'callcenter' }),
    PubSubModule,
    ShiftModule,
    ApiModule,
    ClickhouseModule,
  ],
  controllers: [
    OrdersController,
    SourcesController,
    CurrenciesController,
    CustomersController,
    LocationsController,
    TagsController,
    TagGroupsController,
    CancelReasonsController,
    PagesController,
    PancakeController,
    // UpSaleController,
    ExchangeController,
    PrintNotesController,
    LandingPagesController,
    ChannelsController,
    CareReasonsController,
    LeadsController,
    LeadDistributeConfigsController,
    TelesaleGroupsController,
    MarketplacesController,
    ShopifyController,
    CallController,
    OrderTrackingController,
    CallHistoryController,
    AppReleasesController,
    LeadASDistributeConfigsController,
    LeadsASController,
    OneSignalController,
    EtelecomController,
  ],
  providers: [
    OrdersService,
    SourcesService,
    CurrenciesService,
    CustomersService,
    LocationsService,
    TagsService,
    CancelReasonsService,
    OrdersProcessor,
    OrderSubscriber,
    TagGroupsService,
    PagesService,
    PosOrdersService,
    OrdersResolver,
    // UpSaleService,
    // UpsaleProcessor,
    DateScalar,
    // PfgOrdersService,
    OrderSyncService,
    PrintNotesService,
    OrderLogsService,
    CustomerLogsService,
    FfmOrdersService,
    OrderExportTemplateService,
    LandingPagesService,
    ChannelsService,
    CareReasonsService,
    LeadsService,
    LeadDistributeConfigsService,
    TelesaleGroupsService,
    LeadSubscriber,
    LeadCareItemSubscriber,
    AppointmentsService,
    FacebookOrdersService,
    MarketplaceIntegrationsService,
    ShopifyService,
    ShopifyOrdersService,
    CallService,
    CallHistoryService,
    AppReleasesService,
    CallCenterProcessor,
    LeadASDistributeConfigsService,
    LeadsASService,
    OrderBackupSyncStatusProcessor,
    EtelecomService,
  ],
  exports: [
    BullModule.registerQueue({ name: 'order' }),
    BullModule.registerQueue({ name: 'upsale' }),
    OrdersService,
  ],
})
export class OrderModule {}
