import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import {
  BadRequestException,
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  ForbiddenException,
  Get,
  Headers,
  NotFoundException,
  Param,
  ParseArrayPipe,
  ParseBoolPipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Res,
  Response,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';
import {
  CreateAppointmentScheduleDto,
  UpdateAppointmentScheduleDto,
} from 'apps/order-api/src/dtos/appointment-schedule.dto';
import { CreateOrdersFilterDto } from 'apps/order-api/src/dtos/create-orders-filter.dto';
import { CreateLeadCareItemDto } from 'apps/order-api/src/dtos/lead-item.dto';
import {
  BulkCancelDuplicateLeadsDto,
  CreateLeadDto,
  UpdateLeadDto,
} from 'apps/order-api/src/dtos/lead.dto';
import { ManualDistributeLeadsDto } from 'apps/order-api/src/dtos/manual-distribute-leads.dto';
import { AppointmentSchedule } from 'apps/order-api/src/entities/appointment-schedule.entity';
import { FilterCollection } from 'apps/order-api/src/entities/filter-collection.entity';
import { LeadCareItem } from 'apps/order-api/src/entities/lead-care-item.entity';
import { Lead } from 'apps/order-api/src/entities/lead.entity';
import { AppointmentsFilter } from 'apps/order-api/src/filters/appointments.filter';
import {
  GatherLeadsFilter,
  LeadsFilter,
  MobileLeadsFilter,
} from 'apps/order-api/src/filters/leads.filter';
import { ManualDistributeLeadsFilter } from 'apps/order-api/src/filters/manual-distribute-leads.filter';
import axios from 'axios';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { DisableCache } from 'core/decorators/disable-cache/disable-cache.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import FilterUtils from 'core/utils/FilterUtils';
import * as FormData from 'form-data';
import { Redis } from 'ioredis';
import { UpdateResult } from 'typeorm';
import { ICdrWebhook } from '../../../clients/port-sip.client';
import { SipClient } from '../../../clients/sip.client';
import { AppointmentsService } from '../services/appointments.service';
import { LeadsService } from '../services/leads.service';
import { SalePermission } from 'core/enums/sale-permissions';
import { TelesalesPermission } from 'core/enums/sale-permissions/telesales-permission.enum';
import { OrderPermission } from 'core/enums/sale-permissions/order-permission.enum';
import { OrdersService } from '../services/orders.service';
import { LeadPerformanceByUserIdsDto } from 'apps/order-api/src/dtos/lead-performance-by-user-ids.dto';
import { CallCenterPermission } from 'core/enums/sale-permissions/callcenter-permission.enum';
import {
  CallCenterExtensionsDeleteDto,
  CreateCallCenterExtensionDto,
  CreateEtelecomCallCenterExtensionDto,
  DeActiveCallCenterDto,
  ReturnCallCenterExtensionsDto,
  UpdateCallCenterExtensionDto,
  YcallRequestWebhookDto,
} from 'apps/order-api/src/dtos/callcenter.dto';
import { CallCenterExtensionsFilter } from 'apps/order-api/src/filters/call-center-extensions.filter';
import { NEXT_CARE_STATES } from 'apps/order-api/src/constants/care-states.constant';
import { AfterSalesLeadPermission } from 'core/enums/sale-permissions/after-sales-lead.permission.enum';

@Controller('leads')
@ApiTags('leads')
export class LeadsController {
  constructor(
    private leadsService: LeadsService,
    private ordersService: OrdersService,
    private appointmentsService: AppointmentsService,
    @InjectRedis() private readonly redis: Redis,
    private amqpConnection: AmqpConnection,
  ) {}

  @Get('performance')
  @Auth()
  async getPerformanceByUserIds(
    @Query() filter: LeadPerformanceByUserIdsDto,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.leadsService.getPerformanceByUserIds(filter, header, req);
  }

  @ApiExcludeEndpoint()
  @Post('sync-lead-user-id-to-order-user-in-charge')
  @Auth()
  async syncLeadUserIdToOrderUserInCharge() {
    return this.leadsService.syncLeadUserIdToOrderInCharge();
  }

  @ApiExcludeEndpoint()
  @Post('scan-leads')
  @Auth()
  async scanLeadFromOldOrders(
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ) {
    return this.leadsService.scanLeadFromOldOrders(headers, req);
  }

  @Post('bulk-cancel-duplicate-leads-v2')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.createCareReason],
    [SalePermission.telesales, TelesalesPermission.manualDistribute],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.createCareReason],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.manualDistribute],
  )
  async bulkCancelLeadsV2(
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Body() body: BulkCancelDuplicateLeadsDto,
  ) {
    req.query._byPassSeftData = true;
    return this.leadsService.bulkCancelDuplicateLeadsV2(body, headers, req);
  }

  @Post('bulk-cancel-duplicate-leads')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.createCareReason],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.createCareReason],
  )
  async bulkCancelLeads(
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Body() body: BulkCancelDuplicateLeadsDto,
  ) {
    return this.leadsService.bulkCancelDuplicateLeads(body, headers, req);
  }

  @Get('states/next')
  async getNextStatuses() {
    return this.leadsService.nextStates();
  }

  @Get('extensions')
  // @Auth()
  @DisableCache()
  async getExtensions(
    @Pagination() pagination: PaginationOptions,
    @Headers() headers,
    @Query('box', new DefaultValuePipe('1')) box: number,
    @Query('from') from: number,
    @Query('to') to: number,
  ) {
    return new SipClient().getAvailableExtensions(
      Number(box || 1),
      this.redis,
      null,
      Number(from),
      Number(to),
    );
  }

  @Get('call-logs')
  // @Auth()
  async getCallLogs(
    @Pagination() pagination: PaginationOptions,
    @Query('box', new DefaultValuePipe('1')) box: number,
    @Query('ext') ext: string,
  ) {
    return this.leadsService.getCallLogs(box, ext, pagination);
  }

  @Get()
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
    [SalePermission.telesales, TelesalesPermission.fetchLeads],
  )
  async getLeads(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: LeadsFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ): Promise<any[]> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return [];

    filter.projectIds = projectIds;
    return await this.leadsService.getLeads(pagination, filter, headers, req);
  }

  @Get('mobile')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
    [SalePermission.telesales, TelesalesPermission.fetchLeads],
  )
  async mobileGetLeads(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: MobileLeadsFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ): Promise<Lead[]> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return [];

    filter.projectIds = projectIds;
    return await this.leadsService.mobileGetLeads(pagination, filter, headers, req);
  }

  @Get('/next-states/:currentState')
  @Auth()
  async getNextStates(@Param('currentState') currentState: string) {
    return NEXT_CARE_STATES[currentState] || null;
  }

  @ApiExcludeEndpoint()
  @Post('scan-possible-duplicate-leads/:id')
  @Auth()
  async scanPossibleDuplicateLeads(
    @Param('id', ParseIntPipe) leadId: number,
    @Headers() headers: Record<string, string>,
    @Req() request: Record<string, any>,
  ) {
    const lead = await this.leadsService.getLeadDetail(leadId, headers, request);
    if (!lead) throw new NotFoundException();

    return this.leadsService.scanPossibleDuplicateLeads({ orderId: lead.orderId });
  }

  @Post('delete-incorrect-possible-duplicate-leads')
  @Auth()
  @ApiExcludeEndpoint()
  async deleteIncorrectPossibleDuplicates() {
    return this.leadsService.deleteIncorrectPossibleDuplicates();
  }

  @Get('me')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
    [SalePermission.telesales, TelesalesPermission.fetchLeads],
  )
  async getLeadsOfUser(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: LeadsFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ): Promise<Lead[]> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) throw new ForbiddenException();

    filter.projectIds = projectIds;
    filter.userIds = [req?.user?.id];
    return await this.leadsService.getLeads(pagination, filter, headers, req);
  }

  @Get('count')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
    [SalePermission.telesales, TelesalesPermission.fetchLeads],
  )
  async countLeads(
    @Query() filter: LeadsFilter,
    @Query(
      'groupBy',
      new ParseArrayPipe({
        separator: ',',
        optional: true,
        items: String,
      }),
    )
    groupBy: string[],
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) throw new ForbiddenException();

    filter.projectIds = projectIds;

    filter.projectIds = projectIds;
    return this.leadsService.countLeads(filter, groupBy, headers, req);
  }

  @Get('count/mobile')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
    [SalePermission.telesales, TelesalesPermission.fetchLeads],
  )
  async mobileCountLeads(
    @Query() filter: MobileLeadsFilter,
    @Query(
      'groupBy',
      new ParseArrayPipe({
        separator: ',',
        optional: true,
        items: String,
      }),
    )
    groupBy: string[],
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) throw new ForbiddenException();

    filter.projectIds = projectIds;

    filter.projectIds = projectIds;
    return this.leadsService.mobileCountLeads(filter, groupBy, headers, req);
  }

  @Get('export')
  @Auth('sale', [SalePermission.telesales, TelesalesPermission.exportExcel])
  async exportData(
    @Response() response,
    @Query() filter: LeadsFilter,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) throw new ForbiddenException();

    filter.projectIds = projectIds;

    const buffer = await this.leadsService.exportExcel(filter, req, headers);
    response.attachment(`Leads export.xlsx`);
    return response.send(buffer);
  }

  @Get('manual-filter')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.assignedLeadsFilter],
    [SalePermission.telesales, TelesalesPermission.leadsFilter],
  )
  async getLeadsManualFilter(@Req() request, @Headers() headers): Promise<FilterCollection[]> {
    return await this.leadsService.getLeadsManualFilter(headers, request);
  }

  @Post('manual-filter')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.assignedLeadsFilter],
    [SalePermission.telesales, TelesalesPermission.leadsFilter],
  )
  async createLeadsManualFilter(
    @Req() request,
    @Headers() headers,
    @Body() data: CreateOrdersFilterDto,
  ): Promise<FilterCollection> {
    return await this.leadsService.createLeadsManualFilter(data, request.user, headers);
  }

  @Delete('manual-filter/:id')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.assignedLeadsFilter],
    [SalePermission.telesales, TelesalesPermission.leadsFilter],
  )
  async deleteLeadsManualFilter(
    @Param('id', ParseIntPipe) id: number,
    @Req() request: Record<string, any>,
  ): Promise<boolean> {
    return await this.leadsService.deleteLeadsManualFilter(id);
  }

  @Get('list-reason/:id')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchOne],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchOne],
  )
  async getLeadReason(
    @Param('id') id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<LeadCareItem[]> {
    return this.leadsService.getLeadReason(id);
  }

  @Get('/appointment-schedule')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.appointments],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.appointments],
  )
  async getListAppointmentSchedule(
    @Query() filter: AppointmentsFilter,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<AppointmentSchedule[]> {
    return this.appointmentsService.getListAppointments(filter, headers, req);
  }

  @Get(':id')
  @Auth('sale', [SalePermission.telesales, TelesalesPermission.fetchOne])
  async getLeadDetail(
    @Param('id', ParseIntPipe) id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Lead> {
    req.query._byPassSeftData = true; // Set biến này để lấy được thông tin order của các lead trùng
    return this.leadsService.getLeadDetail(id, req, headers);
  }

  @Get(':id/histories')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.actionLogs],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.actionLogs],
  )
  async getLeadHistories(
    @Param('id', ParseIntPipe) id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ) {
    return this.leadsService.getLeadHistoriesV2(id, headers, req);
  }

  @Get(':id/appointment-schedule')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchOne],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchOne],
  )
  async getAppointmentSchedule(
    @Param('id', ParseIntPipe) id: number,
    @Query('getAll', ParseBoolPipe) getAll: boolean,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<AppointmentSchedule[]> {
    if (getAll) return this.appointmentsService.getAllAppointments(id, headers, req);
    return this.appointmentsService.getAppointmentsByLeadId(id, headers, req);
  }

  @Get(':id/original-lead')
  @Auth('sale', [SalePermission.telesales, TelesalesPermission.fetchOne])
  async findOriginalLeadById(@Param('id', ParseIntPipe) id) {
    return this.leadsService.findOriginalLeadById(id, true);
  }

  @Get(':id/call-histories')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchOne],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchOne],
  )
  async getLeadCallHistories(@Param('id', ParseIntPipe) leadId) {
    return this.leadsService.getLeadCallHistories(leadId);
  }

  @Get(':id/call-histories/v2')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchOne],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchOne],
  )
  async getLeadCallHistoriesV2(@Param('id', ParseIntPipe) leadId) {
    return this.leadsService.getLeadCallHistoriesV2(leadId);
  }

  @Get('count-reason/:id')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchOne],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchOne],
  )
  async countReason(@Param('id', ParseIntPipe) id): Promise<any> {
    return this.leadsService.countReasons(id, undefined);
  }

  @Post('manual-distribute')
  @Auth('sale', [SalePermission.telesales, TelesalesPermission.manualDistribute])
  manualDistribute(
    @Query() filter: ManualDistributeLeadsFilter,
    @Body() body: ManualDistributeLeadsDto,
    @Headers() headers: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.leadsService.manualDistribute(filter, body, headers, req);
  }

  @Post('lead-item')
  @Auth('sale', [SalePermission.telesales, TelesalesPermission.createCareReason])
  async createCareItem(
    @Body() data: CreateLeadCareItemDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<LeadCareItem> {
    return this.leadsService.createCareItem(data, req, headers);
  }

  @Put('manual-gather')
  @Auth('sale', [SalePermission.telesales, TelesalesPermission.manualRevoke])
  async manualGather(
    @Query() filter: GatherLeadsFilter,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<UpdateResult> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) throw new ForbiddenException();

    filter.projectIds = projectIds;

    return this.leadsService.manualGather(filter, headers, req);
  }

  @Post()
  @Auth(
    'sale',
    [SalePermission.order, OrderPermission.create],
    [SalePermission.telesales, TelesalesPermission.duplicateLead],
  )
  async createLead(
    @Body() body: CreateLeadDto,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ): Promise<Lead> {
    return this.leadsService.createLead(body, headers, req);
  }

  @Put(':id/un-duplicate')
  @Auth()
  async unDuplicateData(
    @Param('id', ParseIntPipe) id,
    @Query() filter: LeadsFilter, // Note: Using only field ids
    @Headers() headers,
    @Req() req: Record<string, any>,
  ): Promise<boolean> {
    req.query._byPassSeftData = true; // Set biến này để lấy được thông tin order của các lead trùng
    return this.leadsService.unDuplicateData(id, filter, headers, req);
  }

  @Put(':id')
  @Auth()
  async updateLead(
    @Param('id', ParseIntPipe) id,
    @Body() body: UpdateLeadDto,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ): Promise<Lead> {
    return this.leadsService.updateLead(id, body, headers, req);
  }

  @Post('take-care-leads')
  @Auth()
  async takeCareLeads(@Req() req: Record<string, any>, @Headers() headers: Record<string, any>) {
    req.query._byPassSeftData = true;
    const countryIds = headers['country-ids'].split(',');
    const projectIds = FilterUtils.getProjectIds(headers['project-ids']);

    const countryId = countryIds[0];
    const userId = req.user.id;
    const companyId = req.user.companyId;
    req.query._distributableLeads = true;
    return this.leadsService.takeCareLeads(userId, companyId, countryId, projectIds, req);
  }

  @Post(':id/take-assigned-lead')
  @Auth('sale', [SalePermission.telesales, TelesalesPermission.createCareReason])
  async takeAssignedLead(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Lead> {
    return this.leadsService.takeAssignedLead(id, headers, req);
  }

  @Post(':id/get-next-step')
  @Auth()
  async getNextStepOfLead(@Param('id', ParseIntPipe) id: number) {
    return this.leadsService.getNextStepOfLead(id);
  }

  @Post('/appointment-schedule')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.appointments],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.appointments],
  )
  async createAppointmentSchedule(
    @Body() data: CreateAppointmentScheduleDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<AppointmentSchedule> {
    return this.appointmentsService.createAppointment(data, req, headers);
  }

  @Put('/appointment-schedule/:id')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.appointments],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.appointments],
  )
  async updateAppointmentSchedule(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateAppointmentScheduleDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<AppointmentSchedule> {
    return this.appointmentsService.updateAppointment(id, data, req, headers);
  }

  @Delete('/appointment-schedule/:id')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.appointments],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.appointments],
  )
  async deleteAppointment(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<UpdateResult> {
    return this.appointmentsService.deleteAppointment(id, req, headers);
  }

  @Post('/record-files')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadRecord(@UploadedFile('file') file) {
    const { buffer, originalname } = file;
    const form = new FormData();
    form.append('file', buffer, originalname);
    const headers = {
      'Content-Type': 'multipart/form-data',
    };
    const response = await axios.request({
      method: 'POST',
      baseURL: process.env.PANCAKE_URL_API,
      url: '/contents',
      params: {
        api_key: process.env.POS_API_KEY,
      },
      data: form,
      headers,
      timeout: 60000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });
    const url = response.data?.content_url;
    if (url) {
      return this.leadsService.saveRecordFile({ id: originalname, src: url });
    }
  }

  @Get('/ping/:ext')
  @DisableCache()
  async ping(@Param('ext') ext: string) {
    return this.leadsService.pingCall(ext);
  }

  @Get('/get-ext/:ext')
  @DisableCache()
  async getExt(@Param('ext') ext: string) {
    return this.leadsService.getExt(ext);
  }

  @Get('/record-files/:id')
  @DisableCache()
  async get(@Param('id') id: string, @Res() response) {
    const src = await this.leadsService.getRecordFile(id);
    if (src) {
      return response.redirect(src);
    }
  }

  @Post('/cdr/webhook')
  async cdrWebhook(@Body() data: ICdrWebhook) {
    if (data.call_id) {
      await this.amqpConnection.publish('voip-exchange', 'handle-cdr-webhook', data);
    }
    return {};
  }

  @Get('callcenter-extension/:name/status')
  @Auth()
  async getCallcenterStatus(
    @Param('name') name: string,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    return this.leadsService.getCallCenterStatus(name, req.user, headers);
  }

  @Get('callcenter-extension/:name/extension')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
    [SalePermission.telesales, TelesalesPermission.fetchLeads],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchAssignedLeads],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads],
  )
  async getCallcenterExtension(
    @Param('name') name: string,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    return this.leadsService.getCallcenterExtension(name, req.user, headers);
  }

  @Get('callcenter-extension/:name/extension/:id')
  @Auth(
    'sale',
    [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
    [SalePermission.telesales, TelesalesPermission.fetchLeads],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchAssignedLeads],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads],
  )
  async returnCallcenterExtension(
    @Param('name') name: string,
    @Param('id') id: string,
    @Query() filter: ReturnCallCenterExtensionsDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    return this.leadsService.returnCallcenterExtension(name, id, req.user, headers, filter);
  }

  @Post('/callcenter-extension/:name/de-active')
  // @Auth('sale', [SalePermission.callCenterManagement, CallCenterPermission.create])
  async deActiveCallcenterExtension(
    @Param('name') name: string,
    @Body() data: DeActiveCallCenterDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    return this.leadsService.deActiveCallCenterExtension(name, data, req.user, headers);
  }

  @Post('/callcenter-extension/:name/import')
  // @Auth('sale', [SalePermission.callCenterManagement, CallCenterPermission.create])
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importCallcenterExtension(
    @Param('name') name: string,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @UploadedFile('file') file,
  ) {
    if (!file) throw new BadRequestException('file is required');
    const { buffer } = file;
    return this.leadsService.importCallcenterExtension(buffer, name, req.user, headers);
  }

  @Post('/callcenter-extension/:name')
  // @Auth('sale', [SalePermission.callCenterManagement, CallCenterPermission.create])
  async createCallcenterExtension(
    @Param('name') name: string,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @Body() data: CreateCallCenterExtensionDto,
  ) {
    return this.leadsService.createCallCenterExtension(name, data, req.user, headers);
  }

  @Put('/callcenter-extension/:id')
  // @Auth('sale', [SalePermission.callCenterManagement, CallCenterPermission.create])
  async updateCallcenterExtension(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @Body() data: UpdateCallCenterExtensionDto,
  ) {
    return this.leadsService.updateCallCenterExtension(id, data, req.user, headers);
  }

  @Delete('/callcenter-extension/:name')
  // @Auth('sale', [SalePermission.callCenterManagement, CallCenterPermission.create])
  async deleteCallcenterExtension(
    @Param('name') name: string,
    @Body() data: CallCenterExtensionsDeleteDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ) {
    return this.leadsService.deleteCallCenterExtension(name, req.user, headers, data);
  }

  @Get('callcenter-extension/:name')
  // @Auth('sale', [SalePermission.callCenterManagement, CallCenterPermission.fetchMany])
  async getListCallcenterExtension(
    @Param('name') name: string,
    @Pagination() pagination: PaginationOptions,
    @Query() filter: CallCenterExtensionsFilter,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ) {
    return this.leadsService.getListCallCenterExtension(
      name,
      pagination,
      filter,
      req.user,
      headers,
    );
  }

  @Post('/callcenter-extension/ycall/webhook')
  async ycallWebhook(@Body() data: YcallRequestWebhookDto) {
    return this.leadsService.ycallWebhook(data);
  }

  @Post('/callcenter-extension/etelecom/create-extension')
  async createEtelecomExtension(@Body() data: CreateEtelecomCallCenterExtensionDto) {
    return this.leadsService.createEtelecomExtension(data.hotlineId, data.extensionNumber);
  }

  @Get('/callcenter-extension/etelecom/get-extensions')
  async getEtelecomExtension(@Query('toCall') toCall: boolean) {
    return this.leadsService.getTelecomExtension(toCall);
  }

  @Put('/callcenter-extension/etelecom/set-extensions-in-call/:id')
  async setEtelecomExtensionInCall(@Param('id') id: string) {
    return this.leadsService.setEtelecomExtensionInCall(id);
  }
    
  @Put('/callcenter-extension/etelecom/release-extension/:id')
  async releaseEtelecomExtension(@Param('id') id: string) {
    return this.leadsService.releaseEtelecomExtension(id);
  }
}
