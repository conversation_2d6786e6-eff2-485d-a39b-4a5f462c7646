import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { EtelecomService } from '../services/etelecom.service';
import { CreateCallGroupDto } from 'apps/order-api/src/dtos/create-call-group.dto';
import { AddCallGroupExtensionDto } from 'apps/order-api/src/dtos/add-call-group-extension.dto';
import { AddCallGroupUserDto } from 'apps/order-api/src/dtos/add-call-group-user.dto';
import { UpdateCallGroupDto } from 'apps/order-api/src/dtos/update-call-group.dto';
import { CallCenterDto } from 'apps/order-api/src/dtos/call-center.dto';
import { SearchCallGroupDto } from 'apps/order-api/src/dtos/searchCallGroup.dto';
import { AddCallGroupExtensionByPickDto } from 'apps/order-api/src/dtos/add-call-group-extension-by-pick.dto';
import { GetEtelecomCallHistoryFilter } from 'apps/order-api/src/filters/get-etelecom-call-history.filter';
import { CallHistoryFilter } from 'apps/order-api/src/filters/call-history.filter';
import { CallPresaveDto } from 'apps/order-api/src/dtos/call-presave.dto';

@Controller('etelecom')
@ApiTags('etelecom')
export class EtelecomController {
  constructor(private etelecomService: EtelecomService) {}

  @Get('call-center')
  async getExtension(@Query() callCenterDto: CallCenterDto) {
    return this.etelecomService.getCallCenter(callCenterDto);
  }

  @Get('call-center/:id/users')
  async getCallCenterUsers(@Param('id') callCenterId: number) {
    return this.etelecomService.getCallCenterUsers(callCenterId);
  }

  @Get('call-center/:id/count')
  async getCallCenterCount(@Param('id') callCenterId: number) {
    return this.etelecomService.getCallCenterCount(callCenterId);
  }
  // Call group
  @Get('call-group/:id')
  async getCallGroup(@Param('id') id: number) {
    return this.etelecomService.getCallGroup(id);
  }

  @Get('call-group')
  async findCallGroups(@Query() searchCallGroupDto: SearchCallGroupDto) {
    return this.etelecomService.findCallGroups(searchCallGroupDto);
  }

  @Get('call-center/call-group/:id')
  async getCallCenterCallGroup(@Param('id') callCenterId: number) {
    return this.etelecomService.getCallCenterCallGroup(callCenterId);
  }

  @Post('call-group')
  async createCallGroup(@Body() createCallGroupDto: CreateCallGroupDto) {
    return this.etelecomService.createCallGroup(createCallGroupDto);
  }

  @Delete('call-group/:id/deactive')
  async deactivateCallGroup(@Param('id') id: number) {
    return this.etelecomService.deactivateCallGroup(id);
  }

  @Put('call-group/:id')
  async updateCallGroup(
    @Param('id') id: number,
    @Body() updateCallGroupDto: UpdateCallGroupDto,
  ) {
    return this.etelecomService.updateCallGroup(id, updateCallGroupDto);
  }

  @Delete('call-group/:id/remove')
  async removeCallGroup(
    @Param('id') callGroupId: number,
    @Query('removedBy') removedBy: number,
  ) {
    if (!removedBy) {
      throw new BadRequestException('removedBy is required');
    }
    return this.etelecomService.removeCallGroup(callGroupId, removedBy);
  }

  // Extension
  @Put('call-group/:id/extensions')
  async addExtensionToCallGroup(
    @Param('id') id: number,
    @Body() body: AddCallGroupExtensionDto,
  ) {
    return this.etelecomService.addExtensionToCallGroup(id, body);
  }

  @Put('call-group/:id/extensions/picking')
  async addExtensionToCallGroupByPick(
    @Param('id') id: number,
    @Body() body: AddCallGroupExtensionByPickDto,
  ) {
    return this.etelecomService.addExtensionToCallGroupByPick(id, body);
  }

  @Put('call-group/:id/extensions/remove')
  async removeExtensionFromCallGroup(
    @Param('id') callGroupId: number,
    @Query('numberExtension') numberExtensions: number,
  ) {
    return this.etelecomService.removeExtensionFromCallGroup(
      callGroupId,
      numberExtensions,
    );
  }

  @Put('call-group/:id/extensions/remove-pick')
  async removeExtensionFromCallGroupByPick(
    @Param('id') callGroupId: number,
    @Query('extensionIds') extIds: number[],
  ) {
    return this.etelecomService.removeExtensionFromCallGroupByPick(
      callGroupId,
      extIds,
    );
  }

  @Get('call-center/:id/extensions/not-in-group')
  async getExtensionNotInCallGroup(@Param('id') callCenterId: number) {
    return this.etelecomService.getExtensionNotInCallGroup(callCenterId);
  }

  // Call group user
  @Get('call-group/:id/users')
  async getCallGroupUser(@Param('id') callGroupId: number) {
    return this.etelecomService.getCallGroupUser(callGroupId);
  }

  @Post('call-center/:id/check-call-group-name')
  async checkCallGroupName(
    @Param('id') callCenterId: number,
    @Body() body: { name: string },
  ) {
    return this.etelecomService.checkCallGroupName(callCenterId, body.name);
  }

  @Put('call-group/:id/users')
  async addUserToCallGroup(
    @Param('id') callGroupId: number,
    @Body() body: AddCallGroupUserDto,
  ) {
    return this.etelecomService.addUserToCallGroup(callGroupId, body);
  }

  @Post('test')
  async testAPI2(@Body('url') url: string) {
    return this.etelecomService.testCallPrompt(url);
  }

  @Delete('call-group/:groupId/users/remove')
  async removeUserFromCallGroup(
    @Param('groupId') callGroupId: number,
    @Query('id') ids: number | number[],
    @Query('removedBy') removedBy: number,
  ) {
    return this.etelecomService.removeUserFromCallGroup(callGroupId, ids, removedBy);
  }

  // Calling API
  @Get('call-group/:id/call')
  async getEtelecomExtensionToCall(
    @Param('id') groupId: number,
    @Query('countryId') countryId?: number,
    @Query('companyId') companyId?: number,
  ) {
    return this.etelecomService.getEtelecomExtensionToCall(groupId, countryId, companyId);
  }

  @Put('call-group/extension/:id/release')
  async releaseEtelecomExtension(@Param('id') extId: number) {
    return this.etelecomService.releaseEtelecomExtension(extId);
  }

  @Post('call-group/:id/extension/reinit')
  async reinitEtelecomExtension(@Param('id') groupId: number) {
    return this.etelecomService.reinitRedisSetForCallGroup(groupId);
  }

  @Get('call-group/:id/extension/list')
  async getEtelecomExtensionList(@Param('id') groupId: number) {
    return this.etelecomService.getGroupRedisList(groupId);
  }

  // History

  // @Get('call-history')
  // async getCallHistory(
  //   @Query('callGroupId') callGroupId: number,
  //   @Query('page') page: number,
  //   @Query('limit') limit: number,
  // ) {
  //   return this.etelecomService.getCallHistory(callGroupId);
  // }

  @Get('call-history/crawl')
  async getCallHistoryByCrawl(@Query() body: GetEtelecomCallHistoryFilter) {
    console.log('Checking body for call history:', body);
    return this.etelecomService.getCallHistoryByCrawl(body);
  }

  @Get('call-history')
  async findCallHistory(
    @Query() filter: CallHistoryFilter,
    @Query('limit') limit: number,
    @Query('offset') offset: number,
  ) {
    return this.etelecomService.findCallHistory(filter, limit, offset);
  }

  @Get('call-history/last-call')
  async getLastCallHistoryByUserId(@Query('userId') userId: number[]) {
    return this.etelecomService.getLastCallHistoryByUserId(userId);
  }

  // Logs
  @Get('call-center/:id/logs')
  async getCallCenterLogs(
    @Param('id') callCenterId: number,
    @Query('creatorId') creatorIds: number[],
    @Query('limit') limit: number,
    @Query('offset') offset: number,
  ) {
    return this.etelecomService.getCallCenterLogs(callCenterId, creatorIds, limit, offset);
  }

  // Presave 
  @Post('call-history/presave/:id')
  async presaveCallHistory(
    @Param('id') externalSessionId: string,
    @Body() callPresaveDto: CallPresaveDto) {
    return this.etelecomService.presaveCallHistory(externalSessionId, callPresaveDto);
  }
}
