import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import {
  BadRequestException,
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  ForbiddenException,
  Get,
  Headers,
  NotFoundException,
  Param,
  ParseArrayPipe,
  ParseBoolPipe,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
  Res,
  Response,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';
import {
  CreateAppointmentScheduleDto,
  UpdateAppointmentScheduleDto,
} from 'apps/order-api/src/dtos/appointment-schedule.dto';
import { CreateOrdersFilterDto } from 'apps/order-api/src/dtos/create-orders-filter.dto';
import { CreateLeadCareItemDto } from 'apps/order-api/src/dtos/lead-item.dto';
import {
  BulkCancelDuplicateLeadsDto,
  CreateLeadAfterSaleDto,
  CreateLeadDto,
  UpdateLeadDto,
} from 'apps/order-api/src/dtos/lead.dto';
import {
  ManualDistributeLeadsAfterSaleDto,
  ManualDistributeLeadsDto,
} from 'apps/order-api/src/dtos/manual-distribute-leads.dto';
import { AppointmentSchedule } from 'apps/order-api/src/entities/appointment-schedule.entity';
import { FilterCollection } from 'apps/order-api/src/entities/filter-collection.entity';
import { LeadCareItem } from 'apps/order-api/src/entities/lead-care-item.entity';
import { Lead } from 'apps/order-api/src/entities/lead.entity';
import { AppointmentsFilter } from 'apps/order-api/src/filters/appointments.filter';
import { GatherLeadsFilter, LeadsFilter } from 'apps/order-api/src/filters/leads.filter';
import {
  ManualDistributeLeadsAfterSaleFilter,
  ManualDistributeLeadsFilter,
} from 'apps/order-api/src/filters/manual-distribute-leads.filter';
import axios from 'axios';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { DisableCache } from 'core/decorators/disable-cache/disable-cache.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import FilterUtils from 'core/utils/FilterUtils';
import * as FormData from 'form-data';
import { Redis } from 'ioredis';
import { UpdateResult } from 'typeorm';
import { ICdrWebhook } from '../../../clients/port-sip.client';
import { SipClient } from '../../../clients/sip.client';
import { AppointmentsService } from '../services/appointments.service';
import { LeadsService } from '../services/leads.service';
import { SalePermission } from 'core/enums/sale-permissions';
import { TelesalesPermission } from 'core/enums/sale-permissions/telesales-permission.enum';
import { OrderPermission } from 'core/enums/sale-permissions/order-permission.enum';
import { OrdersService } from '../services/orders.service';
import { LeadPerformanceByUserIdsDto } from 'apps/order-api/src/dtos/lead-performance-by-user-ids.dto';
import { CallCenterPermission } from 'core/enums/sale-permissions/callcenter-permission.enum';
import {
  CallCenterExtensionsDeleteDto,
  CreateCallCenterExtensionDto,
  DeActiveCallCenterDto,
  ReturnCallCenterExtensionsDto,
  UpdateCallCenterExtensionDto,
  YcallRequestWebhookDto,
} from 'apps/order-api/src/dtos/callcenter.dto';
import { CallCenterExtensionsFilter } from 'apps/order-api/src/filters/call-center-extensions.filter';
import { LeadsASService } from '../services/leads-as.service';
import {
  GatherLeadsAfterSaleFilter,
  LeadsASAssignedProjectFilter,
  LeadsASFilterCommon,
  LeadsASUnassignProjectFilter,
} from 'apps/order-api/src/filters/leads-as.filter';
import { DistributeProjectForLeadAsDto } from 'apps/order-api/src/dtos/distribute-project-for-lead-as.dto';
import { LeadAfterSaleSourceFilter } from 'apps/order-api/src/filters/lead-after-sale-source.filter';
import { AfterSalesLeadPermission } from 'core/enums/sale-permissions/after-sales-lead.permission.enum';
import {
  CreateAfterSaleSourceDto,
  UpdateAfterSaleSourceDto,
} from 'apps/order-api/src/dtos/create-aftersales-source.dto';
import { AfterSalesFilterCollectionFilter } from 'apps/order-api/src/filters/aftersales-filter-collection.filter';

@Controller('leads-as')
@ApiTags('leads-as')
export class LeadsASController {
  constructor(
    private leadsASService: LeadsASService,
    private ordersService: OrdersService,
    private appointmentsService: AppointmentsService,
    @InjectRedis() private readonly redis: Redis,
    private amqpConnection: AmqpConnection,
  ) {}

  @Post()
  @Auth(
    'sale',
    [SalePermission.order, OrderPermission.create],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.duplicateLead],
  )
  async createLeadAfterSale(
    @Body() data: CreateLeadAfterSaleDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ) {
    return await this.leadsASService.createLeadAfterSale(data, headers, req);
  }

  @Get('unassign-project')
  @Auth('sale', [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads])
  async getLeadsASUnassignProject(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: LeadsASUnassignProjectFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    req.query._byPassSeftData = true;
    return await this.leadsASService.getLeadsASUnassignProject(pagination, filter, headers, req);
  }

  @Get('unassign-project/count-products')
  @Auth(
    'sale',
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.filterLeadsAfterSale],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads],
  )
  async countProductOfLeadsASUnassignProject(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: LeadsASUnassignProjectFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    req.query._byPassSeftData = true;
    return await this.leadsASService.countProductOfLeadsASUnassignProject(filter, headers, req);
  }

  @Get('count')
  @Auth(
    'sale',
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchAssignedLeads],
  )
  async countLeads(
    @Query() filter: LeadsASAssignedProjectFilter,
    @Query(
      'groupBy',
      new ParseArrayPipe({
        separator: ',',
        optional: true,
        items: String,
      }),
    )
    groupBy: string[],
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    if (filter.isAssignedProject) {
      const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
      if (projectIds?.length === 0) return [];
      filter.projectIds = projectIds;
    } else {
      req.query._byPassSeftData = true;
    }
    return this.leadsASService.countLeadsAfterSale(filter, groupBy, headers, req);
  }

  @Get('assigned-project')
  @Auth(
    'sale',
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchAssignedLeads],
  )
  async getLeadsASAssignedProject(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: LeadsASAssignedProjectFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return [];

    filter.projectIds = projectIds;
    return await this.leadsASService.getLeadsASAssignedProject(pagination, filter, headers, req);
  }

  @Get('source')
  @Auth()
  async getExternalSource(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: LeadAfterSaleSourceFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return [];

    filter.projectIds = projectIds;
    return await this.leadsASService.getExternalSource(pagination, filter, headers, req);
  }

  @Post('source')
  @Auth()
  async createSource(
    @Body() body: CreateAfterSaleSourceDto,
    @Headers() headers?: Record<string, string>,
    @Req() request?: Record<string, any>,
  ) {
    return await this.leadsASService.createSource(body, headers, request);
  }

  @Get('source/:id')
  @Auth()
  async getSource(
    @Param('id', ParseUUIDPipe) id: string,
    @Headers() headers?: Record<string, string>,
    @Req() request?: Record<string, any>,
  ) {
    return await this.leadsASService.getSource(id, headers, request);
  }

  @Put('source/:id')
  @Auth()
  async updateSource(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: UpdateAfterSaleSourceDto,
    @Headers() headers?: Record<string, string>,
    @Req() request?: Record<string, any>,
  ) {
    return await this.leadsASService.updateSource(id, body, headers, request);
  }

  @Delete('source/:id')
  @Auth()
  async removeSource(
    @Param('id', ParseUUIDPipe) id: string,
    @Headers() headers?: Record<string, string>,
    @Req() request?: Record<string, any>,
  ) {
    return await this.leadsASService.deleteSource(id, headers, request);
  }

  @Post('distribute-project-for-lead-as')
  @Auth()
  async distributeProjectForLeadAs(
    @Query() filter: LeadsASUnassignProjectFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
    @Body() data: DistributeProjectForLeadAsDto,
  ) {
    req.query._byPassSeftData = true;
    return await this.leadsASService.distributeProjectForLeadAs(filter, headers, req, data);
  }

  @Post('manual-distribute')
  @Auth()
  async manualDistributeLeads(
    @Query() filter: ManualDistributeLeadsAfterSaleFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
    @Body() data: ManualDistributeLeadsAfterSaleDto,
  ) {
    return await this.leadsASService.manualDistribute(filter, data, headers, req);
  }

  @Post('import')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Auth()
  async importLeadsAfterSale(@UploadedFile('file') file, @Req() request) {
    if (!file) throw new BadRequestException('file is required');
    const { buffer } = file;
    return this.leadsASService.importLeadsAfterSale(buffer, request);
  }

  @Post('manual-gather')
  @Auth()
  async manualGatherLeads(
    @Query() filter: GatherLeadsAfterSaleFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) throw new ForbiddenException();

    filter.projectIds = projectIds;
    return await this.leadsASService.manualGatherLeads(filter, headers, req);
  }

  @Post('take-care-leads')
  @Auth()
  async takeCareLeads(@Req() req: Record<string, any>, @Headers() headers: Record<string, any>) {
    req.query._byPassSeftData = true;
    const countryIds = headers['country-ids'].split(',');
    const projectIds = FilterUtils.getProjectIds(headers['project-ids']);

    const countryId = countryIds[0];
    const userId = req.user.id;
    const companyId = req.user.companyId;
    req.query._distributableLeads = true;
    return this.leadsASService.takeCareLeads(userId, companyId, countryId, projectIds, req);
  }

  @Post('lead-item')
  @Auth('sale', [SalePermission.afterSalesLead, AfterSalesLeadPermission.createCareReason])
  async createCareItem(
    @Body() data: CreateLeadCareItemDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<LeadCareItem> {
    return this.leadsASService.createCareItem(data, req, headers);
  }

  @Get('manual-filter')
  @Auth(
    'sale',
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.assignedLeadsFilter],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.filterLeadsAfterSale],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.filterToTakeCare],
  )
  async getLeadsManualFilter(
    @Req() request,
    @Headers() headers,
    @Query() filter: AfterSalesFilterCollectionFilter,
  ): Promise<FilterCollection[]> {
    return await this.leadsASService.getLeadsManualFilter(headers, request, filter);
  }

  @Post('manual-filter')
  @Auth(
    'sale',
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.assignedLeadsFilter],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.filterLeadsAfterSale],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.filterToTakeCare],
  )
  async createLeadsManualFilter(
    @Req() request,
    @Headers() headers,
    @Body() data: CreateOrdersFilterDto,
  ): Promise<FilterCollection> {
    return await this.leadsASService.createLeadsManualFilter(data, request.user, headers);
  }

  @Delete('manual-filter/:id')
  @Auth(
    'sale',
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.assignedLeadsFilter],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.filterLeadsAfterSale],
    [SalePermission.afterSalesLead, AfterSalesLeadPermission.filterToTakeCare],
  )
  async deleteLeadsManualFilter(
    @Param('id', ParseIntPipe) id: number,
    @Req() request: Record<string, any>,
  ): Promise<boolean> {
    return await this.leadsASService.deleteLeadsManualFilter(id);
  }

  @Get(':id')
  @Auth()
  async getLeadAfterSaleDetail(
    @Param('id', ParseIntPipe) id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ) {
    req.query._byPassSeftData = true;
    return await this.leadsASService.getLeadAfterSaleDetail(id, req, headers);
  }

  @Post(':id/take-assigned-lead')
  @Auth('sale', [SalePermission.afterSalesLead, AfterSalesLeadPermission.createCareReason])
  async takeAssignedLead(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Lead> {
    return this.leadsASService.takeAssignedLead(id, headers, req);
  }
}
