export enum CallHistoryType {
  agcall = 1,
  ycall = 2,
  etelecom = 3.
}

export enum CallHistoryStatus {
  draft = 1,
  not_answered = 2,
  answered = 3,
  busy = 4,
}

export function convertCallHistoryStatusToEnum(status: string): CallHistoryStatus | undefined {
  switch (status.toUpperCase()) {
    case 'ANSWERED':
      return CallHistoryStatus.answered;
    case 'NO ANSWER':
      return CallHistoryStatus.not_answered;
    case 'BUSY':
      return CallHistoryStatus.busy;
    default:
      return CallHistoryStatus.draft;
  }
}

export function convertEtelecomCallStatusToEnum(status: string): CallHistoryStatus | undefined {
  switch (status.toUpperCase()) {
    case 'ANSWERED':
      return CallHistoryStatus.answered;
    case 'NOT_ANSWERED':
      return CallHistoryStatus.not_answered;
    case 'BUSY':
      return CallHistoryStatus.busy;
    default:
      return CallHistoryStatus.draft;
  }
}