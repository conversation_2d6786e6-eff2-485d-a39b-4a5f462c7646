import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

export class CallPresaveDto {
  @ApiProperty({ required: true })
  @IsNumber()
  leadId?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  userId: number;

  @ApiProperty({ required: true })
  @IsNumber()
  extensionId?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  leadCareId?: number;
}