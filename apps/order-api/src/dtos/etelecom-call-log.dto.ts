import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON><PERSON>rra<PERSON>,
  IsDate,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class CallTargetDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  add_time?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  answered_time?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  end_reason?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  ended_time?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  fail_code?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  ring_duration?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  ring_time?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  target_number?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  trunk_name?: string;
}

export class EtelecomCallLogDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  external_session_id: string;

  @ApiProperty()
  @IsString()
  external_id: string;

  @ApiProperty()
  @IsString()
  user_id: string;

  @ApiProperty()
  @IsString()
  direction: string;

  @ApiProperty()
  @IsString()
  call_state: string;

  @ApiProperty()
  @IsString()
  call_status: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  user_data?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  extension_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  contact_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  started_at?: Date;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  ended_at?: Date;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  created_at?: Date;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  updated_at?: Date;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  caller?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  callee?: string;

  @ApiProperty({ type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  recording_urls?: string[];

  @ApiProperty({ type: [CallTargetDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CallTargetDto)
  call_targets?: CallTargetDto[];
}

export class EtelecomRawLogDto {
  @ApiProperty({ type: () => EtelecomCallLogDto })
  @ValidateNested()
  @Type(() => EtelecomCallLogDto)
  log: EtelecomCallLogDto;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  leadId?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  extensionId?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  leadCareId?: number;
}