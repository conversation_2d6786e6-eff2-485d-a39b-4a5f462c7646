import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { FilterCollectionType } from 'core/enums/filter-collection-type.enum';

export class CreateOrdersFilterDto {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  raw: Record<string, any>;

  @ApiProperty({
    required: false,
    enum: FilterCollectionType,
  })
  @IsNotEmpty()
  type?: FilterCollectionType;
}
