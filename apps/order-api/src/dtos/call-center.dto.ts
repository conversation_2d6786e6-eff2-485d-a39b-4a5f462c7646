import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class CallCenterDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  callCenterId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  customerId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  countryId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  companyId: number;

  @ApiProperty({ type: String })
  @IsString()
  @Type(() => String)
  @IsOptional()
  name: string;
}
