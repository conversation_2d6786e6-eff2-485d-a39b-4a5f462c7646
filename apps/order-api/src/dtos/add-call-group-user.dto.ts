import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class AddCallGroupUserDto {
  @ApiProperty({
    type: [Number],
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  userIds: number[];

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNotEmpty()
  @IsNumber()
  addByUserId: number;
}
