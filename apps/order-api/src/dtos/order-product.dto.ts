import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON><PERSON>, Min } from 'class-validator';

export class OrderProductDto {
  @ApiProperty({
    description: 'Id sản phẩm con'
  })
  @IsNumber()
  productId: number;

  @ApiProperty({
    description: 'Số lượng'
  })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Đơn giá'
  })
  @IsNumber()
  price: number;
}
