import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { NonSpecialCharactersTransform } from 'core/decorators/non-special-character-transform.decorator';
import { OrderStatus } from 'core/enums/order-status.enum';
import { isNil } from 'lodash';
import { DiscountType } from '../enums/discount-type.enum';
import { TeamInCharge } from '../enums/team-in-charge.enum';
import { SourceEntity } from '../enums/source-entity.enum';
import { OrderCarrierDto } from './order-carrier.dto';
import { OrderProductDto } from './order-product.dto';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';

export class CreateOrderDto {
  @ApiProperty({
    type: OrderProductDto,
    isArray: true,
    description: 'Danh sách sản phẩm trong đơn',
  })
  @ValidateIf(object => object.status !== OrderStatus.Draft)
  @IsArray()
  @IsNotEmpty({ each: true })
  @ValidateNested({ each: true })
  @Type(() => OrderProductDto)
  products: OrderProductDto[];

  @ApiProperty({
    required: false,
    description: 'ID của nhân viên sale phụ trách đơn',
  })
  @IsOptional()
  @IsNumber()
  saleId?: number;

  @ApiProperty({
    required: false,
    description: 'ID của nhân viên care page phụ trách đơn',
  })
  @IsOptional()
  @IsNumber()
  carePageId?: number;

  @ApiProperty({
    required: false,
    description: 'ID của nhân viên Marketing',
  })
  @IsOptional()
  @IsNumber()
  marketerId?: number;

  @ApiProperty({
    required: false,
    enum: TeamInCharge,
    description: 'Team phụ trách đơn (TLS/ Care Page)',
  })
  @IsOptional()
  @IsEnum(TeamInCharge)
  @EnumTransform(TeamInCharge)
  teamInCharge?: TeamInCharge;

  @ApiProperty({
    required: false,
    description: 'Facebook Global User ID',
  })
  @IsOptional()
  customerFbGlobalId?: string;

  @ApiProperty({
    required: false,
    description: 'Facebook Scoped User ID',
  })
  @IsOptional()
  customerFbScopedUserId?: string;

  @ApiProperty({
    required: false,
    description: 'Facebook Page ID',
  })
  @IsOptional()
  pageId?: string;

  @ApiProperty({
    description: 'Tên khách hàng',
  })
  @IsString()
  @ValidateIf(object => object.status !== OrderStatus.Draft)
  customerName: string;

  @ApiProperty({
    description: 'Số điện thoại khách hàng',
  })
  @ValidateIf(object => object.status !== OrderStatus.Draft)
  @IsString()
  @NonSpecialCharactersTransform()
  customerPhone: string;

  @ApiProperty({
    description: 'Địa chỉ nhận hàng cụ thể',
  })
  @ValidateIf(object => object.status !== OrderStatus.Draft)
  @IsNotEmpty()
  @IsString()
  addressText: string;

  @ApiProperty({
    description: 'Ghi chú cho địa chỉ nhận hàng',
  })
  @ValidateIf(object => object.status !== OrderStatus.Draft)
  @IsOptional()
  @IsString()
  addressNote: string;

  @ApiProperty({
    required: false,
    description: 'ID Phường/Xã',
  })
  @IsOptional()
  @IsString()
  addressWardId?: string;

  @ApiProperty({
    description: 'ID Quận/Huyện',
  })
  @IsString()
  @ValidateIf(object => object.status !== OrderStatus.Draft)
  addressDistrictId: string;

  @ApiProperty({
    description: 'ID Tỉnh/Thành phố/Bang',
  })
  @IsString()
  @ValidateIf(object => object.status !== OrderStatus.Draft)
  addressProvinceId: string;

  @ApiProperty({
    required: false,
    enum: SourceEntity,
    description: 'Loại nguồn',
  })
  @ValidateIf(obj => !isNil(obj.sourceId))
  @EnumTransform(SourceEntity)
  @IsEnum(SourceEntity)
  sourceType?: SourceEntity;

  @ApiProperty({
    required: false,
    description: 'ID nguồn theo loại nguồn',
  })
  @ValidateIf(obj => !isNil(obj.sourceType))
  @IsNotEmpty()
  sourceId?: string | number;

  @ApiProperty({
    required: false,
    deprecated: true,
  })
  @IsOptional()
  @IsNumber()
  carrierId?: number;

  @ApiProperty({
    required: false,
    description: 'Giảm giá',
  })
  @IsOptional()
  @IsNumber()
  discount?: number;

  @ApiProperty({
    required: false,
    enum: DiscountType,
    default: DiscountType.fixed,
    deprecated: true,
    description: 'Loại giảm giá',
  })
  @IsOptional()
  @IsEnum(DiscountType)
  @EnumTransform(DiscountType)
  discountType?: DiscountType;

  @ApiProperty({
    required: false,
    description: 'Phụ thu',
  })
  @IsOptional()
  @IsNumber()
  surcharge?: number;

  @ApiProperty({
    required: false,
    description: 'Phí ship',
  })
  @IsOptional()
  @IsNumber()
  shippingFee?: number;

  @ApiProperty({
    required: false,
    description: 'Số tiền khách đã thanh toán trước',
  })
  @IsOptional()
  @IsNumber()
  paid?: number;

  @ApiProperty({
    required: false,
    description: 'Mã Zip/ Postal Code ',
  })
  @ValidateIf(object => object.status !== OrderStatus.Draft && !object.addressWardId)
  @IsOptional()
  @IsNotEmpty()
  postCode?: string | string[];

  @ApiProperty({
    required: false,
    description: 'Danh sách ID thẻ đơn hàng',
  })
  @IsOptional()
  @IsNumber(undefined, { each: true })
  tagIds: number[];

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  packingAt: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  expectDeliveryAt: string;

  @ApiProperty({
    required: false,
    description: 'Trạng thái đơn hàng',
    enum: OrderStatus,
    default: OrderStatus.New,
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  @EnumTransform(OrderStatus)
  status?: OrderStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isImport: boolean;

  @ApiProperty({
    required: false,
    description: 'Ghi chú đơn hàng',
  })
  @IsOptional()
  @IsBoolean()
  isCreatedByAI: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    required: false,
    description: 'Ghi chú in đơn cụ thể',
  })
  @IsOptional()
  @IsString()
  printNoteText?: string;

  @ApiProperty({
    required: false,
    description: 'ID ghi chú in đơn có sẵn',
  })
  @IsOptional()
  @IsNumber(undefined, { each: true })
  printNoteIds: number[];

  @ApiProperty({
    required: false,
    description: 'Thông tin đơn vị vận chuyển',
    type: OrderCarrierDto,
  })
  @Type(() => OrderCarrierDto)
  @IsOptional()
  @ValidateNested()
  carrier?: OrderCarrierDto;

  @ApiProperty({
    required: false,
    description: 'Đánh dấu có cần chuyển tự động sang data TLS để TLS chăm sóc hay không?',
  })
  @IsOptional()
  @IsBoolean()
  crossCare?: boolean;
}
