import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';

export class CreateCallCenterExtensionDto {
  @ApiProperty()
  @IsString()
  @MaxLength(255, {
    message: 'Mã đầu số không được nhiều hơn 255 ký tự',
  })
  @IsNotEmpty({
    message: 'Mã đầu số trống hoặc sai định dạng',
  })
  extensionNumber: string;

  @ApiProperty()
  @IsString()
  @MaxLength(255, {
    message: 'Mật khẩu không được nhiều hơn 255 ký tự',
  })
  @IsNotEmpty({
    message: 'M<PERSON>t khẩu trống',
  })
  extensionPassword: string;
}

export class UpdateCallCenterExtensionDto extends OmitType(CreateCallCenterExtensionDto, [
  'extensionNumber',
]) {}

export class DeActiveCallCenterDto {
  @ApiProperty()
  @IsBoolean()
  isActive: boolean;
}

export class YcallRequestWebhookDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  callid?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  customer?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  calldate?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  extension?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => Number(value)) // Transform to Number
  duration?: number;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => Number(value)) // Transform to Number
  billsec?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  recording?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  userfield?: string; // callHistoryId,extensionId,leadId

  @ApiProperty()
  @IsOptional()
  @IsString()
  signature?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  answer_stamp?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  end_stamp?: string;
  
  @ApiProperty()
  @IsOptional()
  @IsString()
  start_stamp?: string;
}

export class CallCenterExtensionsDeleteDto {
  @ApiProperty({ required: true })
  @IsArray()
  @ArrayNotEmpty({ message: 'Mảng extensionIds không được trống' })
  extensionIds: number[];
}

export class ReturnCallCenterExtensionsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  @BooleanTransform()
  isInvalid?: boolean;
}

export class CreateEtelecomCallCenterExtensionDto{
  @ApiProperty({required: true})
  @IsString()
  hotlineId: string;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  extensionNumber?: number;
}