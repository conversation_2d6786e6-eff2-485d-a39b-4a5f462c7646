import { FfmCustomerDto } from './ffm-customer.dto';
import { FfmNoteDto } from './ffm-note.dto';
import { FfmProductDto } from './ffm-product.dto';
import { FfmTagDto } from './ffm-tag.dto';

export class FfmOrderDto {
  clientId: number;

  seller?: string;
  
  displayId?: string;

  countryId: string;

  discount: number;

  surcharge: number;

  shippingFee: number;

  paid: number;

  subTotal: number;

  products: FfmProductDto[];

  note?: FfmNoteDto;

  customer: FfmCustomerDto;

  externalId: string;

  internalNote?: string;

  customerEDD?: number;

  tags?: FfmTagDto[]

  waybillNote?: string;

  projectName?: string;

  projectId?: number;

  companyId?: number;

  typeOrder?: number;
}
