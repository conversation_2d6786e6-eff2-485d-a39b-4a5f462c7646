import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsArray, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class SearchCallGroupDto {
    @ApiProperty()
    @IsString()
    @IsOptional()
    @Type(() => String)
    callGroupName: string;

    @ApiProperty()
    @IsOptional()
    @IsNumber({}, { each: true })
    @Type(() => Number)
    @Transform(({ value }) =>
      Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    createdBy: number[];

    @ApiProperty()
    @IsNumber()
    @IsOptional()
    @Type(() => Number)
    callCenterId: number;

    @ApiProperty()
    @IsOptional()
    @IsNumber({}, { each: true })
    @Type(() => Number)
    @Transform(({ value }) =>
      Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    userId: number[];
}
