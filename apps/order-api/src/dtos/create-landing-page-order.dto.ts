import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MinLength } from 'class-validator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { NonSpecialCharactersTransform } from 'core/decorators/non-special-character-transform.decorator';
import { Transform } from 'class-transformer';
import StringUtils from '../../../../core/utils/StringUtils';

export class CreateLandingPageOrderParamsDto {
  @ApiProperty({ name: 'source' })
  @IsOptional()
  source: string;
}

export class CreateLandingPageOrderDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  customerName?: string;

  @ApiProperty()
  @IsString()
  @MinLength(8)
  @NonSpecialCharactersTransform()
  @IsOptional()
  customerPhone?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsString()
  @MinLength(8)
  @NonSpecialCharactersTransform()
  @IsOptional()
  phone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  addressText: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  state: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  district: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  ward: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  note: string;

  @ApiProperty({ required: false })
  @IsOptional()
  postCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  status_send?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @DateTransform()
  message_time?: Date

  @ApiProperty({ required: false })
  @IsOptional()
  utm_source?: string

  @ApiProperty({ required: false })
  @IsOptional()
  utm_medium?: string

  @ApiProperty({ required: false })
  @IsOptional()
  utm_campaign?: string

  @ApiProperty({ required: false })
  @IsOptional()
  utm_term?: string

  @ApiProperty({ required: false })
  @IsOptional()
  link?: string

  sku?: string;

  skuQuantity?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra1?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra2?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra3?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra4?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra5?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra6?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra7?: string;


  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra8?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra9?: string;


  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  extra10?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({value}) => {
    if (!value) {
      return value;
    }
    return StringUtils.getString(value);
  })
  waybillNotes?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  collectMethod?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  message_id?: string;
}
