import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsDateString } from 'class-validator';

export class EtelecomCreateExtensionResponseDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  user_id: string;

  @ApiProperty()
  @IsString()
  account_id: string;

  @ApiProperty()
  @IsString()
  extension_number: string;

  @ApiProperty()
  @IsString()
  extension_password: string;

  @ApiProperty()
  @IsString()
  tenant_id: string;

  @ApiProperty()
  @IsString()
  tenant_domain: string;

  @ApiProperty()
  @IsString()
  hotline_id: string;

  @ApiProperty()
  @IsDateString()
  created_at: string;

  @ApiProperty()
  @IsDateString()
  updated_at: string;

  @ApiProperty()
  @IsDateString()
  expires_at: string;

  @ApiProperty()
  @IsString()
  subscription_id: string;

  @ApiProperty()
  @IsString()
  state: string;
}
