import { ApiProperty } from '@nestjs/swagger';
import { ProductVariation } from 'apps/catalog-api/src/entities/product-variation.entity';
import { Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';

export class VariantsFilter {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  names?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  cateId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isActive?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getCategories?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getAll?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  variantIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  projectIds?: (number | string)[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  productIds?: number[];

  companyId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  fields?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  ignoreProductCombo?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isGetDeleted?: boolean;
}
