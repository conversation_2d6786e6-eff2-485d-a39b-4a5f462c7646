import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  catalogConnection,
  identityConnection,
  orderConnection,
} from 'core/constants/database-connection.constant';
import { Country } from '../../entities-identity/country.entity';
import { User } from '../../entities-identity/user.entity';
import { OrderProduct } from '../../entities-order/order-product.entity';
import { Order } from '../../entities-order/order.entity';
import { AttributeValue } from '../../entities/attribute-value.entity';
import { Attribute } from '../../entities/attribute.entity';
import { Category } from '../../entities/category.entity';
import { ProductComboVariant } from '../../entities/product-combo-variant.entity';
import { ProductSyncPancake } from '../../entities/product-sync-pancake.entity';
import { ProductVariation } from '../../entities/product-variation.entity';
import { Product } from '../../entities/product.entity';
import { StockInventoryLogs } from '../../entities/stock-inventory-log.entity';
import { StockInventory } from '../../entities/stock-inventory.entity';
import { StockMoveLine } from '../../entities/stock-move-line.entity';
import { StockMoveLogs } from '../../entities/stock-move-log.entity';
import { StockMove } from '../../entities/stock-move.entity';
import { Supplier } from '../../entities/supplier.entity';
import { Warehouse } from '../../entities/warehouse.entity';
import { AttributesController } from './controllers/attributes.controller';
import { CategoriesController } from './controllers/categories.controller';
import { ConfigController } from './controllers/config.controller';
import { ProductsController } from './controllers/products.controller';
import { StockMovesController } from './controllers/stock-moves.controller';
import { SupplierController } from './controllers/suppliers.controller';
import { WarehouseController } from './controllers/warehouses.controller';
import { AttributesService } from './services/attributes.service';
import { CategoriesService } from './services/categories.service';
import { ProductsService } from './services/products.service';
import { ShopifyProductsService } from './services/shopify-products.service';
import { StockMoveService } from './services/stock-move.service';
import { SuppliersService } from './services/suppliers.service';
import { VariantsService } from './services/variants.service';
import { WarehousesService } from './services/warehouses.service';
import { PartnerProductSync } from '../../entities/partner-product-sync.entity';

@Module({
  imports: [
    RabbitMQModule.externallyConfigured(RabbitMQModule, 0),
    TypeOrmModule.forFeature(
      [
        ProductSyncPancake,
        ProductComboVariant,
        Product,
        ProductVariation,
        Category,
        Supplier,
        Attribute,
        AttributeValue,
        StockMove,
        Warehouse,
        StockMoveLine,
        StockInventoryLogs,
        StockMoveLogs,
        StockInventory,
        PartnerProductSync
      ],
      catalogConnection,
    ),
    TypeOrmModule.forFeature([User, Country], identityConnection),
    TypeOrmModule.forFeature([Order, OrderProduct], orderConnection),
    // BaseAuthModule.forRoot(),
  ],
  controllers: [
    ProductsController,
    CategoriesController,
    SupplierController,
    AttributesController,
    StockMovesController,
    ConfigController,
    WarehouseController,
  ],
  providers: [
    ProductsService,
    CategoriesService,
    SuppliersService,
    AttributesService,
    StockMoveService,
    WarehousesService,
    VariantsService,
    ShopifyProductsService,
  ],
})
export class CatalogModule {}
