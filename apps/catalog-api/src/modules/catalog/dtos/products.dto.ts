import { ApiProperty, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { ProductComboVariant } from 'apps/catalog-api/src/entities/product-combo-variant.entity';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { CaseSensitivityTransform } from 'core/decorators/case-sensitivity-transform.decorator';
import { NonSpecialCharactersTransform } from 'core/decorators/non-special-character-transform.decorator';
import { CaseSensitivity } from 'core/enums/case-sensitivity.enum';
import { ProductCategoryDto } from './product-category.dto';
import { CreateProductVariantDto, UpdateProductVariantDto } from './product-variant.dto';

export class CreateProductDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  status?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isCombo?: boolean;

  @ApiProperty()
  @IsNotEmpty()
  @CaseSensitivityTransform(CaseSensitivity.upper)
  @NonSpecialCharactersTransform(['-', '_'])
  @MaxLength(50, { message: 'Product SKU must be no more than 50 characters' })
  sku: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  projectId: number;


  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  supplierId: number;

  @ApiProperty({
    type: CreateProductVariantDto,
    isArray: true,
  })
  @IsNotEmpty({ message: 'Bạn cần chọn biến thể cho sản phẩm' })
  @ArrayNotEmpty({ message: 'Giá trị của biến thể là 1 mảng' })
  @ValidateNested({ each: true })
  @Type(() => CreateProductVariantDto)
  variants: CreateProductVariantDto[];

  @ApiProperty()
  @Type(() => ProductComboVariant)
  productCombo: ProductComboVariant[];

  @ApiProperty({
    type: ProductCategoryDto,
    isArray: true,
  })
  @IsNotEmpty({ message: 'Bạn cần chọn danh mục cho sản phẩm' })
  @ArrayNotEmpty({ message: 'Giá trị của danh mục là 1 mảng' })
  @ValidateNested({ each: true })
  @Type(() => ProductCategoryDto)
  categories: ProductCategoryDto[];
}

export class UpdateProductDto extends PartialType(OmitType(CreateProductDto, ['sku', 'variants'])) {
  @ApiProperty({
    type: UpdateProductVariantDto,
    isArray: true,
  })
  @IsNotEmpty({ message: 'Bạn cần chọn biến thể cho sản phẩm' })
  @ArrayNotEmpty({ message: 'Giá trị của biến thể là 1 mảng' })
  @ValidateNested({ each: true })
  @Type(() => UpdateProductVariantDto)
  variants: UpdateProductVariantDto[];
}

export class UpdateProductStatusDto extends PartialType(PickType(CreateProductDto, ['status'])) {}
