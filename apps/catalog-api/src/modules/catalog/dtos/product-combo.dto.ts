import { ApiProperty } from '@nestjs/swagger';
import { Category } from 'apps/catalog-api/src/entities/category.entity';
import { ProductVariation } from 'apps/catalog-api/src/entities/product-variation.entity';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AttributeValue } from '../../../entities/attribute-value.entity';
import { ProductCategoryDto } from './product-category.dto';

export class PriceCountryDto {
  @ApiProperty()
  @IsNotEmpty()
  countryId: number;

  @ApiProperty()
  @IsNotEmpty()
  price: number;

  @ApiProperty()
  @IsOptional()
  variantId: number;
}

export class VariantDto {
  @ApiProperty()
  @IsNotEmpty()
  sku?: string;

  @ApiProperty()
  @IsOptional()
  barcode?: string;

  @ApiProperty()
  @IsNotEmpty()
  price: number;

  @ApiProperty()
  @IsOptional()
  priceMarket?: number;

  @ApiProperty()
  @IsOptional()
  qty?: number;

  @ApiProperty()
  @IsOptional()
  weight?: number;

  @ApiProperty()
  @IsOptional()
  image?: string;

  @ApiProperty({
    type: AttributeValue,
    isArray: true,
  })
  // @IsNotEmpty({message: 'Bạn cần chọn thuộc tính cho biến thể'})
  // @ArrayNotEmpty({message: 'Giá trị của thuộc tính là 1 mảng'})
  @ValidateNested({ each: true })
  @Type(() => AttributeValue)
  properties: AttributeValue[];

  @IsNotEmpty({ message: 'Bạn cần chọn giá bán theo quốc gia' })
  @ArrayNotEmpty({ message: 'Giá bán theo quốc gia là 1 mảng' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: PriceCountryDto,
    isArray: true,
    required: true,
  })
  @Type(() => PriceCountryDto)
  prices: PriceCountryDto[];
}

export class VariantComboDto {
  @ApiProperty()
  @IsNumber()
  qty?: number;

  @ApiProperty()
  @IsNumber()
  price?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  status?: number;

  @ApiProperty({
    type: ProductVariation,
  })
  @IsNotEmpty()
  @Type(() => ProductVariation)
  variant: ProductVariation;
}

export class VariantPropValueDto {
  @ApiProperty({ required: false })
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsOptional()
  image?: string;
}

export class VariantPropsDto {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @ApiProperty({
    type: VariantPropValueDto,
    isArray: true,
  })
  @Type(() => VariantPropValueDto)
  values: VariantPropValueDto[];
}

export class DelProductComboDto {
  @ApiProperty()
  @IsNotEmpty()
  productId: number;

  @ApiProperty()
  @IsNotEmpty()
  variantId: number;
}

export class ProductComboDto {
  @ApiProperty({ required: false })
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @IsNumber()
  projectId: number

  @ApiProperty()
  @IsString()
  sku: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  supplierId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  status: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isCombo: boolean;

  @IsNotEmpty({ message: 'Bạn cần chọn sản phẩm cho combo' })
  @ArrayNotEmpty({ message: 'Danh sách sản phẩm là 1 mảng' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: VariantComboDto,
    isArray: true,
  })
  @Type(() => VariantComboDto)
  productCombo: VariantComboDto[];

  @ApiProperty({
    type: VariantDto,
    isArray: true,
  })
  @IsNotEmpty({ message: 'Bạn cần chọn biến thể cho sản phẩm ' })
  @ArrayNotEmpty({ message: 'Danh sách biến thể là 1 mảng' })
  @ValidateNested({ each: true })
  @Type(() => VariantDto)
  variants: VariantDto[];

  @ApiProperty({
    type: ProductCategoryDto,
    isArray: true,
  })
  @IsNotEmpty({ message: 'Bạn cần chọn danh mục cho sản phẩm' })
  @ArrayNotEmpty({ message: 'Giá trị của danh mục là 1 mảng' })
  @ValidateNested({ each: true })
  @Type(() => ProductCategoryDto)
  categories: ProductCategoryDto[];
}
