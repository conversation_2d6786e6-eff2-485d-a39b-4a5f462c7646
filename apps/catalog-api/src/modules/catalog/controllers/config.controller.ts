import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  STATUS,
  STOCK_STATUS,
  STOCK_STATUS_BLOCK_EDIT,
  STOCK_STATUS_INVENTORY,
  STOCK_STATUS_ROLE,
  _STATUS
} from '../constants/config.constants';
import { AttributesService } from '../services/attributes.service';

@Controller('config')
@ApiTags('config')
export class ConfigController {
  constructor(private readonly attributesService: AttributesService) {}

  @Get('')
  async fetch() {
    return {
      status: STATUS,
      statusText: _STATUS,
      stock: {
        status: STOCK_STATUS,
        roles: STOCK_STATUS_ROLE,
        block: STOCK_STATUS_BLOCK_EDIT,
        inventory: STOCK_STATUS_INVENTORY,
      },
    };
  }
}
