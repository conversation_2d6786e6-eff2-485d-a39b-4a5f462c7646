import { AmqpConnection, Nack, RabbitRP<PERSON> } from '@golevelup/nestjs-rabbitmq';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MarketplaceIntegration } from 'apps/catalog-api/src/entities-order/marketplace-integration.entity';
import { AttributeValue } from 'apps/catalog-api/src/entities/attribute-value.entity';
import { Attribute } from 'apps/catalog-api/src/entities/attribute.entity';
import { ProductVariantPrice } from 'apps/catalog-api/src/entities/product-variant-price.entity';
import { ProductVariation } from 'apps/catalog-api/src/entities/product-variation.entity';
import { Product } from 'apps/catalog-api/src/entities/product.entity';
import { plainToInstance } from 'class-transformer';
import convert from 'convert';
import { createShopifyApiClient } from 'core/clients/shopify/shopify-api.client';
import { SHOPIFY_ENDPOINTS } from 'core/clients/shopify/shopify-endpoints.constant';
import { ShopifyProductOption } from 'core/clients/shopify/shopify-product-option.model';
import { ShopifyProduct } from 'core/clients/shopify/shopify-product.model';
import { catalogConnection } from 'core/constants/database-connection.constant';
import { MarketplacePlatform } from 'core/enums/marketplace-platform.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import StringUtils from 'core/utils/StringUtils';
import { find, isEmpty, uniqBy } from 'lodash';
import { QueryRunner, Repository, getConnection } from 'typeorm';
import { STATUS } from '../constants/config.constants';
import { PartnerProductSync } from 'apps/catalog-api/src/entities/partner-product-sync.entity';
import { PartnerCode } from 'core/enums/partner-code.enum';

@Injectable()
export class ShopifyProductsService {
  constructor(
    @InjectRepository(Product, catalogConnection)
    private productsRepository: Repository<Product>,
    @InjectRepository(ProductVariation, catalogConnection)
    private productVariantsRepository: Repository<ProductVariation>,
    @InjectRepository(PartnerProductSync, catalogConnection)
    private partnerProductSyncRepository: Repository<PartnerProductSync>,
    private amqpConnection: AmqpConnection,
  ) {}

  @RabbitRPC({
    exchange: 'shopify-webhook-event',
    routingKey: 'products-created',
    queue: 'order-process-shopify-products-created',
    errorHandler: rmqErrorsHandler,
  })
  async processShopifyProduct({ shopId, data }: { shopId: string; data: { id: number } }) {
    try {
      const shopUrlSuffix = !shopId.endsWith('myshopify.com') ? 'myshopify.com' : undefined;
      const shopUrl = [shopId];
      if (shopUrlSuffix) shopUrl.push(shopUrlSuffix);
      const { data: marketplace } = await this.amqpConnection.request<{
        data: MarketplaceIntegration;
      }>({
        exchange: 'order-service',
        routingKey: 'get-marketplace-integration-by-shop-url',
        payload: {
          shopUrl: shopUrl.join('.'),
          platform: MarketplacePlatform.shopify,
        },
        timeout: 10000,
      });
      if (!marketplace) return new Nack(false);

      // console.log(`marketplace`, marketplace)

      const clientApi = createShopifyApiClient({
        storeDomain: marketplace.shopUrl,
        accessToken: marketplace.extraData.accessToken,
      });

      const response = await clientApi(SHOPIFY_ENDPOINTS.PRODUCTS + `/${data.id}`);
      if (!response?.product) {
        console.log(`Cannot get shopify product information`);
        return new Nack(false);
      }

      const product = await this.saveProduct(response.product, marketplace);
      console.log(`save shopify product result`, product);

      return new Nack(false);
    } catch (error) {
      console.log(`process shopify error`, error);
      throw error;
    }
  }

  @RabbitRPC({
    exchange: 'catalog-service-products',
    routingKey: 'sync-shopify-products',
    queue: 'catalog-sync-shopify-products',
    errorHandler: rmqErrorsHandler,
  })
  async syncShopifyProducts(payload) {
    const { id } = payload;
    if (!id) return new Nack(false);

    try {
      const { data: marketplace } = await this.amqpConnection.request<{
        data: MarketplaceIntegration;
      }>({
        exchange: 'order-service',
        routingKey: 'get-marketplace-integration-by-id',
        payload: { id },
        timeout: 10000,
      });
      console.log(`marketplace`, marketplace);
      if (!marketplace) return new Nack(false);

      const clientApi = createShopifyApiClient({
        storeDomain: marketplace.shopUrl,
        accessToken: marketplace.extraData.accessToken,
      });

      const response = await clientApi(SHOPIFY_ENDPOINTS.PRODUCTS, {
        method: 'GET',
        params: { fields: ['id'] },
      });
      if (!response?.products) return new Nack(false);

      await Promise.all(
        response.products.map(p =>
          this.amqpConnection.publish('shopify-webhook-event', 'products-created', {
            shopId: marketplace.shopUrl,
            data: { id: p.id },
          }),
        ),
      );
    } catch (error) {
      console.log(`sync shopify products error`, error);
      throw error;
    }

    return new Nack(false);
  }

  // Sync single Shopify product => AG system
  async saveProduct(data: ShopifyProduct, marketplace: MarketplaceIntegration) {
    const product = new Product();
    product.name = data.title;
    product.sku = StringUtils.removeAccents(data.handle).toUpperCase();
    product.status = STATUS.active;
    product.companyId = marketplace.companyId;
    product.projectId = marketplace.projectId;

    const connection = getConnection(catalogConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const attributes = await this.saveProductOptions(
        data.options,
        marketplace.companyId,
        queryRunner,
      );

      const insertResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(Product)
        .values(product)
        .orUpdate(['status'], 'UQ_SKU_PRODUCT')
        .execute();
      product.id = insertResult.identifiers?.[0]?.id;
      if (!product?.id) throw new UnprocessableEntityException(`Cannot save Shopify product`);

      const variations = [];
      for (const item of data.variants) {
        if (isEmpty(item.sku)) continue;
        const variation = plainToInstance(ProductVariation, {
          sku: item.sku,
          name: item.title,
          barcode: item.barcode,
          weight: convert(item.weight, item.weight_unit).to('g'),
          status: STATUS.active,
          priceMarket: 0,
          qty: 0,
          productId: product.id,
          projectId: marketplace.projectId,
          companyId: marketplace.companyId,
        });

        const variantInsertResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(ProductVariation)
          .values(variation)
          .orIgnore()
          .execute();

        variation.id = variantInsertResult.identifiers?.[0]?.id;
        if (!variation.id) continue;

        const prices = [
          plainToInstance(ProductVariantPrice, {
            price: item.price,
            variantId: variation.id,
            countryId: marketplace.countryId,
            status: STATUS.active,
          }),
        ];

        const properties = [];
        const optionKeys = ['option1', 'option2', 'option3'];
        for (let i = 0; i < optionKeys.length; i++) {
          const key = optionKeys[i];
          if (item[key]) {
            const attr = attributes[i];
            if (attr) {
              const prop = attr.properties.find(p => p.name === item[key]);
              if (prop)
                properties.push({
                  id_product_variation: variation.id,
                  id_attributes_value: prop.id,
                });
            }
          }
        }

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(ProductVariantPrice)
          .values(prices)
          .orIgnore()
          .execute();

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(`product_variation_attributes_value`)
          .values(properties)
          .orIgnore()
          .execute();

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(PartnerProductSync)
          .values({
            productId: product.id,
            variantId: variation.id,
            partnerProductId: String(product.id),
            partnerVariantId: String(item.id),
            partnerCode: PartnerCode.shopify,
            marketplaceIntegrationId: marketplace.id,
          })
          .orIgnore()
          .execute();

        variations.push(variation);
      }

      // INSERT INTO "product_variant_price"("price", "variant_id", "country_id", "status") VALUES ($1, $2, $3, DEFAULT), ($4, $5, $6, DEFAULT) RETURNING "status" -- PARAMETERS: ["6000",2134,84,"9000",2135,84]
      // INSERT INTO "product_variation_attributes_value"("id_product_variation", "id_attributes_value") VALUES ($1, $2), ($3, $4) -- PARAMETERS: [2134,541,2135,542]

      console.log(`variations inserted`, variations);
      await queryRunner.commitTransaction();
      // console.log(`product`, product);
      return product;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async saveProductOptions(
    options: ShopifyProductOption[],
    companyId: number,
    queryRunner: QueryRunner,
  ): Promise<Attribute[]> {
    const attributes = [];

    for (const option of options) {
      const attribute = new Attribute();
      attribute.name = option.name;
      attribute.companyId = companyId;
      attribute.status = STATUS.active;

      const insertAttResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(Attribute)
        .values(attribute)
        .orUpdate(['status'], ['name', 'company_id'])
        .execute();

      attribute.id = insertAttResult.identifiers?.[0]?.id;

      if (!attribute.id) {
        console.log(`Cannot save attribute from Shopify`, insertAttResult);
        throw new UnprocessableEntityException(
          insertAttResult,
          `Cannot save attribute from Shopify`,
        );
      }

      console.log(`Insert attribute from Shopify result`, insertAttResult);

      const attValues = option.values.map(value =>
        plainToInstance(AttributeValue, {
          name: value,
          attributesId: attribute.id,
          status: STATUS.active,
        }),
      );

      const insertAttValuesResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(AttributeValue)
        .values(attValues)
        .orUpdate(['status'], ['name', 'attributes_id'])
        .execute();

      console.log(`Insert attribute values from Shopify result`, insertAttValuesResult);

      attribute.properties = insertAttValuesResult.identifiers.map((it, idx) =>
        plainToInstance(AttributeValue, { id: it.id, attribute, ...attValues[idx] }),
      );
      attributes.push(attribute);
    }

    return attributes;
  }

  @RabbitRPC({
    exchange: 'catalog-service-products',
    routingKey: 'sync-variant-price-to-shopify',
    queue: 'catalog-service-product-sync-variant-price-to-shopify',
    errorHandler: rmqErrorsHandler,
  })
  async syncShopifyVariantPrices(payload) {
    const { data } = payload;

    const variantAvailable = [];
    for (const vrt of data) {
      const vrtSaved = await this.partnerProductSyncRepository.findOne({
        where: {
          productId: vrt.productId,
          variantId: vrt.variantId,
          partnerCode: PartnerCode.shopify,
        },
      });
      if (!isEmpty(vrtSaved)) {
        variantAvailable.push({
          ...vrtSaved,
          price: vrt.price,
          countryId: vrt.countryId,
        });
      }
    }
    if (variantAvailable.length === 0) return new Nack(false);
    const variantWithMarketInfo = [];
    for (const variant of variantAvailable) {
      const {
        data: marketplace,
      }: { data: MarketplaceIntegration } = await this.amqpConnection.request({
        exchange: 'order-service',
        routingKey: 'get-marketplace-integration-by-id',
        payload: {
          id: variant.marketplaceIntegrationId,
        },
        timeout: 10000,
      });
      if (marketplace && marketplace.isSyncProductPrices)
        variantWithMarketInfo.push({
          ...variant,
          marketplace,
        });
    }
    const uqVariantWithMarketInfo = uniqBy(variantWithMarketInfo, x => x.id);
    if (isEmpty(uqVariantWithMarketInfo)) return new Nack(false);
    for (const vMarket of uqVariantWithMarketInfo) {
      await this.syncShopifyVariantPrice(
        vMarket.marketplace,
        vMarket.partnerVariantId,
        vMarket.price,
      );
    }
    return new Nack(false);
  }

  async syncShopifyVariantPrice(
    marketplace: MarketplaceIntegration,
    variantId: number,
    price: number,
  ) {
    const clientApi = createShopifyApiClient({
      storeDomain: marketplace.shopUrl,
      accessToken: marketplace.extraData.accessToken,
    });
    const response = await clientApi(`${SHOPIFY_ENDPOINTS.VARIANTS}/${variantId}`, {
      method: 'PUT',
      data: {
        variant: {
          id: variantId,
          price,
        },
      },
    });
    console.log(`syncShopifyVariantPrice success`, response);
  }
}
