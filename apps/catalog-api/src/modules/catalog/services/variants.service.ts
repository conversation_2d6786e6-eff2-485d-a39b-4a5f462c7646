import { <PERSON><PERSON>, <PERSON>RP<PERSON>, default<PERSON>ack<PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '@golevelup/nestjs-rabbitmq';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductSyncPancake } from 'apps/catalog-api/src/entities/product-sync-pancake.entity';
import { ProductVariation } from 'apps/catalog-api/src/entities/product-variation.entity';
import { catalogConnection } from 'core/constants/database-connection.constant';
import { isEmpty } from 'lodash';
import { Repository, getConnection } from 'typeorm';
import { STATUS } from '../constants/config.constants';
import { plainToInstance } from 'class-transformer';
import { AttributeValue } from 'apps/catalog-api/src/entities/attribute-value.entity';
import { Attribute } from 'apps/catalog-api/src/entities/attribute.entity';
import { ProductVariantPrice } from 'apps/catalog-api/src/entities/product-variant-price.entity';
import { Product } from 'apps/catalog-api/src/entities/product.entity';
import { Category } from 'apps/catalog-api/src/entities/category.entity';
import { ErrorCode } from 'apps/catalog-api/src/enums/error-code.enum';

@Injectable()
export class VariantsService {
  constructor(
    @InjectRepository(ProductVariation, catalogConnection)
    private variantsRepository: Repository<ProductVariation>,
    @InjectRepository(ProductSyncPancake, catalogConnection)
    private productSyncRepo: Repository<ProductSyncPancake>,
  ) {}

  async upsertMultipleVariants(variants: ProductVariation[]) {
    const connection = getConnection(catalogConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const variant of variants) {
        const productInsertResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(Product)
          .values(variant.product)
          .orUpdate(['status'], 'UQ_SKU_PRODUCT')
          .execute();
        if (!productInsertResult.identifiers?.[0]?.id) {
          console.log(`Cannot save product`, productInsertResult);
          throw new UnprocessableEntityException(`Cannot save product`);
        }
        variant.product.id = productInsertResult.identifiers?.[0]?.id;

        const variantInsertResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(ProductVariation)
          .values(variant)
          .orUpdate(['status'], 'UQ_SKU_VARIANT')
          .execute();
        if (!variantInsertResult.identifiers?.[0]?.id) {
          console.log(`Cannot save product variation`, variantInsertResult);
          throw new UnprocessableEntityException(`Cannot save product variation`);
        }
        variant.id = variantInsertResult.identifiers?.[0]?.id;
        variant.prices = variant.prices.map(price =>
          plainToInstance(ProductVariantPrice, { ...price, variantId: variant.id }),
        );

        for (const prop of variant.properties) {
          const insertAttResult = await queryRunner.manager
            .createQueryBuilder()
            .insert()
            .into(Attribute)
            .values(prop.attributes)
            .orUpdate(['status'], ['name', 'company_id'])
            .execute();

          if (!insertAttResult.identifiers?.[0]?.id) {
            console.log(`Cannot save attribute`, insertAttResult);
            throw new UnprocessableEntityException(insertAttResult, `Cannot save attribute`);
          }

          prop.attributesId = insertAttResult.identifiers?.[0]?.id;
          prop.attributes.id = prop.attributesId;

          const insertAttValuesResult = await queryRunner.manager
            .createQueryBuilder()
            .insert()
            .into(AttributeValue)
            .values(prop)
            .orUpdate(['status'], ['name', 'attributes_id'])
            .execute();

          if (!insertAttValuesResult.identifiers?.[0]?.id) {
            console.log(`Cannot save attribute value`, insertAttValuesResult);
            throw new UnprocessableEntityException(
              insertAttValuesResult,
              `Cannot save attribute value`,
            );
          }
          prop.id = insertAttValuesResult.identifiers?.[0]?.id;
        }

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(`product_variation_attributes_value`)
          .values(
            variant.properties.map(p => ({
              id_product_variation: variant.id,
              id_attributes_value: p.id,
            })),
          )
          .orIgnore()
          .execute();

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(ProductVariantPrice)
          .values(variant.prices)
          .orIgnore()
          .execute();
      }

      await queryRunner.commitTransaction();
      return variants;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async upsertSingleVariant(variant: ProductVariation) {
    const connection = getConnection(catalogConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const productInsertResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(Product)
        .values(variant.product)
        .orUpdate(['status'], 'UQ_SKU_PRODUCT')
        .execute();
      if (!productInsertResult.identifiers?.[0]?.id) {
        console.log(`Cannot save product`, productInsertResult);
        throw new UnprocessableEntityException(`Cannot save product`);
      }

      variant.product.id = productInsertResult.identifiers?.[0]?.id;

      if (variant.product?.categories) {
        const categoryInsertResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(Category)
          .values(variant.product.categories)
          .orUpdate(['status'], 'UQ_CATEGORY_NAME')
          .execute();

        if (!categoryInsertResult.identifiers?.[0]?.id) {
          console.log(`Cannot save category`, categoryInsertResult);
          throw new UnprocessableEntityException(`Cannot save category`);
        }
        const categoryId = categoryInsertResult.identifiers?.[0]?.id;

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into('category_product')
          .values({
            id_product: variant.product.id,
            id_category: categoryId,
          })
          .orIgnore()
          .execute();
      }

      const variantInsertResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(ProductVariation)
        .values(variant)
        .orIgnore()
        .execute();
      if (!variantInsertResult.identifiers?.[0]?.id) {
        console.log(`Cannot save product variation`, variantInsertResult);
        throw new UnprocessableEntityException({
          code: ErrorCode.PRD_0001,
          message: `SKU ${variant.sku} đã tồn tại`,
          sku: variant.sku,
        });
      }
      variant.id = variantInsertResult.identifiers?.[0]?.id;
      variant.prices = variant.prices.map(price =>
        plainToInstance(ProductVariantPrice, { ...price, variantId: variant.id }),
      );

      for (const prop of variant.properties) {
        const insertAttResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(Attribute)
          .values(prop.attributes)
          .orUpdate(['status'], ['name', 'company_id'])
          .execute();

        if (!insertAttResult.identifiers?.[0]?.id) {
          console.log(`Cannot save attribute`, insertAttResult);
          throw new UnprocessableEntityException(insertAttResult, `Cannot save attribute`);
        }

        prop.attributesId = insertAttResult.identifiers?.[0]?.id;
        prop.attributes.id = prop.attributesId;

        const insertAttValuesResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(AttributeValue)
          .values(prop)
          .orUpdate(['status'], ['name', 'attributes_id'])
          .execute();

        if (!insertAttValuesResult.identifiers?.[0]?.id) {
          console.log(`Cannot save attribute value`, insertAttValuesResult);
          throw new UnprocessableEntityException(
            insertAttValuesResult,
            `Cannot save attribute value`,
          );
        }
        prop.id = insertAttValuesResult.identifiers?.[0]?.id;
      }

      await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(`product_variation_attributes_value`)
        .values(
          variant.properties.map(p => ({
            id_product_variation: variant.id,
            id_attributes_value: p.id,
          })),
        )
        .orIgnore()
        .execute();

      await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(ProductVariantPrice)
        .values(variant.prices)
        .orIgnore()
        .execute();

      await queryRunner.commitTransaction();
      return variant;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  @RabbitRPC({
    exchange: 'catalog-service-variants',
    routingKey: 'find-variant',
    queue: 'catalog-find-variant',
    errorHandler: defaultNackErrorHandler,
  })
  async findVariantById(id: number) {
    try {
      const record = await this.variantsRepository.findOne(id);
      if (!record) return new Nack(false);
      return record;
    } catch (error) {
      console.log(`find variant by id error`, error);
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'catalog-service-variants',
    routingKey: 'find-variant-by-list-sku',
    queue: 'catalog-find-variant-by-list-sku',
    errorHandler: defaultNackErrorHandler,
  })
  async findByListSku({ listSku, countryId, projectIds, companyId }) {
    const qb = this.variantsRepository
      .createQueryBuilder('v')
      .where('v.sku IN (:...listSku)', { listSku })
      .andWhere('v.status = :status', {
        status: STATUS.active,
      })
      .leftJoinAndSelect('v.product', 'product')
      .leftJoinAndSelect('v.properties', 'props')
      .leftJoinAndSelect('props.attributes', 'att')
      .innerJoinAndSelect('v.prices', 'prices', 'prices.country_id = :countryId', {
        countryId,
      });
    if (!isEmpty(projectIds)) qb.andWhere('product.projectId IN (:...projectIds)', { projectIds });
    if (companyId) qb.andWhere('v.companyId = :companyId', { companyId });

    return qb.getMany();
  }

  @RabbitRPC({
    exchange: 'catalog-service-variants',
    routingKey: 'find-variant-by-ids',
    queue: 'catalog-find-variant-by-ids',
    errorHandler: defaultNackErrorHandler,
  })
  async findByIds({ ids, countryId }) {
    const qb = this.variantsRepository
      .createQueryBuilder('variants')
      .andWhereInIds(ids)
      .andWhere('variants.status = :status', {
        status: STATUS.active,
      })
      .leftJoinAndSelect('variants.product', 'product')
      .leftJoinAndSelect('product.combo', 'product_combo')
      .leftJoinAndSelect('product_combo.variant', 'product_combo_variant')
      .leftJoinAndSelect('product_combo_variant.product', 'product_combo_variant_product')
      .leftJoinAndSelect('variants.properties', 'properties')
      .leftJoinAndSelect('properties.attributes', 'attributes')
      .leftJoinAndSelect('variants.prices', 'prices', 'prices.country_id = :countryId', {
        countryId,
      })
      .leftJoinAndSelect('variants.productSync', 'productSync');

    const data = await qb.getMany();
    return data;
  }

  @RabbitRPC({
    exchange: 'catalog-service-variants',
    routingKey: 'find-product-sync-by-ids',
    queue: 'catalog-find-product-sync-by-ids',
    errorHandler: defaultNackErrorHandler,
  })
  async findProductSyncByIds({ variantIds, countryId, type }) {
    const query = this.productSyncRepo.createQueryBuilder('o');
    query.andWhere('o.variant_id IN (:...variantIds)', { variantIds });
    if (countryId) query.andWhere('o.country_id = :countryId', { countryId });
    if (type) query.andWhere('o.type = :type', { type });
    const data = await query.getMany();
    return data;
  }

  @RabbitRPC({
    exchange: 'catalog-service-variants',
    routingKey: 'find-variants-by-sku-query',
    queue: 'catalog-find-variants-by-sku-query',
    errorHandler: defaultNackErrorHandler,
  })
  async findVariantsByString({ query, projectId, countryId, companyId }) {
    if (isEmpty(query)) return [];

    const qb = this.variantsRepository
      .createQueryBuilder('v')
      .where(`:query ILIKE CONCAT('%', v.sku, '%')`)
      .andWhere(`v.sku IS NOT NULL`)
      .andWhere('v.status = :status')
      .innerJoinAndSelect('v.product', 'product', 'product.projectId = :projectId')
      .innerJoinAndSelect('v.properties', 'props')
      .innerJoinAndSelect('props.attributes', 'att')
      .innerJoinAndSelect('v.prices', 'prices', 'prices.country_id = :countryId')
      .setParameters({ query, projectId, countryId, status: STATUS.active });
    if (companyId) qb.andWhere('v.companyId = :companyId', { companyId });

    return qb.getMany();
  }
}
