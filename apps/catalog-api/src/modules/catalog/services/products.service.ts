import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Country } from 'apps/catalog-api/src/entities-identity/country.entity';
import { ProductSyncPancake } from 'apps/catalog-api/src/entities/product-sync-pancake.entity';
import ApiPancakeUtils from 'apps/catalog-api/src/utils/ApiPancakeUtils';
import ApiPfgUtils from 'apps/catalog-api/src/utils/ApiPfgUtils';
import axios from 'axios';
import {
  catalogConnection,
  identityConnection,
  orderConnection,
} from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { OrderStatus } from 'core/enums/order-status.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import ExcelUtils from 'core/utils/ExcelUtils';
import {
  chunk,
  concat,
  find,
  findIndex,
  identity,
  isArray,
  isEmpty,
  isEqual,
  isNil,
  map,
  omit,
  reduce,
  snakeCase,
  startsWith,
  uniq,
  uniqBy,
  uniqWith,
} from 'lodash';
import xlsx from 'node-xlsx';
import { ILike, In, Repository, SelectQueryBuilder, getConnection } from 'typeorm';
import { OrderProduct } from '../../../entities-order/order-product.entity';
import { AttributeValue } from '../../../entities/attribute-value.entity';
import { Attribute } from '../../../entities/attribute.entity';
import { Category } from '../../../entities/category.entity';
import { ProductComboVariant } from '../../../entities/product-combo-variant.entity';
import { ProductVariation } from '../../../entities/product-variation.entity';
import { Product } from '../../../entities/product.entity';
import { StockInventory } from '../../../entities/stock-inventory.entity';
import { Supplier } from '../../../entities/supplier.entity';
import { STATUS, TypeSyncProduct } from '../constants/config.constants';
import { ProductComboDto } from '../dtos/product-combo.dto';
import { SetProjectOnProductsDto } from '../dtos/product-project.dto';
import { ProductComboSyncDto, VariantComboSyncDto } from '../dtos/products-sync-combo.dto';
import {
  ProductSyncDto,
  ProductSyncPfgDto,
  VariantSyncDto,
  VariantSyncPfgDto,
} from '../dtos/products-sync.dto';
import { CreateProductDto, UpdateProductDto, UpdateProductStatusDto } from '../dtos/products.dto';
import { StockMovesFilter } from '../filters/stock-moves.filter';
import { ProductsFilter } from '../filters/products.filter';
import { VariantsFilter } from '../filters/variants.filter';
import { plainToInstance } from 'class-transformer';
import { ProductVariantPrice } from 'apps/catalog-api/src/entities/product-variant-price.entity';
import * as countries from 'core/constants/countries.json';
import { Project } from 'apps/catalog-api/src/entities-identity/project.entity';
import { BulkCreateProductLine, BulkCreateProductsDto } from '../dtos/bulk-create-products.dto';
import { VariantsService } from './variants.service';
import StringUtils from 'core/utils/StringUtils';
import { RawResponse } from 'core/raw/raw-response';
import { Brackets } from 'typeorm';
import { ErrorCode } from '../../../enums/error-code.enum';
import { CheckProductsExistInProjectDto } from '../dtos/check-products-exist-in-project.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product, catalogConnection)
    private productsRepo: Repository<Product>,
    @InjectRepository(ProductVariation, catalogConnection)
    private variantRepo: Repository<ProductVariation>,
    @InjectRepository(Attribute, catalogConnection)
    private attrRepository: Repository<Attribute>,
    @InjectRepository(StockInventory, catalogConnection)
    private siRepository: Repository<StockInventory>,
    @InjectRepository(Category, catalogConnection)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Supplier, catalogConnection)
    private supplierRepository: Repository<Supplier>,
    @InjectRepository(AttributeValue, catalogConnection)
    private avRepository: Repository<AttributeValue>,
    @InjectRepository(ProductComboVariant, catalogConnection)
    private productComboVariantsRepo: Repository<ProductComboVariant>,
    @InjectRepository(OrderProduct, orderConnection)
    private odpRepository: Repository<OrderProduct>,
    @InjectRepository(ProductSyncPancake, catalogConnection)
    private pspRepository: Repository<ProductSyncPancake>,
    @InjectRepository(Country, identityConnection)
    private ctRepository: Repository<Country>,
    private variantsService: VariantsService,
    private amqpConnection: AmqpConnection,
  ) {}

  joinVariation(qb: SelectQueryBuilder<Product>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'var') > -1;
    if (hasJoined) return qb;

    qb.leftJoin(ProductVariation, 'var', 'var.productId = p.id');
  }

  getProductsQuery(
    filter: ProductsFilter,
    countryId: number | string,
    pagination?: PaginationOptions,
  ): SelectQueryBuilder<Product> {
    const qb = this.productsRepo
      .createQueryBuilder('p')
      .where('p.status != :deletedStatus')
      .andWhere('p.projectId IS NOT NULL')
      .setParameters({ deletedStatus: STATUS.delete });

    if (pagination) qb.take(pagination.limit).skip(pagination.skip);

    const { companyId, cateId, projectIds, productIds, isCombo } = filter;
    const name = filter.name;
    if (!isNil(isCombo)) {
      if (isCombo === false) qb.andWhere('(p.isCombo IS NULL OR p.isCombo = FALSE)');
      else qb.andWhere('p.isCombo = TRUE');
    }
    if (companyId) qb.andWhere({ companyId });
    if (cateId)
      qb.innerJoin(
        'category_product',
        'cp',
        'cp.id_product = p.id AND cp.id_category = :cateId',
      ).setParameters({ cateId });
    if (!isEmpty(productIds)) qb.andWhere('p.id IN (:...productIds)', { productIds });
    if (!isEmpty(projectIds)) qb.andWhere('p.projectId IN (:...projectIds)', { projectIds });
    if (countryId) {
      this.joinVariation(qb);
      qb.innerJoin(
        'var.prices',
        'pr',
        `pr.status = ${STATUS.active} AND pr.countryId = ${countryId}`,
      );
    }
    if (name) {
      this.joinVariation(qb);
      // const query = name.trim();
      qb.andWhere(
        new Brackets(sqb => {
          sqb
            .where(`unaccent(p.name) ILIKE unaccent('%${name}%')`)
            .orWhere(`unaccent(p.sku) ILIKE unaccent('%${name}%')`)
            .orWhere(`unaccent(var.sku) ILIKE unaccent('%${name}%')`);
        }),
      );
    }

    qb.leftJoinAndSelect('p.combo', 'combo');
    qb.leftJoinAndSelect('combo.variant', 'variantCombo');

    return qb;
  }

  async find(
    filter: ProductsFilter,
    countryId: number | string,
    pagination?: PaginationOptions,
  ): Promise<[Product[], number]> {
    const qb = this.getProductsQuery(filter, countryId, pagination)
      .leftJoin('p.supplier', 'sup')
      .addSelect(['sup.id', 'sup.name'])
      .leftJoin('p.categories', 'c')
      .addSelect(['c.id', 'c.name'])
      .leftJoin('p.variations', 'v')
      .addSelect(['v.id', 'v.name', 'v.sku', 'v.status', 'v.weight'])
      .leftJoinAndSelect('v.prices', 'prices', `prices.status = ${STATUS.active}`)
      .leftJoin('v.properties', 'props')
      .addSelect(['props.id', 'props.name'])
      .leftJoin('props.attributes', 'attr')
      .addSelect(['attr.id', 'attr.name', 'attr.status']);
    if (filter.isActive) {
      qb.andWhere('p.status = :isActive', { isActive: STATUS.active });
      qb.andWhere('v.status = :isActive', { isActive: STATUS.active });
    }

    if (filter.status === STATUS.active) {
      qb.andWhere('p.status = :isActive', { isActive: STATUS.active });
    } else if (filter.status === STATUS.deactivate) {
      qb.andWhere('p.status = :isActive', { isActive: STATUS.deactivate });
    }

    qb.orderBy('p.createdAt', 'DESC');
    return qb.getManyAndCount();

    // const { productIds, getAll } = filter || {};
    // const companyId = request?.user?.companyId ?? filter?.companyId;

    // return this.productsRepo.findAndCount({
    //   take: pagination?.limit,
    //   skip: pagination?.skip,
    //   where: qb => {
    //     qb.where({ status: STATUS.active })
    //       // .andWhere('Product__variations.status = :stt', { stt: STATUS.active })
    //       .andWhere('Product__variations__prices.status = :stt', { stt: STATUS.active });
    //     // .andWhere('Product__categories.status = :stt', {
    //     //   stt: STATUS.active,
    //     // });

    //     if (!getAll) {
    //       if (!!header['project-ids'])
    //         qb.andWhere({
    //           projectId: In(
    //             header['project-ids'].includes(',')
    //               ? header['project-ids'].split(',').map(item => {
    //                   return Number(item);
    //                 })
    //               : [header['project-ids']],
    //           ),
    //         });
    //       if (!!header['country-ids'])
    //         qb.andWhere('Product__variations__prices.countryId IN (:...countryIds)', {
    //           countryIds: header['country-ids'].includes(',')
    //             ? header['country-ids'].split(',').map(item => {
    //                 return Number(item);
    //               })
    //             : [header['country-ids']],
    //         });
    //     }

    //     if (filter?.cateId)
    //       qb.andWhere('Product__categories.id = :cateId', {
    //         cateId: filter?.cateId,
    //       });

    //     if (!isEmpty(productIds))
    //       qb.andWhere({
    //         id: In(productIds),
    //       });

    //     if (filter?.name)
    //       qb.andWhere([{ name: ILike(`%${filter?.name}%`) }, { sku: ILike(`%${filter?.name}%`) }]);
    //     if (filter.projectIds)
    //       qb.andWhere('Product.projectId in (:...projectIds)', {
    //         projectIds: filter.projectIds,
    //       });

    //     if (!isEmpty(filter?.variantIds))
    //       qb.andWhere('Product__variations.id in (:...variantIds)', {
    //         variantIds: filter?.variantIds,
    //       });

    //     if (companyId) qb.andWhere({ companyId });
    //   },
    //   order: {
    //     createdAt: 'DESC',
    //   },
    //   relations: [
    //     'variations',
    //     'variations.prices',
    //     'supplier',
    //     'categories',
    //     'variations.properties',
    //     'variations.properties.attributes',
    //   ],
    // });
  }

  async findVariants(
    filter: VariantsFilter,
    countryId: number | string,
    pagination: PaginationOptions,
  ): Promise<[ProductVariation[], number]> {
    const {
      companyId,
      names,
      variantIds,
      projectIds,
      productIds,
      getCategories,
      getAll,
      fields,
      ignoreProductCombo,
      cateId,
      isActive,
      isGetDeleted,
    } = filter;
    const qb = this.variantRepo.createQueryBuilder('var');

    if (!isGetDeleted) {
      qb.where(`var.status != ${STATUS.delete}`).innerJoinAndSelect(
        'var.product',
        'product',
        `product.status != ${STATUS.delete}`,
      );
    } else {
      qb.innerJoinAndSelect('var.product', 'product');
    }

    qb.leftJoinAndSelect('product.categories', 'categories').leftJoinAndSelect(
      'var.prices',
      'prices',
    );

    const name = filter.name?.trim();

    if (companyId) qb.andWhere('product.companyId = :companyId', { companyId });
    if (getCategories) qb.leftJoinAndSelect('product.categories', 'categories');
    if (getAll) qb.andWhere('product.isCombo != :isCombo', { isCombo: true });

    if (cateId) {
      qb.innerJoin(
        'category_product',
        'cp',
        'cp.id_product = product.id AND cp.id_category = :cateId',
      ).setParameters({ cateId });
    }

    if (isActive && !isGetDeleted) {
      qb.andWhere(`var.status = ${STATUS.active}`);
      qb.andWhere(`product.status = ${STATUS.active}`);
    }

    if (!isEmpty(name))
      qb.andWhere(
        "(product.name ILIKE '%" +
          name +
          "%' or var.sku ILIKE '%" +
          name +
          "%' or var.name ILIKE '%" +
          name +
          "%')",
      );
    if (names) qb.andWhere('var.sku in (:...names)', { names });
    if (projectIds) qb.andWhere('product.projectId IN (:...projectIds)', { projectIds });
    if (variantIds) qb.andWhere('var.id IN (:...variantIds)', { variantIds });
    if (productIds) {
      const comboVariantsQb = this.productComboVariantsRepo
        .createQueryBuilder('pcv')
        .leftJoin('pcv.variant', 'variant')
        .where('"variant"."id_product" IN (:...pIds)')
        .select(['"pcv"."id_product_variation"', '"pcv"."id_product"']);

      qb.leftJoin(
        '(' + comboVariantsQb.getQuery() + ')',
        'comVar',
        '("comVar"."id_product" = "var"."id_product")',
      ).andWhere('(product.id IN (:...pIds) OR var.id_product = "comVar"."id_product")', {
        pIds: productIds,
      });
    }
    if (ignoreProductCombo) {
      qb.andWhere('(product.isCombo IS NULL OR product.isCombo = FALSE)');
    }
    if (countryId)
      qb.andWhere(`prices.status = ${STATUS.active} AND prices.countryId = ${countryId}`);
    if (pagination && isEmpty(productIds) && isEmpty(variantIds))
      qb.take(pagination.limit).skip(pagination.skip);

    if (!isEmpty(fields)) {
      qb.select(`var.id`);
      for (const field of fields) {
        const columns = qb.expressionMap.mainAlias.metadata.columns;
        const hasKey = columns.find(col => col.propertyName === field);
        if (hasKey) {
          qb.addSelect(`${qb.expressionMap.mainAlias.name}.${hasKey.propertyAliasName}`);
        } else {
          const [alias, key] = field.split('.');
          if (alias && key) {
            const selectAlias = qb.expressionMap.aliases.find(it => it.name === alias);
            const selectAliasColumns = selectAlias?.metadata?.columns;
            const selectKey = selectAliasColumns?.find(col => col.propertyName === key);
            if (selectKey) {
              qb.addSelect(`${selectAlias.name}.${selectKey.propertyAliasName}`);
            }
          }
        }
      }

      return qb.getManyAndCount();
    }

    // const subQuery = this.siRepository
    //   .createQueryBuilder('sub')
    //   .select(['sub.product_id as pid', 'SUM(sub.inventory) as quantity'])
    //   .groupBy('sub.id');

    return (
      qb
        .leftJoinAndSelect('var.properties', 'properties')
        .leftJoinAndSelect('properties.attributes', 'attributes')
        .leftJoinAndSelect('product.combo', 'combo', 'combo.status <> -1')
        .leftJoin('combo.variant', 'combo_variant')
        .addSelect(['combo_variant.id', 'combo_variant.sku'])
        // .leftJoin('(' + subQuery.getQuery() + ')', 'inv', '"var".id = "inv".pid')
        // .addSelect('CASE WHEN inv.quantity > 0 THEN inv.quantity ELSE 0 END as quantity')
        .orderBy('var.createdAt', 'DESC')
        .getManyAndCount()
    );
  }

  async fetchStock(request, pagination: PaginationOptions, query: StockMovesFilter): Promise<any> {
    const { sku, variantIds } = query;

    const sql = this.siRepository
      .createQueryBuilder('sml')
      .select([
        'warehouse.name as whname',
        'product.name as name',
        'variant.name as variantName',
        'variant.sku as sku',
        'sml.product_id',
        'SUM(sml.total) as total',
        'SUM(sml.inventory) as inventory',
        'SUM(sml.qty) as qty',
      ])
      .skip(pagination?.skip)
      .take(pagination?.limit)
      .where('sml.status = :stt', { stt: STATUS.active })
      .leftJoin('sml.product', 'variant')
      .leftJoin('sml.warehouse', 'warehouse')
      .leftJoin('variant.product', 'product')
      .groupBy('(warehouse.id,sml.product_id,variant.name,variant.sku,product.name)');
    console.log(variantIds, sku);
    if (!!variantIds && variantIds.length > 0)
      sql.andWhere('sml.product_id in (:...ids)', {
        ids: variantIds.split(',').map(item => {
          return item;
        }),
      });
    if (!!sku && sku.length > 0)
      sql.andWhere('variant.sku in (:...sku)', {
        sku: sku.split(',').map(item => {
          return item;
        }),
      });

    const data = await sql.getRawMany();

    return data;
  }

  async findOne(id: number, companyId?: number): Promise<Product> {
    const qb = this.productsRepo
      .createQueryBuilder('p')
      .where(`p.status <> ${STATUS.delete}`)
      .andWhere('p.id = :id', { id });

    qb.leftJoinAndSelect('p.variations', 'v', `v.status != ${STATUS.delete}`)
      .leftJoinAndSelect('v.properties', 'properties')
      .leftJoinAndSelect('v.prices', 'prices', `prices.status = ${STATUS.active}`)
      .leftJoinAndSelect('p.supplier', 's')
      .leftJoinAndSelect('p.categories', 'c', `c.status = ${STATUS.active}`);

    const product = await qb.getOne();
    if (!isNil(companyId) && !isNil(product) && product.companyId !== companyId)
      throw new ForbiddenException();
    return product;
  }

  async findCombo(id: number, companyId: number): Promise<Product> {
    const qb = this.productsRepo
      .createQueryBuilder('p')
      .leftJoinAndSelect('p.variations', 'v', `v.status <> ${STATUS.delete}`)
      .leftJoinAndSelect('v.properties', 'props')
      .leftJoinAndSelect('v.prices', 'prices', `prices.status = ${STATUS.active}`)
      .leftJoinAndSelect('p.supplier', 's')
      .leftJoinAndSelect('p.categories', 'c', `c.status = ${STATUS.active}`)
      .leftJoinAndSelect('p.combo', 'combo', `combo.status <> ${STATUS.delete}`)
      .leftJoinAndSelect('combo.variant', 'cv')
      .leftJoin('cv.properties', 'cvProps')
      .addSelect(['cvProps.id', 'cvProps.name', 'cvProps.status'])
      .leftJoinAndSelect('cv.product', 'cvProduct')
      .leftJoin('cvProps.attributes', 'cvPropAbs')
      .addSelect(['cvPropAbs.id', 'cvPropAbs.name', 'cvPropAbs.status'])
      .where(`p.status <> ${STATUS.delete}`)
      .andWhere('p.id = :id')
      .setParameters({ id });

    // const product = await this.productsRepo.findOne(id, {
    //   where: qb => {
    //     qb.where({
    //       status: STATUS.active,
    //     })
    //       .andWhere('Product__variations.status = :stt', { stt: STATUS.active })
    //       .andWhere('Product__categories.status = :stt', { stt: STATUS.active })
    //       .andWhere('Product__combo.status != :sttDelete', {
    //         sttDelete: STATUS.delete,
    //       })
    //       .andWhere('Product__variations__prices.status = :stt', {
    //         stt: STATUS.active,
    //       });
    //     if (companyId) qb.andWhere({ companyId });
    //     if (!!header['project-ids'])
    //       qb.andWhere({
    //         projectId: In(
    //           header['project-ids'].includes(',')
    //             ? header['project-ids'].split(',').map(item => {
    //                 return Number(item);
    //               })
    //             : [Number(header['project-ids'])],
    //         ),
    //       });
    //   },
    //   relations: [
    //     'variations',
    //     'supplier',
    //     'categories',
    //     'variations.properties',
    //     'variations.prices',
    //     'combo',
    //     'combo.variant',
    //     'combo.variant.properties',
    //     'combo.variant.product',
    //   ],
    // });
    const product = await qb.getOne();
    if (!product) return product;
    if (!isNil(companyId) && !isNil(product) && product.companyId !== companyId)
      throw new ForbiddenException();

    return product;
  }

  async findAttributes(id): Promise<Attribute[]> {
    const pp = await this.avRepository
      .createQueryBuilder('av')
      .select('av.attributes_id', 'id')
      .leftJoin('av.variations', 'v')
      .where('v.productId = :productId', { productId: id })
      .getRawMany();

    if (!pp || pp.length < 1) return [];

    return await this.attrRepository
      .createQueryBuilder('a')
      .leftJoinAndSelect('a.properties', 'p')
      .where('a.status = :stt', { stt: STATUS.active })
      .andWhere('p.status = :stt', { stt: STATUS.active })
      .andWhere('a.id IN (:...attrIds)', {
        attrIds: pp.map(item => {
          return item?.id;
        }),
      })
      .getMany();
  }

  async create(
    data: CreateProductDto | ProductComboDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ): Promise<Product> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException("User doesn't belong to any companies");
    console.log(`data`, data.categories);
    const prod = new Product();
    prod.companyId = companyId;
    prod.name = data?.name;
    prod.sku = data?.sku;
    prod.description = data?.description;
    if (data?.supplierId) prod.supplierId = data?.supplierId;
    prod.projectId = data?.projectId;
    prod.status = STATUS.active;
    prod.categories = data?.categories;
    prod.isCombo = data?.isCombo ? true : false;
    if (data?.isCombo && data?.productCombo && data?.productCombo.length > 0)
      prod.combo = data?.productCombo.map(item => {
        return {
          ...item,
          status: STATUS.active,
        };
      });

    const params = [];
    data?.variants.forEach(e => {
      const variant = new ProductVariation();
      variant.projectId = prod.projectId;
      variant.companyId = prod.companyId;
      variant.product = prod;
      variant.status = !!e?.status ? e?.status : STATUS.active;

      if (e?.sku) {
        variant.sku = e?.sku;
        variant.barcode = e?.barcode;
        variant.name = e?.name;
        variant.image = e?.image;
        // variant.price = e?.price ?? 0;
        variant.qty = e?.qty ?? 0;
        variant.priceMarket = e?.priceMarket;
        variant.weight = e?.weight;
        variant.properties = e?.properties;
        variant.prices = e?.prices;
        params.push(variant);
      }
    });

    if (params.length <= 0) throw new BadRequestException('Biến thể của sản phẩm không để trống');
    try {
      const res = await this.variantRepo.save(params);
      console.log(`save variants response`, res);
      if (!!prod?.id) await this.syncPancake(prod?.id);
      return prod;
    } catch (err) {
      console.log(`save variants errors`, err);
      if (err?.driverError) {
        if (Number(err?.driverError?.code) === 23505) {
          // Split the string by parentheses and get the second part (which contains the SKU)
          const parts = err?.driverError.detail.split('(');
          const skuPart = parts[2];
          const sku = skuPart.split(',')[0].trim();
          throw new UnprocessableEntityException({
            code: ErrorCode.PRD_0001,
            message: `SKU đã tồn tại`,
            sku,
          });
        }
        throw new BadRequestException(err?.driverError?.detail);
      }
      throw new BadRequestException(err.detail);
    }
  }

  parserError(error: string) {
    if (startsWith(error, 'Key (sku)')) {
      const mess = error
        .split('=')?.[1]
        ?.split(' ')?.[0]
        ?.replace(/([()])/g, '');
      if (mess) return `Mã ${mess} đã tồn tại trên hệ thống`;
      else return error;
    } else return error;
  }

  async updateProject(data: SetProjectOnProductsDto): Promise<Product[]> {
    await this.productsRepo.update({ projectId: data?.projectId }, { projectId: null });

    await this.productsRepo.update({ id: In(data?.productIds) }, { projectId: data?.projectId });

    return this.productsRepo.find({
      where: qb => {
        qb.where({
          id: In(data?.productIds),
        });
      },
    });
  }

  async updateStatus(
    id: number,
    data: UpdateProductStatusDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const connection = getConnection(catalogConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const prod = await this.productsRepo.findOne(id);
      if (!prod) throw new BadRequestException('Product not found');
      prod.status = data?.status;
      await queryRunner.manager.getRepository(Product).save(prod);
      if (prod.isCombo) {
        await queryRunner.manager
          .getRepository(ProductComboVariant)
          .createQueryBuilder()
          .update()
          .set({ status: data?.status })
          .where('id_product = :id', { id })
          .execute();
      } else {
        const variants = await queryRunner.manager
          .getRepository(ProductVariation)
          .createQueryBuilder('pv')
          .where('id_product = :id', { id })
          .getMany();
        const vIds = variants.map(v => v.id);
        if (vIds.length > 0) {
          await queryRunner.manager
            .getRepository(ProductComboVariant)
            .createQueryBuilder()
            .update()
            .set({ status: data?.status })
            .where('id_product_variation IN (:...ids)', { ids: vIds })
            .execute();
        }
        await queryRunner.manager
          .getRepository(ProductVariation)
          .createQueryBuilder()
          .update()
          .set({ status: data?.status })
          .where('id_product = :id', { id })
          .execute();
      }
      await queryRunner.commitTransaction();
      return prod;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async update(
    id: number,
    data: UpdateProductDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ): Promise<Product> {
    const prod = await this.productsRepo.findOne(id, {
      relations: ['variations', 'variations.prices'],
    });
    if (data?.name) prod.name = data?.name;
    if (data?.status) prod.status = data?.status;
    if (data?.description) prod.description = data?.description;
    if (data?.supplierId) prod.supplierId = data?.supplierId;
    if (data?.projectId) prod.projectId = data?.projectId;
    if (data?.categories) prod.categories = data?.categories;
    if (prod.isCombo && data?.productCombo && data?.productCombo.length > 0)
      prod.combo = data?.productCombo;
    const params = [];
    const variantPriceChanges = [];
    const vChangeStatus: Record<string, number> = {};
    data?.variants.forEach(e => {
      const variant = new ProductVariation();
      variant.projectId = prod.projectId;
      variant.companyId = prod.companyId;
      variant.product = prod;
      variant.status = !!e?.status ? e?.status : STATUS.active;
      if (e?.id) {
        variant.id = e?.id;
        const vrt = find(prod?.variations, function(o) {
          return o?.id == e?.id;
        });

        if (vrt.status !== e?.status) {
          vChangeStatus[e?.id] = e?.status;
        }

        let prices = vrt.prices.map(item => {
          const indexPrice = findIndex(e.prices, function(o) {
            return o?.countryId == item?.countryId;
          });
          if (indexPrice > -1) {
            if (item.price !== e.prices[indexPrice].price) {
              variantPriceChanges.push({
                variantId: vrt.id,
                countryId: item?.countryId,
                price: e.prices[indexPrice].price,
                productId: id,
              });
            }
            item.price = e.prices[indexPrice].price;
            item.status = STATUS.active;
            e.prices.splice(indexPrice, 1);
          } else {
            item.status = STATUS.deactivate;
          }
          return item;
        });
        prices = concat(prices, e.prices);
        variant.prices = prices;
      } else {
        variant.prices = e?.prices;
      }
      if (e?.sku) {
        if (!e?.id) variant.sku = e?.sku;
        variant.barcode = e?.barcode;
        variant.image = e?.image;
        variant.name = e?.name;
        // variant.price = e?.price ?? 0;
        variant.qty = e?.qty ?? 0;
        variant.priceMarket = e?.priceMarket;
        variant.weight = e?.weight;
        variant.properties = e?.properties.map(i => {
          return {
            id: i?.id,
          };
        });

        params.push(variant);
      }
    });
    if (params.length <= 0) throw new BadRequestException('Biến thể của sản phẩm không để trống');

    await this.variantRepo.save(params).catch(err => {
      if (Number(err?.driverError?.code) === 23505) {
        throw new UnprocessableEntityException({
          code: ErrorCode.PRD_0001,
          message: `Variant SKU đã tồn tại`,
        });
      }
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });
    for (const key in vChangeStatus) {
      await this.productComboVariantsRepo.update(
        { variantId: Number(key) },
        { status: vChangeStatus[key] },
      );
    }
    await this.syncPancake(id);
    if (variantPriceChanges.length > 0) {
      await this.syncVariantPricesToShopify(variantPriceChanges);
    }
    console.log(`variantPriceChanges`, variantPriceChanges);
    return prod;
  }

  async syncVariantPricesToShopify(variantPrices) {
    return this.amqpConnection.publish(
      'catalog-service-products',
      'sync-variant-price-to-shopify',
      {
        data: variantPrices,
      },
    );
  }

  async updateCombo(data: ProductComboDto, id: number): Promise<Product> {
    const prod = await this.productsRepo.findOne(id, {
      relations: [
        'variations',
        'variations.properties',
        'variations.prices',
        'combo',
        'combo.variant',
      ],
    });
    if (data?.name) prod.name = data?.name;
    if (data?.status) prod.status = data?.status;
    // if(data?.sku) prod.sku = data?.sku;
    if (data?.description) prod.description = data?.description;
    if (data?.supplierId) prod.supplierId = data?.supplierId;
    if (data?.projectId) prod.projectId = data?.projectId;
    if (data?.categories) prod.categories = data?.categories;
    if (prod?.variations) {
      let prices = prod?.variations[0].prices.map(item => {
        const indexPrice = findIndex(data?.variants[0]?.prices, function(o) {
          return o?.countryId == item?.countryId;
        });
        if (indexPrice > -1) {
          item.price = data?.variants[0]?.prices[indexPrice].price;
          item.status = STATUS.active;
          data?.variants[0]?.prices.splice(indexPrice, 1);
        } else {
          item.status = STATUS.deactivate;
        }
        return item;
      });
      prices = concat(prices, data?.variants[0]?.prices);

      prod.variations[0] = {
        ...prod?.variations[0],
        price: data?.variants[0]?.price ?? 0,
        prices: prices,
        status: STATUS.active,
        ...(data?.variants[0]?.weight && { weight: data.variants[0].weight }),
      } as ProductVariation;

      await this.variantRepo.save(prod.variations[0]);
      await this.productsRepo.save(
        plainToInstance(Product, { ...omit(prod, ['variations', 'combo']) }),
      );
      if (prod.isCombo && data?.productCombo && data?.productCombo.length > 0) {
        const params = [];
        data?.productCombo.forEach(e => {
          const pcv = new ProductComboVariant();
          pcv.productId = id;
          pcv.qty = e?.qty;
          pcv.price = e?.price ?? 0;
          pcv.status = e?.status ?? STATUS.active;
          pcv.variantId = e?.variant?.id;
          params.push(pcv);
        });
        await this.productComboVariantsRepo.save(params).catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });
      }
    }
    this.syncPancake(id);
    return prod;
  }

  async delete(id): Promise<Product> {
    const data = await this.productsRepo.findOne({
      where: qb => {
        qb.where({
          id,
        });
      },
      relations: ['variations'],
    });
    if (!data) throw new BadRequestException('Không tìm thấy sản phẩm này');
    if (data?.isCombo == false) {
      const valid = await this.odpRepository.findOne({
        where: qb => {
          qb.where({
            productId: In(
              data?.variations.map(item => {
                return item?.id;
              }),
            ),
          });
          qb.andWhere('OrderProduct__order.status >= :stt', {
            stt: OrderStatus.Confirmed,
          });
        },
        relations: ['order'],
      });
      if (!!valid)
        throw new BadRequestException(
          'Bạn không thể xóa sản phẩm này vì đã được tạo trong đơn hàng',
        );
    }
    if (!!data) data.status = STATUS.deactivate;

    await this.productsRepo.save(data);
    this.syncPancake(data?.id);
    return data;
  }

  async removeVariant(id): Promise<ProductVariation> {
    const data = await this.variantRepo.findOne(
      { id },
      {
        relations: ['prices'],
      },
    );
    console.log('data', data);
    if (!data) throw new BadRequestException('Không tìm thấy sản phẩm này');
    data.status = STATUS.delete;
    // let valid = await this.odpRepository.findOne({
    //   where: qb => {
    //     qb.where({
    //       productId: id
    //     });
    //     // qb.andWhere('OrderProduct__order.status >= :stt', { stt: OrderStatus.Confirmed });
    //   },
    //   // relations: [
    //   //   'order'
    //   // ]
    // });
    // console.log('valid',valid);
    // throw new BadRequestException('Bạn không thể xóa sản phẩm này vì đã được tạo trong đơn hàng');
    await this.variantRepo.save(data);

    // this.syncPancake(data?.productId);
    if (data?.prices) {
      let countryIds = data?.prices.map(e => {
        return e?.countryId;
      });
      countryIds = uniq(countryIds);
      countryIds.forEach(countryId => {
        this.amqpConnection.publish(
          'CatalogService.Product.SyncPancake',
          'remove-variant-product',
          {
            variantId: id,
            productId: data?.productId,
            countryId,
          },
        );
      });
    }
    return data;
  }

  async deleteProductCombo(data): Promise<ProductComboVariant> {
    const prod = await this.productComboVariantsRepo.findOne({
      where: qb => {
        qb.where({
          variantId: data?.variantId,
          productId: data?.productId,
        });
      },
    });
    if (!prod) throw new BadRequestException('Không tìm thấy sản phẩm này trong combo');
    prod.status = STATUS.delete;

    const product = await this.productsRepo.findOne({
      where: qb => {
        qb.where({
          id: data?.productId,
        });
      },
      relations: ['variations'],
    });
    if (!product) throw new BadRequestException('Không tìm thấy sản phẩm này trong combo');

    // const valid = await this.odpRepository.findOne({
    //   where: qb => {
    //     qb.where({
    //       productId: In(
    //         product?.variations.map(item => {
    //           return item?.id;
    //         }),
    //       ),
    //     });
    //     qb.andWhere('OrderProduct__order.status >= :stt', {
    //       stt: OrderStatus.Confirmed,
    //     });
    //   },
    //   relations: ['order'],
    // });
    // if (!!valid)
    //   throw new BadRequestException('Bạn không thể xóa sản phẩm này vì đã được tạo trong đơn hàng');

    await this.productComboVariantsRepo.save(prod);
    await this.amqpConnection.publish('CatalogService.Product.SyncPancake', 'product-item-combo', {
      variantId: data?.variantId,
      productId: data?.productId,
      remove: true,
    });
    return prod;
  }

  async bulkCreateProductVariations(
    { lines: data }: BulkCreateProductsDto,
    companyId: number,
    creatorId?: number,
  ) {
    const projectNames = data.map(it => it.project);
    const payload = { names: projectNames, companyId };
    const { data: projects } = await this.amqpConnection.request({
      exchange: 'identity-service-projects',
      routingKey: 'get-project-by-short-names',
      payload,
      timeout: 5000,
    });

    const projectsLookup = (projects as Project[]).reduce((prev, p) => {
      prev[p.shortName] = p;
      return prev;
    }, []);

    const variants = [];

    for (const line of data) {
      const project = projectsLookup[line.project];

      const variant = plainToInstance(ProductVariation, {
        name: line.variantName,
        sku: line.variantSku,
        weight: line.variantWeight || 0,
        projectId: project?.id,
        companyId,
        status: STATUS.active,
        priceMarket: 0,
        qty: 0,
      });

      variant.properties = line.variantProperties.split(';').map((it: string) => {
        const [attName, attValue] = it.split(':');
        const attribute = plainToInstance(Attribute, {
          name: attName?.trim(),
          companyId,
          status: STATUS.active,
        });

        const attVal = plainToInstance(AttributeValue, {
          name: attValue?.trim(),
          attributes: attribute,
          status: STATUS.active,
        });

        return attVal;
      });

      variant.prices = line.variantPrices
        .split(';')
        .map((it: string) => {
          const [countryCode, countryPrice] = it.split(':');
          const country = find(countries, item => item.code === countryCode.trim());
          if (!country) return;

          const price = new ProductVariantPrice();
          price.countryId = Number(country.dial_code?.replace('\\D', ''));
          price.price = Number(countryPrice.trim());

          return price;
        })
        .filter(identity);

      variant.product = plainToInstance(Product, {
        name: line.productName,
        sku: line.productSku,
        projectId: project?.id,
        companyId,
        status: STATUS.active,
      });

      if (!isEmpty(line.category.trim())) {
        variant.product.categories = [
          { name: line.category.trim(), companyId, status: STATUS.active },
        ];
      }

      variants.push(variant);
    }

    const result = { successLines: [], errorLines: [] };

    const chunkSize = 2;
    const chunks = chunk(variants, chunkSize);

    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
      const chunkItem = chunks[chunkIndex];
      await Promise.all(
        chunkItem.map(async (variant, idx) => {
          const lineIndex = chunkIndex * chunkSize + idx;
          try {
            const record = await this.variantsService.upsertSingleVariant(variant);
            result.successLines.push({ index: lineIndex, data: record.product.id });
          } catch (error) {
            result.errorLines.push({
              index: data[lineIndex].lineIndex || lineIndex,
              data: error?.driverError?.detail || error.response,
            });
          }
        }),
      );
    }

    return result;
  }

  async importExcel(buffer: Buffer, companyId: number, creatorId?: number) {
    const data = ExcelUtils.read(buffer, 0);

    const colNames = {
      index: 'No.',
      name: 'Product Name *',
      sku: 'Product ID *',
      project: 'Project Short Name *',
      category: 'Category *',
      variantName: 'Variant Name',
      variantSku: 'Variant SKU *',
      variantProperties: 'Variant Properties *',
      variantWeight: 'Variant Weight *',
      variantPrices: 'Variant Prices *',
    };
    return this.bulkCreateProductVariations(
      {
        lines: data.map(line =>
          plainToInstance(BulkCreateProductLine, {
            productName: line[colNames.name],
            productSku: line[colNames.sku],
            project: line[colNames.project],
            category: line[colNames.category],
            variantName: line[colNames.variantName],
            variantSku: line[colNames.variantSku],
            variantProperties: line[colNames.variantProperties],
            variantWeight: line[colNames.variantWeight],
            variantPrices: line[colNames.variantPrices],
          }),
        ),
      },
      companyId,
      creatorId,
    );
  }

  @RabbitRPC({
    exchange: 'CatalogService.Product.SyncPancake',
    routingKey: 'remove-variant-product',
    queue: 'ag-sync-remove-variant-product-pancake',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async syncRemoveVariantProductPancake(payload) {
    console.log('payload-remove-variant', payload);
    const { productId, variantId, countryId } = payload;
    const country = await this.ctRepository.findOne({ id: countryId });

    const product = await this.productsRepo.findOne(productId, {
      where: qb => {
        qb.where('Product__variations.id = :id', { id: variantId });
      },
      relations: [
        'variations',
        'variations.properties',
        'variations.properties.attributes',
        'variations.properties.attributes.properties',
        'variations.productSync',
        'variations.prices',
        'productSync',
      ],
    });

    if (!product) return new Nack(false);
    this.syncProductPfg(product, country);

    if (!country || !country?.pancakeShopId || !country?.pancakeApiKey) {
      console.log('Không tìm thấy country', payload);
      return new Nack(false);
    }

    const data = new ProductSyncDto();
    data.name = product?.name;
    data.weight = 0;
    data.display_id = product?.id;
    data.custom_id = product?.id.toString();
    data.product_attributes = [];

    const productIdPc = product?.productSync[0]?.productPcId;

    data.variations = [];
    let valid = false;
    product?.variations.forEach(item => {
      const variant = new VariantSyncDto();
      variant.is_locked = item?.status == STATUS.deactivate ? true : false;
      variant.is_removed = item?.status == STATUS.delete ? true : false;
      if (item?.productSync.length > 0) {
        const variantItem = find(item?.productSync, function(o) {
          return (
            o?.countryId == country?.id &&
            o?.productId == product?.id &&
            o?.variantId == item?.id &&
            o?.type == TypeSyncProduct.pancake &&
            o?.productPcId == productIdPc
          );
        });
        if (!!variantItem?.variantPcId) {
          variant.id = variantItem?.variantPcId;
        } else {
          valid = true;
        }
      }

      if (!!item?.properties)
        item?.properties.forEach(el => {
          data.product_attributes?.push({
            name: el?.attributes?.name,
            values: el?.attributes?.properties.map(e => {
              return e?.name;
            }),
          });

          variant.fields?.push({
            name: el?.attributes?.name,
            value: el?.name,
          });
        });
      if (item?.status == STATUS.delete) data.variations?.push(variant);
    });
    if (valid) return new Nack(false);
    data.product_attributes = uniqWith(data.product_attributes, isEqual);

    await axios({
      method: 'put',
      url:
        process.env.PANCAKE_URL_API +
        '/shops/' +
        country?.pancakeShopId +
        '/products/' +
        productIdPc +
        '?api_key=' +
        country?.pancakeApiKey,
      headers: {
        'Content-Type': 'application/json',
      },
      data: JSON.stringify({ product: data }),
    })
      .then(function(response) {
        console.log('data', response?.data);
      })
      .catch(function(error) {
        console.log(error);
      });
    return new Nack(false);
  }

  // Đồng bộ variant trong sản phẩm combo
  @RabbitRPC({
    exchange: 'CatalogService.Product.SyncPancake',
    routingKey: 'product-item-combo',
    queue: 'ag-sync-product-combo-pancake',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async syncProductItemComboPancake(payload) {
    console.log('payload-combo', payload);
    const { productId, variantId, countryId } = payload;
    if (!productId || !variantId || !countryId) return new Nack(false);

    const country = await this.ctRepository.findOne({ id: countryId });
    if (!country || !country?.pancakeShopId || !country?.pancakeApiKey) {
      console.log('Không tìm thấy country', payload);
      return new Nack(false);
    }

    const prod = await this.productComboVariantsRepo.findOne({
      where: qb => {
        qb.where({
          variantId,
          productId,
        });
      },
      relations: ['product', 'product.productSync', 'variant', 'variant.productSync'],
    });
    const productSync = find(prod?.product?.productSync, function(o) {
      return o?.countryId == country?.id && o?.type == TypeSyncProduct.pancake;
    });

    const productIdPc = productSync?.productPcId;

    const variantPancakeCombo = find(prod?.variant?.productSync, function(o) {
      return (
        o?.countryId == country?.id &&
        o?.productId == productId &&
        o?.type == TypeSyncProduct.pancake &&
        o?.variantId == variantId
      );
    });
    const variantPancakeOrigin = find(prod?.variant?.productSync, function(o) {
      return (
        o?.countryId == country?.id &&
        o?.productId == prod?.variant?.productId &&
        o?.type == TypeSyncProduct.pancake &&
        o?.variantId == variantId
      );
    });

    // console.log('params',prod,productIdPc,variantPancake);

    if (!productIdPc || !variantPancakeOrigin) return new Nack(false);

    const params = new VariantComboSyncDto();
    params.combo_product_id = productIdPc;
    params.count = prod?.qty;
    params.product_id = variantPancakeOrigin?.productPcId;
    params.variation_id = variantPancakeOrigin?.variantPcId;

    let paramSync = {};

    if (!!variantPancakeCombo) {
      paramSync = ApiPancakeUtils.getParams(
        prod?.status == STATUS.delete ? 'delete' : 'put',
        'PRODUCT_COMBO_VARIANT_EDIT',
        prod?.status == STATUS.delete ? {} : JSON.stringify({ combo_product_variation: params }),
        null,
        null,
        country?.pancakeShopId,
        country?.pancakeApiKey,
      );
    } else {
      paramSync = ApiPancakeUtils.getParams(
        'post',
        'PRODUCT_COMBO_VARIANT',
        JSON.stringify({ combo_product_variation: params }),
        null,
        null,
        country?.pancakeShopId,
        country?.pancakeApiKey,
      );
    }
    console.log('paramSync', paramSync);
    const response = await ApiPancakeUtils.getApi(paramSync);
    if (response?.data?.success) {
      if (prod?.status == STATUS.delete && !!variantPancakeCombo) {
        await this.pspRepository.delete({
          countryId: country?.id,
          productId,
          productPcId: productIdPc,
          variantId,
          variantPcId: variantPancakeCombo.variantPcId,
          type: TypeSyncProduct.pancake,
        });
      } else if (!variantPancakeCombo) {
        const psp = new ProductSyncPancake();
        psp.type = TypeSyncProduct.pancake;
        psp.productId = productId;
        psp.productPcId = productIdPc;
        psp.variantId = variantId;
        psp.countryId = country?.id;
        psp.variantPcId = response?.data?.data?.id;

        const res = this.pspRepository.save(psp).catch(err => {
          return false;
        });
        if (!res) return new Nack(false);
      }
    }

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'catalog-service-products',
    routingKey: 'find-products-by-ids',
    queue: 'catalog-find-products-by-ids',
    errorHandler: rmqErrorsHandler,
  })
  async findByIds({ ids }) {
    return await this.productsRepo.findByIds(ids);
  }

  // B1. Gửi lên id product mỗi khi tạo hay update để đồng bộ. Tách theo quốc gia để đồng bộ
  @RabbitRPC({
    exchange: 'CatalogService.Product.SyncPancake',
    routingKey: 'product',
    queue: 'ag-sync-product-single-pancake',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async syncProductPancake(payload) {
    console.log('payload-product', payload);

    if (!payload?.productId) return new Nack(false);
    const { productId } = payload;
    const product = await this.productsRepo.findOne(productId, {
      relations: [
        'variations',
        'variations.properties',
        'variations.prices',
        'variations.properties.attributes',
        'variations.properties.attributes.properties',
        'variations.productSync',
        'productSync',
      ],
    });
    let countryIds = [];
    product?.variations.forEach(e => {
      e?.prices.forEach(element => {
        countryIds.push(element?.countryId);
      });
    });

    countryIds = uniq(countryIds);
    if (countryIds.length > 0)
      countryIds.forEach(countryId => {
        this.amqpConnection.publish('CatalogService.Product.SyncPancake', 'product-country', {
          product,
          countryId,
        });
      });
    return new Nack(false);
  }

  // B2. Đồng bộ sản phẩm theo quốc gia => pancake là từng pos tương ứng
  @RabbitRPC({
    exchange: 'CatalogService.Product.SyncPancake',
    routingKey: 'product-country',
    queue: 'ag-sync-product-country-pancake',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async productByCountryPancake(payload) {
    console.log('payload-product-sync-by-country', payload);

    const { product, countryId } = payload ?? {};
    if (!product || !countryId) return new Nack(false);
    const country = await this.ctRepository.findOne({ id: countryId });
    if (!country || !country?.pancakeShopId || !country?.pancakeApiKey) {
      console.log('Không tìm thấy country', payload);
      return new Nack(false);
    }

    if (product?.isCombo) {
      await this.syncPancakeProductCombo(product, country);
    } else {
      await this.syncPancakeProductSingle(product, country);
      if (country?.isSyncPfg) await this.syncProductPfg(product, country);
    }
    return new Nack(false);
  }

  async syncPancakeProductCombo(product, country) {
    product = await this.productsRepo.findOne(product?.id, {
      relations: ['variations', 'variations.prices', 'productSync', 'combo', 'combo.variant'],
    });

    const price = find(product?.variations[0]?.prices, function(o) {
      return o.countryId == country?.id;
    });

    const data = new ProductComboSyncDto();
    data.name = product?.name;
    data.is_value_combo = true;
    data.is_variation = true;
    data.custom_id = product?.id.toString();
    data.shop_id = process.env.PANCAKE_SHOP_ID;
    data.cal_value_variation = price?.price ?? 0;
    data.value_combo = price?.price ?? 0;
    const pspRepository = this.pspRepository;
    let productIdPc = find(product?.productSync, function(o) {
      return o.countryId == country?.id && o?.type == TypeSyncProduct.pancake;
    });

    productIdPc = productIdPc?.productPcId;

    let paramSync = {};
    if (!productIdPc || (!!productIdPc && product?.status == STATUS.delete)) {
      paramSync = ApiPancakeUtils.getParams(
        'post',
        product?.status == STATUS.delete ? 'PRODUCT_COMBO_DELETE' : 'PRODUCT_COMBO',
        product?.status == STATUS.delete
          ? {
              combo_ids: {
                '0': productIdPc,
              },
            }
          : JSON.stringify({ combo_product: data }),
        null,
        null,
        country?.pancakeShopId,
        country?.pancakeApiKey,
      );
    } else {
      paramSync = ApiPancakeUtils.getParams(
        'put',
        'PRODUCT_COMBO',
        JSON.stringify({ combo_product: data }),
        null,
        null,
        country?.pancakeShopId,
        country?.pancakeApiKey,
      );
    }
    console.log('paramSync', paramSync);

    const response = await ApiPancakeUtils.getApi(paramSync);

    if (response?.data?.success) {
      if (!productIdPc) {
        const psp = new ProductSyncPancake();
        psp.productId = product?.id;
        psp.countryId = country?.id;
        psp.type = TypeSyncProduct.pancake;
        psp.productPcId = response?.data?.data?.id;
        psp.variantId = product?.variations[0]?.id;
        psp.variantPcId = response?.data?.data?.id;
        const res = pspRepository.save(psp).catch(err => {
          return false;
        });
        if (!res) return new Nack(false);
      }

      await Promise.all(
        product?.combo?.map(async e => {
          if (e?.status != STATUS.delete)
            this.amqpConnection.publish(
              'CatalogService.Product.SyncPancake',
              'product-item-combo',
              {
                variantId: e?.variantId,
                productId: e?.productId,
                countryId: country?.id,
              },
            );
        }),
      );
    }

    return new Nack(false);
  }

  async syncProductPfg(product, country) {
    const data = new ProductSyncPfgDto();
    data.name = product?.name;
    data.country_code = country?.id;
    data.custom_id = product?.id.toString();
    data.weight = product?.weight ?? 0;
    data.product_attributes = [];
    data.user_id = process.env.PFG_UID;
    data.variations = [];

    let productIdPc = find(product?.productSync, function(o) {
      return o?.countryId == country?.id && o?.type == TypeSyncProduct.pfg;
    });
    productIdPc = productIdPc?.productPcId;

    product?.variations.forEach(item => {
      const price = find(item?.prices, function(o) {
        return o.countryId == country?.id;
      });

      const variant = new VariantSyncPfgDto();
      variant.barcode = item?.barcode;
      variant.import_price = price?.price ?? 0;
      variant.retail_price = price?.price ?? 0;
      variant.weight = item?.weight ?? 0;
      variant.total_quantity = 0;
      variant.remain_quantity = 0;
      variant.custom_id = item?.id.toString();
      variant.fields = [];

      if (item?.productSync.length > 0) {
        const variantItem = find(item?.productSync, function(o) {
          return (
            o?.countryId == country?.id &&
            o?.productId == product?.id &&
            o?.variantId == item?.id &&
            o?.type == TypeSyncProduct.pfg &&
            o?.productPcId == productIdPc
          );
        });
        if (!!variantItem?.variantPcId) variant.id = variantItem?.variantPcId;
      }

      if (!!item?.properties)
        item?.properties.forEach(el => {
          data.product_attributes.push({
            name: encodeURI(el?.attributes?.name),
            values: el?.attributes?.properties.map(e => {
              return encodeURI(e?.name);
            }),
          });

          variant.fields.push({
            name: encodeURI(el?.attributes?.name),
            value: encodeURI(el?.name),
          });
        });

      if (item?.status != STATUS.delete && price?.status != STATUS.deactivate)
        data.variations.push(variant);
    });

    if (isEmpty(data.variations)) return new Nack(false);

    data.product_attributes = uniqWith(data.product_attributes, isEqual);

    let paramSync = {};
    if (!productIdPc) {
      paramSync = ApiPfgUtils.getParams(
        'post',
        'CREATE',
        Object.keys(data)
          .map(key => {
            if (!isArray(data[key])) {
              return `${key}=${encodeURI(data[key])}`;
            } else {
              return data[key]
                .map(key2 => {
                  return `${key}[]=${JSON.stringify(key2)}`;
                })
                .join('&');
            }
          })
          .join('&'),
        null,
        null,
      );
    } else {
      paramSync = ApiPfgUtils.getParams(
        'put',
        'UPDATE',
        JSON.stringify(data),
        productIdPc,
        country?.id,
      );
    }

    const result = await ApiPfgUtils.getApi(paramSync);
    const pspRepository = this.pspRepository;

    if (result?.data?.success && !productIdPc) {
      const productPancake = result?.data?.product;
      const params = [];
      productPancake?.variations.forEach(item => {
        const variant = find(product?.variations, function(o) {
          return o?.id == item?.custom_id;
        });
        if (!!variant) {
          const psp = new ProductSyncPancake();
          psp.type = TypeSyncProduct.pfg;
          psp.productId = product?.id;
          psp.productPcId = productPancake?.id;
          psp.countryId = country?.id;
          psp.variantId = variant?.id;
          psp.variantPcId = item?.id;
          params.push(psp);
        }
      });
      if (params.length > 0) {
        const res = pspRepository.save(params, { chunk: 200 }).catch(err => {
          return false;
        });
      }
    }
    return new Nack(false);
  }

  async syncPancakeProductSingle(product, country) {
    const data = new ProductSyncDto();
    data.name = product?.name;
    data.weight = 0;
    data.display_id = product?.id;
    data.custom_id = product?.id.toString();
    data.note_product = product?.description;
    data.product_attributes = [];

    const remove = product?.status == STATUS.deactivate;

    let productIdPc = find(product?.productSync, function(o) {
      return o?.countryId == country?.id && o?.type == TypeSyncProduct.pancake;
    });

    productIdPc = productIdPc?.productPcId;

    data.variations = [];
    product?.variations.forEach(item => {
      const price = find(item?.prices, function(o) {
        return o.countryId == country?.id;
      });
      const variant = new VariantSyncDto();
      variant.barcode = item?.barcode;
      variant.display_id = item?.id;
      variant.images = [
        item?.image ?? 'https://statics.pancake.vn/user-content.pancake.vn/2021/8/5/fccd6.jpg',
      ];
      variant.weight = item?.weight;
      variant.retail_price = price?.price ?? 0;
      variant.fields = [];
      variant.is_locked =
        item?.status == STATUS.deactivate
          ? true
          : price?.status == STATUS.deactivate
          ? true
          : false;
      variant.is_removed = item?.status == STATUS.delete ? true : false;
      if (item?.productSync.length > 0) {
        const variantItem = find(item?.productSync, function(o) {
          return (
            o?.countryId == country?.id &&
            o?.productId == product?.id &&
            o?.variantId == item?.id &&
            o?.type == TypeSyncProduct.pancake &&
            o?.productPcId == productIdPc
          );
        });
        if (!!variantItem?.variantPcId) variant.id = variantItem?.variantPcId;
      }

      if (!!item?.properties)
        item?.properties.forEach(el => {
          data.product_attributes.push({
            name: el?.attributes?.name,
            values: el?.attributes?.properties.map(e => {
              return e?.name;
            }),
          });

          variant.fields.push({
            name: el?.attributes?.name,
            value: el?.name,
          });
          console.log(variant.fields);
        });
      if (item?.status != STATUS.delete) data.variations.push(variant);
    });

    data.product_attributes = uniqWith(data.product_attributes, isEqual);

    const pspRepository = this.pspRepository;

    let paramSync = {};
    if (!productIdPc || (!!productIdPc && remove)) {
      paramSync = ApiPancakeUtils.getParams(
        'post',
        remove ? 'PRODUCT_DELETE' : 'PRODUCT',
        remove
          ? {
              product_ids: {
                '0': productIdPc,
              },
            }
          : JSON.stringify({ product: data }),
        null,
        null,
        country?.pancakeShopId,
        country?.pancakeApiKey,
      );
    } else {
      paramSync = ApiPancakeUtils.getParams(
        'put',
        'PRODUCT_EDIT',
        JSON.stringify({ product: data }),
        productIdPc,
        null,
        country?.pancakeShopId,
        country?.pancakeApiKey,
      );
    }
    const result = await ApiPancakeUtils.getApi(paramSync);

    console.log('pancake', result, product?.status);

    if (result?.data?.success && product?.status != STATUS.deactivate) {
      const productPancake = result?.data?.data;
      console.log('pancake', productPancake, product?.variations);
      const params = [];
      productPancake?.variations.forEach(item => {
        const variant = find(product?.variations, function(o) {
          return o?.id == item?.display_id;
        });
        if (!!variant) {
          const psp = new ProductSyncPancake();
          psp.type = TypeSyncProduct.pancake;
          psp.productId = product?.id;
          psp.productPcId = productPancake?.id;
          psp.countryId = country?.id;
          psp.variantId = variant?.id;
          psp.variantPcId = item?.id;
          params.push(psp);
        }
      });
      if (params.length > 0) {
        const res = pspRepository.save(params, { chunk: 200 }).catch(err => {
          return false;
        });
      }
    }
    return new Nack(false);
  }

  async syncPancake(id: number): Promise<void> {
    return this.amqpConnection.publish('CatalogService.Product.SyncPancake', 'product', {
      productId: id,
    });
  }

  async getProjectByIds(ids: number[], companyId: number) {
    if (ids.length === 0) return [];
    const payload = { ids, companyId };
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-projects',
      routingKey: 'get-project-by-ids',
      payload,
      timeout: 10000,
    });
    return data;
  }

  async getCountries() {
    const payload = {};
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-countries',
      routingKey: 'get-countries',
      payload,
      timeout: 10000,
    });
    return data;
  }

  _getPropertiesOfProduct(variations: ProductVariation[] | ProductComboVariant[]) {
    const proStr = reduce(
      variations,
      (prev, item: any) => {
        return prev.concat(
          item.properties.map((pr: any) => ({
            attributesId: pr.attributesId,
            value: `${pr?.attributes?.name}: ${pr.name} `,
          })),
        );
      },
      [],
    );
    const str = proStr.map(x => x.value).join(', ');
    return str;
  }

  async exportExcel(filter: ProductsFilter, countryId: number | string) {
    const qb = this.getProductsQuery(filter, countryId)
      .leftJoin('p.supplier', 'sup')
      .addSelect(['sup.id', 'sup.name'])
      .leftJoin('p.categories', 'c')
      .addSelect(['c.id', 'c.name'])
      .leftJoinAndSelect('p.variations', 'v')
      .leftJoinAndSelect('v.prices', 'prices', `prices.status = ${STATUS.active}`)
      .leftJoinAndSelect('v.properties', 'props')
      .leftJoinAndSelect('props.attributes', 'attr')
      .leftJoinAndSelect('combo.variant', 'combo_variant')
      .addSelect([
        'combo_variant.id',
        'combo_variant.sku',
        'combo_variant.name',
        'combo_variant.barcode',
        'combo_variant.weight',
        'combo_variant.qty',
      ])
      .leftJoin('combo_variant.prices', 'combo_variant_price')
      .addSelect(['combo_variant_price.price', 'combo_variant_price.countryId'])
      .leftJoin('combo_variant.properties', 'combo_variant_pro')
      .addSelect('combo_variant_pro.name')
      .leftJoin('combo_variant_pro.attributes', 'combo_variant_pro_attributes')
      .addSelect(['combo_variant_pro_attributes.name']);
    qb.orderBy('p.createdAt', 'DESC');
    const products = await qb.getMany();

    const projectIds = products.map(pr => pr.projectId);
    const projects = await this.getProjectByIds(projectIds, filter.companyId);
    const countries = products?.length > 0 ? await this.getCountries() : [];
    const columns = [
      'STT',
      'Loại sản phẩm',
      'SKU - Sản phẩm',
      'Tên sản phẩm',
      'Danh mục sản phẩm',
      'Nhà cung cấp',
      'Dự án',
      'Ghi chú',
      'Thuộc tính sản phẩm',
      'Số biến thể',
      'SKU - biến thể',
      'Mã vạch',
      'Tên biến thể',
      'Thuộc tính biến thể',
      'Trọng lượng (gram)',
      'Số lượng',
      'Quốc gia',
      'Giá bán theo quốc gia',
    ];

    const data: unknown[][] = [columns];

    for (let index = 0; index < products.length; index++) {
      const product = products[index];

      if (product?.isCombo) {
        const combo = product.combo;
        const comboRows = [...Array((combo.length || 1) + 1)].map(o =>
          Array(columns.length).fill(null),
        );
        try {
          comboRows[0][0] = index + 1;
          comboRows[0][1] = 'Combo';
          comboRows[0][2] = product.sku;
          comboRows[0][3] = product?.name;
          comboRows[0][4] = product?.categories?.[0]?.name;
          comboRows[0][5] = product.supplier?.name;
          comboRows[0][6] =
            find(projects, pr => pr.id === product.projectId)?.name ?? 'Không xác định';
          comboRows[0][7] = product.description;
          comboRows[0][8] = '';
          comboRows[0][9] = product?.combo?.filter(item => item.status === STATUS.active).length;
          comboRows[0][14] = product?.combo?.reduce((totalWeght, item) => {
            return totalWeght + (item.status === 1 ? item.variant?.weight * item.qty : 0);
          }, 0);
          comboRows[0][16] = product?.variations?.[0].prices
            ?.map(it => {
              return (
                find(countries, country => country.id === it.countryId)?.name ?? 'Không xác định'
              );
            })
            .join(', ');
          comboRows[0][17] = product?.variations?.[0].prices?.map(it => it.price).join(', ');
          combo
            .filter(item => item.status === STATUS.active)
            .forEach((item, index) => {
              const variant = item?.variant;
              const newComboIndex = index + 1;
              comboRows[newComboIndex][10] = variant?.sku;
              comboRows[newComboIndex][11] = variant?.barcode;
              comboRows[newComboIndex][12] = variant?.name;
              comboRows[newComboIndex][13] = variant?.properties
                ?.map(pr => `${pr.attributes.name}: ${pr.name}`)
                .join(', ');
              comboRows[newComboIndex][15] = item?.qty || 0;
            });

          data.push(...comboRows);
        } catch (error) {
          console.log(`error at`, index, products);
          console.log(`error reason`, error);
        }
      } else {
        const variations = product.variations;
        const rows = [...Array((variations.length || 1) + 1)].map(o =>
          Array(columns.length).fill(null),
        );
        try {
          rows[0][0] = index + 1;
          rows[0][1] = product?.isCombo ? 'Combo' : 'Thường';
          rows[0][2] = product.sku;
          rows[0][3] = product?.name;
          rows[0][4] = product?.categories?.[0]?.name;
          rows[0][5] = product.supplier?.name;
          rows[0][6] = find(projects, pr => pr.id === product.projectId)?.name ?? 'Không xác định';
          rows[0][7] = product.description;
          rows[0][8] = product.isCombo ? '' : this._getPropertiesOfProduct(product?.variations);
          rows[0][9] = product?.isCombo ? product?.combo?.length : product?.variations?.length;

          for (let pIndex = 0; pIndex < variations.length; pIndex++) {
            const variant = variations[pIndex];
            const newIndex = pIndex + 1;
            rows[newIndex][10] = variant?.sku;
            rows[newIndex][11] = variant?.barcode;
            rows[newIndex][12] = variant?.name;
            rows[newIndex][13] = variant?.properties
              ?.map(pr => `${pr.attributes.name}: ${pr.name}`)
              .join(', ');
            (rows[newIndex][14] = variant.weight ?? 0),
              (rows[newIndex][15] = variant?.qty ?? 0),
              (rows[newIndex][16] = variant?.prices
                ?.map(it => {
                  return (
                    find(countries, country => country.id === it.countryId)?.name ??
                    'Không xác định'
                  );
                })
                .join(', '));
            rows[newIndex][17] = variant.prices?.map(it => it.price).join(', ');
          }
          data.push(...rows);
        } catch (error) {
          console.log(`error at`, index, products);
          console.log(`error reason`, error);
        }
      }
    }

    const sheetOptions = {
      '!cols': [
        { wch: 6 },
        { wch: 20 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 20 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
        { wch: 30 },
      ],
    };

    const buffer = xlsx.build([{ name: 'Danh sách sản phẩm', data, options: {} }], {
      sheetOptions,
    }); // Returns a buffer
    return buffer;
  }

  async checkProductsExistInProject(body: CheckProductsExistInProjectDto) {
    const products = await this.variantRepo.find({ where: { sku: In(body.productSku) } });
    const productOfProject = body.projectIds.reduce((prev, item) => {
      prev[item] = products.filter(p => p.projectId === item).map(p => p.sku);
      return prev;
    }, {});
    return productOfProject;
  }
}
