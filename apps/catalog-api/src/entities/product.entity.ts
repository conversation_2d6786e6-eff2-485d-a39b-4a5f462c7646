import { Expose, Type, plainToInstance } from 'class-transformer';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { IntegerIdEntity } from 'core/entities/base/integer-id-entity.entity';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  Unique,
} from 'typeorm';
import { Category } from './category.entity';
import { ProductComboVariant } from './product-combo-variant.entity';
import { ProductSyncPancake } from './product-sync-pancake.entity';
import { ProductVariation } from './product-variation.entity';
import { Supplier } from './supplier.entity';
import { Scope } from 'core/decorators/typeorm-scope.decorator';
import { ProductsFilter } from '../modules/catalog/filters/products.filter';
import { isEmpty, uniq } from 'lodash';

@Scope<Product>([
  (qb, alias, user, headers, query: ProductsFilter) => {
    if (!user) return qb;
    const _alias = qb.expressionMap.findAliasByName(alias);
    if (_alias.type !== 'from') return qb;
    const scopeProjectIds = uniq(user.profiles.flatMap(p => p[4].map(it => it[1])));
    if (scopeProjectIds && !scopeProjectIds.includes(null)) {
      if (isEmpty(scopeProjectIds)) qb.andWhere('FALSE');
      else qb.andWhere(`${alias}.project_id IN (${scopeProjectIds.join(',')})`);
    }

    if (headers['project-ids']) qb.andWhere(`${alias}.project_id IN (${headers['project-ids']})`);

    query = plainToInstance(ProductsFilter, query);
    if (!isEmpty(query.projectIds))
      qb.andWhere(`${alias}.project_id IN (${query.projectIds.join(',')})`);
    return qb.andWhere(`${alias}.company_id = :companyId`, { companyId: user.companyId });
  },
])
@Entity({ name: 'product' })
@Unique('UQ_SKU_PRODUCT', ['sku', 'projectId', 'companyId'])
export class Product extends IntegerIdEntity {
  @Column({
    name: 'name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  name?: string;

  @Column({
    name: 'sku',
    type: 'varchar',
    nullable: true,
  })
  @Index({ where: 'sku IS NOT NULL' })
  @Expose()
  @NonEmptyTransform()
  sku?: string;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  description?: string;

  @Column({
    name: 'status',
    type: 'integer',
  })
  @Expose()
  status?: number;

  @Column({
    name: 'is_combo',
    type: 'boolean',
    nullable: true,
  })
  @Expose()
  isCombo?: boolean;

  @ManyToMany(
    () => Category,
    cate => cate.products,
    { nullable: true, cascade: true },
  )
  @JoinTable({
    name: 'category_product',
    joinColumn: {
      name: 'id_product',
    },
    inverseJoinColumn: {
      name: 'id_category',
    },
  })
  @Expose()
  categories?: Category[];

  @Column({
    name: 'supplier_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  supplierId?: number;

  @Column({
    name: 'project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  projectId?: number;

  @ManyToOne(
    () => Supplier,
    sup => sup.products,
    { nullable: true, cascade: true },
  )
  @JoinColumn({
    name: 'supplier_id',
  })
  @Expose()
  @NonEmptyTransform()
  supplier?: Supplier;

  // @ManyToMany(() => Warehouse, (wh) => wh.products, { nullable: true,cascade: true })
  // @JoinTable({
  //   name: 'warehouse_product',
  //   joinColumn: {
  //     name: 'id_product',
  //   },
  //   inverseJoinColumn: {
  //     name: 'id_warehouse',
  //   },
  // })
  // @Expose()
  // warehouses?: Warehouse[];

  @OneToMany(
    () => ProductVariation,
    prod => prod.product,
    { nullable: true },
  )
  @Type(() => ProductVariation)
  @Expose()
  variations?: ProductVariation[];

  @OneToMany(
    () => ProductComboVariant,
    prod => prod.product,
    { nullable: true, cascade: true },
  )
  @Type(() => ProductComboVariant)
  @Expose()
  combo?: ProductComboVariant[];

  // @ManyToMany(() => ProductVariation, (pv) => pv.combos, { nullable: true })
  // combos: ProductVariation[];

  @OneToMany(
    () => ProductSyncPancake,
    rl => rl.product,
  )
  @Type(() => ProductSyncPancake)
  @Expose()
  productSync?: ProductSyncPancake[];

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyId?: number;
}
