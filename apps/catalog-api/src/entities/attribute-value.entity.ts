import { Expose } from 'class-transformer';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';
import { Attribute } from './attribute.entity';
import { ProductVariation } from './product-variation.entity';

@Entity({
  name: 'attributes_value',
  database: process.env.DATABASE_CATALOG,
})
@Unique('UQ_ATTRIBUTE_VALUE_NAME', ['name', 'attributesId'])
export class AttributeValue extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id?: number;

  @Column({
    name: 'name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  name?: string;

  @Column({
    name: 'status',
    type: 'int',
  })
  @Expose()
  status?: number;

  @Column({ type: 'integer', name: 'attributes_id', nullable: true })
  @Expose()
  @NonEmptyTransform()
  attributesId?: number;

  @ManyToOne(
    () => Attribute,
    att => att.properties,
    { nullable: true, cascade: true },
  )
  @JoinColumn({
    name: 'attributes_id',
  })
  @Expose()
  @NonEmptyTransform()
  attributes?: Attribute;

  @ManyToMany(
    () => ProductVariation,
    pv => pv.properties,
    { nullable: true },
  )
  variations?: ProductVariation[];
}
