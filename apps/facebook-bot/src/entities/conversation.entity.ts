import { Expose, Transform, Type } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { VirtualColumn } from 'core/decorators/virtual-column.decorator';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, PrimaryColumn } from 'typeorm';
import { CareStatus } from '../enums/care-status.enum';
import { CareScenario } from './care-scenario.entity';
import { ConversationNote } from './conversation-note.entity';
import { ConversationOrder } from './conversation-order.entity';
import { ConversationTagsHistory } from './conversation-tags-history.entity';
import { ConversationsPhones } from './conversations-phones.entity';
import { FanPage } from './fanpage.entity';
import { SeenHistory } from './seen-history.entity';
import { Tag } from './tag.entity';
import { PageScopedUser } from './page-scoped-user.entity';
import { PageScopedUserCareItem } from './page-scoped-user-care-item.entity';

@Entity({
  name: 'conversations',
  database: process.env.DATABASE_MESSAGE,
})
@Index('conversation_page', ['pageId'])
@Index('conversation_seen', ['unread'])
@Index('conversation_creation', ['createdAt'])
@Index('IDX_CONVERSATION_SCOPED_UID', ['scopedUserId'])
@Index('IDX_CONVERSATION_PSID', ['pageId', 'scopedUserId'])
@Index('IDX_CONVERSATION_THREAD_ID', ['threadId'])
export class Conversation {
  @PrimaryColumn({
    name: 'page_id',
    type: 'bigint',
  })
  @Expose()
  pageId: string;

  @PrimaryColumn({
    name: 'scoped_user_id',
    type: 'bigint',
  })
  @Expose()
  @Transform(({ value, obj }) => `${obj.pageId}_${value}`, { toPlainOnly: true })
  @Transform(({ value, obj }) => value.replace(`${obj.pageId}_`, ''), { toClassOnly: true })
  scopedUserId: string;

  // @ManyToOne(
  //   () => PageScopedUser,
  //   item => item.conversations,
  //   {
  //     nullable: true,
  //     cascade: true,
  //     createForeignKeyConstraints: false,
  //   },
  // )
  @Expose()
  user?: PageScopedUser;

  @ManyToOne(() => FanPage, {
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'page_id' })
  @Expose()
  page?: FanPage;

  @Column({
    name: 'user_global_id',
    type: 'bigint',
    nullable: true,
  })
  @Expose()
  userGlobalId?: string;

  @Column({
    name: 'conversation_thread_id',
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  @Index({ unique: true, where: 'conversation_thread_id IS NOT NULL' })
  conversationThreadId?: string;

  @Column({
    name: 'last_default_message_sent',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @DateTransform()
  @NonEmptyTransform()
  @Expose()
  lastDefaultMessageSent?: Date;

  @Column({
    name: 'created_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @DateTransform()
  @NonEmptyTransform()
  @Expose()
  createdAt?: Date;

  @NonEmptyTransform()
  @Expose()
  get tags(): Tag[] {
    return this.user?.tags || [];
  }

  @Column({
    name: 'care_scenario_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  careScenarioId?: number;

  @ManyToOne(
    () => CareScenario,
    item => item.conversations,
    { nullable: true },
  )
  @JoinColumn({ name: 'care_scenario_id' })
  @Type(() => CareScenario)
  @Expose()
  careScenario?: CareScenario;

  @Column({
    name: 'updated_at',
    type: 'timestamp with time zone',
  })
  @DateTransform()
  @Index()
  @Expose()
  updatedAt?: Date;

  @Column({
    name: 'snippet',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  snippet?: string;

  @Column({
    name: 'user_read_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  userReadAt?: Date;

  @Column({
    name: 'last_seen_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  lastSeenAt?: Date;

  @Column({
    name: 'unread',
    type: 'boolean',
    default: true,
  })
  @Expose()
  unread?: boolean;

  @Column({
    name: 'is_bot_enabled',
    type: 'boolean',
    default: true,
  })
  @Expose()
  isBotEnabled: boolean;

  @Column({
    name: 'is_sent_start_message',
    type: 'boolean',
    nullable: true,
  })
  @Expose()
  isSentStartMessage?: boolean;

  @Column({
    name: 'last_sent_by_page',
    type: 'boolean',
    nullable: true,
  })
  @Expose()
  lastSentByPage?: boolean;

  @Column({
    name: 'last_seen_by',
    type: 'integer',
    nullable: true,
  })
  @Expose()
  lastSeenBy?: number;

  @Expose()
  get assignedTo(): number {
    return this.user?.careUserId;
  }

  @Column({
    name: 'status',
    type: 'smallint',
    default: CareStatus.new,
  })
  @Expose()
  status: CareStatus;

  @Column({
    name: 'care_user_id',
    type: 'int',
    default: null,
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  careUserId?: number;

  @Column({
    name: 'last_updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  lastUpdatedBy?: number;

  @OneToMany(
    () => ConversationNote,
    c => c.conversation,
  )
  notes: ConversationNote[];

  @OneToMany(
    () => ConversationsPhones,
    item => item.conversation,
    { nullable: true },
  )
  @Expose()
  phones?: ConversationsPhones[];

  @VirtualColumn('number_of_unread_messages')
  @NonEmptyTransform()
  @Expose()
  @Type(() => Number)
  numberOfUnreadMessages?: number;

  @OneToMany(
    () => SeenHistory,
    item => item.conversation,
    { nullable: true },
  )
  seenHistories?: SeenHistory[];

  @OneToMany(
    () => ConversationTagsHistory,
    item => item.conversation,
    { nullable: true },
  )
  tagsHistories?: ConversationTagsHistory[];

  @Expose()
  orders: ConversationOrder[];

  @PrimaryColumn({
    name: 'feed_id',
    type: 'varchar',
    length: 50,
    default: '',
  })
  @Expose()
  feedId: string;

  @Column({
    name: 'first_reply_by_employee_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  firstReplyByEmployeeId?: number;

  @Column({
    name: 'thread_id',
    type: 'varchar',
    length: '64',
    nullable: true,
  })
  @Expose()
  threadId?: string;

  @Expose({
    toPlainOnly: true,
  })
  get seen() {
    return !this.unread;
  }

  @VirtualColumn('has_orders')
  @Expose()
  hasOrders?: boolean;

  @Expose()
  @NonEmptyTransform()
  isMessageReceived?: boolean;

  lastCare?: PageScopedUserCareItem;

  @Column({
    name: 'product_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  productId?: number;

  @Column({
    name: 'last_message_id_get_summary',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  lastMessageIdGetSummary?: string;

  @Column({
    name: 'last_summary',
    type: 'text',
    nullable: true,
  })
  @Expose()
  lastSummary?: string;

  @Column({
    name: 'last_summary_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  lastSummaryAt?: Date;

  @Column({
    name: 'last_summary_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastSummaryBy?: number;
}
