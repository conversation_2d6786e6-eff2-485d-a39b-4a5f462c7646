import { Expose } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { Column, Entity, Index, JoinColumn, JoinTable, ManyToMany, OneToMany } from 'typeorm';
import { UserStatus } from '../enums/user-status.enum';
import { UserType } from '../enums/user-type.enum';
import { IFbCtx } from '../facebook-api';
import { FanPage } from './fanpage.entity';
import { UserSession } from './user-sessions.entity';
import { VirtualColumn } from 'core/decorators/virtual-column.decorator';

@Entity({
  name: 'user',
  database: process.env.DATABASE_MESSAGE,
})
export class User extends BaseEntity {
  @Column({
    name: 'id',
    type: 'bigint',
    primary: true,
  })
  @Expose()
  id: string;

  @Column({
    name: 'care_page_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  carePageId: number;

  @OneToMany(
    () => UserSession,
    session => session.user,
  )
  @JoinColumn({
    name: 'id',
    referencedColumnName: 'user_id',
  })
  sessions: UserSession[];

  get appState() {
    if (!this.sessions) {
      return undefined;
    }
    return this.sessions[0]?.cookies?.map(i => {
      return {
        key: i?.name || i.key,
        path: i?.path,
        value: i.value,
        domain: i.domain,
        expires: i.expirationDate || i.exprires,
      };
    });
  }

  @Column({
    name: 'name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  name: string;

  @Column({
    name: 'is_subscriber',
    type: 'boolean',
    default: false,
  })
  isSubscriber: boolean;

  get token(): string {
    if (!this.sessions) {
      return undefined;
    }
    return this.sessions[0]?.token;
  }

  @Column({
    name: 'creator_id',
    type: 'int',
    nullable: true,
  })
  creatorId: number;

  @Column({
    name: 'two_factor',
    type: 'varchar',
    nullable: true,
  })
  @Expose({
    groups: ['show'],
  })
  twoFactor: string;

  @Column({
    name: 'email',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  @Index({ unique: true, where: 'email IS NOT NULL' })
  @Expose()
  email: string;

  @Column({
    name: 'password',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  @Expose({
    groups: ['show'],
  })
  password: string;

  @Expose()
  get status(): UserStatus {
    const session = this.sessions?.find(i => i.ip === global.currentIp && i.status > -1);
    if (!session) {
      return -1;
    }
    return session?.status;
  }

  @ManyToMany(
    () => FanPage,
    av => av.users,
    { nullable: true, cascade: true },
  )
  @JoinTable({
    name: 'user_page',
    joinColumn: {
      name: 'user_id',
    },
    inverseJoinColumn: {
      name: 'page_id',
    },
  })
  @Expose()
  pages?: FanPage[];

  get context(): IFbCtx {
    if (!this.sessions) {
      return undefined;
    }
    return this.sessions[0]?.context;
  }

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyId?: number;

  @Column({
    name: 'type',
    type: 'smallint',
    default: UserType.auto,
  })
  @Expose()
  @EnumTransform(UserType)
  type?: UserType;

  @VirtualColumn('page_count')
  @Expose()
  pageCount?: number;

  @Column({
    name: 'project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  projectId?: number;
}
