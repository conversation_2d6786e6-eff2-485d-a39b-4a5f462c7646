import { Expose } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';
import { FanPage } from './fanpage.entity';

export interface IFeedAttachment {
  id: string;
  type: 'photo' | 'video';
  title?: string;
  mediaSrc?: string;
  targetUrl?: string;
}

@Entity({
  name: 'feeds',
  database: process.env.DATABASE_MESSAGE,
})
export class Feed {
  @PrimaryColumn({
    name: 'id',
    type: 'varchar',
    length: 50,
    default: null,
  })
  @Expose()
  id: string;

  @Column({
    name: 'created_at',
    type: 'timestamp with time zone',
  })
  @DateTransform()
  @NonEmptyTransform()
  @Expose()
  createdAt?: Date;

  @Column({
    name: 'updated_at',
    type: 'timestamp with time zone',
  })
  @DateTransform()
  @Index()
  @Expose()
  updatedAt?: Date;

  @Column({
    name: 'message',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  message?: string;

  @Column({
    name: 'attachments',
    type: 'jsonb',
    nullable: true,
  })
  @Expose()
  attachments?: IFeedAttachment[];

  @Column({
    name: 'page_id',
    type: 'bigint',
  })
  @Index()
  @Expose()
  pageId?: string;

  @Column({
    name: 'product_id',
    type: 'bigint',
  })
  @Index()
  @Expose()
  productId?: number;

  @Column({
    name: 'hide_comments',
    type: 'boolean',
    default: false,
  })
  @Expose()
  hideComments?: boolean;

  @ManyToOne(() => FanPage, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'page_id' })
  @Expose()
  page?: FanPage;

  @Expose()
  productName?: string;
  @Expose()
  productSKU?: string;
}
