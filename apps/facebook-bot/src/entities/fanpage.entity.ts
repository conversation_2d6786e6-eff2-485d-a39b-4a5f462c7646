import { Expose, plainToInstance, Type } from 'class-transformer';
import { VirtualColumn } from 'core/decorators/virtual-column.decorator';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import {
  Brackets,
  Column,
  DeleteDateColumn,
  UpdateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { ScopedUser } from './scoped-user.entity';
import { Campaign } from './campaign.entity';
import { PageMemberTag } from './page-member-tag.entity';
import { User } from './user.entity';
import { ScopedUserPage } from './scoped-user-page.entity';
import { PageGroup } from './page-group.entity';
import { KeywordConfigurationGroup } from './keyword-configuration-group.entity';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { User as Marketer } from '../read-entities/identity-api/user.entity';
import { CrossCareMode } from '../enums/cross-care.enum';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { Scope } from 'core/decorators/typeorm-scope.decorator';
import { isEmpty, intersection, uniq } from 'lodash';
import { PagesFilter } from '../message/filters/pages.filter';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';
import * as moment from 'moment-timezone';
import { AiConfigurationGroup } from './ai-configuration-group.entity';
import { all } from 'bluebird';
import { OrderType } from '../enums/order-type.enum';

@Scope<FanPage>([
  (qb, alias, user, headers, query) => {
    if (!user) return qb;
    // const _alias = qb.expressionMap.findAliasByName(alias);
    // if (_alias.type !== 'from') return qb;
    if (query.update) {
      return qb;
    }

    if (query._bypassPagePermission) {
      return qb;
    }

    query = plainToInstance(PagesFilter, query);
    const v2Filter = !isEmpty(query.tab);
    if (v2Filter) {
      let allowAccessData = false;
      let allowCountries = [];
      let allowProjects = [];
      for (const record of user.profiles) {
        const [dataAccessLevel, moduleInCharge, , , scopes] = record;
        const projectIds = scopes.map(i => i[1]);
        const countryIds = scopes.map(i => i[0]);
        allowCountries.push(...countryIds);
        allowProjects.push(...projectIds);

        if (!isEmpty(moduleInCharge)) {
          if (dataAccessLevel === DataAccessLevel.parentAuthority) {
            allowAccessData = true;
          }

          qb.andWhere(
            new Brackets(mQb => {
              for (const module of moduleInCharge) {
                switch (module) {
                  case ModuleInCharge.marketer:
                    if (dataAccessLevel === DataAccessLevel.personal) {
                      mQb.orWhere(`${alias}.marketer_id = ${user.id}`);
                    } else {
                      mQb.andWhere(`${alias}.marketer_id IN (:...descendantMarketerIds)`, {
                        descendantMarketerIds: user?.descendants?.marketerIds,
                      });
                    }
                    break;
                  default:
                    break;
                }
              }
            }),
          );
        } else {
          allowAccessData = true;
        }
      }
      const needToAdd =
        query?.tab === 'linked' || !isEmpty(query?.countryIds) || !isEmpty(query?.projectIds);
      if (needToAdd) {
        const filterExpr = `${alias}.country_id IN (:...allowCountries) AND ${alias}.project_id IN (:...allowProjects)`;
        allowCountries = uniq(allowCountries);
        allowProjects = uniq(allowProjects);

        if (query.countryIds?.length > 0) {
          allowCountries = intersection(allowCountries, query.countryIds);
        }

        if (query.projectIds?.length > 0) {
          allowProjects = intersection(allowProjects, query.projectIds);
        }

        qb.andWhere(filterExpr, { allowCountries, allowProjects });
      }

      if (!allowAccessData) {
        // qb.expressionMap.wheres = [];
        qb.andWhere(`${alias}.marketer_id = ${user.id}`);
        // qb.andWhere('1 = 0'); // disable user has no marketer module in charge
      }

      return qb.andWhere(`${alias}.company_id = :companyId`, { companyId: user.companyId });
    }

    if (isNaN(query.name)) {
      qb.andWhere(
        new Brackets(sqb => {
          sqb.orWhere(`(${alias}.country_id IS NULL AND ${alias}.project_id IS NULL)`);
          for (const record of user.profiles) {
            const [dataAccessLevel, moduleInCharge, scopes] = record;
            sqb.orWhere(
              new Brackets(pQb => {
                let hasAll = false;
                const countryId = scopes[0][0];
                const projectIds = scopes.map(i => i[1]);

                const scopePairs = scopes.map(sc => {
                  if (sc[0] === null && sc[1] === null) {
                    hasAll = true;
                  }
                  return `(${sc[0]}, ${sc[1]})`;
                });
                if (hasAll) {
                  return;
                } else {
                  pQb.andWhere(
                    `${alias}.country_id = ${countryId} AND ${alias}.project_id IN (${projectIds.join(
                      ',',
                    )})`,
                  );
                }
                if (!isEmpty(moduleInCharge)) {
                  pQb.andWhere(
                    new Brackets(mQb => {
                      for (const module of moduleInCharge) {
                        switch (module) {
                          case ModuleInCharge.marketer:
                            if (dataAccessLevel === DataAccessLevel.personal) {
                              mQb.orWhere(`${alias}.marketer_id = ${user.id}`);
                            } else {
                              mQb.andWhere(`${alias}.marketer_id IN (:...descendantMarketerIds)`, {
                                descendantMarketerIds: user?.descendants?.marketerIds,
                              });
                            }
                            break;
                          default:
                            break;
                        }
                      }
                    }),
                  );
                }
              }),
            );
          }
        }),
      );
    }

    if (query.isSetProjectsAndCountries) {
      // if (headers['project-ids'])
      //   qb.andWhere(`${alias}.project_id IN (:...headerProjectIds)`, {
      //     headerProjectIds: headers['project-ids'].split(','),
      //   });
      if (!isEmpty(query.projectIds))
        qb.andWhere(`${alias}.project_id IN (:...queryProjectIds)`, {
          queryProjectIds: query.projectIds,
        });
    }

    return qb.andWhere(`${alias}.company_id = :companyId`, { companyId: user.companyId });
  },
])
@Entity({
  name: 'fanpage',
  database: process.env.DATABASE_MESSAGE,
})
@Index('page_group_id', ['groupId'])
@Index('page_company_id', ['companyId'])
@Index('page_country_id', ['countryId'])
@Index('page_project_id', ['projectId'])
@Index('IDX_FANPAGE', ['countryId', 'projectId'])
export class FanPage extends BaseEntity {
  @PrimaryColumn({
    name: 'id',
    type: 'bigint',
  })
  @Expose()
  id: string;

  @Column({
    name: 'name',
    type: 'varchar',
  })
  @Expose()
  name: string;

  @Column({
    name: 'is_bot_enabled',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isBotEnabled: boolean;

  @Column({
    name: 'is_sync_conversation',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isSyncConversation: boolean;

  @Column({
    name: 'is_subscribed',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isSubscribed: boolean;

  @Column({
    name: 'project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  projectId?: number;

  @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  countryId?: number;

  @Column({
    name: 'product_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  productId?: number;

  @OneToMany(
    () => ScopedUserPage,
    item => item.page,
    { nullable: true },
  )
  scopedUserPages?: ScopedUserPage[];

  @ManyToMany(
    () => ScopedUser,
    u => u.pages,
    { nullable: true },
  )
  @JoinTable({
    name: 'scoped_user_pages',
    joinColumn: {
      name: 'page_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'scoped_user_id',
      referencedColumnName: 'id',
    },
  })
  @Expose()
  scopedUsers?: ScopedUser[];

  @OneToMany(
    () => PageMemberTag,
    pmt => pmt.page,
    { nullable: true },
  )
  @Type(() => PageMemberTag)
  @Expose()
  tags?: PageMemberTag[];

  @ManyToOne(
    () => PageGroup,
    pg => pg.pages,
  )
  @JoinColumn({ name: 'group_id' })
  @Expose()
  group?: PageGroup;

  @Column({
    name: 'group_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  groupId?: number;

  @Column({
    name: 'keyword_group_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  keywordGroupId?: number;

  @Column({
    name: 'marketer_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  marketerId?: number;

  @ManyToOne(
    () => KeywordConfigurationGroup,
    obj => obj.pages,
    { nullable: true },
  )
  @JoinColumn({ name: 'keyword_group_id' })
  keywordGroup?: KeywordConfigurationGroup;

  @ManyToMany(
    () => Campaign,
    sm => sm.pages,
    { nullable: true },
  )
  campaigns: Campaign[];

  @ManyToMany(
    () => User,
    u => u.pages,
    { nullable: true },
  )
  users?: User[];

  @VirtualColumn('total_campaign')
  @Expose()
  totalCampaign?: number;

  @VirtualColumn('total_user')
  @Expose()
  totalUser?: number;

  @VirtualColumn('config_chatbot')
  @Expose()
  configChatbot?: boolean;

  @VirtualColumn('unread_conversations')
  @Expose()
  @Type(() => Number)
  @NonEmptyTransform()
  unreadConversations?: number;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyId?: number;
  @Expose()
  @NonEmptyTransform()
  marketer?: Marketer;
  @Column({
    name: 'cross_care_mode',
    type: 'smallint',
    default: CrossCareMode.off,
  })
  @Expose()
  @EnumTransform(CrossCareMode)
  crossCareMode?: CrossCareMode;
  @ManyToOne(
    () => AiConfigurationGroup,
    av => av.pages,
    { nullable: true },
  )
  @JoinColumn({ name: 'ai_config_id' })
  @Expose()
  aiConfig?: AiConfigurationGroup;

  @Column({
    name: 'ai_config_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  aiConfigId?: number;

  @DeleteDateColumn({
    name: 'deleted_at',
  })
  deletedAt?: Date;

  @UpdateDateColumn({
    name: 'updated_at',
  })
  updatedAt?: Date;

  @Expose()
  @Column({ select: false, nullable: true, insert: false, update: false })
  tokensActive: number;

  @Expose()
  @Column({ select: false, nullable: true, insert: false, update: false })
  tokensExpiringSoon: number;

  @Expose()
  @Column({ select: false, nullable: true, insert: false, update: false })
  tokensExpired: number;

  @Column({
    name: 'hide_comments',
    type: 'boolean',
    default: false,
  })
  @Expose()
  hideComments?: boolean;

  @Expose()
  productName?: string;

  get accessTokens(): string[] {
    return this.scopedUserPages?.map(item => item.accessToken);
  }

  get isBotWorking() {
    if (this.group && this.group.botStartAt && this.group.botEndAt) {
      const [sHour, sMinute, sSecond] = String(this.group.botStartAt).split(':');
      const [eHour, eMinute, eSecond] = String(this.group.botEndAt).split(':');

      const timezone = 'Asia/Ho_Chi_Minh';
      const now = moment().tz(timezone);
      const botStartAt = now.clone().set({
        hour: Number(sHour),
        minute: Number(sMinute),
        second: Number(sSecond),
      });
      const botEndAt = now.clone().set({
        hour: Number(eHour),
        minute: Number(eMinute),
        second: Number(eSecond),
      });

      const isBetweenTimeRange = !botEndAt.isBefore(botStartAt)
        ? now.isAfter(botStartAt) && botEndAt.isAfter(now)
        : (now.isAfter(botStartAt.clone().subtract(1, 'days')) && botEndAt.isAfter(now)) ||
          (now.isAfter(botStartAt) &&
            botEndAt
              .clone()
              .add(1, 'days')
              .isAfter(now));
      return this.isBotEnabled && isBetweenTimeRange;
    }

    return this.isBotEnabled;
  }

   @Column({
    name: 'active_conversation_summary',
    type: 'boolean',
    default: false,
  })
  @Expose()
  activeConversationSummary?: boolean;

  @Column({
    name: 'order_type',
    type: 'smallint',
    default: OrderType.normal,
  })
  @Expose()
  orderType?: OrderType;
}
