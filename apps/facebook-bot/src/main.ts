import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { TransformInterceptor } from 'core/interceptors';
import { Logger } from 'core/interceptors/logger.interceptors';
import { AppModule } from './app.module';
import * as basicAuth from 'express-basic-auth';
import { router } from 'bull-board';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { RedisIoAdapter } from './socket/adapters/redis.adapter';
import { cacheRawBodyOnRequest } from 'core/interceptors/raw-requests.interceptors';
import 'core/extensions';
import 'core/interceptors/axios.interceptors';
import * as ip from 'public-ip';
import StringUtils from 'core/utils/StringUtils';

process.on('unhandledRejection', async (reason: Error | any, promise) => {
  console.log('Unhandled Rejection at: Promise', promise, 'reason:', reason);
  console.log('Unhandled Rejection reason stack trace', reason?.stack);
  // application specific logging, throwing an error, or other logic here
  await promise.catch(e => {
    console.log(`Unhandled Rejection catch raw exception: `, StringUtils.getString(e));
    console.log(`Unhandled Rejection catch stack trace`, e?.stack);
  });
});

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors({
    origin: [/https?:\/\/.*$/],
    credentials: true,
    allowedHeaders: '*',
  });

  const options = new DocumentBuilder()
    .setTitle('Node Backend')
    .setDescription('Backend writing in Node.js')
    .setVersion('0.1.0')
    .addServer(process.env.BASE_API_URL || '')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('swagger', app, document);
  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  app.useGlobalInterceptors(
    new TransformInterceptor(),
    process.env.SEQ_KEY ? new Logger('facebook-bot') : undefined,
  );
  app.use(cacheRawBodyOnRequest);
  app.use(
    '/queues',
    basicAuth({
      users: { admin: process.env.BULL_PASSWORD },
      challenge: true,
    }),
    (req, res, next) => {
      req.proxyUrl = (process.env.API_SUBPATH || '') + '/queues';
      next();
    },
    router,
  );
  const redisIoAdapter = new RedisIoAdapter(app);
  await redisIoAdapter.connectToRedis();

  app.useWebSocketAdapter(redisIoAdapter);

  try {
    global.currentIp = await ip.v4();
    await app.listen(3004);
  } catch (e) {
    console.log('e', e);
    await app.close();
  }
}

bootstrap();
