import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { messageConnection } from 'core/constants/database-connection.constant';
import { RedisCacheModule } from '../../../../core/cache/redisCache.module';
import { CampaignMessageMember } from '../entities/campaign-message-member.entity';
import { Campaign } from '../entities/campaign.entity';
import { CareReason } from '../entities/care-reason.entity';
import { CareScenario } from '../entities/care-scenario.entity';
import { CareScenariosMessages } from '../entities/care-scenarios-messages.entity';
import { CommentConfiguration } from '../entities/comment-configuration.entity';
import { Comment } from '../entities/comment.entity';
import { ConversationAssignHistory } from '../entities/conversation-assign-history.entity';
import { ConversationOrder } from '../entities/conversation-order.entity';
import { ConversationReferral } from '../entities/conversation-referral.entity';
import { ConversationTag } from '../entities/conversation-tag.entity';
import { ConversationTagsHistory } from '../entities/conversation-tags-history.entity';
import { Conversation } from '../entities/conversation.entity';
import { ConversationsPhones } from '../entities/conversations-phones.entity';
import { FanPage } from '../entities/fanpage.entity';
import { FacebookBotMessage } from '../entities/fb-bot-message.entity';
import { Feed } from '../entities/feed.entity';
import { KeywordConfigurationGroup } from '../entities/keyword-configuration-group.entity';
import { KeywordConfiguration } from '../entities/keyword-configuration.entity';
import { FanPageMember } from '../entities/member.entity';
import { Message } from '../entities/message.entity';
import { PageCampaignAttachment } from '../entities/page-campaign-attachment.entity';
import { PageMemberTag } from '../entities/page-member-tag.entity';
import { Report } from '../entities/report.entity';
import { ScheduledJob } from '../entities/scheduled-job.entity';
import { ScopedUserPage } from '../entities/scoped-user-page.entity';
import { ScopedUser } from '../entities/scoped-user.entity';
import { SystemLog } from '../entities/system-log.entity';
import { Tag } from '../entities/tag.entity';
import { TempMessage } from '../entities/temp-message.entity';
import { UserSession } from '../entities/user-sessions.entity';
import { User } from '../entities/user.entity';
import { UsersReports } from '../entities/users-reports.entity';
import { FacebookChatModule } from '../facebook-chat/facebook-chat.module';
import { SocketModule } from '../socket/socket.module';
import { CareReasonsController } from './controllers/care-reasons.controller';
import { CareScenarioController } from './controllers/care-scenario.controller';
import { CdnController } from './controllers/cdn.controller';
import { CommentsConfigurationsController } from './controllers/comment-configurations.controller';
import { ConversationTagsController } from './controllers/conversation-tags.controller';
import { ConversationsController } from './controllers/conversations.controller';
import { DashboardReportsController } from './controllers/dashboard-reports.controller';
import { FacebookBotController } from './controllers/facebook-bot.controller';
import { FacebookController } from './controllers/facebook.controller';
import { KeywordConfigurationsController } from './controllers/keyword-configurations.controller';
import { KeywordGroupsController } from './controllers/keyword-groups.controller';
import { MessageController } from './controllers/message.controller';
import { ReportsController } from './controllers/reports.controller';
import { ScheduleJobsController } from './controllers/scheduled-job.controller';
import { ScopedUsersController } from './controllers/scoped-users.controller';
import { StatisticsController } from './controllers/statistics.controller';
import { UserBotController } from './controllers/user-bot.controller';
import { UsersReportsController } from './controllers/users-reports.controller';
import { CampaignProcessor } from './processors/campaign.processor';
import { FacebookJobsProcessor } from './processors/facebook-jobs.processor';
import { AutomationService } from './services/automation.service';
import { BotService } from './services/bot.service';
import { CareReasonsService } from './services/care-reasons.service';
import { CareScenarioService } from './services/care-scenario.service';
import { CommentConfigurationService } from './services/comment-configurations.service';
import { ConversationSourcesService } from './services/conversation-sources.service';
import { ConversationTagsService } from './services/conversation-tags.service';
import { ConversationsService } from './services/conversation.service';
import { FbMessagesService } from './services/facbook-messages.service';
import { FacebookBotService } from './services/facebook-bot.service';
import { FacebookFeedsService } from './services/facebook-feed.service';
import { FacebookOrdersService } from './services/facebook-orders.service';
import { FacebookService } from './services/facebook.service';
import { FanPagesService } from './services/fanpage.service';
import { FshareService } from './services/fshare.service';
import { KeywordGroupsService } from './services/keyword-groups.service';
import { KeywordsService } from './services/keywords.service';
import { MessageService } from './services/message.service';
import { MessageSubscriber } from './services/message.subscriber';
import { ProductAutomationConfigsService } from './services/product-automation-configs.service';
import { ReportsService } from './services/reports.service';
import { ScheduledJobsService } from './services/scheduled-jobs.service';
import { PageScopedUserCareItemSubscriber } from './services/page-scoped-user-care-item.subscriber';
import { ScopedUserPagesService } from './services/scoped-user-pages.service';
import { ScopedUsersService } from './services/scoped-users.service';
import { StatisticsService } from './services/statistics.service';
import { LimitationSetting } from '../entities/limitation-setting.entity';
import { LimitationSettingsController } from './controllers/limitation-settings.controller';
import { LimitationSettingsService } from './services/limitation-settings.service';
import { scopedUserCaresQueue } from '../constants/bull-queue.constant';
import { ScopedUserCaresProcessor } from './processors/su-cares.processor';
import { FacebookPixel } from '../entities/facebook-pixel.entity';
import { FacebookPixelController } from './controllers/facebook-pixel.controller';
import { FacebookPixelService } from './services/facebook-pixel.service';
import { MessageTag } from '../entities/message-tag.entity';
import { MessageEntity } from '../entities/message-entities.entity';
import { Appointment } from '../entities/appointment.entity';
import { AppointmentsService } from './services/appointments.service';
import { AppointmentsController } from './controllers/appointments.controller';
import { PageScopedUser } from '../entities/page-scoped-user.entity';
import { PageScopedUserCare } from '../entities/page-scoped-user-care.entity';
import { PageScopedUserCareItem } from '../entities/page-scoped-user-care-item.entity';
import { CampaignsService } from './services/campaigns.service';
import { AiProductConfiguration } from '../entities/ai-product-configuration.entity';
import { AiProductImage } from '../entities/ai-product-image.entity';
import { ConfigurationGroup } from '../entities/configuration-groups.entity';
import { AiConfigurationGroup } from '../entities/ai-configuration-group.entity';
import { UserReportLogs } from '../entities/user-report-logs.entity';
import { FilterCollection } from '../entities/filter-collection.entity';
import { LimitationSettingsLogs } from '../entities/limitation-settings-logs.entity';

@Module({
  imports: [
    RabbitMQModule.externallyConfigured(RabbitMQModule, 0),
    TypeOrmModule.forFeature(
      [
        Tag,
        PageMemberTag,
        FanPage,
        FanPageMember,
        CampaignMessageMember,
        Campaign,
        User,
        Conversation,
        Message,
        ScopedUser,
        ScopedUserPage,
        PageCampaignAttachment,
        FacebookBotMessage,
        ConversationTag,
        CareScenario,
        CareScenariosMessages,
        TempMessage,
        KeywordConfiguration,
        ScheduledJob,
        CommentConfiguration,
        KeywordConfigurationGroup,
        ConversationsPhones,
        UserSession,
        ConversationTagsHistory,
        Report,
        UsersReports,
        ConversationAssignHistory,
        PageScopedUser,
        PageScopedUserCare,
        PageScopedUserCareItem,
        SystemLog,
        CareReason,
        Feed,
        Comment,
        ConversationOrder,
        ConversationReferral,
        LimitationSetting,
        FacebookPixel,
        MessageTag,
        MessageEntity,
        Appointment,
        AiProductConfiguration,
        AiProductImage,
        KeywordConfigurationGroup,
        ConfigurationGroup,
        AiConfigurationGroup,
        UserReportLogs,
        FilterCollection,
        LimitationSettingsLogs,
        FilterCollection,
      ],
      messageConnection,
    ),
    RedisCacheModule,
    BullModule.registerQueue({ name: 'campaign' }),
    BullModule.registerQueue({ name: 'facebook-jobs' }),
    BullModule.registerQueue({ name: scopedUserCaresQueue }),
    SocketModule,
    FacebookChatModule,
  ],
  controllers: [
    MessageController,
    CdnController,
    FacebookController,
    FacebookBotController,
    CareScenarioController,
    ConversationsController,
    ConversationTagsController,
    KeywordConfigurationsController,
    ScheduleJobsController,
    CommentsConfigurationsController,
    KeywordGroupsController,
    UserBotController,
    ReportsController,
    UsersReportsController,
    DashboardReportsController,
    StatisticsController,
    ScopedUsersController,
    CareReasonsController,
    LimitationSettingsController,
    FacebookPixelController,
    AppointmentsController,
  ],
  providers: [
    MessageService,
    CampaignProcessor,
    BotService,
    FshareService,
    FacebookService,
    FacebookFeedsService,
    ConversationsService,
    FbMessagesService,
    FacebookBotService,
    ScopedUsersService,
    ScopedUserPagesService,
    CareScenarioService,
    ConversationTagsService,
    FanPagesService,
    MessageSubscriber,
    KeywordsService,
    ScheduledJobsService,
    FacebookJobsProcessor,
    CommentConfigurationService,
    KeywordGroupsService,
    FacebookOrdersService,
    ProductAutomationConfigsService,
    ReportsService,
    AutomationService,
    StatisticsService,
    ConversationSourcesService,
    PageScopedUserCareItemSubscriber,
    CareReasonsService,
    LimitationSettingsService,
    ScopedUserCaresProcessor,
    FacebookPixelService,
    AppointmentsService,
    CampaignsService,
  ],
  exports: [
    BullModule.registerQueue({ name: 'campaign' }),
    BullModule.registerQueue({ name: 'facebook-jobs' }),
    BullModule.registerQueue({ name: scopedUserCaresQueue }),
  ],
})
export class MessageModule {}
