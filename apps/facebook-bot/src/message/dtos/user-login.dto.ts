import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { EnumTransform } from '../../../../../core/decorators/enum-transform.decorator';
import { UserType } from '../../enums/user-type.enum';

export class UserLoginDto {
  @ApiProperty()
  @IsNotEmpty()
  password: string;

  @ApiProperty()
  @IsNotEmpty()
  user: string;

  @ApiProperty()
  @IsOptional()
  code: string;

  @ApiProperty()
  @IsOptional()
  force: boolean;

  @ApiProperty({ required: false, default: UserType.manual })
  @IsOptional()
  @EnumTransform(UserType)
  @IsEnum(UserType)
  type?: UserType;
  
  @ApiProperty()
  @IsOptional()
  projectId?: number;
}
