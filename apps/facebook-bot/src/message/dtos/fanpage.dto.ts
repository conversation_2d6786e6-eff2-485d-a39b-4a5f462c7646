import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { CrossCareMode } from '../../enums/cross-care.enum';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { OrderType } from '../../enums/order-type.enum';

export class FanPageDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  projectId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  countryId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  productId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  marketerId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  keywordGroupId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isBotEnabled?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isGetStartedEnabled?: boolean;

  @ApiProperty({ required: false, enum: CrossCareMode })
  @IsOptional()
  @IsEnum(CrossCareMode)
  @EnumTransform(CrossCareMode)
  crossCareMode?: CrossCareMode;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  groupId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  deletedAt?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  hideComments?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  internalNote?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  orderType?: OrderType;
  
}

export class UpdatePageDto extends PartialType(FanPageDto) {}
