import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { FilterDefault } from './filterDefault.filter';

export class PagesFilter extends PartialType(FilterDefault) {
  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  productIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isSetProjectsAndCountries?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isBotWarning?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isGrouped?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getUnreadConversationCount?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  marketerIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  assignedKeywordGroup?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  countryIds?: Array<string | number>;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  projectIds?: Array<number>;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getAllName?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getPageGroup?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  aiConfigId?: number;

  companyId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  pageIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  action?: String;

  @ApiProperty({ required: false })
  @IsOptional()
  tab?: String;

  @ApiProperty({ required: false })
  @IsOptional()
  orderBy?: String;

  @ApiProperty({ required: false })
  @IsOptional()
  sort?: String;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  viaIds?: string[]; 

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getVia?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  ordersType?: number[];
}
