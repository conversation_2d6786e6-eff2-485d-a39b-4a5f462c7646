import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { UserType } from '../../enums/user-type.enum';
import { FilterDefault } from './filterDefault.filter';

export class UsersFilter extends OmitType(FilterDefault, ['type']) {
  @ApiProperty({
    required: false,
    enum: UserType,
  })
  @IsEnum(UserType)
  @EnumTransform(UserType)
  @IsOptional()
  type?: UserType;
}
