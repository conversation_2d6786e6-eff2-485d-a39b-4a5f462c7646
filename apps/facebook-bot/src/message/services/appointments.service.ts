import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { messageConnection } from 'core/constants/database-connection.constant';
import { Repository, UpdateResult } from 'typeorm';
import { Appointment } from '../../entities/appointment.entity';
import { CreateAppointmentDto, UpdateAppointmentDto } from '../dtos/appointment.dto';
import { AppointmentsFilter } from '../filters/appointments.filter';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';
import { SystemLog } from '../../entities/system-log.entity';
import { LogAction } from 'core/enums/log-action.enum';
@Injectable()
export class AppointmentsService {
  constructor(
    @InjectRepository(Appointment, messageConnection)
    private appointmentsRepo: Repository<Appointment>,
    @InjectRepository(PageScopedUser, messageConnection)
    private suRepo: Repository<PageScopedUser>,
    @InjectRepository(SystemLog, messageConnection)
    private systemLogRepo: Repository<SystemLog>,
  ) {}

  async findOne(id: number): Promise<Appointment> {
    const qb = this.appointmentsRepo
      .createQueryBuilder('a')
      .leftJoin('a.scopedUser', 'su')
      .addSelect(['su.id', 'su.name'])
      .where('a.id = :id', { id });
    return qb.getOne();
  }

  async createAppointment(
    scopedUserId: string,
    body: CreateAppointmentDto,
    creatorId: number,
  ): Promise<Appointment> {
    const scopedUser = await this.suRepo
      .createQueryBuilder('su')
      .select('su.id', 'id')
      .addSelect('su.page_id', 'page_id')
      .addSelect('su.scoped_user_id', 'scoped_user_id')
      .addSelect('su.name', 'su_name')
      .where('su.id = :scopedUserId', { scopedUserId })
      .getRawOne();

    if (!scopedUser) throw new NotFoundException('Khách hàng không tồn tại!');
    const appointment = plainToInstance(Appointment, {
      content: body?.content,
      careId: body?.careId,
      appointmentTime: body?.appointmentTime,
      creatorId,
      pageId: scopedUser.page_id,
      scopedUserId: scopedUser.scoped_user_id,
    });

    const result = await this.appointmentsRepo.save(appointment);
    appointment.id = result.id;
    appointment.scopedUser = {
      id: scopedUser.id,
      name: scopedUser.su_name,
    };
    const changes = [appointment.appointmentTime, appointment.content];
    const log = plainToInstance(SystemLog, {
      action: LogAction.INSERT,
      event: 'Create',
      type: 'Appointment',
      changes,
      afterChanges: changes,
      tableName: 'page_scoped_users',
      recordId: scopedUser.id,
      creatorId,
    });
    await this.systemLogRepo.save(log);
    return appointment;
  }

  async updateAppointment(
    id: number,
    body: UpdateAppointmentDto,
    updatedBy: number,
  ): Promise<Appointment> {
    const appointment = await this.appointmentsRepo.findOne({ id });
    if (!appointment) throw new NotFoundException('Lịch hẹn không tồn tại!');
    const changes = [appointment.appointmentTime, appointment.content];
    const beforeChanges = [`isContacted: ${appointment?.isContacted}`, ...changes];
    const afterChanges = [`isContacted: ${body?.isContacted}`, ...changes];
    appointment.isContacted = body?.isContacted;
    appointment.updatedBy = updatedBy;
    const log = plainToInstance(SystemLog, {
      action: LogAction.UPDATE,
      event: 'Change',
      type: `Appointment's status`,
      changes,
      beforeChanges: beforeChanges,
      afterChanges: afterChanges,
      tableName: 'page_scoped_users',
      recordId: appointment.psId,
      creatorId: appointment.creatorId,
    });
    await this.systemLogRepo.save(log);
    return this.appointmentsRepo.save(appointment);
  }

  async deleteAppointment(id: number, updatedBy: number): Promise<UpdateResult> {
    const appointment = await this.appointmentsRepo.findOne({ id });
    if (!appointment) throw new NotFoundException('Lịch hẹn không tồn tại!');

    return this.appointmentsRepo.update(
      { id },
      {
        deletedAt: new Date(),
        updatedBy,
      },
    );
  }

  async getListAppointments(filter?: AppointmentsFilter): Promise<Appointment[]> {
    const qb = this.appointmentsRepo
      .createQueryBuilder('a')
      .leftJoin('a.scopedUser', 'su')
      .select([
        'a.id',
        'a.content',
        'a.careId',
        'a.scopedUserId',
        'a.pageId',
        'a.appointmentTime',
        'a.isContacted',
        'a.creatorId',
      ])
      .addSelect(['su.id', 'su.name'])
      .orderBy('a.appointment_time', 'ASC');
    const {
      scopedUserIds,
      excludeScopedUserIds,
      pageGroupIds,
      pageIds,
      from,
      to,
      currentUserIds,
    } = filter;
    if (from) qb.andWhere('a.appointment_time >= :from', { from });
    if (to) qb.andWhere('a.appointment_time <= :to', { to });
    if (pageIds) qb.andWhere('a.page_id IN (:...pageIds)', { pageIds });
    if (pageGroupIds)
      qb.innerJoin(
        'fanpage',
        'page',
        'page.id = a.page_id AND page.group_id IN (:...pageGroupIds)',
        { pageGroupIds },
      );
    if (scopedUserIds) qb.andWhere('su.id IN (:...scopedUserIds)', { scopedUserIds });
    if (excludeScopedUserIds)
      qb.andWhere('su.id NOT IN (:...excludeScopedUserIds)', { excludeScopedUserIds });
    if (currentUserIds) qb.andWhere('su.care_user_id IN (:...currentUserIds)', { currentUserIds });
    const appointments = await qb.getMany();
    return appointments;
  }

  async getAllAppointments(scopedUserId: string, companyId: number): Promise<Appointment[]> {
    const qb = this.appointmentsRepo
      .createQueryBuilder('a')
      .leftJoin('a.scopedUser', 'su')
      .where('su.id = :scopedUserId')
      .select(['a.id', 'a.content', 'a.appointmentTime'])
      .addSelect(['su.id', 'su.name'])
      .withDeleted()
      .setParameters({ scopedUserId, companyId });

    return qb.getMany();
  }

  async getAppointmentsByScopedUserId(
    scopedUserId: string,
    companyId: number,
  ): Promise<Appointment[]> {
    const qb = this.appointmentsRepo
      .createQueryBuilder('a')
      .leftJoin('a.scopedUser', 'su')
      .where('su.id = :scopedUserId')
      .setParameters({ scopedUserId, companyId });
    return qb.getMany();
  }
}
