import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  OnModuleInit,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Queue } from 'bull';
import { instanceToPlain, plainToInstance } from 'class-transformer';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { messageConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import StringUtils from 'core/utils/StringUtils';
import { Redis } from 'ioredis';
import { cloneDeep, find, has, isArray, isEmpty, isNil, remove, uniqBy } from 'lodash';
import * as moment from 'moment-timezone';
import { getConnection, In, IsNull, Repository, SelectQueryBuilder } from 'typeorm';
import { v4 as uuid } from 'uuid';
import { rmqErrorsHandler } from '../../../../../core/handlers/rmq-errors.handler';
import { TIME_OUT_CAMPAIGN } from '../../constants/facebook-bot.constant';
import { TYPE_DB, TYPE_USER, WORDS } from '../../constants/message-parse.constants';
import { CampaignMessageMember } from '../../entities/campaign-message-member.entity';
import { Campaign } from '../../entities/campaign.entity';
import { ConversationTag } from '../../entities/conversation-tag.entity';
import { Conversation } from '../../entities/conversation.entity';
import { FanPage } from '../../entities/fanpage.entity';
import { FanPageMember } from '../../entities/member.entity';
import { Message, parseAttachments } from '../../entities/message.entity';
import { PageCampaignAttachment } from '../../entities/page-campaign-attachment.entity';
import { PageMemberTag } from '../../entities/page-member-tag.entity';
import { Tag } from '../../entities/tag.entity';
import { UserSession } from '../../entities/user-sessions.entity';
import { User } from '../../entities/user.entity';
import { CampaignCondition } from '../../enums/campaign-condition.enum';
import {
  GenderEnum,
  GenderIndexEnum,
  WordNeedParse,
} from '../../enums/campaign-message-parse.enum';
import { CampaignStatus } from '../../enums/campaign-status.enum';
import { CampaignTypeSend } from '../../enums/campaign-type-send.enum';
import { CampaignType } from '../../enums/campaign-type.enum';
import { UserActive, UserStatus } from '../../enums/user-status.enum';
import login, { IFbApi, INSTA_APP_ID } from '../../facebook-api';
import { findAvailableKeys, IFbAttachment } from '../../facebook-api/functions/uploadAttachments';
import { BotApiService } from '../../facebook-chat/services/bot.service';
import { PagesService } from '../../facebook-chat/services/pages.service';
import {
  AttachmentDto,
  CampaignDto,
  CampaignUpdateDto,
  MessageSentDto,
  ThreadDto,
} from '../dtos/campaign.dto';
import { CheckpointVerifyDto } from '../dtos/checkpoint-verify.dto';
import { MemberInfoDto } from '../dtos/tag.dto';
import { UserLoginDto } from '../dtos/user-login.dto';
import { FilterDefault } from '../filters/filterDefault.filter';
import { PagesFilter } from '../filters/pages.filter';
import { UsersFilter } from '../filters/users.filter';
import ParseMessageUtils from '../utils/StringMessageUtils';
import { BotService } from './bot.service';
import { FacebookService } from './facebook.service';
import FilterUtils from 'core/utils/FilterUtils';
import { LarkClient } from '../../../../../core/clients/lark.client';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';
import { UpdateUserDto, UserSaveDto } from '../dtos/user-save.dto';
import { ScopedUser } from '../../entities/scoped-user.entity';
import { FACEBOOK_API_BASE_URL } from '../../constants/fb-api-endpoints.constant';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import { ErrorCode } from '../../enums/error-code.enum';
import { FACEBOOK_API_ENDPOINT } from '../../constants/fb-api-endpoints.constant';
import { FbSendMessagesContent, MessageAttachmentType } from '../dtos/facebook-send-messages.dto';
import { IFbMessage } from '../../facebook-api/functions/sendMessage';
import { FbMessagingTag, FbMessagingType } from '../../enums/fb-message.enum';
import { FileUtils } from 'core/utils/FileUtils';
import { PageScopedUserCareItem } from '../../entities/page-scoped-user-care-item.entity';
import { Product } from '../../read-entities/catalog-api/product.entity';

@Injectable()
export class MessageService implements OnModuleInit {
  constructor(
    @InjectRepository(User, messageConnection)
    private uRepository: Repository<User>,
    @InjectRepository(UserSession, messageConnection)
    private sessionRepository: Repository<UserSession>,
    @InjectRepository(FanPage, messageConnection)
    private fanPageRepository: Repository<FanPage>,
    @InjectRepository(Campaign, messageConnection)
    private spRepository: Repository<Campaign>,
    @InjectRepository(ScopedUser, messageConnection)
    private suRepository: Repository<ScopedUser>,
    @InjectRepository(Tag, messageConnection)
    private tagRepository: Repository<Tag>,
    @InjectRepository(FanPageMember, messageConnection)
    private memberRepository: Repository<FanPageMember>,
    @InjectRepository(CampaignMessageMember, messageConnection)
    private cmRepository: Repository<CampaignMessageMember>,
    @InjectRepository(PageMemberTag, messageConnection)
    private pmtRepository: Repository<PageMemberTag>,
    @InjectRepository(Conversation, messageConnection)
    private conversationRepository: Repository<Conversation>,
    @InjectRepository(ConversationTag, messageConnection)
    private conversationTagRepository: Repository<ConversationTag>,
    @InjectRepository(PageCampaignAttachment, messageConnection)
    private pcaRepository: Repository<PageCampaignAttachment>,
    @InjectQueue('campaign')
    private campaignQueue: Queue,
    private readonly amqpConnection: AmqpConnection,
    private botService: BotService,
    private fbService: FacebookService,
    private pagesService: PagesService,
    private readonly redisService: RedisCacheService,
    private readonly botApiService: BotApiService,
    @InjectRedis() private readonly redis: Redis,
    @InjectRepository(UserSession, messageConnection)
    private userSessionRepository: Repository<UserSession>,
    @InjectRepository(Message, messageConnection)
    private messageRepository: Repository<Message>,
  ) {}

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'handle-new-message-attachments',
    queue: 'handle-new-message-attachments',
    errorHandler: rmqErrorsHandler,
  })
  async handleMessageAttachments() {
    while (1) {
      const messages = await this.messageRepository
        .createQueryBuilder('m')
        .where(
          "(raw->'messaging'->0->'message'->'attachments' -> 0 -> 'payload' -> 'url'::VARCHAR IS NOT NULL OR raw->'attachments'::VARCHAR IS NOT NULL)",
        )
        .andWhere('attachments IS NULL')
        .take(100)
        .getMany();
      if (isEmpty(messages)) {
        break;
      }
      console.log(`found ${messages.length} messages`);
      for (const message of messages) {
        message._attachments = parseAttachments(message.raw);
      }
      const shouldUpdateMessages = messages.filter(i => !isEmpty(i._attachments));
      console.log(
        `should update ${shouldUpdateMessages.length} messages`,
        messages[0]._attachments,
        messages[0].raw.attachments.data,
      );
      console.time('save message');
      await this.messageRepository.save(shouldUpdateMessages);
      await this.messageRepository.save(
        messages
          .filter(i => isEmpty(i._attachments))
          .map(i => {
            i.raw = null;
            return i;
          }),
      );
      console.timeEnd('save message');
      console.log('updated');
      if (messages.length < 100) {
        break;
      }
    }
    return new Nack(false);
  }

  async getConnectedPages(from?: Date) {
    const qb = this.fanPageRepository
      .createQueryBuilder()
      .select('id')
      .where('project_id IS NOT NULL');
    if (from) {
      qb.andWhere('updated_at >= :from', { from });
    }
    if (process.env.FB_COMPANY_ID) {
      qb.andWhere('company_id = :companyId', { companyId: process.env.FB_COMPANY_ID });
    }
    return (await qb.getRawMany()).map(i => i.id);
  }

  async handleCheckpoint(id: string, verifyData: CheckpointVerifyDto) {
    const user = await this.uRepository.findOne(id);

    let ticketData: {
      form: Record<string, any>;
      url: string;
      id: string;
      codeField: string;
      captcha: string;
      unsetContactPoint?: boolean;
    };
    if (verifyData && verifyData.ticket) {
      ticketData = JSON.parse(await this.redis.get(verifyData.ticket));
      if (!ticketData) {
        throw new BadRequestException('Yêu cầu đã hết hạn thực hiện');
      }
      if (ticketData.id !== id) {
        throw new BadRequestException('Tài khoản không hợp lệ');
      }
      ticketData.form[ticketData.codeField] = verifyData.code;
      ticketData.unsetContactPoint = verifyData.unsetContactPoint;
    }
    const api = await this.botApiService.getUserApi(id);
    const { response: data, error } = await api.getCheckpointStatus(ticketData);
    if (error) {
      throw new BadRequestException(error);
    }
    if (data.html) {
      return data;
    }
    if (data.url?.includes('/search/')) {
      if (data.newPassword) {
        await this.uRepository.update(id, { password: data.newPassword });
      }
      await this.userSessionRepository.update({ user_id: id, ip: global.currentIp }, { status: 1 });
      return this.pagesService.updateUserPages(api, user.companyId);
    }
    if (data.form && data.url && data.codeField) {
      data.id = id;
      const ticket = Buffer.from(uuid()).toString('base64');
      await this.redis.set(ticket, JSON.stringify(data), 'EX', 300);
      let user = {};
      if (data.type === 'email') {
        user = await this.uRepository.findOne({ id }, { select: ['email'] });
      }
      return {
        ticket,
        ...user,
        captcha: data.captcha,
        codeField: data.codeField,
        type: data.type,
        image: data.image,
      };
    }
  }

  async onModuleInit() {
    if (!!process.env.ENV && process.env.ENV == 'stg') {
      console.log('init-job-campaign-with-start-server');
      await this.syncJobCampaign();

      const jobName = 'init-job-campaign';
      const repeatable = await this.campaignQueue.getRepeatableJobs();

      for (const job1 of repeatable) {
        if (job1.id !== jobName) {
          continue;
        }
        await this.campaignQueue.removeRepeatableByKey(job1.key);
      }
      const queue = await this.campaignQueue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            cron: '0 0 * * * *',
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: false,
        },
      );
      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm'),
      );
    }
  }

  async syncJobCampaign() {
    const campaigns = await this.spRepository.find({
      where: qb => {
        qb.where('status != :status', { status: CampaignStatus.end });
        qb.andWhere({
          typeSent: CampaignTypeSend.customTime,
          // timeEnd: MoreThanOrEqual(new Date(moment().valueOf()))
        });
      },
    });
    const jobName = 'campaign-inbox-member-page';
    const repeatable = await this.campaignQueue.getRepeatableJobs();

    // console.log('job-campaign-process', campaigns);
    console.log('repeatable', repeatable);
    for (const job1 of repeatable) {
      if (job1.name !== jobName) {
        continue;
      }
      await this.campaignQueue.removeRepeatableByKey(job1.key);
    }

    if (!!campaigns) {
      const updateParams = [];
      campaigns.forEach(async campaign => {
        if (moment(campaign?.timeEnd).add(TIME_OUT_CAMPAIGN, 'hours') > moment()) {
          try {
            const queue = await this.campaignQueue.add(
              jobName,
              {
                id: campaign?.id,
                startDate: moment().isSameOrBefore(moment(campaign?.timeStart))
                  ? moment(campaign?.timeStart)
                  : moment(),
                endDate: moment(campaign?.timeEnd),
                repeat: campaign?.repeat,
              },
              {
                attempts: 3,
                repeat: {
                  cron: `0 ${moment(campaign?.timeSent, 'hh:mm:ss').format('mm')} ${moment(
                    campaign?.timeSent,
                    'hh:mm:ss',
                  ).format('HH')} * * *`,
                  tz: 'Asia/Ho_Chi_Minh',
                  startDate: campaign?.timeStart,
                  endDate: campaign?.timeEnd,
                },
                jobId: campaign?.id,
                removeOnComplete: false,
                removeOnFail: false,
              },
            );
            console.log(
              campaign?.id +
                ' - ' +
                jobName +
                ' will run at ' +
                moment(queue.timestamp + queue.opts.delay)
                  .tz('Asia/Ho_Chi_Minh')
                  .format('DD/MM/yyyy HH:mm'),
            );
          } catch (error) {
            console.log('error-create-job', error);
          }
        } else {
          if (!campaign?.histories) campaign.histories = [];
          campaign.histories.push({
            error: null,
            message: 'Job đóng',
            time: moment().valueOf(),
          });
          campaign.status = CampaignStatus.end;
          updateParams.push(campaign);
        }
      });
      if (updateParams.length > 0) await this.spRepository.save(updateParams);
    }
    return;
  }

  async takeUser(userId: string, requestUser: { id: number; companyId: number }) {
    const user = await this.uRepository.findOne({
      id: userId,
      companyId: requestUser.companyId,
    });
    if (!user) {
      throw new BadRequestException('User does not exist');
    }
    if (user.carePageId === requestUser.id) {
      return instanceToPlain(user, { groups: ['show'] });
    }
    if (user.carePageId) {
      throw new ForbiddenException('Via đã thuộc sở hữu của nhân viên khác');
    }
    const res = await this.uRepository.update(
      {
        id: userId,
        carePageId: IsNull(),
      },
      {
        carePageId: requestUser.id,
      },
    );
    if (res.affected) return instanceToPlain(user, { groups: ['show'] });
  }

  async findUsers(
    filter: UsersFilter,
    pagination: PaginationOptions,
    companyId: number,
    userId: number,
  ): Promise<[User[], number]> {
    const qb = this.uRepository
      .createQueryBuilder('u')
      .where(`u.companyId = :companyId`, { companyId })
      .leftJoinAndSelect('u.pages', 'pages')
      .unScope('pages');

    if (pagination) qb.skip(pagination.skip).take(pagination.limit);

    if (filter.available) {
      qb.andWhere('(u.care_page_id IS NULL OR u.care_page_id = :carePageId)', {
        carePageId: userId,
      });
    }

    if (filter.invitePageId) {
      return qb
        .innerJoin(
          subQuery => {
            return subQuery
              .from('user_page', 'up')
              .select('user_id', 'id')
              .addSelect('COUNT(*)', 'count')
              .setParameter('pageId', filter.invitePageId)
              .groupBy('user_id')
              .having('(SUM(CASE WHEN "up"."page_id" = :pageId THEN 1 ELSE 0 END) = 0)');
          },
          'p',
          'p.id = u.id',
        )
        .addSelect('p.count', 'page_count')
        .orderBy('page_count', 'ASC')
        .innerJoin(UserSession, 'us', 'us.ip = :ip AND us.user_id = u.id AND us.status = 1', {
          ip: global.currentIp,
        })
        .getManyAndCount();
    }

    if (filter?.name)
      qb.andWhere(
        `(u.name ILIKE :name OR cast(u.id as text) ILIKE :name OR pages.id::text LIKE :name)`,
        {
          name: `%${filter?.name}%`,
        },
      );

    if (!isNil(filter?.type)) qb.andWhere('u.type = :type', { type: filter?.type });

    if (!isEmpty(filter?.projectIds)) {
      qb.andWhere(`(u.projectId IN (:...projectIds) OR u.projectId IS NULL)`, {
        projectIds: filter.projectIds,
      });
    }

    const [data, count] = await qb
      .leftJoinAndSelect('u.sessions', 'sessions', 'sessions.ip = :ip')
      .setParameter('ip', global.currentIp)
      .leftJoin(
        subQuery => {
          return subQuery
            .from('user_page', 'up')
            .select('user_id', 'id')
            .addSelect('COUNT(*)', 'count')
            .groupBy('user_id');
        },
        'p',
        'p.id = u.id',
      )
      .addSelect('COALESCE(p.count, 0)', 'page_count')
      .addSelect('COALESCE(sessions.status, -1)', 'us_status')
      .orderBy('us_status', 'DESC')
      .addOrderBy('page_count', 'ASC')
      .getManyAndCount();

    console.log('qb', qb.getQueryAndParameters());

    for (const item of data) {
      if (item.pageCount) item.pageCount = Number(item.pageCount);
    }

    return [data, count];
  }

  async findCampaign(
    request,
    pagination: PaginationOptions,
    query: FilterDefault,
    headers?: Record<string, string>,
  ): Promise<[Campaign[], number]> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const sql = this.spRepository
      .createQueryBuilder('campaign')
      .andWhere('campaign.companyId = :companyId', { companyId })
      .skip(pagination?.skip)
      .take(pagination?.limit)
      .leftJoinAndSelect('campaign.pages', 'pages')
      .leftJoinAndSelect('campaign.tags', 'tags')
      .orderBy('campaign.createdAt', 'DESC');
    // sql.leftJoin(
    //   '(SELECT COUNT(DISTINCT member_id) as total, SUM(qty) as message, campaign_id FROM campaign_message_member GROUP BY campaign_id)',
    //   'cmm',
    //   'cmm.campaign_id = campaign.id',
    // );

    // sql.addSelect('cmm.total', 'campaign_total_reach');
    // sql.addSelect('cmm.message', 'campaign_total_sent');

    if (query?.pageIds)
      sql.where('pages.id IN (:...pageIds)', {
        pageIds: isArray(query?.pageIds) ? query?.pageIds : [query?.pageIds],
      });

    if (query?.name) {
      sql.innerJoin(
        'campaign.pages',
        'cpages',
        'CAST(cpages.id AS VARCHAR) = :id OR cpages.name ilike :search OR campaign.name ILIKE :search',
        {
          id: query?.name,
          search: `%${query?.name}%`,
        },
      );
    }

    // if (query?.pageId) sql.andWhere('pages.id = :pageId', { pageId: query?.pageId });

    if (query?.typeSent == CampaignTypeSend.customTime)
      sql.andWhere('campaign.typeSent = :typeSent', {
        typeSent: CampaignTypeSend.customTime,
      });

    if (query?.typeSent == CampaignTypeSend.now)
      sql.andWhere('campaign.typeSent != :typeSent', {
        typeSent: CampaignTypeSend.customTime,
      });

    if (query?.type == CampaignType.active) {
      sql.andWhere(
        '((campaign.timeStart <= :now and campaign.typeSent = :typeSent and campaign.status = :status) OR (campaign.status = :status and campaign.typeSent = :typeSentNow))',
        {
          now: new Date(),
          typeSent: CampaignTypeSend.customTime,
          status: CampaignStatus.active,
          typeSentNow: CampaignTypeSend.now,
        },
      );
    }
    if (query?.type == CampaignType.pending) {
      sql.andWhere('campaign.status = :status', {
        status: CampaignStatus.active,
      });
      sql.andWhere('campaign.typeSent = :typeSent', {
        typeSent: CampaignTypeSend.customTime,
      });
      sql.andWhere('campaign.timeStart > :now', {
        now: new Date().toISOString(),
      });
    }
    if (query?.type == CampaignType.end) {
      sql.andWhere('campaign.status = :status', { status: CampaignStatus.end });
    }

    if (query?.dateRangeType && (query?.from || query?.to)) {
      switch (query?.dateRangeType) {
        case 'applicationTime': {
          if (query?.from)
            sql.andWhere('campaign.time_start >= :from', {
              from: query?.from,
            });
          if (query?.to)
            sql.andWhere('campaign.time_end <= :to ', {
              to: query?.to,
            });
          break;
        }
        case 'creationTime': {
          if (query?.from)
            sql.andWhere('campaign.created_at >= :from ', {
              from: query?.from,
            });
          if (query?.to)
            sql.andWhere('campaign.created_at <= :to ', {
              to: query?.to,
            });
          break;
        }
      }
    }

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds))
        sql.andWhere('campaign.countryId IN (:...countryIds)', { countryIds });
    }

    // console.log(await sql.getManyAndCount());
    const data = await sql.getManyAndCount();
    if (data[0] && data[0].length > 0) {
      const counts = await this.spRepository.query(
        ` SELECT COUNT(DISTINCT member_id) as total, SUM(qty) as message, campaign_id as id
          FROM campaign_message_member
          WHERE campaign_id IN (${data[0]
            .map(item => {
              return Number(item.id);
            })
            .toString()})
          GROUP BY campaign_id `,
      );
      const lookupData = [];
      if (!!counts)
        counts.forEach(e => {
          lookupData[e?.id] = e;
        });

      data[0] = data[0].map(i => {
        const prev = {
          ...i,
          totalReach: lookupData[i?.id]?.total ? lookupData[i?.id]?.total : 0,
          totalSent: lookupData[i?.id]?.message ? lookupData[i?.id]?.message : 0,
        };
        return prev;
      });
    }

    return data;
  }

  async countCampaign(
    request,
    pagination: PaginationOptions,
    query: FilterDefault,
    headers?: Record<string, string>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const sql = this.spRepository
      .createQueryBuilder('campaign')
      .where('campaign.companyId = :companyId', { companyId })
      .leftJoinAndSelect('campaign.pages', 'pages');

    const parameters = [];
    if (query?.pageId) {
      sql.where('pages.id = :pageId', { pageId: query?.pageId });
      parameters.push(query?.pageId);
    }

    if (query?.pageIds) {
      sql.andWhere('pages.id IN (:...pageIds)', {
        pageIds: isArray(query?.pageIds) ? query?.pageIds : [query?.pageIds],
      });
      parameters.push(query?.pageIds);
    }
    if (query?.name) {
      const id = query?.name;
      const search = `%${query?.name}%`;
      sql.innerJoin(
        'campaign.pages',
        'cpages',
        'CAST(cpages.id AS VARCHAR) = :id OR cpages.name ilike :search OR campaign.name ILIKE :search',
        { id: id, search: search },
      );
      // sql.andWhere({ name: ILike(`%${query?.name}%`) });
      parameters.push(id, search, search);
    }

    parameters.push(companyId);

    if (query?.typeSent == CampaignTypeSend.customTime) {
      sql.andWhere('campaign.typeSent = :typeSent', {
        typeSent: CampaignTypeSend.customTime,
      });
      parameters.push(query?.typeSent);
    }

    if (query?.typeSent == CampaignTypeSend.now) {
      sql.andWhere('campaign.typeSent = :typeSent', {
        typeSent: CampaignTypeSend.now,
      });
      parameters.push(query?.typeSent);
    }

    if (query?.dateRangeType && (query?.from || query?.to)) {
      switch (query?.dateRangeType) {
        case 'applicationTime': {
          if (query?.from)
            sql.andWhere('campaign.time_start >= :from', {
              from: query?.from,
            });
          if (query?.to)
            sql.andWhere('campaign.time_end <= :to ', {
              to: query?.to,
            });

          parameters.push(query.from, query.to);
          break;
        }
        case 'creationTime': {
          if (query?.from)
            sql.andWhere('campaign.created_at >= :from ', {
              from: query?.from,
            });
          if (query?.to)
            sql.andWhere('campaign.created_at <= :to ', {
              to: query?.to,
            });
          parameters.push(query.from, query.to);

          break;
        }
      }
    }

    if (!isNil(headers) && !isNil(headers['country-ids'])) {
      sql.andWhere('campaign.countryId = :countryId', {
        countryId: headers['country-ids'],
      });
      parameters.push(headers['country-ids']);
    }

    const statuses = {
      end: '(campaign.status = ' + CampaignStatus.end + ')',
      active:
        '((campaign.status = ' +
        CampaignStatus.active +
        ' and campaign.typeSent = ' +
        CampaignTypeSend.now +
        ') OR (campaign.status = ' +
        CampaignStatus.active +
        ' and campaign.typeSent = ' +
        CampaignTypeSend.customTime +
        " and campaign.timeStart <= '" +
        new Date().toISOString() +
        "'))",
      pending:
        '(campaign.status = ' +
        CampaignStatus.active +
        ' and campaign.typeSent = ' +
        CampaignTypeSend.customTime +
        " and campaign.timeStart > '" +
        new Date().toISOString() +
        "')",
    };

    let select = '(CASE';
    for (const key of Object.keys(statuses)) {
      select += ` WHEN ${statuses[key]} THEN '${key}' `;
    }
    select += 'ELSE NULL END)';

    sql.select(select, 'type');

    sql.groupBy('campaign.id');

    return await this.spRepository.query(
      ` SELECT COUNT(CP.*) as count, CP.type
        FROM (${sql.getSql()}) CP
        GROUP BY CP.type `,
      parameters,
    );
  }

  async findOneCampaign(id: number, request?: Record<string, any>): Promise<Campaign> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const campaign = await this.spRepository
      .createQueryBuilder('c')
      .leftJoinAndSelect('c.pages', 'pages')
      .leftJoinAndSelect('c.tags', 'tags')
      .where('c.id = :id')
      .andWhere('c.company_id = :companyId')
      .setParameters({ id, companyId })
      .getOne();
    return campaign;
  }

  async findAllPageName(filter: PagesFilter, headers: Record<string, string>): Promise<FanPage[]> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return [];

    const { countryIds, companyId } = filter;

    const qb = this.fanPageRepository
      .createQueryBuilder('page')
      .select(['page.id', 'page.name'])
      .andWhere('page.companyId = :companyId', { companyId })
      .orderBy('page.createdAt', 'DESC');

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      const projectIds = headers['project-ids']?.split(',');
      if (!isEmpty(countryIds)) qb.andWhere('page.countryId IN (:...countryIds)', { countryIds });
      if (!isEmpty(projectIds)) qb.andWhere('page.projectId IN (:...projectIds)', { projectIds });
    }

    return qb.getMany();
  }

  async findPages(
    filter: PagesFilter,
    pagination: PaginationOptions,
    headers: Record<string, string>,
  ): Promise<[FanPage[], number]> {
    const { countryIds, companyId } = filter;

    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    // console.log('filter', filter, headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0 && filter.isSetProjectsAndCountries) return [[], 0];

    const qb = this.fanPageRepository
      .createQueryBuilder('page')
      .where('page.companyId = :companyId', { companyId })
      .offset(pagination?.skip)
      .limit(pagination?.limit)
      .orderBy('page.createdAt', 'DESC');
    if (filter?.aiConfigId) {
      qb.andWhere('page.aiConfigId = :aiConfigId', { aiConfigId: filter.aiConfigId });
    }

    if (filter?.name) {
      let w = `page.name ILike '%${filter?.name}%'`;
      if (!isNaN(filter.name as any)) {
        // w = `(page.name ILike '%${filter?.name}%' OR page.id = ${filter.name})`;
        w = `page.id = ${filter.name}`;
      } else {
        qb.andWhere(`page.country_id IS NOT NULL AND page.project_id IS NOT NULL`);
      }
      qb.andWhere(w);
    }

    if (filter?.pageIds)
      qb.andWhere('page.id IN (:...pageIds)', {
        pageIds: isArray(filter?.pageIds) ? filter?.pageIds : [filter?.pageIds],
      });
    // sql.addSelect((subQuery) => {
    //   return subQuery
    //     .select('COUNT(*) as total_campaign')
    //     .from('campaign_page', 'cp')
    //     .where('cp.page_id = page.id')
    //     .orderBy('id', 'DESC');
    // }, 'totalCampaign');

    if (filter?.configChatbot) {
      if (filter.configChatbot.toString() == 'true') {
        qb.innerJoin(
          '(SELECT page_id from scoped_user_pages GROUP BY page_id)',
          'sup',
          '"sup".page_id = "page".id',
        );
      } else {
        qb.leftJoin(
          '(SELECT page_id from scoped_user_pages GROUP BY page_id)',
          'sup',
          '"sup".page_id = "page".id',
        );
        qb.andWhere('"sup".page_id IS NULL');
      }
    } else {
      qb.leftJoin(
        '(SELECT page_id from scoped_user_pages GROUP BY page_id)',
        'sup',
        '"sup".page_id = "page".id',
      );
    }

    qb.addSelect('(CASE WHEN sup.page_id is not null THEN true ELSE false END) as config_chatbot');

    qb.leftJoin(
      '(SELECT COUNT(*) as total, page_id FROM "campaign_page" LEFT JOIN campaign ON campaign.id = campaign_page.campaign_id WHERE campaign.status = ' +
        CampaignStatus.active +
        ' GROUP BY page_id)',
      'cp',
      '"cp".page_id = "page".id',
    );
    qb.addSelect('cp.total as total_campaign');

    qb.leftJoin(
      '(SELECT COUNT(DISTINCT u.id) as total, page_id FROM "user_page" LEFT JOIN "user" u ON u.id = user_page.user_id INNER JOIN user_sessions s ON s.user_id = u.id AND s.ip = :ip AND s.status = 1 GROUP BY page_id)',
      'up',
      '"up".page_id = "page".id',
      {
        ip: global.currentIp,
      },
    );
    qb.addSelect('up.total as total_user')
      .orderBy('total_user', 'DESC', 'NULLS LAST')
      .addOrderBy('page.id', 'ASC');

    if (filter.isGrouped === false) {
      qb.andWhere('page.groupId IS NULL');
    }
    if (filter.isGrouped === true) {
      qb.andWhere('page.groupId IS NOT NULL');
    }

    if (!isNil(filter.isSetProjectsAndCountries)) {
      if (filter.isSetProjectsAndCountries) {
        if (!isEmpty(projectIds)) {
          if (!filter?.name) qb.andWhere('page.projectId IN (:...projectIds)', { projectIds });
        } else qb.andWhere('page.countryId IS NOT NULL');
        if (!isEmpty(countryIds)) {
          if (!filter?.name) qb.andWhere('page.countryId IN (:...countryIds)', { countryIds });
        } else qb.andWhere('page.productId IS NOT NULL');
      } else {
        qb.andWhere('page.countryId IS NULL');
        qb.andWhere('page.productId IS NULL');
      }
    } else {
      if (!isEmpty(projectIds) && !filter?.name)
        qb.andWhere('page.projectId IN (:...projectIds)', { projectIds });
      if (!isEmpty(countryIds) && !filter?.name)
        qb.andWhere('page.countryId IN (:...countryIds)', { countryIds });
    }

    if (!isEmpty(filter?.productIds)) {
      if (filter?.productIds[0] === -1) {
        console.log(`query?.productId`, filter?.productIds);
        qb.andWhere('page.productId IS NULL');
      } else {
        qb.andWhere('page.productId IN (:...productId)', {
          productId: filter.productIds,
        });
      }
    }

    if (!isEmpty(filter?.marketerIds)) {
      qb.andWhere('page.marketerId IN (:...marketerIds)', {
        marketerIds: filter.marketerIds,
      });
    }

    if (filter?.getUnreadConversationCount) {
      qb.leftJoin(
        '(SELECT COUNT(*) as count, page_id FROM conversations WHERE unread = true GROUP BY page_id)',
        'conversations',
        'conversations.page_id = page.id',
      );
      qb.addSelect('conversations.count', 'unread_conversations');
    }

    if (filter?.assignedKeywordGroup) {
      qb.andWhere('page.keyword_group_id IS NOT NULL');
    }

    if (filter?.getPageGroup) {
      qb.leftJoin('page.group', 'group').addSelect([
        'group.id',
        'group.name',
        'group.configurationGroupId',
      ]);
    }

    return qb.getManyAndCount();
  }

  async findPagesV2(
    filter: PagesFilter,
    pagination: PaginationOptions,
    headers: Record<string, string>,
  ): Promise<[FanPage[], number]> {
    let { countryIds, companyId, projectIds } = filter;

    const qb = this.fanPageRepository
      .createQueryBuilder('page')
      .where('page.companyId = :companyId', { companyId })
      .offset(pagination?.skip)
      .limit(pagination?.limit);

    // qb.addSelect('page.scopedUsers', 'scopedUsers');
    if (filter.getVia) {
      qb.leftJoinAndSelect('page.scopedUsers', 'scopedUser')
      qb.leftJoinAndSelect('page.scopedUserPages', 'scopedUserPage')
    }
    
    let orderBy = 'page.updatedAt';
    let sortMode: 'ASC' | 'DESC' = 'DESC';

    if (!isNil(filter.orderBy)) {
      orderBy = `page.${filter.orderBy}`;
      if (!isNil(filter.sort)) {
        sortMode = `${filter.sort}` as 'ASC' | 'DESC';
      }
    }
    qb.orderBy(orderBy, sortMode);

    qb.leftJoin(
      qb.subQuery()
        .select(`page_id`)
        .addSelect(`COUNT(*) FILTER (
          WHERE (updated_at + INTERVAL '90 days') > (CURRENT_TIMESTAMP + INTERVAL '3 days')
        )::int`, 'tokens_active')  // Match these aliases
        .addSelect(`COUNT(*) FILTER (
          WHERE (updated_at + INTERVAL '90 days') <= (CURRENT_TIMESTAMP + INTERVAL '3 days')
          AND (updated_at + INTERVAL '90 days') > CURRENT_TIMESTAMP
        )::int`, 'tokens_expiring_soon')  // with these in the addSelect below
        .addSelect(`COUNT(*) FILTER (
          WHERE (updated_at + INTERVAL '90 days') <= CURRENT_TIMESTAMP
        )::int`, 'tokens_expired')
        .from('scoped_user_pages', 'sup')
        .groupBy('page_id')
        .getQuery(),
      'tokens',
      'tokens.page_id = page.id'
    );
    qb.addSelect('COALESCE(tokens.tokens_active, 0)', 'page_tokensActive')
    .addSelect('COALESCE(tokens.tokens_expiring_soon, 0)', 'page_tokensExpiringSoon')
    .addSelect('COALESCE(tokens.tokens_expired, 0)', 'page_tokensExpired');

    switch (filter.tab) {
      case 'linked':
        qb.andWhere('page.productId IS NOT NULL');
        break;
      case 'all':
        break;
      case 'unlinked':
        qb.andWhere('page.productId IS NULL');
        break;
      default:
        throw new BadRequestException('invalid filter');
    }

    if (filter?.name) {
      const name = StringUtils.nonAccentVietnamese(filter?.name);
      let w = `unaccent(page.name) ILike '%${name}%'`;
      if (!isNaN(filter.name as any)) {
        w = `(page.name ILike '%${filter?.name}%' OR page.id = ${filter.name})`;
        // w = `page.id = ${filter.name}`;
      }
      qb.andWhere(w);
    }

    if (filter?.pageIds)
      qb.andWhere('page.id IN (:...pageIds)', {
        pageIds: isArray(filter?.pageIds) ? filter?.pageIds : [filter?.pageIds],
      });

    if (!isEmpty(filter?.productIds)) {
      if (filter?.productIds[0] === -1) {
        console.log(`query?.productId`, filter?.productIds);
        qb.andWhere('page.productId IS NULL');
      } else {
        qb.andWhere('page.productId IN (:...productId)', {
          productId: filter.productIds,
        });
      }
    }

    if (!isEmpty(filter?.marketerIds)) {
      qb.andWhere('page.marketerId IN (:...marketerIds)', {
        marketerIds: filter.marketerIds,
      });
    }
    if (filter?.ordersType) {
      qb.andWhere('page.orderType IN (:...ordersType)', { ordersType: filter.ordersType });
    }
    const result = await qb.getManyAndCount();
    if (!result) return result;
    if (filter.getVia) {
      if (result[0] instanceof Array) {
        const validIds = []
        const userPageMap = {}
        for (const p of result[0]) {
          for (const su of p.scopedUserPages) {
            validIds.push(su.scopedUserId);
            userPageMap[su.scopedUserId] = su;
          }
        }

        result[0].forEach(p => {
          p.scopedUsers = p.scopedUsers?.filter(su => validIds.includes(su.id));
          p.scopedUsers = p.scopedUsers?.map(su => {
            const lastUserPageUpdatedAt = userPageMap[su.id]?.updatedAt
            su.expiringDate = lastUserPageUpdatedAt?.getTime() + moment.duration(90, 'days');
            su.connectedAt = lastUserPageUpdatedAt?.getTime();
            if (lastUserPageUpdatedAt && lastUserPageUpdatedAt.getTime() > su.updatedAt?.getTime()) {
              su.updatedAt = lastUserPageUpdatedAt.getTime();
            }
            return su;
          })
        });
      }
    }
    const productIds = result[0].filter(p => p.productId).map(p => p.productId);
    const data = await this.amqpConnection.request({
      exchange: 'catalog-service-products',
      routingKey: 'find-products-by-ids',
      payload: { ids: productIds },
      timeout: 10000,
    });
    const products = data.data as Product[];
    const hashMapProductNameByProductId = products.reduce((prev, item) => {
      prev[item.id] = item.name;
      return prev;
    }, {});
    result[0].forEach(p => {
      p.productName = hashMapProductNameByProductId[p.productId];
    });
    return result;
  }

  async getPage(id: string): Promise<User[]> {
    return (
      this.uRepository
        .createQueryBuilder('u')
        // .where({
        //   isSubscriber: false,
        // })
        .innerJoin('u.pages', 'p', 'p.id = :pageId', { pageId: id })
        .innerJoinAndSelect('u.sessions', 's', '(s.ip = :ip and s.status = :status)', {
          ip: global.currentIp,
          status: UserStatus.active,
        })
        .getMany()
    );
  }

  // async findTags(
  //   request,
  //   pagination: PaginationOptions,
  //   query: FilterDefault,
  // ): Promise<[Tags[], number]> {
  //   const sql = this.tagRepository
  //     .createQueryBuilder('tag')
  //     .skip(pagination?.skip)
  //     .take(pagination?.limit)
  //     .leftJoinAndSelect('tag.tags', 'tags')
  //     .orderBy('tag.createdAt', 'DESC');
  //
  //   if (query?.pageIds)
  //     sql.where('tags.pageId IN (:...pageIds)', {
  //       pageIds: isArray(query?.pageIds) ? query?.pageIds : [query?.pageIds],
  //     });
  //
  //   return sql.getManyAndCount();
  // }

  // async createTags(data: TagsDto): Promise<Tags[]> {
  //   if (!data?.tags || data?.tags.length == 0) {
  //     await this.pmtRepository
  //       .createQueryBuilder()
  //       .delete()
  //       .from(PageMemberTag)
  //       .where('pageId = :pageId and memberId = :memberId', {
  //         pageId: data?.pageId,
  //         memberId: data?.memberId,
  //       })
  //       .execute();
  //
  //     return [];
  //   } else {
  //     let [member, tags] = await Promise.all([
  //       this.memberRepository.findOne(data?.memberId),
  //       this.tagRepository.find({
  //         where: qb => {
  //           qb.where({ name: In(data?.tags) });
  //         },
  //         relations: ['tags'],
  //       }),
  //     ]);
  //
  //     const params = [];
  //
  //     data?.tags.forEach(item => {
  //       let tag = new Tags();
  //       tag.name = item;
  //
  //       const findTag = find(tags, function(o) {
  //         return o.name == item;
  //       });
  //       if (!!findTag) {
  //         tag = findTag;
  //         const findPageTag = find(tag?.tags, function(o) {
  //           return o.memberId == data?.memberId && o.pageId == data?.pageId;
  //         });
  //         if (!findPageTag) tag?.tags.push(findPageTag);
  //       } else {
  //         const pmt = new PageMemberTag();
  //         // pmt.memberId = data?.memberId;
  //         if (!member) {
  //           member = new FanPageMember();
  //           member.id = data?.memberId;
  //         }
  //         member.info = {
  //           ...data?.memberInfo,
  //           accountType: TYPE_USER,
  //         };
  //         pmt.member = member;
  //         // pmt.threadId = data?.threadId;
  //         pmt.pageId = data?.pageId;
  //         tag.tags = [pmt];
  //       }
  //       params.push(tag);
  //     });
  //
  //     return this.tagRepository.save(params);
  //   }
  // }

  async createCampaign(
    data: CampaignDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Campaign> {
    const companyId = request?.user?.companyId;
    const creatorId = request?.user?.id;

    if (!companyId) throw new ForbiddenException();

    if (data.limitLastTime && !data.timeRange) {
      data.timeRange = {
        until: moment()
          .subtract(data.limitLastTime, 'h')
          .toDate(),
      };
    }

    if (data?.typeSent == CampaignTypeSend.customTime) {
      if (!data?.repeat || !data?.timeSent)
        throw new BadRequestException('Tần suất gửi, thời gian gửi là bắt buộc');
      if (!data?.timeStart || !data?.timeEnd) {
        throw new BadRequestException('Thời gian bắt đầu, kết thúc là bắt buộc');
      }
    }

    if (data?.condition.length == 0) {
      throw new BadRequestException('Điều kiện nhận tin nhắn là bắt buộc');
    } else {
      if (
        data?.condition.includes(CampaignCondition.haveTag) &&
        (!data?.tags || data?.tags.length < 1)
      )
        throw new BadRequestException('Danh sách tag là bắt buộc');
      if (
        data?.condition.includes(CampaignCondition.noTag) &&
        (!data?.noTags || data?.noTags.length < 1)
      )
        throw new BadRequestException('Danh sách tag là bắt buộc');
      // if (
      //   data?.condition.some(condition => [CampaignCondition.lastMessage, CampaignCondition.lastMessageByPage].includes(condition)) &&
      //   !data?.limitLastTime
      // )
      //   throw new BadRequestException('Giới hạn giờ là bắt buộc');

      if (
        data?.condition.some(condition =>
          [CampaignCondition.lastMessageByPage, CampaignCondition.lastMessage].includes(condition),
        ) &&
        !data.timeRange &&
        !data.timeRange.until
      ) {
        throw new BadRequestException('Khoảng thời gian bắt đầu và kết thúc là bắt buộc');
      }

      if (
        data?.condition.some(condition =>
          [CampaignCondition.conversationCreationTime].includes(condition),
        ) &&
        !data.conversationCreationTimeRange &&
        (!data.conversationCreationTimeRange.until || !data.conversationCreationTimeRange.since)
      ) {
        throw new BadRequestException('Khoảng thời gian bắt đầu và kết thúc là bắt buộc');
      }

      if (
        data?.condition.includes(CampaignCondition.includeLastReasons) &&
        (!data?.lastReasonIds || data?.lastReasonIds.length < 1)
      )
        throw new BadRequestException('Danh sách lý do là bắt buộc');
      if (
        data?.condition.includes(CampaignCondition.excludeLastReasons) &&
        (!data?.excludeLastReasonIds || data?.excludeLastReasonIds.length < 1)
      )
        throw new BadRequestException('Danh sách lý do là bắt buộc');
    }

    if (!data?.message && !data?.images) throw new BadRequestException('Nội dung không hợp lệ');

    if (!headers['country-ids']) {
      throw new BadRequestException('country-ids không hợp lệ');
    } else {
      data.countryId = Number(headers['country-ids']);
    }

    const campaign = await this.saveCampaign({ ...data, companyId, creatorId }, new Campaign());

    if (!!campaign && !!campaign?.id) {
      await this.amqpConnection.publish('message-campaign-inbox', 'after-created-campaign', {
        id: campaign?.id,
      });
    } else {
      throw new BadRequestException('Không tạo được chiến dịch');
    }
    return campaign;
  }

  async uploadAtt(url: string, pageIds: string[], type = MessageAttachmentType.image) {
    const res: Record<string, any> = {};
    for (const pageId of pageIds) {
      const botApi = await this.botApiService.getPageTokenApi(pageId);
      const content: FbSendMessagesContent = {
        message: {
          attachment: {
            type: type,
            payload: {
              url: url,
              is_reusable: true,
            },
          },
        },
      };
      const rawData = await botApi({
        method: 'POST',
        url: FACEBOOK_API_ENDPOINT.MESSAGE_ATTACHMENTS(pageId),
        data: content,
        headers: { 'Content-Type': 'application/json' },
      });
      res[pageId] = rawData;
    }
    return res;
  }

  @RabbitRPC({
    exchange: 'message-campaign-inbox',
    routingKey: 'after-created-campaign',
    queue: 'ag-campaign-save-attachment-after-created',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async saveAttachmentAfterCreatedCampaign(payload) {
    console.log('payload-campaign-init', payload);

    if (!payload?.id) return new Nack(false);

    const { id } = payload;

    const campaign = await this.spRepository
      .createQueryBuilder('c')
      .leftJoin('c.pages', 'pages')
      .leftJoin('pages.users', 'users')
      .leftJoin('users.sessions', 'sessions')
      .where('c.id = :id', { id })
      .andWhere('sessions.status = :status', { status: UserStatus.active })
      .addSelect(['pages.id'])
      .getOne();

    if (!!campaign?.id) {
      const pageIds = campaign?.pages.map(item => {
        return (item?.id).toString();
      });
      if (!campaign?.histories) campaign.histories = [];

      if (!isEmpty(campaign.images)) {
        const [images, videos] = campaign.images.reduce(
          (prev, it) => {
            const fileType = FileUtils.getFileTypeFromPath(it);
            switch (fileType) {
              case 'image':
                prev[0].push(it);
                break;
              case 'video':
                prev[1].push(it);
                break;
              default:
                break;
            }
            return prev;
          },
          [[], []],
        );

        console.log(`videos`, videos);

        const atts = [];

        try {
          if (!isEmpty(videos)) {
            for (const url of videos) {
              const pageAttachmentsLookup = await this.uploadAtt(
                url,
                pageIds,
                MessageAttachmentType.video,
              );

              for (const [key, value] of Object.entries(pageAttachmentsLookup)) {
                const pca = new PageCampaignAttachment();
                const att = new AttachmentDto();
                att.attachment_id = value?.attachment_id;
                att.filetype = MessageAttachmentType.video;

                pca.attachment = att;
                pca.attachmentId = value?.attachment_id;
                pca.pageId = key;
                pca.campaignId = campaign?.id;
                atts.push(pca);
              }
            }
          }
          if (images.length > 0) {
            if (images.length === 1) {
              const pageImagesLookup = await this.uploadAtt(
                images[0],
                pageIds,
                MessageAttachmentType.image,
              );

              for (const [key, value] of Object.entries(pageImagesLookup)) {
                const pca = new PageCampaignAttachment();
                const att = new AttachmentDto();
                att.attachment_id = value?.attachment_id;
                att.filetype = MessageAttachmentType.image;

                pca.attachment = att;
                pca.attachmentId = value?.attachment_id;
                pca.pageId = key;
                pca.campaignId = campaign?.id;
                atts.push(pca);
              }
            } else {
              for (const item of images) {
                const response = await axios.get(item, { responseType: 'arraybuffer' });
                const buffer = Buffer.from(response.data, 'binary');
                const extensions = TYPE_DB[response?.headers['content-type']]?.extensions;
                if (extensions) {
                  const filename =
                    moment()
                      .valueOf()
                      .toString() +
                    '.' +
                    extensions[0];
                  const attachments = await this.botService.uploadAttachment(
                    {
                      buffer,
                      originalname: filename,
                      name: filename,
                    },
                    pageIds,
                  );

                  if (!!attachments)
                    for (const [key, value] of Object.entries(attachments)) {
                      const attachmentIdKey = findAvailableKeys(value);

                      const pca = new PageCampaignAttachment();
                      const att = new AttachmentDto();
                      att.video_id = value?.video_id;
                      att.image_id = value?.image_id;
                      att.filename = value?.filename;
                      att.filetype = value?.filetype;
                      att.src = value?.src;
                      att.fbid = value?.fbid;

                      pca.attachment = att;
                      pca.attachmentId = value?.[attachmentIdKey];
                      pca.pageId = key;
                      pca.campaignId = campaign?.id;
                      atts.push(pca);
                    }
                  console.log('delay sent attachment 1.5s', moment().valueOf());

                  await new Promise(resolve => setTimeout(resolve, 1500));
                }
              }
            }
          }

          if (atts.length > 0) {
            await this.pcaRepository
              .createQueryBuilder()
              .delete()
              .from(PageCampaignAttachment)
              .where('campaignId = :campaignId', { campaignId: campaign?.id })
              .execute();

            await this.pcaRepository.save(atts);
          }
        } catch (error) {
          console.log(error);
          campaign.histories.push({
            error,
            message: 'Không tạo được ảnh',
            time: moment().valueOf(),
          });
          await this.spRepository.update(
            { id: campaign?.id },
            {
              // status: CampaignStatus.end,
              histories: campaign?.histories,
            },
          );
          // return new Nack(false);
        }
      }

      if (campaign?.typeSent == CampaignTypeSend.now) {
        await this.jobCampaign(campaign?.id);
      } else {
        if (campaign?.timeSent && campaign?.repeat && campaign?.timeStart && campaign?.timeEnd) {
          const jobName = 'campaign-inbox-member-page';

          const repeatable = await this.campaignQueue.getRepeatableJobs();
          for (const job1 of repeatable) {
            if (job1.id !== campaign?.id.toString()) {
              continue;
            }
            await this.campaignQueue.removeRepeatableByKey(job1.key);
          }
          try {
            const queue = await this.campaignQueue.add(
              jobName,
              {
                id: campaign?.id,
                startDate: moment().isSameOrBefore(moment(campaign?.timeStart))
                  ? moment(campaign?.timeStart)
                  : moment(),
                endDate: moment(campaign?.timeEnd),
                repeat: campaign?.repeat,
              },
              {
                attempts: 3,
                repeat: {
                  cron: `0 ${moment(campaign?.timeSent, 'hh:mm:ss').format('mm')} ${moment(
                    campaign?.timeSent,
                    'hh:mm:ss',
                  ).format('HH')} * * *`,
                  // cron: '0 */3 * * * *',
                  tz: 'Asia/Ho_Chi_Minh',
                  startDate: campaign?.timeStart,
                  endDate: campaign?.timeEnd,
                },
                jobId: campaign?.id,
                removeOnComplete: false,
                removeOnFail: false,
              },
            );
            this.campaignQueue.on('completed', function(job, result) {
              console.log('Completed: job-' + job?.data?.id);
            });

            this.campaignQueue.on('failed', async function(job, error) {
              console.log('Failed: job-' + job?.data?.id + ' Error: ' + job + error);
            });
            this.campaignQueue.on('error', function(error) {
              // An error occured.
              console.log('job-error: ' + error);
            });
            this.campaignQueue.on('failed', function(job, err) {
              // A job failed with reason `err`!
              console.log('job-error: ' + job?.data?.id + ' : ' + err);
            });

            // console.log(
            //   campaign?.id +
            //     ' - ' +
            //     jobName +
            //     ' will run at ' +
            //     moment(queue.timestamp + queue.opts.delay)
            //       .tz('Asia/Ho_Chi_Minh')
            //       .format('DD/MM/yyyy HH:mm'),
            // );
          } catch (error) {
            console.log('error', error);
            campaign.histories.push({
              error,
              message: 'Không tạo được job',
              time: moment().valueOf(),
            });
            await this.spRepository.update(
              {
                id: campaign?.id,
              },
              {
                status: CampaignStatus.end,
                histories: campaign?.histories,
              },
            );
            return new Nack(false);
          }
        }
      }
    } else {
      await this.spRepository.update(
        {
          id: campaign?.id,
        },
        {
          status: CampaignStatus.end,
          histories: [
            {
              message: 'Không tìm thấy bot',
              time: moment().valueOf(),
            },
          ],
        },
      );
    }
    return new Nack(false);
  }

  async updateCampaign(
    data: CampaignUpdateDto,
    id: number,
    request?: Record<string, any>,
  ): Promise<Campaign> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const campaign = await this.spRepository.findOne(id);
    if (!campaign) throw new NotFoundException();

    if (campaign.companyId !== companyId) throw new UnauthorizedException();

    return this.saveCampaign(data, campaign);
  }

  async saveCampaign(
    data,
    campaign: Campaign,
    type: 'create' | 'update' = 'create',
  ): Promise<Campaign> {
    if (data?.repeat) campaign.repeat = data?.repeat;
    if (data?.name) campaign.name = data?.name;
    if (data?.message) campaign.message = data?.message;
    if (data?.timeStart) campaign.timeStart = data?.timeStart;
    if (data?.timeSent) campaign.timeSent = data?.timeSent;
    if (data?.timeEnd) campaign.timeEnd = data?.timeEnd;
    if (data?.typeSent) campaign.typeSent = data?.typeSent;
    if (data?.condition) campaign.condition = data?.condition;
    if (data?.countryId) campaign.countryId = data?.countryId;
    if (data?.limitLastTime) campaign.limitLastTime = data?.limitLastTime;
    if (data?.timeRange) campaign.timeRange = data?.timeRange;
    if (data?.conversationCreationTimeRange)
      campaign.conversationCreationTimeRange = data?.conversationCreationTimeRange;
    if (data?.lastReasonIds) campaign.lastReasonIds = data?.lastReasonIds;
    if (data?.excludeLastReasonIds) campaign.excludeLastReasonIds = data?.excludeLastReasonIds;
    if (data?.tags) campaign.listTagIds = data?.tags;
    if (data?.noTags) campaign.listNoTagIds = data.noTags;
    if (data?.images) campaign.images = data?.images;
    if (data?.status && !!campaign?.id) campaign.status = data?.status;
    if (data?.companyId) campaign.companyId = data?.companyId;
    if (data?.delay) campaign.delay = data.delay;
    if (data?.creatorId) campaign.creatorId = data.creatorId;

    if (!!data?.pages && data?.pages.length > 0) {
      campaign.pages = await this.fanPageRepository.find({
        where: qb => {
          qb.andWhere({ id: In(data?.pages) });
        },
      });
    }

    return this.spRepository.save(campaign);
  }

  async jobCampaign(id: number) {
    return this.amqpConnection.publish('message-campaign-inbox', 'campaign', {
      id,
    });
  }

  @RabbitRPC({
    exchange: 'message-campaign-inbox',
    routingKey: 'campaign',
    queue: 'ag-campaign-inbox',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async campaignInboxMemberPage(payload) {
    if (!payload?.id) return new Nack(false);
    const { id } = payload;

    const campaign = await this.spRepository
      .createQueryBuilder('c')
      .leftJoin('c.pages', 'pages')
      .leftJoin('pages.users', 'users')
      .leftJoin('users.sessions', 'sessions')
      .leftJoinAndSelect('c.attachments', 'attachments')
      .where('c.id = :id', { id })
      .andWhere('c.status = :status', { status: CampaignStatus.active })
      .addSelect(['pages.id'])
      .getOne();
    console.log('payload-campaign-start', campaign);

    if (!campaign) {
      await this.closeCampaign(id);
      return new Nack(false);
    }

    if (!campaign?.message && !campaign?.images) {
      await this.spRepository.update({ id: campaign?.id }, { status: CampaignStatus.end });
      return new Nack(false);
    }
    const message = new MessageSentDto();
    message.body = campaign?.message;

    if (!!campaign)
      for (const item of campaign?.pages) {
        if (!!campaign?.attachments && campaign?.attachments.length > 0) {
          message.attachment = [];
          campaign?.attachments.forEach(o => {
            if (o.pageId == item?.id) {
              message.attachment.push(o?.attachment);
            }
          });
        }
        await this.jobCampaignGetMember(
          campaign,
          item?.id.toString(),
          Number(process.env.CAMPAIGN_LIMIT),
          // campaign?.condition.some(condition => [CampaignCondition.lastMessage, CampaignCondition.lastMessageByPage].includes(condition))
          //   ? moment()
          //     .add(-Number(campaign?.limitLastTime), 'hours')
          //     .valueOf()
          //   : null,
          undefined,
          message,
        );
      }
    // console.log(campaign,appState);
    return new Nack(false);
  }

  async closeCampaign(id: string) {
    const cp = await this.spRepository.findOne(id);
    if (!!cp) {
      if (!cp?.histories) cp.histories = [];
      cp.histories.push({
        message: 'Không tìm thấy Bot facebook',
        time: new Date().toISOString(),
      });
      if ((cp.typeSent = CampaignTypeSend.now)) cp.status = CampaignStatus.end;
      await this.spRepository.save(cp);
    }
  }

  async jobCampaignGetMember(
    campaign: Campaign,
    pageID: string,
    limit: number,
    timestamp?: number,
    message?: MessageSentDto,
  ) {
    return this.amqpConnection.publish('message-campaign-inbox', 'inbox', {
      pageID,
      limit,
      timestamp,
      message,
      campaign,
    });
  }

  @RabbitRPC({
    exchange: 'message-campaign-inbox',
    routingKey: 'inbox',
    queue: 'ag-campaign-inbox-get-member',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async campaignInboxGetMemberPage(payload) {
    const { campaign, pageID, timestamp, message } = payload;
    console.log('payload-inbox', pageID);

    const camp = await this.spRepository
      .createQueryBuilder('c')
      .where('c.id = :id', { id: campaign.id })
      .andWhere('c.status = :status', { status: CampaignStatus.active })
      .leftJoin('c.pages', 'pages')
      .addSelect(['pages.id'])
      .getOne();

    if (!camp) return new Nack(false);

    // if (data?.condition.includes(CampaignCondition.tag)) {
    //   await this.sendMessageByTag(pageID, campaign, limit, timestamp, message);
    // } else {
    // await this.sendMessageByThread(pageID, limit, timestamp, message, campaign);
    // }
    const limit = 25;

    const threadsQb = this.conversationRepository
      .createQueryBuilder('c')
      .take(limit)
      .leftJoinAndMapOne(
        'c.user',
        PageScopedUser,
        'u',
        'u.pageId = c.pageId AND u.scopedUserId = c.scopedUserId',
      )
      .where('c.pageId = :pageID', { pageID });

    if (timestamp) {
      threadsQb.andWhere('c.updatedAt <= :updatedAt', {
        updatedAt: new Date(timestamp).toISOString(),
      });
    }
    if (camp?.condition.includes(CampaignCondition.lastMessageByPage)) {
      threadsQb.andWhere('c.lastSentByPage is true');
    }

    if (camp?.condition.includes(CampaignCondition.haveTag)) {
      threadsQb.innerJoin(
        'page_scoped_users_tags',
        'sutHaveTag',
        '(sutHaveTag.scoped_user_id = u.id AND sutHaveTag.tag_id IN (:...tagIds))',
        { tagIds: camp.listTagIds },
      );
    }
    if (camp?.condition.includes(CampaignCondition.noTag)) {
      threadsQb.leftJoin(
        'page_scoped_users_tags',
        'sutNoTag',
        'sutNoTag.scoped_user_id = u.id AND sutNoTag.tag_id IN (:...noTagIds)',
        { noTagIds: camp.listNoTagIds },
      );
      threadsQb.andWhere('sutNoTag.scoped_user_id IS NULL');
    }

    if (camp?.condition.includes(CampaignCondition.includeLastReasons)) {
      this.leftJoinLastCareItem(threadsQb);
      threadsQb.andWhere(`lci.reason_id IN (:...lastReasonIds)`, {
        lastReasonIds: camp.lastReasonIds,
      });
    }
    if (camp?.condition.includes(CampaignCondition.excludeLastReasons)) {
      this.leftJoinLastCareItem(threadsQb);
      threadsQb.andWhere(`lci.reason_id NOT IN (:...excludeLastReasonIds)`, {
        excludeLastReasonIds: camp.excludeLastReasonIds,
      });
    }

    if (
      camp?.condition.some(condition =>
        [CampaignCondition.customerHavePhone, CampaignCondition.customerNoPhone].includes(
          condition,
        ),
      )
    ) {
      threadsQb.leftJoin('c.phones', 'phones');
      if (camp?.condition.includes(CampaignCondition.customerHavePhone)) {
        threadsQb.andWhere('phones.id is not null');
      } else {
        threadsQb.andWhere('phones.id is null');
      }
    }
    // if (camp.condition.includes(CampaignCondition.firstTimeMessage)) {
    //   threads.andWhere('c.createdAt >= :since AND c.createdAt <= :until', {
    //     since: camp?.timeRange?.since,
    //     until: camp?.timeRange?.until
    //   })
    // }
    if (
      camp.condition.some(condition =>
        [CampaignCondition.lastMessage, CampaignCondition.lastMessageByPage].includes(condition),
      )
    ) {
      threadsQb.andWhere('c.updatedAt <= :until', { until: camp?.timeRange?.until });
      if (camp?.timeRange?.since)
        threadsQb.andWhere('c.updatedAt >= :since', { since: camp?.timeRange?.since });
    }
    if (camp.condition.includes(CampaignCondition.conversationCreationTime)) {
      threadsQb.andWhere('c.createdAt <= :until', {
        until: camp?.conversationCreationTimeRange?.until,
      });
      if (camp?.conversationCreationTimeRange?.since)
        threadsQb.andWhere('c.createdAt >= :since', {
          since: camp?.conversationCreationTimeRange?.since,
        });
    }

    const threadList = await threadsQb
      .orderBy('c.updatedAt', 'DESC')
      .andWhere('c.userGlobalId IS NOT NULL')
      .andWhere(`c.feedId = ''`)
      .getMany();

    const data = threadList?.map(c => {
      const item = new ThreadDto();
      const user = new MemberInfoDto();
      user.accountType = TYPE_USER;
      user.userID = c.scopedUserId;
      user.name = c.user?.name;
      user.shortName = c.user?.shortName;
      user.gender = c.user?.gender;
      item.participants = [user];
      item.threadID = c.userGlobalId.toString();
      item.updatedAt = c.updatedAt;
      return item;
    });

    console.log('data', data.length);
    await this.sendMessage(campaign?.id, pageID, message, data, campaign.delay);

    return new Nack(false);
  }

  leftJoinLastCareItem(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'lci') > -1;
    if (hasJoined) return qb;

    qb.leftJoin(PageScopedUserCareItem, 'lci', 'lci.id = u.latest_care_item_id');
  }

  // async sendMessageByThread(
  //   api: IFbApi,
  //   pageID: any,
  //   limit: number,
  //   timestamp: any,
  //   message: MessageSentDto,
  //   campaign: Campaign,
  // ) {
  //   console.log('sendMessageByThread', pageID);
  //   if (!campaign?.id) return new Nack(false);
  //
  //   const $ = this;
  //   const camp = await this.spRepository.findOne(campaign?.id, {
  //     select: ['status', 'condition'],
  //     relations: ['tags'],
  //   });
  //   if (camp.status != CampaignStatus.active) {
  //     return;
  //   }
  //   console.log(camp);
  //
  //   try {
  //     api = api || (await this.botApiService.getApi(pageID));
  //
  //     limit = !!limit && limit > 0 ? limit : 25;
  //
  //     const threads = await this.conversationRepository
  //       .createQueryBuilder('c')
  //       .take(limit)
  //       .where('c.pageId = :pageID', { pageID });
  //
  //     if (timestamp) {
  //       threads.andWhere('c.updatedAt <= :updatedAt', {
  //         updatedAt: new Date(timestamp).toISOString(),
  //       });
  //     }
  //     if (camp?.condition == CampaignCondition.lastMessageByPage) {
  //       threads.andWhere('c.lastSentByPage is true');
  //     }
  //
  //     if (
  //       [CampaignCondition.haveTag, CampaignCondition.noTag].includes(camp?.condition) &&
  //       camp?.tags.length > 0
  //     ) {
  //       threads.leftJoin('c.tags', 'tags');
  //       const tagIds = camp?.tags.map(item => {
  //         return item?.id;
  //       });
  //       if (camp?.condition == CampaignCondition.haveTag) {
  //         threads.andWhere('tags.id IN (:...tagIds)', {
  //           tagIds,
  //         });
  //       } else {
  //         threads.andWhere('tags.id NOT IN (:...tagIds)', {
  //           tagIds,
  //         });
  //       }
  //     }
  //
  //     if (
  //       [CampaignCondition.customerHavePhone, CampaignCondition.customerNoPhone].includes(
  //         camp?.condition,
  //       )
  //     ) {
  //       threads.leftJoin('c.phones', 'phones');
  //       if (camp?.condition == CampaignCondition.customerHavePhone) {
  //         threads.andWhere('phones.id is not null');
  //       } else {
  //         threads.andWhere('phones.id is null');
  //       }
  //     }
  //
  //     const threadList = await threads
  //       .leftJoinAndSelect('c.user', 'u')
  //       .orderBy('c.updatedAt', 'DESC')
  //       .getMany()
  //       .catch(err => {
  //         if (err?.driverError) {
  //           console.log(err?.driverError?.detail);
  //         }
  //         new Nack(false);
  //         return err;
  //       });
  //
  //
  //     const data = threadList?.map(o => {
  //       const item = new ThreadDto();
  //       const user = new MemberInfoDto();
  //       user.accountType = TYPE_USER;
  //       user.userID = o.userGlobalId;
  //       user.name = o.user?.name;
  //       user.shortName = o.user?.shortName;
  //       user.gender = o.user?.gender;
  //       item.participants = [user];
  //       item.threadID = o.userGlobalId.toString();
  //       item.updatedAt = o.updatedAt;
  //       return item;
  //     });
  //
  //     console.log('data', data.length)
  //     await this.sendMessage(campaign?.id, pageID, message, data, campaign.delay);
  //   } catch (error) {
  //     console.log('error', error);
  //     await this.jobCampaignGetMember(
  //       campaign,
  //       pageID,
  //       limit,
  //       timestamp ? timestamp : null,
  //       message,
  //     );
  //   }
  // }
  @RabbitRPC({
    exchange: 'delay-exchange',
    routingKey: 'handle-send-message',
    queue: 'campaign-handle-send-message',
    errorHandler: rmqErrorsHandler,
    // queueOptions: {
    //   channel: 'crawler'
    // }
  })
  async handleSendMessage(payload: {
    campaignId: number;
    pageID: string;
    mess: MessageSentDto;
    data: ThreadDto[];
    delay: number;
  }) {
    console.log(`send message payload`, payload);
    const handling = async () => {
      const camp = await this.spRepository.findOne(
        { id: payload.campaignId },
        { select: ['status'] },
      );
      if (camp?.status !== CampaignStatus.active) {
        return new Nack(false);
      }
      const { campaignId, pageID, mess, delay } = payload;
      let { data } = payload;
      console.log('send-campaign-message data', data);
      data = uniqBy(data, 'threadID');
      const item = data.shift();
      if (!item) {
        console.log('send-campaign-message stop');
        await this.spRepository.update({ id: campaignId }, { status: CampaignStatus.end });
        return new Nack(false);
      }
      console.log('send-campaign-message item', item);
      const { message, member } = this.parseMessage(mess, item?.participants);

      console.log(`message?.attachment`, message?.attachment);

      let res;
      try {
        const imageAttachments = remove(message?.attachment, (it: any) => !isNil(it.image_id));
        if (imageAttachments && imageAttachments.length > 0) {
          const api = await this.botApiService.getApi(pageID);

          const _mess: IFbMessage = {};
          if (message.body) _mess.body = message.body;

          _mess.attachment = await Promise.all(
            imageAttachments
              .filter(i => findAvailableKeys(i))
              .map((item: IFbAttachment) => {
                return api.duplicateAttachment(item);
              }),
          );

          try {
            res = await api.sendMessage(_mess, item?.threadID);
            delete message?.body;
          } catch (e) {
            res = e;
          }
        }
        if (message?.attachment && message.attachment.length > 0) {
          const [images, videos] = message.attachment.reduce(
            (prev, it) => {
              if (it.filetype === 'image') {
                prev[0].push(it);
              } else {
                prev[1].push(it);
              }
              return prev;
            },
            [[], []],
          );

          const tokenApi = await this.botApiService.getPageTokenApi(pageID);

          const img = images[0];
          if (message.body) {
            res = await tokenApi({
              method: 'POST',
              url: FACEBOOK_API_ENDPOINT.MESSAGES(),
              data: {
                messaging_type: FbMessagingType.MESSAGE_TAG,
                tag: FbMessagingTag.ACCOUNT_UPDATE,
                recipient: { id: member?.userID },
                message: { text: message.body },
              },
              headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json;charset=UTF-8',
              },
            });
          }
          if (img) {
            res = await tokenApi({
              method: 'POST',
              url: FACEBOOK_API_ENDPOINT.MESSAGES(),
              data: {
                messaging_type: FbMessagingType.MESSAGE_TAG,
                tag: FbMessagingTag.ACCOUNT_UPDATE,
                recipient: { id: member?.userID },
                message: {
                  attachment: {
                    type: img.filetype,
                    payload: {
                      attachment_id: img.attachment_id,
                    },
                  },
                },
              },
            });
          }

          for (const vid of videos) {
            res = await tokenApi({
              method: 'POST',
              url: FACEBOOK_API_ENDPOINT.MESSAGES(),
              data: {
                messaging_type: FbMessagingType.MESSAGE_TAG,
                tag: FbMessagingTag.ACCOUNT_UPDATE,
                recipient: { id: member?.userID },
                message: {
                  attachment: {
                    type: vid.filetype,
                    payload: {
                      attachment_id: vid.attachment_id,
                    },
                  },
                },
              },
              headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json;charset=UTF-8',
              },
            });
          }
        }

        console.log('send-campaign-message res', pageID, item?.threadID, member?.name, res);
        if (!!res && res?.error) {
          await this.afterSendMessage(campaignId, pageID, member?.userID, member, 0, {
            time: moment().valueOf(),
            error: res,
          });
        } else {
          await this.afterSendMessage(campaignId, pageID, member?.userID, member, 1, {
            time: moment().valueOf(),
            message: res,
          });
        }
      } catch (error) {
        console.log('send-campaign-message error', error);
        await this.afterSendMessage(campaignId, pageID, member?.userID, member, 0, {
          time: moment().valueOf(),
          error: error,
        });
        if (error?.code == '100001') {
          await this.spRepository.update({ id: campaignId }, { status: CampaignStatus.end });
          return new Nack(false);
        }
      }
      if (isEmpty(data)) {
        console.log('send-campaign-message get next page');
        const campaign = await this.spRepository.findOne(campaignId);
        await this.jobCampaignGetMember(
          campaign,
          pageID,
          Number(process.env.CAMPAIGN_LIMIT || 20),
          new Date(item?.updatedAt).getTime() - 1,
          mess,
        );
        console.log('send-campaign-message got next page');
      } else {
        console.log('process next send-campaign-message', { campaignId, pageID, mess, delay });
        // if (delay < 60) {
        //   // await new Promise(resolve => setTimeout(resolve, delay * 1000));
        //   await this.sendMessage(campaignId, pageID, mess, data, delay);
        // } else {
        // await this.campaignQueue.add('send-campaign-message', {campaignId, pageID, mess, data, delay}, {
        //   delay: Math.max(Number(delay), 1) * 1000,
        //   jobId: `send-message-${campaignId}-${pageID}-${item.threadID}`,
        //   attempts: 3,
        //   removeOnComplete: true,
        //   removeOnFail: false,
        // });
        // }
        await this.sendMessage(campaignId, pageID, mess, data, delay);
        console.log('after send-campaign-message');
      }
      return new Nack(false);
    };
    return await new Promise(async (resolve, reject) => {
      const timeoutErr = () => reject({ message: 'Handle timeout' });
      const timeout = setTimeout(timeoutErr, 120000);
      try {
        await handling();
      } catch (e) {
        reject(e)
      }
      clearTimeout(timeout);
      return resolve(new Nack(false));
    });
  }

  async sendMessage(
    campaignId: number,
    pageID: string,
    mess: MessageSentDto,
    data: ThreadDto[],
    delay: number,
  ) {
    return await this.amqpConnection.publish(
      'delay-exchange',
      'handle-send-message',
      {
        campaignId,
        pageID,
        mess,
        delay,
        data,
      },
      {
        headers: {
          'x-delay': (delay || 1) * 1000,
        },
      },
    );
  }

  parseMessage(_mess: MessageSentDto, participants: any) {
    const mess = cloneDeep(_mess);
    const member = find(participants, function(o) {
      return o.accountType == TYPE_USER;
    });
    const message = plainToInstance(MessageSentDto, instanceToPlain(mess));
    if (!!member && !!message?.body) {
      const params = WORDS.map(el => {
        const item = {
          from: '',
          to: '',
        };
        switch (el) {
          case WordNeedParse.gender:
            if (!!member && member?.gender) {
              const index =
                Number(GenderIndexEnum[member?.gender]) >= 0
                  ? GenderIndexEnum[member?.gender]
                  : GenderIndexEnum[GenderEnum.OTHER];
              message.body = ParseMessageUtils.randomStringMessage(
                message?.body,
                new RegExp(`\\@${WordNeedParse.gender}\\((.*?)\\)`, 'g'),
                index,
              );
            }
            break;
          case WordNeedParse.name:
            item.from = `@${WordNeedParse.name}`;
            if (!!member?.shortName) item.to = member?.shortName;
            break;
          case WordNeedParse.fullName:
            item.from = `@${WordNeedParse.fullName}`;
            if (!!member?.name) item.to = member?.name;
            break;
        }
        return item;
      });
      message.body = ParseMessageUtils.randomStringMessage(
        message?.body,
        /\{(.*?)\}/gims,
        'random',
      );
      message.body = ParseMessageUtils.replaceString(message?.body, params);
    }
    return { message, member: member ?? {} };
  }

  async afterSendMessage(
    campaignId: number,
    pageId: number | string,
    memberId: any,
    memberInfo: any,
    qty: number,
    histories: any,
  ) {
    return this.amqpConnection.publish('message-campaign-inbox', 'member', {
      campaignId,
      pageId,
      memberId,
      memberInfo,
      qty,
      histories,
    });
  }

  @RabbitRPC({
    exchange: 'message-campaign-inbox',
    routingKey: 'member',
    queue: 'ag-campaign-inbox-save-member',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async campaignInboxSaveMember(payload) {
    console.log('payload-member');
    if (!payload?.campaignId || !payload?.pageId || !payload?.memberId) return new Nack(false);
    const { campaignId, pageId, memberId, memberInfo, qty, histories } = payload;

    let memberMessage = await this.cmRepository.findOne({
      where: qb => {
        qb.where({ campaignId, pageId, memberId });
      },
    });

    if (!memberMessage) {
      memberMessage = new CampaignMessageMember();
      memberMessage.campaignId = campaignId;
      memberMessage.pageId = pageId;
      let member = await this.memberRepository.findOne(memberId);
      if (!member) {
        member = new FanPageMember();
        member.id = memberId;
      }
      member.info = memberInfo;

      memberMessage.member = member;
      memberMessage.histories = [];
      memberMessage.qty = 0;
    } else {
      if (!memberMessage.histories) memberMessage.histories = [];
    }

    memberMessage.qty += qty;
    if (!!histories) memberMessage.histories.push(histories);

    await this.cmRepository.save(memberMessage);
    return new Nack(false);
  }

  async fetchDataUser(id: string, request?, refreshToken = false): Promise<User> {
    const user = await this.uRepository
      .createQueryBuilder('u')
      .where({ id })
      .leftJoinAndSelect('u.sessions', 's', 's.ip = :ip', {
        ip: global.currentIp,
      })
      .leftJoinAndSelect('u.pages', 'p')
      .unScope('p')
      .getOne();
    if (!user) throw new NotFoundException('Không tìm thấy bot');
    if (isEmpty(user.sessions)) {
      return this.loginBot(
        {
          user: user.email || `${user.id}`,
          password: user.password,
          code: user.twoFactor,
          force: true,
        },
        request?.user || { companyId: user.companyId },
        true,
      );
    }
    const api = await this.botApiService.getUserApi(id);
    const status = await api.checkLoggedIn();
    console.log('user status', status);
    if (status === UserActive.checkpoint) {
      user.sessions = [
        await this.sessionRepository.save({
          ...user.sessions[0],
          status: UserStatus.deactivate,
        }),
      ];
      return user;
    }
    if (status === UserActive.loggedOut) {
      await this.sessionRepository.save({
        ...user.sessions[0],
        status: UserStatus.deactivate,
      });
      return this.loginBot(
        {
          user: user.email || `${user.id}`,
          password: user.password,
          code: user.twoFactor,
          force: true,
        },
        request?.user || { companyId: user.companyId },
        true,
      );
    }
    try {
      const res = await this.pagesService.updateUserPages(
        api,
        user.companyId,
        refreshToken ? null : user.token,
      );
      if (res.status === UserStatus.deactivate) {
        return this.loginBot(
          {
            user: user.email || `${user.id}`,
            password: user.password,
            code: user.twoFactor,
            force: true,
          },
          request?.user || { companyId: user.companyId },
          true,
        );
      }
      return res;
    } catch (e) {
      if (e.code === 190) {
        if ([458, 460].includes(e?.error_subcode)) {
          await this.sessionRepository.delete({
            user_id: user.id,
            ip: global.currentIp,
          });
          user.sessions = [];
        } else if (e.code === 459) {
          return this.fetchDataUser(id, request, true);
        } else {
          user.sessions = [
            await this.sessionRepository.save({
              ...user.sessions[0],
              status: UserStatus.deactivate,
              token: [463, 492, 467, 464].includes(e?.error_subcode)
                ? null
                : user.sessions[0]?.token,
            }),
          ];
          if (user.sessions[0].token) {
            return user;
          }
        }
        return this.loginBot(
          {
            user: user.email || `${user.id}`,
            password: user.password,
            code: user.twoFactor,
            force: true,
          },
          request?.user || { companyId: user.companyId },
          true,
        );
      }
    }
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'handle-instagram-token',
    queue: 'handle-instagram-token',
    errorHandler: rmqErrorsHandler,
    allowNonJsonMessages: true,
  })
  async handleInstagramToken({ id }: { id: string }) {
    return new Nack(false);
    try {
      const api = await this.botApiService.getUserApi(id);
      const token = await api.getRealInstagramToken();
      if (!token) {
        return new Nack(false);
      }
      const { user, pages } = await this.pagesService.updatePageToken(
        token,
        INSTA_APP_ID.toString(),
        `${id}`,
      );
      return user;
    } catch (e) {
      console.log('handle instagram token error', e);
    }
    return new Nack(false);
  }

  async handleLoginUser(
    api: IFbApi,
    password: string,
    companyId: number,
    code?: string,
    skipToken = false,
    userId?: number,
    projectId?: number,
  ) {
    const id = await api.getCurrentUserID();
    let oldToken: string;
    if (!skipToken) {
      const res = await this.userSessionRepository.findOne({
        where: { user_id: id, ip: global.currentIp },
        select: ['token'],
      });
      oldToken = res?.token;
      console.log('oldToken', oldToken);
    }
    const token = oldToken || (await api.getInstagramToken());
    let saveUser;
    let session = new UserSession();
    if (token && !skipToken) {
      try {
        const { user, pages } = await this.pagesService.updatePageToken(
          token,
          '1733556690196497',
          `${id}`,
        );
        saveUser = plainToInstance(
          User,
          {
            id: String(id),
            name: user.name,
            email: user.email,
            twoFactor: code,
            password: password,
            companyId,
            creatorId: userId,
          } as User,
          { groups: ['show'] },
        );
        session = plainToInstance(UserSession, {
          user_id: String(id),
          token,
          context: api.getContexts(),
          cookies: api.getAppState().map(i => ({
            name: i.key,
            path: i.path,
            value: i.value,
            domain: i.domain,
            expirationDate: i.expires === 'Infinity' ? null : i.expires?.getTime() / 1000,
          })) as Record<string, any>[],
          status: UserStatus.active,
          ip: global.currentIp,
        } as UserSession);
        const oldPages = await this.fanPageRepository
          .createQueryBuilder('p')
          .where('p.id IN (:...ids)', { ids: pages.map(i => i.id) })
          .innerJoin('user_page', 'up', 'up.page_id = p.id')
          .innerJoin('user', 'u', 'u.id = up.user_id')
          .getMany();
        const oldPageLookup: Record<string, FanPage> = oldPages.reduce((prev, item) => {
          prev[item.id] = item;
          return prev;
        }, {});
        saveUser.pages = pages.map(i => {
          if (oldPageLookup[i.id]) {
            return oldPageLookup[i.id];
          }
          const page = new FanPage();
          page.id = i.id;
          page.name = i.name;
          page.companyId = companyId;
          return page;
        });
      } catch (e) {
        return this.handleLoginUser(api, password, companyId, code, true, userId, projectId);
      }
    } else {
      const user = Object.values(await api.getUserInfo([`${id}`]))[0];
      console.log('id', id, user);
      saveUser = plainToInstance(
        User,
        {
          id: String(id),
          name: user.name,
          twoFactor: code,
          password: password,
          companyId,
          creatorId: userId,
        } as User,
        { groups: ['show'] },
      );
      session = plainToInstance(UserSession, {
        user_id: String(id),
        token: null,
        context: api.getContexts(),
        cookies: api.getAppState().map(i => ({
          name: i.key,
          path: i.path,
          value: i.value,
          domain: i.domain,
          expirationDate: i.expires === 'Infinity' ? null : i.expires?.getTime() / 1000,
        })) as Record<string, any>[],
        status: UserStatus.active,
        ip: global.currentIp,
      } as UserSession);
      // const pages = await api.getPageList();
      // const oldPages = await this.fanPageRepository.findByIds(pages.map(i => Number(i.id)));
      // const oldPageLookup: Record<string, FanPage> = oldPages.reduce((prev, item) => {
      //   prev[item.id] = item;
      //   return prev;
      // }, {});
      // saveUser.pages = pages.map(i => {
      //   if (oldPageLookup[i.id]) {
      //     return oldPageLookup[i.id];
      //   }
      //   const page = new FanPage();
      //   page.id = i.id;
      //   page.name = i.name;
      //   page.companyId = companyId;
      //   return page;
      // });
    }

    const userQb = this.uRepository
      .createQueryBuilder('u')
      .where({ id: saveUser.id })
      .innerJoin('u.sessions', 's', 's.ip = :ip', {
        ip: global.currentIp,
      });
    const user = await userQb.getOne();

    if (user && user.companyId !== companyId) {
      throw new BadRequestException({
        userId: saveUser.id,
        message: `Tài khoản đã được thêm tại công ty ${user.companyId}`,
        userCompanyId: user.companyId,
      });
    }
    if (isNil(user?.projectId) && projectId) {
      saveUser.projectId = projectId;
    }

    const saved = await this.uRepository.save(saveUser);
    const savedSession = await this.sessionRepository.save(session);
    saved.sessions = [savedSession];
    // try {
    //   await this.amqpConnection.publish('facebook-bot', 'new-user', {
    //     userId: saveUser.id,
    //   });
    //   console.log('published');
    // } catch (e) {
    //   console.log('cannot publish new user message', e);
    // }
    return saved;
  }

  async saveUser(data: UserSaveDto, requestUser: { id: number; companyId: number }) {
    const companyId = requestUser?.companyId;

    if (!companyId) {
      throw new ForbiddenException(`User's company is missing`);
    }
    const { user, email, password, code, projectId } = data;
    return this.uRepository.save({
      id: user,
      email,
      password,
      twoFactor: code,
      projectId,
      creatorId: requestUser.id,
      companyId,
    });
  }
  async getInstaToken(data: UserLoginDto) {
    let api: IFbApi;
    const bot = await this.userSessionRepository.findOne({
      ip: global.currentIp,
      user_id: data.user,
    });
    if (bot) {
      const instaToken = await this.suRepository.findOne({
        globalId: data.user,
        appId: INSTA_APP_ID.toString(),
      });
      if (instaToken) {
        const res = await axios({
          method: 'GET',
          baseURL: FACEBOOK_API_BASE_URL,
          params: {
            access_token: instaToken.accessToken,
          },
          url: '/me',
        });
        if (res.data.id) {
          return instaToken.accessToken;
        }
      }
      api = await login({
        appState: bot.appState,
        contexts: bot.context,
      });
      const token = await api.getRealInstagramToken();
      if (token) {
        return token;
      }
    }
    api = await login({
      email: data.user,
      password: data.password,
      code: data.code,
    });

    const token = await api.getRealInstagramToken();
    const id = (await api.getCurrentUserID()).toString();
    const session = plainToInstance(UserSession, {
      user_id: String(id),
      token,
      context: api.getContexts(),
      cookies: api.getAppState().map(i => ({
        name: i.key,
        path: i.path,
        value: i.value,
        domain: i.domain,
        expirationDate: i.expires === 'Infinity' ? null : i.expires?.getTime() / 1000,
      })) as Record<string, any>[],
      status: UserStatus.active,
      ip: global.currentIp,
    } as UserSession);
    await this.sessionRepository.save(session);
    await this.suRepository.save({
      id,
      globalId: id,
      appId: INSTA_APP_ID.toString(),
      accessToken: token,
    } as ScopedUser);
    return token;
  }

  async updateUser(userId: string, body: UpdateUserDto, requestUser: AuthUser) {
    const result = await this.uRepository.update({ id: userId }, body);
    return result.affected > 0;
  }

  async deleteUser(userId: string, requestUser: AuthUser) {
    const user = await this.uRepository.findOne(userId);
    if (!user) throw new NotFoundException(`Không tìm thấy user ${userId}`);
    if (!requestUser.isAdmin && (!user.creatorId || user.creatorId !== requestUser.id)) {
      throw new ForbiddenException({
        code: ErrorCode.VIA_0001,
        message: 'Bạn chỉ được phép xoá via do bạn thêm.',
      });
    }

    const connection = getConnection(messageConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.delete(UserSession, { user_id: userId });
      const result = await queryRunner.manager.delete(User, { id: userId });
      await queryRunner.commitTransaction();
      return result.affected > 0;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      if (e?.driverError) throw new BadRequestException(e?.driverError?.detail);
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async loginBot(data: UserLoginDto, requestUser, refresh = false) {
    const companyId = requestUser?.companyId;

    if (!companyId) {
      throw new ForbiddenException(`User's company is missing`);
    }

    try {
      const api = await login({
        email: data.user,
        password: data.password,
        code: data.code,
        lastRefresh: new Date(),
      });
      return await this.handleLoginUser(
        api,
        data.password,
        companyId,
        data.code,
        false,
        requestUser?.id,
        data?.projectId,
      );
    } catch (e) {
      console.log('e', e);
      if (refresh) {
        throw new BadRequestException('Thông tin bot không hợp lệ');
      }
      if (e.cookies) {
        const ticket = Buffer.from(uuid()).toString('base64');
        const ticketData: Record<string, any> = {
          cookies: e.cookies,
          password: data.password,
          companyId,
        };
        if (data.type) ticketData.type = data.type;
        await this.redisService.set(ticket, JSON.stringify(ticketData), { ttl: 300 });
        return { ticket };
      } else {
        if (e?.response?.userId) {
          const response = e.response;
          throw new BadRequestException({
            userId: response.userId,
            message: response.message,
            userCompanyId: response.userCompanyId,
          });
        }
        throw new BadRequestException('Thông tin đăng nhập không hợp lệ');
      }
    }
  }

  @RabbitRPC({
    exchange: 'delay-exchange',
    routingKey: 'refresh-bot',
    queue: 'delay-refresh-bot',
    errorHandler: rmqErrorsHandler,
    // queueOptions: {
    //   channel: 'crawler'
    // }
  })
  async refreshUser({ id, force }) {
    await this.redis.del(`queue-refresh-bot-${id}`);
    const key = `refreshing-${id}`;
    const current = await this.redisService.get(key);
    if (current) {
      console.log('bot is refreshing');
      return new Nack();
    }
    await this.redisService.set(key, 1, {
      ttl: 3600,
    });
    try {
      const api = await this.botApiService.getUserApi(`${id}`);
      const status = await api.checkLoggedIn();
      console.log('user current status', status);
      let user = await this.uRepository
        .createQueryBuilder('u')
        .where({ id })
        .leftJoinAndSelect('u.sessions', 's', 's.ip = :ip', {
          ip: global.currentIp,
        })
        .getOne();
      console.log('user session status', user.status);
      if (status === UserActive.loggedOut) {
        try {
          user = await this.fetchDataUser(`${id}`);
        } catch (e) {}
        if (user.status === UserStatus.deactivate) {
          console.log('user can not refresh should notify');
          //TODO: notify to creator
          const { data: company } =
            (await this.amqpConnection.request<{ data: { domainName: string } }>({
              exchange: 'identity-service-companies',
              routingKey: 'get-company-by-id',
              payload: { id: user.companyId },
              timeout: 10000,
            })) || {};
          await this.sendLarkMessage(
            user.creatorId,
            `VIA [${id}](https://${company.domainName}/marketing-automation/manage-bots?type=manual&name=${id}) vừa bị đăng xuất và không thể đăng nhập lại`,
          );
          await this.sendTelegramMessage(`VIA ${id} vừa bị đăng xuất và không thể đăng nhập lại`);
        }
      } else if (status === UserActive.checkpoint) {
        console.log('user is checked point');
        const { data: company } =
          (await this.amqpConnection.request<{ data: { domainName: string } }>({
            exchange: 'identity-service-companies',
            routingKey: 'get-company-by-id',
            payload: { id: user.companyId },
            timeout: 10000,
          })) || {};
        await this.sendLarkMessage(
          user.creatorId,
          `VIA [${id}](https://${company.domainName}/marketing-automation/manage-bots?type=manual&name=${id}) vừa dính check point`,
        );
        await this.sendTelegramMessage(`VIA ${id} vừa dính check point`);
      }
    } catch (e) {
      console.log('refresh error', e);
    } finally {
      await this.redisService.del(key);
    }
    return new Nack(false);
  }

  async sendLarkMessage(userId: number, text: string) {
    let email = '<EMAIL>';
    if (userId) {
      const { data: user } =
        (await this.amqpConnection.request<{ data: { email: string } }>({
          exchange: 'identity-service-roles',
          routingKey: 'get-user',
          payload: { id: userId },
          timeout: 10000,
        })) || {};
      email = user.email;
    }
    await new LarkClient(this.redis).sendMessage(email, text);
  }

  async sendTelegramMessage(text: string) {
    if (!process.env.TELEGRAM_TOKEN || !process.env.TELEGRAM_GROUP_ID) {
      return;
    }
    try {
      const rawResponse = await axios.request({
        baseURL: `https://api.telegram.org/bot${process.env.TELEGRAM_TOKEN}`,
        url: '/sendMessage',
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json;charset=UTF-8',
        },
        data: {
          text,
          chat_id: process.env.TELEGRAM_GROUP_ID,
        },
      });
      const response = rawResponse.data;
      return { response };
    } catch (error) {
      return { error };
    }
  }
}
