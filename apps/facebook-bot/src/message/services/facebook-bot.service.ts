import {
  AmqpConnection,
  defaultN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { plainToClass, plainToInstance } from 'class-transformer';
import { messageConnection } from 'core/constants/database-connection.constant';
import { CommonStatus } from 'core/enums/common-status.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { IRmqMessage } from 'core/interfaces';
import { Redis } from 'ioredis';
import { find, identity, isEmpty, isNil, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import { getConnection, Repository } from 'typeorm';
import { GET_STARTED_PAYLOAD } from '../../constants/facebook-bot.constant';
import {
  FACEBOOK_API_BASE_URL,
  FACEBOOK_API_ENDPOINT,
} from '../../constants/fb-api-endpoints.constant';
import { TYPE_DB, WORDS } from '../../constants/message-parse.constants';
import { Conversation } from '../../entities/conversation.entity';
import { FanPage } from '../../entities/fanpage.entity';
import { FacebookBotMessage } from '../../entities/fb-bot-message.entity';
import { KeywordConfigurationGroup } from '../../entities/keyword-configuration-group.entity';
import { KeywordConfiguration } from '../../entities/keyword-configuration.entity';
import { ScheduledJob } from '../../entities/scheduled-job.entity';
import { ScopedUserPage } from '../../entities/scoped-user-page.entity';
import { User } from '../../entities/user.entity';
import {
  GenderEnum,
  GenderIndexEnum,
  WordNeedParse,
} from '../../enums/campaign-message-parse.enum';
import { BotMessageType } from '../../enums/fb-bot.enum';
import { FbMessagingType } from '../../enums/fb-message.enum';
import { KeywordCondition } from '../../enums/keyword-condition.enum';
import { ScheduledAction } from '../../enums/scheduled-action.enum';
import { IFbApi } from '../../facebook-api';
import { IFbMessage } from '../../facebook-api/functions/sendMessage';
import { AttachmentsService } from '../../facebook-chat/services/attachments.service';
import { BotApiService } from '../../facebook-chat/services/bot.service';
import { BotProfile } from '../../read-entities/bot-profile.entity';
import { KeywordAction } from '../../read-entities/keyword-action.entity';
import {
  IContentMessage,
  IContentScenario,
  IContentTags,
} from '../../read-entities/scheduled-content.entity';
import { BotProfileDto } from '../dtos/bot-profile.dto';
import { CreateBotMessageDto, GetStartedDto } from '../dtos/facebook-bot-message.dto';
import {
  FbSendMessagesContent,
  FbSendMessagesDto,
  MessageAttachmentType,
} from '../dtos/facebook-send-messages.dto';
import { FbMessagingEntry } from '../dtos/facebook-webhook-message.dto';
import { BotMessagesFilter } from '../filters/bot-message.filter';
import BotUtils from '../utils/BotUtils';
import ParseMessageUtils from '../utils/StringMessageUtils';
import { BotService } from './bot.service';
import { CareScenarioService } from './care-scenario.service';
import { ConversationsService } from './conversation.service';
import { FanPagesService } from './fanpage.service';
import { InjectQueue } from '@nestjs/bull';
import Bull, { Queue } from 'bull';
import utils from '../../facebook-api/utils';
import { TempMessage } from '../../entities/temp-message.entity';
import { Message, parseAttachments } from '../../entities/message.entity';
import { findAvailableKeys } from '../../facebook-api/functions/uploadAttachments';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';

@Injectable()
export class FacebookBotService {
  constructor(
    @InjectRepository(User, messageConnection)
    private userRepo: Repository<User>,
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
    @InjectRepository(FacebookBotMessage, messageConnection)
    private fbmRepo: Repository<FacebookBotMessage>,
    @InjectRepository(ScheduledJob, messageConnection)
    private scheduledJobRepo: Repository<ScheduledJob>,
    @InjectRepository(ScopedUserPage, messageConnection)
    private supRepo: Repository<ScopedUserPage>,
    @InjectRepository(KeywordConfigurationGroup, messageConnection)
    private keywordGroupsRepo: Repository<KeywordConfigurationGroup>,
    @InjectRepository(KeywordConfiguration, messageConnection)
    private keywordRepo: Repository<KeywordConfiguration>,
    @InjectRepository(TempMessage, messageConnection)
    private tempMessageRepository: Repository<TempMessage>,
    @InjectRepository(Message, messageConnection)
    private messageRepository: Repository<Message>,
    private careScenarioService: CareScenarioService,
    private fanpageService: FanPagesService,
    private conversationService: ConversationsService,
    private attachmentsService: AttachmentsService,
    private botService: BotService,
    private botApiService: BotApiService,
    @InjectRedis() private readonly redis: Redis,
    @InjectQueue('facebook-jobs')
    private jobQueue: Queue,
    private amqpConnection: AmqpConnection,
  ) {}

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'process-queue-action',
    queue: 'facebook-bot-process-queue-action',
    errorHandler: defaultNackErrorHandler,
  })
  async processQueueAction({ action, content, source }) {
    return await new Promise(async (resolve, reject) => {
      const timeoutErr = () => reject({ message: 'Handle timeout' });
      const timeout = setTimeout(timeoutErr, 120000);
      try {
        await this.executeAction({ action }, content, null, null, source);
      } catch (e) {
        reject(e);
      }
      clearTimeout(timeout);
      return resolve(new Nack(false));
    });
  }
  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'auto-reply',
    queue: 'agbiz-process-auto-reply-facebook-message',
    errorHandler: defaultNackErrorHandler,
  })
  async processAutoReplyMessage(payload: IRmqMessage<FbMessagingEntry>) {
    const message = payload.message;
    const messaging = message.messaging?.[0];
    if (!message || (!messaging?.message?.text && !messaging?.postback)) return new Nack(false);

    const entryId = message.id;
    const senderId = messaging.sender.id;
    const recipientId = messaging.recipient.id;
    const pageId = entryId;
    const userId = entryId !== senderId ? senderId : recipientId;

    if (senderId === pageId) return new Nack(false);

    const fanpage = await this.fanpageService.getPageById(pageId, false, true);
    if (!fanpage) {
      return new Nack();
    }

    let access_token: string;
    for (const token of fanpage.accessTokens) {
      if (token) {
        access_token = token;
        break;
      }
    }
    if (isEmpty(fanpage) || !fanpage.isBotWorking || !access_token) return new Nack(false);

    const conversation = await this.conversationService.getConversation(pageId, userId);

    // Check if conversation has enabled bot
    if (conversation && !conversation.isBotEnabled) return new Nack(false);

    const api = await this.botApiService.getApi(pageId);

    // if (messaging.postback?.payload === GET_STARTED_PAYLOAD || !conversation)
    //   await this.procressGetStartedMessage(payload, fanpage, api);
    // else
    if (messaging.message) await this.replyDefaultMessage(payload, fanpage, api, conversation);

    return new Nack(false);
  }

  // async procressGetStartedMessage(
  //   payload: IRmqMessage<FbWebhookMessageDto>,
  //   page: FanPage,
  //   api: IFbApi,
  // ) {
  //   console.log(`case auto reply with get started message`);
  //   const data = payload.message as FbWebhookMessageDto;
  //   const message = data.entry[0];
  //   const entryId = message.id;
  //   const senderId = message.messaging[0].sender.id;
  //   const recipientId = message.messaging[0].recipient.id;
  //   const pageId = entryId;
  //   const userId = entryId !== senderId ? senderId : recipientId;

  //   const botMessage = await this.getBotMessage(
  //     pageId,
  //     BotMessageType.get_started,
  //   );

  //   if (!botMessage) return;

  //   const conversation = plainToInstance(Conversation, {
  //     pageId: pageId,
  //     scopedUserId: userId,
  //   });

  //   for (const content of botMessage.content) {
  //     await this.botSendMessage(conversation, api, content, page);
  //   }
  // }

  async replyDefaultMessage(
    payload: IRmqMessage<FbMessagingEntry>,
    page: FanPage,
    api: IFbApi,
    conversation: Conversation,
  ) {
    console.log(`case auto reply with default message`);
    const message = payload.message;
    const messaging = message.messaging[0];

    const entryId = message.id;
    const senderId = messaging.sender.id;
    const recipientId = messaging.recipient.id;
    const pageId = entryId;
    const userId = entryId !== senderId ? senderId : recipientId;

    const { configurationGroupId } = page.group || {};
    if (!configurationGroupId) return;

    const botMessage = await this.fbmRepo.findOne({
      type: BotMessageType.default_message,
      groupId: configurationGroupId,
      status: true,
    });

    if (!botMessage) return;
    const { lastDefaultMessageSent, userGlobalId: threadId } = conversation || {};
    const messagePeriod = botMessage.defaultMessagePeriod;
    const timestamp = messaging.timestamp;
    const lastSent = lastDefaultMessageSent?.getTime() || 0;
    const diffPeriod = timestamp - lastSent;

    const isSendable = lastDefaultMessageSent ? diffPeriod > messagePeriod : true;

    if (!isSendable) return;

    for (const content of botMessage.content) {
      await this.botSendMessage(conversation, content, page, { messageId: botMessage.id });
    }

    const result = await this.conversationRepo.update(
      { pageId, scopedUserId: userId },
      { lastDefaultMessageSent: new Date(timestamp) },
    );
    console.log(`result`, result);
  }

  async getBotProfile(pageId: string): Promise<BotProfile> {
    const page = await this.fanpageService.getPageById(pageId);
    if (!page) throw new NotFoundException('Không tìm thấy page');

    let getStarted = false;
    let greeting = '';
    const isBotEnabled = page.isBotEnabled;

    let access_token: string;
    for (const token of page.accessTokens) {
      if (token) {
        access_token = token;
        break;
      }
    }

    let error;
    tokenLoop: for (const access_token of page.accessTokens) {
      if (access_token) {
        try {
          // Get messenger profile
          const response = await axios.request({
            method: 'GET',
            url: FACEBOOK_API_ENDPOINT.MESSENGER_PROFILE(),
            baseURL: FACEBOOK_API_BASE_URL,
            params: {
              access_token,
              fields: 'get_started,greeting',
            },
          });

          if (response?.data?.data) {
            const profile = response.data.data[0] as GetStartedDto;
            const payload = profile?.get_started?.payload;
            getStarted = payload === GET_STARTED_PAYLOAD;

            if (profile?.greeting) {
              const defaultGreeting = find(profile.greeting, item => item.locale === 'default');
              if (defaultGreeting) greeting = defaultGreeting.text;
            }

            return plainToClass(BotProfile, {
              getStarted,
              isBotEnabled,
              greeting,
            });
          }
        } catch (err) {
          const code = err.response?.data?.error?.code;
          error = err.response?.data?.error;
          switch (code) {
            case 100: // (#100) No matching user found
              break tokenLoop;
            case 190: // access token expired
              await this.supRepo.delete({ accessToken: access_token });
              break;
            default:
              break;
          }
        }
      }
    }

    console.log(`get bot profile error`, error);
    throw new BadRequestException(error);
  }

  async setBotProfile(pageId: string, data: BotProfileDto): Promise<BotProfile> {
    const page = await this.fanpageService.getPageById(pageId);
    if (!page) throw new NotFoundException('Không tìm thấy page');

    let access_token: string;
    for (const token of page.accessTokens) {
      if (token) {
        access_token = token;
        break;
      }
    }

    const { getStarted, greeting, isBotEnabled } = data;

    const result: Record<string, string> = {};
    try {
      const setProps: Record<string, any> = {};
      const unsetProps: string[] = [];

      if (getStarted) {
        setProps.get_started = {
          payload: GET_STARTED_PAYLOAD,
        };
      } else if (getStarted === false) unsetProps.push('get_started');

      if (greeting) {
        setProps.greeting = [
          {
            locale: 'default',
            text: greeting,
          },
        ];
      } else if (greeting === false) unsetProps.push('greeting');

      if (!isEmpty(setProps)) {
        const response = await axios.request({
          method: 'POST',
          url: FACEBOOK_API_ENDPOINT.MESSENGER_PROFILE(),
          baseURL: FACEBOOK_API_BASE_URL,
          params: { access_token },
          data: setProps,
        });
        for (const key of Object.keys(setProps)) {
          result[key] = response?.data.result;
        }
      }
      if (!isEmpty(unsetProps)) {
        const response = await this.unsetMessengerProfile(access_token, unsetProps);
        for (const props of unsetProps) {
          result[props] = response?.data.result;
        }
      }

      if (!isNil(isBotEnabled)) {
        const update = await this.fanpageService.setBotState(pageId, isBotEnabled);
        result.isBotEnabled = update.affected > 0 ? 'success' : '';
      }
    } catch (error) {
      console.log(`set bot profile error`, error);
      throw new BadRequestException();
    }

    return plainToInstance(BotProfile, result);
  }

  async unsetMessengerProfile(access_token: string, properties: string[]) {
    try {
      return await axios.request({
        method: 'DELETE',
        url: FACEBOOK_API_ENDPOINT.MESSENGER_PROFILE(),
        baseURL: FACEBOOK_API_BASE_URL,
        params: { access_token },
        data: { fields: properties },
      });
    } catch (error) {
      console.log(`unsetMessengerProfile error`, error);
      throw new BadRequestException(error);
    }
  }

  async getBotMessageById(id: number, request?: Record<string, any>): Promise<FacebookBotMessage> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const query = this.fbmRepo.createQueryBuilder('o');
    query.andWhere('o.id = :id', { id });
    query.leftJoinAndMapMany('o.pages', 'scoped_user_pages', 'pages', 'pages.page_id = o.page_id');
    const message = await query.getOne();
    if (!message) throw new NotFoundException(`Message ${id} not found`);
    if (message.companyId !== companyId) throw new UnauthorizedException();

    return message;
  }

  async save(
    data: CreateBotMessageDto,
    request?: Record<string, any>,
  ): Promise<FacebookBotMessage> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    let botMessage;
    const { type, groupId } = data;

    if (type === BotMessageType.default_message || type === BotMessageType.get_started) {
      botMessage = await this.fbmRepo.findOne({
        where: {
          type,
          groupId,
          companyId,
        },
      });
    }

    const attachments = await this.attachmentsService.saveAttachments(
      data.content.map(i => i.url).filter(identity),
    );
    const saveMessage = plainToInstance(FacebookBotMessage, {
      ...botMessage,
      ...data,
      companyId,
    });
    const attachmentContents = saveMessage.content.filter(i => i.message?.attachment?.payload?.url);
    for (let i = 0; i < attachmentContents.length; i++) {
      const content = attachmentContents[i];
      if (attachments[i]) {
        content.message.attachment.id = attachments[i].id;
      }
    }

    return await this.fbmRepo.save(saveMessage);
  }

  async deleteBotMessageById(
    id: number,
    request?: Record<string, any>,
  ): Promise<FacebookBotMessage> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const message = await this.fbmRepo.findOne(id);
    if (!message) throw new NotFoundException(`Message ${id} not found`);

    if (message.companyId !== companyId) throw new UnauthorizedException();

    return await this.fbmRepo.softRemove(message);
  }

  async botSendMessage(
    conversation: Conversation,
    content: FbSendMessagesContent,
    page: FanPage,
    source: Record<string, any>,
  ) {
    let error: string;
    content.message.text = this.parseContentText(content.message.text, conversation.user);
    if (content.message?.attachment?.id) delete content.message.attachment.id;
    const data: FbSendMessagesDto = {
      recipient: {
        id: String(conversation.scopedUserId),
      },
      ...content,
      messaging_type: FbMessagingType.RESPONSE,
    };
    const botApi = await this.botApiService.getPageTokenApi(`${page.id}`);

    if (botApi) {
      try {
        const date = new Date();
        const message = await botApi({
          method: 'POST',
          url: FACEBOOK_API_ENDPOINT.MESSAGES(),
          data,
        });
        console.log('handle message', message, data);
        const mes = await this.messageRepository.save({
          id: message.message_id,
          scopedUserId: conversation.scopedUserId,
          pageId: page.id,
          _attachments: parseAttachments({
            attachments: [
              {
                type: data.message.attachment.type,
                payload: {
                  url: data.message.attachment.payload.url,
                },
              },
            ],
          }),
          recipientId: conversation.scopedUserId,
          senderId: page.id,
          isEcho: true,
          text: data.message?.text,
          timestamp: date,
          raw: source,
        });
        return message;
      } catch (e) {
        console.log(`send message with official api error`, e.response?.data?.error);
        console.log(`send message with official api data`, data);
        console.log(
          `send message with official api conversation`,
          conversation.pageId,
          conversation.scopedUserId,
        );
        error = e.response?.data?.error?.message;
      }
    }

    if (error) console.log(`bot send message error`, error);
  }

  async parseGlobalContent(
    data: FbSendMessagesContent,
    conversation: Conversation,
  ): Promise<IFbMessage> {
    const { message } = data;
    const { pageId, user } = conversation;

    if (message) {
      if (message.text) {
        message.text = this.parseContentText(message.text, user);
        return {
          body: message.text,
        };
      }
      if (message.attachment) {
        switch (message.attachment.type) {
          case MessageAttachmentType.image:
          case MessageAttachmentType.video: {
            const attachments = await this.attachmentsService.findAttachments([
              message.attachment.id,
            ]);
            const uploadedAttachment = await this.attachmentsService.getPageAttachments(
              attachments,
              pageId,
            );
            return {
              attachment: uploadedAttachment,
            };
          }
          case MessageAttachmentType.template:
            const urls = message.attachment.payload.elements.map(el => el.image_url);
            console.log(`urls`, urls);
            const attachments = (
              await Promise.all(
                urls.map(async url => {
                  const response = await axios.get(url, {
                    responseType: 'arraybuffer',
                  });
                  const buffer = Buffer.from(response.data, 'binary');
                  if (TYPE_DB[response?.headers['content-type']]?.extensions) {
                    const timestamp = moment()
                      .valueOf()
                      .toString();
                    const attachment = (
                      await this.botService.uploadAttachment(
                        {
                          buffer,
                          originalname:
                            timestamp +
                            '.' +
                            TYPE_DB[response?.headers['content-type']]?.extensions[0],
                          name:
                            timestamp +
                            '.' +
                            TYPE_DB[response?.headers['content-type']]?.extensions[0],
                        },
                        [String(pageId)],
                      )
                    )[pageId];
                    return attachment;
                  }
                  return null;
                }),
              )
            ).filter(i => i);
            console.log('attachments', attachments);
            return { attachment: attachments };
          default:
            return;
        }
      }
    }
    return;
  }

  parseContentText(text: string, user: PageScopedUser) {
    if (isEmpty(text)) return text;

    const params = WORDS.map(el => {
      const item = {
        from: '',
        to: '',
      };
      switch (el) {
        case WordNeedParse.gender:
          if (!!user && user?.gender) {
            const index =
              Number(GenderIndexEnum[user?.gender]) >= 0
                ? GenderIndexEnum[user?.gender]
                : GenderIndexEnum[GenderEnum.OTHER];
            text = ParseMessageUtils.randomStringMessage(
              text,
              new RegExp(`\\@${WordNeedParse.gender}\\((.*?)\\)`, 'g'),
              index,
            );
          }
          break;
        case WordNeedParse.name:
          item.from = `@${WordNeedParse.name}`;
          item.to = user?.shortName || '';
          break;
        case WordNeedParse.fullName:
          item.from = `@${WordNeedParse.fullName}`;
          item.to = user?.name || '';
          break;
      }
      return item;
    });
    text = ParseMessageUtils.randomStringMessage(text, /\{(.*?)\}/gims, 'random');
    text = ParseMessageUtils.replaceString(text, params);

    return text;
  }

  async getMessages(
    filters: BotMessagesFilter,
    request?: Record<string, any>,
  ): Promise<FacebookBotMessage[]> {
    const query = this.fbmRepo.createQueryBuilder('o');
    if (request) {
      const companyId = request?.user?.companyId;
      if (!companyId) throw new UnauthorizedException();
      query.andWhere('o.companyId = :companyId', { companyId });
    }

    const { ids, types, pageIds, groupId, status } = filters;
    if (types) query.andWhere('o.type IN (:...types)', { types });
    if (pageIds) query.andWhere('o.pageId IN (:...pageIds)', { pageIds });
    if (groupId) query.andWhere('o.groupId = :groupId', { groupId });
    if (ids) query.andWhere('o.id IN (:...ids)', { ids });
    if (status) query.andWhere('o.status = :status', { status });

    const messages = await query.getMany();
    return messages;
  }

  async executeAction(
    action: KeywordAction,
    content?: Record<string, any>,
    pageId?: string,
    userId?: string,
    source: Record<string, any> = {},
  ) {
    if (!content) {
      content = BotUtils.parseBotAction(action, pageId, userId);
    }

    if (!content) return;
    console.log(`executeAction`, ScheduledAction[action.action], content);

    switch (action.action) {
      case ScheduledAction.sendMessage:
        await this.processScheduledMessages(content as IContentMessage, source);
        break;
      case ScheduledAction.attachCareScenario:
        await this.processScheduledAttachScenario(content as IContentScenario, source);
        break;
      case ScheduledAction.removeCareScenario:
        await this.processScheduledRemoveScenario(content as IContentScenario);
        break;
      case ScheduledAction.attachTag:
        await this.processScheduledAttachTags(content as IContentTags);
        break;
      case ScheduledAction.removeTag:
        await this.processScheduledRemoveTags(content as IContentTags);
        break;
      default:
        break;
    }
  }

  async processScheduledMessages(content: IContentMessage, source: Record<string, any>) {
    const { pageId, scopedUserId, botMessageIds, careScenarioId } = content;

    const fanpage = await this.fanpageService.getPageById(pageId, false, true);
    if (isEmpty(fanpage) || !fanpage.isBotWorking) return;

    const conversation = await this.conversationService.getConversation(pageId, scopedUserId);

    /**
     * We must check 2 conditions:
     * - If bot is enabled on the conversation
     * - If this scheduled message has come from any care-scenario and whether that scenario is activating at current moment
     */
    if (
      !conversation?.isBotEnabled ||
      (careScenarioId && conversation.careScenarioId !== careScenarioId)
    )
      return;

    const messages = await this.getMessages({
      ids: botMessageIds,
      status: true,
    });

    for (const message of messages) {
      for (const content of message.content) {
        source.messageId = message.id;
        await this.botSendMessage(conversation, content, fanpage, source);
      }
    }
  }

  async processScheduledAttachTags(content: IContentTags) {
    const { pageId, scopedUserId, tagIds } = content;

    const fanpage = await this.fanpageService.getPageById(pageId);

    if (isEmpty(fanpage) || !fanpage.isBotEnabled) return;

    const conversation = await this.conversationService.getConversation(pageId, scopedUserId);

    if (!conversation?.isBotEnabled) return;

    const cTagIds = conversation.tags.map(item => item.id);
    const mTagIds = uniq([...cTagIds, ...tagIds]);

    const addedTag = await getConnection(messageConnection)
      .createQueryBuilder()
      .insert()
      .into('page_scoped_users_tags')
      .values(
        tagIds.map(i => ({
          scoped_user_id: `${pageId}_${scopedUserId}`,
          tag_id: i,
        })),
      )
      .orIgnore()
      .execute();
    console.log('addedTag', addedTag);

    // await this.conversationService.updateConversation(pageId, scopedUserId, {
    //   tagIds: mTagIds,
    // });
  }

  async processScheduledRemoveTags(content: IContentTags) {
    const { pageId, scopedUserId, tagIds } = content;

    const fanpage = await this.fanpageService.getPageById(pageId);

    if (isEmpty(fanpage) || !fanpage.isBotEnabled) return;

    const conversation = await this.conversationService.getConversation(pageId, scopedUserId);

    if (!conversation?.isBotEnabled) return;

    const cTagIds = conversation.tags.map(item => item.id);
    const mTagIds = cTagIds.filter(id => !tagIds.includes(id));

    const removedTags = await getConnection(messageConnection)
      .createQueryBuilder()
      .delete()
      .from('page_scoped_users_tags')
      .where('scoped_user_id = :userId AND tag_id IN (:...tagIds)', {
        userId: `${pageId}_${scopedUserId}`,
        tagIds,
      })
      .execute();
    console.log('removedTags', removedTags);

    // await this.conversationService.updateConversation(pageId, scopedUserId, {
    //   tagIds: mTagIds,
    // });
  }

  async processScheduledAttachScenario(content: IContentScenario, source: Record<string, any>) {
    const { pageId, scopedUserId, scenarioId } = content;

    const fanpage = await this.fanpageService.getPageById(pageId);

    if (isEmpty(fanpage) || !fanpage.isBotEnabled) return;

    const conversation = await this.conversationService.getConversation(pageId, scopedUserId);

    if (!conversation?.isBotEnabled) return;

    await this.conversationService.updateConversation(
      pageId,
      scopedUserId,
      { scenarioId },
      null,
      '',
      source,
    );
  }

  async processScheduledRemoveScenario(content: IContentScenario) {
    const { pageId, scopedUserId, scenarioId } = content;

    const fanpage = await this.fanpageService.getPageById(pageId);

    if (isEmpty(fanpage) || !fanpage.isBotEnabled) return new Nack(false);

    const conversation = await this.conversationService.getConversation(pageId, scopedUserId);

    if (!conversation?.isBotEnabled) return;

    await this.conversationService.updateConversation(pageId, scopedUserId, {
      scenarioId: scenarioId * -1,
    });
  }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'after-updated-conversation-with-care-scenario-id',
    queue: 'process-after-updated-conversation-with-care-scenario-id',
    errorHandler: rmqErrorsHandler,
  })
  async afterUpdateConversationWithScenarioId({
    oldCareScenarioId,
    conversation,
    source,
  }: {
    oldCareScenarioId: number;
    conversation: Conversation;
    source: Record<string, any>;
  }) {
    return await new Promise(async (resolve, reject) => {
      const timeoutErr = () => reject({ message: 'Handle timeout' });
      const timeout = setTimeout(timeoutErr, 120000);
      const { pageId, scopedUserId, careScenarioId } = conversation;
      console.log(`afterUpdateConversationWithScenarioId`);
      console.log(`pageId`, pageId);
      console.log(`scopedUserId`, scopedUserId);
      console.log(`careScenarioId`, careScenarioId);
      console.log(`oldCareScenarioId`, oldCareScenarioId);
      if (oldCareScenarioId === careScenarioId) return resolve(false);

      try {
        // remove old scheduled job of old care scenario id
        await this.jobQueue.removeJobs(`message-schedule-action-${scopedUserId}*`);

        if (!careScenarioId) return resolve(false);
        // Get bot messages of new care scenario
        const messages = await this.careScenarioService.getScenarioMessages(careScenarioId);

        const scheduleJobs: Bull.Job[] = [];
        let i = 0;
        for (const message of messages) {
          i++;
          if (message.status !== CommonStatus.activated) continue;
          const { scheduleDuration } = message;
          const content: IContentMessage = {
            pageId: String(pageId),
            scopedUserId: String(scopedUserId),
            botMessageIds: [message.botMessageId],
            careScenarioId,
          };
          console.log('handle', scheduleDuration, content);
          const messageSource = {
            messageId: message.botMessageId,
            careScenarioId: careScenarioId,
            seqIdx: i,
            ...(source || {}),
          };
          if (!scheduleDuration) {
            try {
              await this.processScheduledMessages(content, messageSource);
            } catch (e) {
              console.log('handle scenario error', e);
            }
            continue;
          }
          const job = await this.jobQueue.add(
            'message-schedule-action',
            {
              action: ScheduledAction.sendMessage,
              content,
              source: messageSource,
            },
            {
              removeOnComplete: true,
              delay: scheduleDuration,
              jobId: `message-schedule-action-${scopedUserId}-${utils.getGUID()}`,
            },
          );
          scheduleJobs.push(job);
          // const executeAt = moment().valueOf() + scheduleDuration;
          // const scheduleJob = plainToInstance(ScheduledJob, {
          //   executeAt,
          //   action: ScheduledAction.sendMessage,
          //   content,
          // });
          // scheduleJobs.push(scheduleJob);
        }
        // if (!isEmpty(scheduleJobs)) this.scheduledJobRepo.save(scheduleJobs);
      } catch (error) {
        console.log('error in handling changes conversation care scenario', error);
        reject(error);
      } finally {
        clearTimeout(timeout);
        return resolve(new Nack(false));
      }
    });
  }

  async removeScheduledJobsByCareScenario(
    pageId: string,
    scopedUserId: string,
    careScenarioId?: number,
  ) {
    if (!careScenarioId) return;

    console.log(
      'removeScheduledJobsByCareScenario for conversation',
      pageId + '/' + scopedUserId,
      'which has careScenarioId is',
      careScenarioId,
    );
    const result = await this.scheduledJobRepo
      .createQueryBuilder()
      .where(`content::json ->> 'careScenarioId' = :careScenarioId`, {
        careScenarioId,
      })
      .andWhere(`content::json ->> 'pageId' = :pageId`, {
        pageId,
      })
      .andWhere(`content::json ->> 'scopedUserId' = :scopedUserId`, {
        scopedUserId,
      })
      .delete()
      .execute()
      .catch(error => {
        console.log(`error`, error.driverError.detail);
        return;
      });

    return result;
  }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'after-updated-conversation-with-user-global-id',
    queue: 'process-after-updated-conversation-with-user-global-id',
    errorHandler: rmqErrorsHandler,
  })
  async afterUpdateConversationWithUserGlobalId(payload: IRmqMessage<Conversation>) {
    const { pageId, scopedUserId } = payload.message || {};
    if (!pageId || !scopedUserId) return new Nack(false);

    const conversation = await this.conversationRepo
      .createQueryBuilder('c')
      .where('c.pageId = :pageId')
      .andWhere('c.scopedUserId = :scopedUserId')
      .andWhere('c.isSentStartMessage IS NULL')
      .andWhere('c.isBotEnabled = TRUE')
      .andWhere('c.userGlobalId IS NOT NULL')
      .leftJoinAndMapOne(
        'c.user',
        PageScopedUser,
        'u',
        'u.pageId = c.pageId AND u.scopedUserId = c.scopedUserId',
      )
      .setParameters({
        pageId,
        scopedUserId,
      })
      .getOne();
    if (!conversation) return new Nack(false);

    const page = await this.fanpageService.getPageById(pageId, false, true);
    const sendingKey = `bot-sending-start-message.${conversation.scopedUserId}`;
    const [, [, sending]] = await this.redis
      .multi()
      .set(sendingKey, 0, 'EX', 60, 'NX')
      .incr(sendingKey)
      .exec();
    if (sending > 1) {
      return new Nack(false);
    }
    if (page?.isBotWorking && page.group?.configurationGroupId) {
      const botMessage = await this.fbmRepo.findOne({
        type: BotMessageType.get_started,
        groupId: page.group?.configurationGroupId,
        status: true,
      });
      const getStartedConfig = await this.getGetStartedConfig(page);
      if (botMessage || getStartedConfig) {
        // const api = await this.botApiService.getApi(pageId);

        if (botMessage) {
          for (const content of botMessage.content) {
            await this.botSendMessage(conversation, content, page, {
              messageId: botMessage.id,
            });
          }
        }

        if (getStartedConfig) {
          const { scheduleDuration, actions } = getStartedConfig;

          const scheduleJobs: ScheduledJob[] = [];
          for (const action of actions) {
            const content = BotUtils.parseBotAction(action, pageId, scopedUserId);
            if (!scheduleDuration) {
              await this.executeAction(action, content, pageId, scopedUserId, {
                getStartId: getStartedConfig.id,
              });
            } else {
              // const executeAt = conversation.updatedAt?.getTime() + scheduleDuration;
              // const scheduleJob = plainToInstance(ScheduledJob, {
              //   executeAt,
              //   action: action.action,
              //   content,
              // });
              // scheduleJobs.push(scheduleJob);
              await this.jobQueue.add(
                'message-schedule-action',
                {
                  action: action.action,
                  content,
                  source: { getStartId: getStartedConfig.id },
                },
                {
                  removeOnComplete: true,
                  delay: Math.max(
                    Date.now() + scheduleDuration - conversation.updatedAt?.getTime(),
                    0,
                  ),
                },
              );
            }
          }
          if (!isEmpty(scheduleJobs)) await this.scheduledJobRepo.save(scheduleJobs);
        }
      }
    }

    await this.conversationRepo.update({ pageId, scopedUserId }, { isSentStartMessage: true });

    return new Nack(false);
  }

  async getGetStartedConfig(page: FanPage): Promise<KeywordConfiguration> {
    // Get keyword configuration group that applied to this page
    let keywordGroupId = page.keywordGroupId;
    if (!keywordGroupId) {
      const defaultKeywordGroup = await this.keywordGroupsRepo.findOne({
        where: {
          groupId: page.group?.configurationGroupId,
          isDefault: true,
        },
      });
      if (!defaultKeywordGroup) return;
      keywordGroupId = defaultKeywordGroup.id;
    }

    try {
      const keywordConfig = await this.keywordRepo
        .createQueryBuilder('keyword')
        .andWhere(
          `keyword.id IN (SELECT k."id" FROM keyword_configurations k, jsonb_array_elements(requirements) arr WHERE arr->>'condition' = :condition AND k.groupId = :keywordGroupId)`,
          { condition: KeywordCondition.includes, keywordGroupId },
        )
        .getOne();
      return keywordConfig;
    } catch (error) {
      console.log(`get keyword config error`, error);
    }

    return;
  }
}
