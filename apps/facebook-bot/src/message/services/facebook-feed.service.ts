import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { messageConnection } from 'core/constants/database-connection.constant';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { IRmqMessage } from 'core/interfaces';
import PhoneUtils from 'core/utils/PhoneUtils';
import { isEmpty, isNil, omitBy, uniq } from 'lodash';
import { Repository } from 'typeorm';
import { FACEBOOK_API_ENDPOINT } from '../../constants/fb-api-endpoints.constant';
import { WORDS } from '../../constants/message-parse.constants';
import { CommentConfiguration } from '../../entities/comment-configuration.entity';
import { Comment } from '../../entities/comment.entity';
import { Conversation } from '../../entities/conversation.entity';
import { ConversationsPhones } from '../../entities/conversations-phones.entity';
import { Feed, IFeedAttachment } from '../../entities/feed.entity';
import {
  GenderEnum,
  GenderIndexEnum,
  WordNeedParse,
} from '../../enums/campaign-message-parse.enum';
import { IFbApi } from '../../facebook-api';
import { BotApiService } from '../../facebook-chat/services/bot.service';
import { FbMessagingEntry, FbWebhookMessageDto } from '../dtos/facebook-webhook-message.dto';
import ParseMessageUtils from '../utils/StringMessageUtils';
import { ConversationsService } from './conversation.service';
import { FacebookService } from './facebook.service';
import { FanPagesService } from './fanpage.service';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';
import { Message } from '../../entities/message.entity';
import { ConsoleMessage } from 'puppeteer';

export interface IFbFeedAttachmentMedia {
  image: {
    height: number;
    src: string;
    width: number;
  };
}

export interface IFbFeedAttachment {
  media?: IFbFeedAttachmentMedia;
  target: {
    id: string;
    url: string;
  };
  media_type: 'photo' | 'video' | 'link';
  type: 'photo' | 'video';
  description: string;
  title: string;
  url: string;
  subattachments?: {
    data: IFbFeedAttachment[];
  };
}

export interface IFbFeed {
  message: string;
  attachments?: {
    data: IFbFeedAttachment[];
  };
  id: string;
  created_time: string;
  updated_time: string;
}

export interface IFbComment {
  message?: string;
  attachment?: IFbFeedAttachment;
  id: string;
  from?: {
    id: string;
    name?: string;
  };
  created_time?: string;
  updated_time?: string;
  parent?: IFbComment;
  comment_count?: number;
  user_likes?: boolean;
  is_hidden?: boolean;
  can_reply_privately?: boolean;
}

@Injectable()
export class FacebookFeedsService {
  constructor(
    @InjectRepository(CommentConfiguration, messageConnection)
    private commentConfigRepo: Repository<CommentConfiguration>,
    @InjectRepository(Feed, messageConnection)
    private feedRepository: Repository<Feed>,
    @InjectRepository(Comment, messageConnection)
    private commentRepository: Repository<Comment>,
    @InjectRepository(Message, messageConnection)
    private messageRepository: Repository<Message>,
    @InjectRepository(ConversationsPhones, messageConnection)
    private conversationPhonesRepo: Repository<ConversationsPhones>,
    private conversationService: ConversationsService,
    private fanpageService: FanPagesService,
    private facebookService: FacebookService,
    private botApiService: BotApiService,
    private amqpConnection: AmqpConnection,
  ) {}

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'feeds',
    queue: 'agbiz-process-facebook-webhook-feeds',
    errorHandler: rmqErrorsHandler,
  })
  async processWebhookFeeds(payload: IRmqMessage<FbWebhookMessageDto>) {
    const data = payload.message;
    const message = data.entry?.[0];
    const change = message?.changes?.[0];
    const { field, value } = change || {};
    // Check if this event was a comment
    if (!message || field !== 'feed') return new Nack(false);

    if (value.post_id && !value.comment_id) {
      if (value.item === 'reaction') {
        return new Nack(false);
      }
      await this.savePost(message);
      // return new Nack(false);
    }
    const conversations = [];
    const entryId = message.id;
    const pageId = entryId;
    const senderId = value.from?.id;
    if (value.message && value.comment_id) {
      const updateData: Partial<Conversation> = {};
      updateData.updatedAt = new Date((value.created_time || message.time) * 1000);
      updateData.snippet = value.message;
      updateData.unread = true;
      if (value.created_time) {
        updateData.createdAt = new Date(value.created_time * 1000);
      }
      const scopedUserIds: string[] = senderId === pageId ? [] : [senderId];
      for (const scopedUserId of scopedUserIds) {
        if (!scopedUserId) {
          continue;
        }
        const scopeUser = await this.facebookService.saveFacebookUser(
          scopedUserId,
          pageId,
          value.from,
        );
        if (!scopeUser || !scopedUserId) {
          continue;
        }
        const conversation = await this.conversationService.save(
          {
            scopedUserId,
            pageId,
            feedId: value.post_id,
            ...updateData,
            createdAt: new Date(value.created_time * 1000),
          } as Conversation,
          scopeUser,
        );
        conversations.push(conversation);
      }
    }
    if (value.item === 'comment') {
      await this.saveComment(message);
      // Check if this event was sent from user
      if (senderId != pageId) {
        try {
          await this.processCommentAutoActions(value.message, value.comment_id, pageId, value.from);
        } catch (e) {
          console.log('handle comment error', e);
        }
      }
    }

    return new Nack(false);
  }

  async savePost(data: FbMessagingEntry) {
    const value = data.changes[0].value;
    const id = value.post_id;
    const pageId = (id || '').split('_')[0];
    if (!pageId) {
      return null;
    }
    let api;
    try {
      api = await this.botApiService.getPageTokenApi(pageId, '***************');
      const response = await api({
        url: FACEBOOK_API_ENDPOINT.ME(id),
        params: {
          fields:
            'attachments.limit(10){target,media_type,subattachments,url,type,media},message,created_time,updated_time',
        },
      });
      const feed: IFbFeed = response;
      return this.feedRepository.save(this.handleFbFeed(feed));
    } catch (e) {
      console.log('save post error', id, e);
      try {
        const { message, created_time, item } = value;
        if (!message || !created_time || item != 'post') {
          return null;
        }
        return this.feedRepository.save(
          this.handleFbFeed({
            message,
            id,
            created_time: new Date(created_time * 1000).toISOString(),
            updated_time: new Date(data.time * 1000).toISOString(),
          }),
        );
      } catch (err) {
        console.log('save post error', id, err);
      }
    }
  }

  async saveComment(message: FbMessagingEntry) {
    const change = message.changes?.[0];
    const { value } = change || {};
    const id = value.comment_id;
    if (!id || (value.from.id !== message.id && value.item === 'reaction')) {
      return;
    }
    let commentData: IFbComment;
    try {
      const api = await this.botApiService.getPageTokenApi(message.id, '***************');
      commentData = await api({
        url: FACEBOOK_API_ENDPOINT.ME(id),
        params: {
          fields:
            'message,attachment,created_time,from,parent,is_hidden,user_likes,can_reply_privately',
        },
      });
    } catch (e) {
      console.log('save comment error', id, e);
    }
    if (!commentData) {
      commentData = {
        id,
        message: value.message,
        created_time: new Date(value.created_time * 1000).toISOString(),
        from: value.from,
      };
      if (value.parent_id) {
        commentData.parent = { id: value.parent_id };
      }
    } else {
      commentData.id = id;
    }
    let comment = await this.parseFacebookComment(commentData, value.post_id);
    if (value.parent_id) {
    }
    comment = await this.commentRepository.save(comment);

    await this.amqpConnection.publish('facebook-webhook-event', 'phones-in-comments', comment);
    return comment;
  }

  async parseFacebookComment(commentData: IFbComment, feedId: string) {
    let parentIds = [],
      parentId;
    if (commentData.parent) {
      const parent = await this.commentRepository.findOne(commentData.parent.id, {
        select: ['parentIds'],
      });
      if (parent) {
        const pageId = feedId.split('_')[0];
        const parents = await this.commentRepository.findByIds(parent.parentIds);
        if (pageId == commentData.from.id) {
          const users = parents.filter(i => i.senderId != pageId);
          for (const user of users) {
            await this.conversationService.save({
              scopedUserId: user.senderId,
              pageId,
              feedId: feedId,
              lastSentByPage: true,
              snippet: commentData.message,
              updatedAt: new Date(commentData.created_time),
            });
          }
        } else {
          const scopeUser = await this.facebookService.saveFacebookUser(
            commentData.from.id,
            pageId,
            commentData.from,
          );
          await this.conversationService.save({
            scopedUserId: commentData.from.id,
            pageId,
            feedId: feedId,
            lastSentByPage: false,
            snippet: commentData.message,
            updatedAt: new Date(commentData.created_time),
          });
        }
        parentIds = parent.parentIds;
        parentId = parentIds[0];
        parentIds.push(commentData.parent.id);
      }
    }
    parentIds.push(commentData.id);

    const comment: Partial<Comment> = {
      id: commentData.id,
      senderId: commentData.from?.id,
      attachment: commentData.attachment
        ? this.parseFeedAttachment([commentData.attachment])[0]
        : undefined,
      parentIds: uniq(parentIds),
      parentId,
      createdAt: new Date(commentData.created_time),
      message: commentData.message,
      feedId,
      isHidden: commentData.is_hidden,
      isLiked: commentData.user_likes,
      canReplyPrivately: commentData.can_reply_privately,
    };
    return omitBy(comment, isNil);
  }

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'phones-in-comments',
    queue: 'agbiz-process-phones-in-comments',
    errorHandler: rmqErrorsHandler,
  })
  async processPhonesInComment(comment: Comment) {
    if (isEmpty(comment?.message)) return new Nack(false);

    const pageId = comment.feedId.split('_')[0];
    if (!pageId || !comment.senderId || pageId === comment.senderId) return new Nack(false);

    const matches = PhoneUtils.extractPhone(comment.message);
    const phones = matches.map(item => {
      return plainToInstance(ConversationsPhones, {
        phone: item.text,
        offset: item.offset,
        length: item.length,
        messageId: comment.id,
        pageId,
        scopedUserId: comment.senderId,
        feedId: comment.feedId,
      });
    });

    const records = [];
    const insertPromises = phones.map(async phone => {
      const insertResult = await this.conversationPhonesRepo
        .createQueryBuilder('phones')
        .insert()
        .values(phone)
        .orIgnore()
        .execute();

      if (!isEmpty(insertResult.raw)) {
        records.push(phone);
      }
    });
    await Promise.all(insertPromises);

    await Promise.all(
      records.map(item =>
        this.amqpConnection.publish('facebook-bot', 'request-tls-cross-care', {
          pageId: item.pageId,
          scopedUserId: item.scopedUserId,
          messageId: item.messageId,
          phone: item,
          text: comment?.message,
        }),
      ),
    );

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'fetch-page-feed-comments',
    queue: 'ag-facebook-fetch-feed-comments',
    errorHandler: rmqErrorsHandler,
    allowNonJsonMessages: true,
  })
  async handleFbComments({ id, pageId, after, feedId, afterTime }) {
    let fetchData = true;
    const api = await this.botApiService.getPageTokenApi(pageId, '***************');
    let next = '';
    try {
      const response = await api({
        url: FACEBOOK_API_ENDPOINT.COMMENTS(id),
        params: {
          fields:
            'message,attachment,created_time,from,parent,comment_count,user_likes,is_hidden,can_reply_privately',
          limit: 25,
          after,
        },
      });
      const comments: IFbComment[] = response?.data;
      for (const comment of comments) {
        const parsedComment = await this.parseFacebookComment(comment, feedId);
        if (afterTime && parsedComment.createdAt?.getTime() < new Date(afterTime).getTime()) {
          fetchData = false;
          continue;
        }
        await this.commentRepository.save(parsedComment);
        if (`${parsedComment.senderId}` !== `${pageId}`) {
          const scopeUser = await this.facebookService.saveFacebookUser(
            parsedComment.senderId,
            pageId,
            parsedComment.sender,
          );

          if (scopeUser) {
            const conversation = await this.conversationService.save({
              scopedUserId: parsedComment.senderId,
              pageId,
              feedId: parsedComment.feedId,
              snippet: parsedComment.message,
              updatedAt: parsedComment.createdAt,
              createdAt: parsedComment.createdAt,
            } as Partial<Conversation>);
          }
        }
        if (comment.comment_count && fetchData) {
          await this.amqpConnection.publish(
            'facebook-conversation-event',
            'fetch-page-feed-comments',
            {
              id: comment.id,
              pageId,
              feedId,
              afterTime,
            },
          );
        }
      }
      next = response?.paging?.next ? response?.paging?.cursors?.after : null;
      if (next && fetchData) {
        await global.sleep(2000);
        await this.amqpConnection.publish(
          'facebook-conversation-event',
          'fetch-page-feed-comments',
          {
            id,
            pageId,
            after: next,
            feedId,
            afterTime,
          },
        );
      }
      return comments;
    } catch (e) {
      console.log('handle comment error', pageId, id, e);
      throw e;
    }
    return new Nack(false);
  }

  parseFeedAttachment(attachments: IFbFeedAttachment[]): IFeedAttachment[] {
    return attachments.reduce((prev, item) => {
      if (item.media_type === 'link') {
        prev.push({
          type: item.media_type || item.type,
          id: item.target?.id,
          data: item,
        });
      } else if (isEmpty(item.subattachments?.data)) {
        prev.push({
          id: item.target?.id,
          type: item.media_type || item.type,
        });
      } else {
        for (const subattachment of item.subattachments.data) {
          prev.push({
            id: subattachment.target?.id,
            type: subattachment.media_type || item.type,
          });
        }
      }
      return prev;
    }, []);
  }

  handleFbFeed(feed: IFbFeed): Feed {
    const pageId: string = feed.id.split('_')[0];
    const attachments: IFeedAttachment[] = feed.attachments
      ? this.parseFeedAttachment(feed.attachments.data)
      : undefined;
    return {
      id: feed.id,
      attachments,
      createdAt: new Date(feed.created_time),
      updatedAt: new Date(feed.updated_time),
      message: feed.message,
      pageId,
    };
  }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'fetch-page-feed',
    queue: 'ag-facebook-fetch-feed',
    errorHandler: rmqErrorsHandler,
    allowNonJsonMessages: true,
  })
  async fetchPageFeed(payload: { id: string; after?: string; afterTime: string }) {
    const { id, after, afterTime } = payload || {};
    let next = after;
    let fetchData = true;
    if (!id) return new Nack(false);
    const api = await this.botApiService.getPageTokenApi(id, '***************');
    try {
      const response = await api({
        method: 'GET',
        url: FACEBOOK_API_ENDPOINT.FEED(),
        params: {
          fields:
            'attachments.limit(10){target,media_type,subattachments,url,type,media},message,created_time,updated_time',
          limit: 4,
          after: next,
        },
      });
      const data: IFbFeed[] = response?.data;
      const parsedFeeds = data.map(i => this.handleFbFeed(i));
      const feeds = await this.feedRepository.save(parsedFeeds);
      for (const feed of feeds) {
        if (afterTime && feed.updatedAt?.getTime() < new Date(afterTime).getTime()) {
          fetchData = false;
          continue;
        }
        await this.amqpConnection.publish(
          'facebook-conversation-event',
          'fetch-page-feed-comments',
          {
            id: feed.id,
            pageId: id,
            feedId: feed.id,
            afterTime,
          },
        );
      }
      next = response?.paging?.next ? response?.paging?.cursors?.after : null;
      if (next && fetchData) {
        await global.sleep(2000);
        await this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-feed', {
          id,
          after: next,
          afterTime,
        });
      }
      return feeds;
    } catch (e) {
      console.log('get feed error', id, e);
      throw e;
    }
    return new Nack(false);
  }

  async processCommentAutoActions(
    comment: string,
    commentId: string,
    pageId: string,
    user: PageScopedUser,
  ) {
    const fanpage = await this.fanpageService.getPageById(pageId, false, true);
    let skipReply = false;
    if (fanpage.aiConfigId) {
      await this.amqpConnection.publish('facebook-webhook-event', 'hande-send-llm-message', {
        pageId,
        commentId,
        userId: user.id,
      });
      skipReply = true;
    }
    if (!fanpage?.isBotWorking || !fanpage?.group?.configurationGroupId) return;

    // Get comment bot config
    const config = await this.commentConfigRepo.findOne({
      where: {
        groupId: fanpage.group.configurationGroupId,
      },
    });

    // Check if this config is exist
    if (!config) return;

    console.log('config', config);

    let api: IFbApi;
    let tokenApi;
    try {
      tokenApi = await this.botApiService.getPageTokenApi(pageId, '***************');
    } catch (e) {}
    try {
      api = await this.botApiService.getApi(pageId);
    } catch (e) {}
    const publicReply = this.getReplyCommentMessage(comment, config);
    if (publicReply) {
      const message = this.parseWordsInMessage(publicReply, user);
      if (tokenApi) {
        try {
          const res = await tokenApi({
            method: 'POST',
            url: `${commentId}/comments`,
            data: {
              message,
            },
          });
        } catch (e) {}
      }
    }
    if (!skipReply) {
      const privateReply = this.getPrivateReplyMessage(comment, config);
      if (privateReply) {
        const message = this.parseWordsInMessage(privateReply, user);
        if (tokenApi) {
          try {
            const date = new Date();
            const res = await tokenApi({
              method: 'POST',
              url: `${pageId}/messages`,
              params: {
                message: JSON.stringify({ text: message }),
                message_type: 'RESPONSE',
                recipient: JSON.stringify({ comment_id: commentId }),
              },
            });
            await this.messageRepository.save({
              id: res.message_id,
              scopedUserId: user.id,
              pageId,
              recipientId: user.id,
              senderId: pageId,
              // isEcho: true,
              text: message,
              raw: {
                comment_id: commentId,
              },
              timestamp: date,
            });
          } catch (e) {}
        }
      }
    }

    if (this.isGoinToHide(comment, config)) {
      try {
        const res = await tokenApi({
          url: commentId,
          method: 'POST',
          data: {
            is_hidden: true,
          },
        });
        console.log('hide', res);
      } catch (e) {
        try {
          await api?.hideComment(commentId);
        } catch (e) {
          console.log('hide 2 error', e);
        }
      }
    }

    if (config.isAutoLikeComment) {
      try {
        const res = await tokenApi({
          url: `${commentId}/likes`,
          method: 'POST',
        });
        console.log('like', res);
      } catch (e) {
        try {
          const res = await api.likeComment(commentId);
          console.log('like 2', res);
        } catch (e) {}
      }
    }

    return;
  }

  isGoinToHide(commentText: string, config: CommentConfiguration): boolean {
    const {
      isHideAllComment,
      isHideCommentHasPhone,
      isHideCommentHasKeywords,
      keywordsToHide,
    } = config;

    if (isHideAllComment) return true;
    if (isHideCommentHasPhone && !isEmpty(PhoneUtils.extractPhone(commentText))) return true;
    if (isHideCommentHasKeywords) {
      const matches = commentText.match(new RegExp(keywordsToHide.join('|'), 'i'))
      if (matches) return true
    }

    return false;
  }

  getPrivateReplyMessage(userComment: string, config: CommentConfiguration): string | undefined {
    const {
      isReplyPrivatelyToAllComment,
      onlyReplyPrivatelyToCommentIncludesPhone,
      onlyReplyPrivatelyToCommentNotIncludePhone,
      defaultPrivateReplyText,
      privateReplyMessageWhenIncludesPhone,
    } = config;
    const hasPhones = !isEmpty(PhoneUtils.extractPhone(userComment));

    if (isReplyPrivatelyToAllComment) {
      if (!isEmpty(privateReplyMessageWhenIncludesPhone) && hasPhones)
        return privateReplyMessageWhenIncludesPhone;
      if (!isEmpty(defaultPrivateReplyText)) return defaultPrivateReplyText;
    }

    if (
      onlyReplyPrivatelyToCommentIncludesPhone &&
      !isEmpty(privateReplyMessageWhenIncludesPhone) &&
      hasPhones
    )
      return privateReplyMessageWhenIncludesPhone;

    if (onlyReplyPrivatelyToCommentNotIncludePhone && !isEmpty(defaultPrivateReplyText) && !hasPhones)
      return defaultPrivateReplyText;

    return;
  }

  getReplyCommentMessage(userComment: string, config: CommentConfiguration): string | undefined {
    const {
      isCommentBackToAllComment,
      onlyCommentBackWhenIncludesPhone,
      onlyCommentBackWhenNotIncludesPhone,
      defaultCommentMessage,
      commentMessageWhenIncludesPhone,
    } = config;
    const hasPhones = !isEmpty(PhoneUtils.extractPhone(userComment));

    if (isCommentBackToAllComment) {
      if (!isEmpty(commentMessageWhenIncludesPhone) && hasPhones) {
        return commentMessageWhenIncludesPhone;
      }
      if (!isEmpty(defaultCommentMessage)) return defaultCommentMessage;
    }

    if (onlyCommentBackWhenIncludesPhone && !isEmpty(commentMessageWhenIncludesPhone) && hasPhones)
      return commentMessageWhenIncludesPhone;

    if (onlyCommentBackWhenNotIncludesPhone && !isEmpty(defaultCommentMessage) && !hasPhones)
      return defaultCommentMessage;

    return;
  }

  parseWordsInMessage(input: string, user?: PageScopedUser): string {
    let output: string = input;
    const params = WORDS.map(el => {
      const item = {
        from: '',
        to: '',
      };
      switch (el) {
        case WordNeedParse.gender:
          if (!!user) {
            const index =
              Number(GenderIndexEnum[user?.gender]) >= 0
                ? GenderIndexEnum[user?.gender]
                : GenderIndexEnum[GenderEnum.OTHER];
            output = ParseMessageUtils.randomStringMessage(
              output,
              new RegExp(`\\@${WordNeedParse.gender}\\((.*?)\\)`, 'g'),
              index,
            );
          }
          break;
        case WordNeedParse.name:
          item.from = `@${WordNeedParse.name}`;
          if (!!user?.shortName) item.to = user?.shortName;
          break;
        case WordNeedParse.fullName:
          item.from = `@${WordNeedParse.fullName}`;
          if (!!user?.name) item.to = user?.name;
          break;
      }
      return item;
    });
    output = ParseMessageUtils.randomStringMessage(output, /\{(.*?)\}/gims, 'random');
    output = ParseMessageUtils.replaceString(output, params);
    return output;
  }
}
