import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1729563199030 implements MigrationInterface {
    name = 'migration1729563199030'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "scoped_users" ADD "creator_id" integer`);
        await queryRunner.query(`ALTER TABLE "scoped_users" ADD "company_id" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "scoped_users" DROP COLUMN "company_id"`);
        await queryRunner.query(`ALTER TABLE "scoped_users" DROP COLUMN "creator_id"`);
    }

}
