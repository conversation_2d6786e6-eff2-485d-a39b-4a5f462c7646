import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1679991882892 implements MigrationInterface {
    name = 'migration1679991882892'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "type" smallint NOT NULL DEFAULT '1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "type"`);
    }

}
