import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1749625649670 implements MigrationInterface {
  name = 'migration1749625649670';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "fanpage" ADD "active_conversation_summary" boolean NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "fanpage" DROP COLUMN "active_conversation_summary"`);
  }
}
