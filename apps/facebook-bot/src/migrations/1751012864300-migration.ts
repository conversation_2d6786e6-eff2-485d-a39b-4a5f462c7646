import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1751012864300 implements MigrationInterface {
  name = 'migration1751012864300';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "fanpage" ADD "order_type" smallint NOT NULL DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "fanpage" DROP COLUMN "order_type"`);
  }
}
