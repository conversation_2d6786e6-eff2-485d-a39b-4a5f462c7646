import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1742878472813 implements MigrationInterface {
    name = 'migration1742878472813'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "scoped_users" DROP CONSTRAINT "FK_5b11de316c61e25b8d5e1edb27f"`);
        await queryRunner.query(`ALTER TABLE "appointments" DROP CONSTRAINT "FK_a405fe25356adc02ae6cc5d4786"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP CONSTRAINT "FK_16626e51cedba57a91fa270ba60"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5b11de316c61e25b8d5e1edb27"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_PS_USER_PSID"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_EMPLOYEE_ID"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_16626e51cedba57a91fa270ba6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5fea8b3a2fc96220253490505e"`);
        await queryRunner.query(`ALTER TABLE "scoped_users" DROP CONSTRAINT "UQ_5b11de316c61e25b8d5e1edb27f"`);
        await queryRunner.query(`ALTER TABLE "scoped_users" DROP COLUMN "current_care_id"`);
        await queryRunner.query(`ALTER TABLE "scoped_users" DROP COLUMN "care_user_id"`);
        await queryRunner.query(`ALTER TABLE "scoped_users" DROP COLUMN "project_id"`);
        await queryRunner.query(`ALTER TABLE "configuration_groups" DROP COLUMN "ai_config_ids"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "app_id"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "access_token"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "app_id" bigint NOT NULL DEFAULT '1733556690196497'`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "access_token" character varying`);
        await queryRunner.query(`ALTER TABLE "fanpage" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "new_messages" ALTER COLUMN "raw" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "new_messages" ALTER COLUMN "is_trained" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "feeds" ALTER COLUMN "id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "appointments" ALTER COLUMN "psid" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "new_logs" ALTER COLUMN "table_name" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "new_logs" DROP CONSTRAINT "PK_fb0a39481a9a99b744a59921728"`);
        await queryRunner.query(`ALTER TABLE "new_logs" ADD CONSTRAINT "PK_a9b2cd8e7c83d3e1929cd667147" PRIMARY KEY ("id")`);
        await queryRunner.query(`CREATE INDEX "IDX_PS_USER_PSID" ON "page_scoped_users" ("page_id", "scoped_user_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_9e3516cf97a57b6f6199fa95a8" ON "user" ("email") WHERE email IS NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_16626e51cedba57a91fa270ba6" ON "scoped_user_pages" ("page_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_5fea8b3a2fc96220253490505e" ON "scoped_user_pages" ("scoped_user_id") `);
        await queryRunner.query(`ALTER TABLE "new_messages" ADD CONSTRAINT "FK_225b71f95f78ae13e27ae107c72" FOREIGN KEY ("scoped_user_id", "page_id") REFERENCES "page_scoped_users"("scoped_user_id","page_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "new_messages" ADD CONSTRAINT "FK_a8cabcecd3ac18674b5498f44f1" FOREIGN KEY ("tag_id") REFERENCES "message_tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_04ea171e0fad3969a9c04947b09" FOREIGN KEY ("feed_id") REFERENCES "feeds"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversations_phones" ADD CONSTRAINT "FK_1fce2f712919b4a051fc1309f80" FOREIGN KEY ("scoped_user_id") REFERENCES "conversations"("scoped_user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD CONSTRAINT "FK_16626e51cedba57a91fa270ba60" FOREIGN KEY ("page_id") REFERENCES "fanpage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "keyword_config_groups" ADD CONSTRAINT "FK_4cab6452270a6a98f8f8b0ce73e" FOREIGN KEY ("group_id") REFERENCES "configuration_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "fanpage" ADD CONSTRAINT "FK_64715a2c199b8d5efd76e4295fe" FOREIGN KEY ("ai_config_id") REFERENCES "ai_configuration_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_sessions" ADD CONSTRAINT "FK_e9658e959c490b0a634dfc54783" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversation_assign_histories" ADD CONSTRAINT "FK_ce8207d59a0fd643208018a3e59" FOREIGN KEY ("scoped_user_id", "page_id", "feed_id") REFERENCES "conversations"("scoped_user_id","page_id","feed_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ai_product_configuration_groups" ADD CONSTRAINT "FK_c293f3a5b6bae363ad00972df3e" FOREIGN KEY ("group_config_id") REFERENCES "ai_configuration_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "ai_product_configuration_groups" ADD CONSTRAINT "FK_f2076c268ceee61db010be994ce" FOREIGN KEY ("product_config_id") REFERENCES "ai_product_configurations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ai_product_configuration_groups" DROP CONSTRAINT "FK_f2076c268ceee61db010be994ce"`);
        await queryRunner.query(`ALTER TABLE "ai_product_configuration_groups" DROP CONSTRAINT "FK_c293f3a5b6bae363ad00972df3e"`);
        await queryRunner.query(`ALTER TABLE "conversation_assign_histories" DROP CONSTRAINT "FK_ce8207d59a0fd643208018a3e59"`);
        await queryRunner.query(`ALTER TABLE "user_sessions" DROP CONSTRAINT "FK_e9658e959c490b0a634dfc54783"`);
        await queryRunner.query(`ALTER TABLE "fanpage" DROP CONSTRAINT "FK_64715a2c199b8d5efd76e4295fe"`);
        await queryRunner.query(`ALTER TABLE "keyword_config_groups" DROP CONSTRAINT "FK_4cab6452270a6a98f8f8b0ce73e"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP CONSTRAINT "FK_16626e51cedba57a91fa270ba60"`);
        await queryRunner.query(`ALTER TABLE "conversations_phones" DROP CONSTRAINT "FK_1fce2f712919b4a051fc1309f80"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_04ea171e0fad3969a9c04947b09"`);
        await queryRunner.query(`ALTER TABLE "new_messages" DROP CONSTRAINT "FK_a8cabcecd3ac18674b5498f44f1"`);
        await queryRunner.query(`ALTER TABLE "new_messages" DROP CONSTRAINT "FK_225b71f95f78ae13e27ae107c72"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5fea8b3a2fc96220253490505e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_16626e51cedba57a91fa270ba6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9e3516cf97a57b6f6199fa95a8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_PS_USER_PSID"`);
        await queryRunner.query(`ALTER TABLE "new_logs" DROP CONSTRAINT "PK_a9b2cd8e7c83d3e1929cd667147"`);
        await queryRunner.query(`ALTER TABLE "new_logs" ADD CONSTRAINT "PK_fb0a39481a9a99b744a59921728" PRIMARY KEY ("id", "table_name")`);
        await queryRunner.query(`ALTER TABLE "new_logs" ALTER COLUMN "table_name" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "appointments" ALTER COLUMN "psid" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "feeds" ALTER COLUMN "id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "new_messages" ALTER COLUMN "is_trained" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "new_messages" ALTER COLUMN "raw" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "fanpage" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "access_token"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "app_id"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "access_token" character varying`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "app_id" bigint NOT NULL DEFAULT '1733556690196497'`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "configuration_groups" ADD "ai_config_ids" integer array`);
        await queryRunner.query(`ALTER TABLE "scoped_users" ADD "project_id" integer`);
        await queryRunner.query(`ALTER TABLE "scoped_users" ADD "care_user_id" integer`);
        await queryRunner.query(`ALTER TABLE "scoped_users" ADD "current_care_id" integer`);
        await queryRunner.query(`ALTER TABLE "scoped_users" ADD CONSTRAINT "UQ_5b11de316c61e25b8d5e1edb27f" UNIQUE ("current_care_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_5fea8b3a2fc96220253490505e" ON "scoped_user_pages" ("scoped_user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_16626e51cedba57a91fa270ba6" ON "scoped_user_pages" ("page_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_EMPLOYEE_ID" ON "new_messages" ("employee_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_PS_USER_PSID" ON "page_scoped_users" ("page_id", "scoped_user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_5b11de316c61e25b8d5e1edb27" ON "scoped_users" ("current_care_id") `);
        await queryRunner.query(`ALTER TABLE "scoped_user_pages" ADD CONSTRAINT "FK_16626e51cedba57a91fa270ba60" FOREIGN KEY ("page_id") REFERENCES "fanpage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "appointments" ADD CONSTRAINT "FK_a405fe25356adc02ae6cc5d4786" FOREIGN KEY ("care_id") REFERENCES "scoped_user_cares"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "scoped_users" ADD CONSTRAINT "FK_5b11de316c61e25b8d5e1edb27f" FOREIGN KEY ("current_care_id") REFERENCES "scoped_user_cares"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
