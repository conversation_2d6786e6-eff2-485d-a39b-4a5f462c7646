import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1749694905369 implements MigrationInterface {
  name = 'migration1749694905369';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "conversations" ADD "last_message_id_get_summary" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "conversations" ADD "last_summary" text`);
    await queryRunner.query(`ALTER TABLE "conversations" ADD "last_summary_by" int4`);
    await queryRunner.query(
      `ALTER TABLE "conversations" ADD "last_summary_at" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "conversations" DROP COLUMN "last_summary_at"`);
    await queryRunner.query(`ALTER TABLE "conversations" DROP COLUMN "last_summary_by"`);
    await queryRunner.query(`ALTER TABLE "conversations" DROP COLUMN "last_summary"`);
    await queryRunner.query(
      `ALTER TABLE "conversations" DROP COLUMN "last_message_id_get_summary"`,
    );
  }
}
