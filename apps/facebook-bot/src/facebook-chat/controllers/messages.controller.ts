import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  Post,
  Query,
  Req,
  Request,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags } from '@nestjs/swagger';
import { SalePermission } from 'core/enums/sale-permissions';
import { CarePagePermission } from 'core/enums/sale-permissions/care-page-permission.enum';
import { Auth } from '../../../../../core/auth/decorators/auth/new-auth.decorator';
import { PageCursor } from '../../../../../core/decorators/page-cursor/page-cursor.decorator';
import { PageCursorOptions } from '../../../../../core/decorators/page-cursor/page-cursor.model';
import { Message } from '../../entities/message.entity';
import { MessagesFilter } from '../../message/filters/messages.filter';
import { SendMessageDto } from '../dtos/send-message.dto';
import { MessagesService } from '../services/messages.service';
import { TelesalesPermission } from 'core/enums/sale-permissions/telesales-permission.enum';
import { SaveMessageDto } from '../dtos/save-message.dto';
import { UpdateConversationSummaryDto } from '../dtos/update-conversation-summary.dto';
import { GetConversationSummaryCustomPromptDto } from '../dtos/get-convaersation-summary-custom-prompt.dto';

@Controller('pages/:id/messages/:userId')
@ApiTags('page-messages')
export class MessagesController {
  constructor(private readonly messagesService: MessagesService) {}

  @Get('')
  @Auth(
    'sale',
    [SalePermission.carePage, CarePagePermission.process],
    [SalePermission.telesales, TelesalesPermission.fetchOne],
  )
  async getMessages(
    @Request() request,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @PageCursor(Message, ['timestamp'], null, 'DESC')
    paging: PageCursorOptions<Message>,
    @Query() filters: MessagesFilter,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.messagesService.find(paging, pageId, psid, filters);
  }

  @Get('temp-messages')
  @Auth()
  async getTempMessages(@Request() request, @Param('userId') userId: string) {
    return this.messagesService.findTempMessages(userId);
  }

  @Get('conversation-summary')
  @Auth()
  async getConversationSummary(@Request() request, @Param('userId') userId: string) {
    return await this.messagesService.getConversationSummary(userId);
  }

  @Post('conversation-summary/custom-prompt')
  @Auth()
  async getConversationSummaryWithCustomPrompt(
    @Request() request, @Param('userId') userId: string,
    @Body() data: GetConversationSummaryCustomPromptDto,
  ) {
    return await this.messagesService.getConversationSummaryWithCustomPrompt(userId, data);
  }

  @Post('conversation-summary')
  @Auth()
  async updateConversationSummary(
    @Request() request,
    @Param('userId') userId: string,
    @Body() data: UpdateConversationSummaryDto,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.messagesService.updateConversationSummary(pageId, psid, data, request.user.id);
  }

  @Post('')
  @Auth('sale', [SalePermission.carePage, CarePagePermission.process])
  @UseInterceptors(FilesInterceptor('files'))
  async sendMessage(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Body('body') body: string,
    @Body('clientId') clientId: string,
    @UploadedFiles() files: Array<any>,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.messagesService.send(pageId, psid, { files, body, clientId }, headers, request);
  }

  @Post('extension-message')
  @Auth('sale', [SalePermission.carePage, CarePagePermission.process])
  async saveMessage(
    @Req() request: Record<string, any>,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Body() data: SaveMessageDto,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.messagesService.saveTempMessage(pageId, psid, data, request);
  }

  @Post('v2')
  @Auth('sale', [SalePermission.carePage, CarePagePermission.process])
  async sendMessageV2(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Body() data: SendMessageDto,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.messagesService.send(pageId, psid, data, headers, request);
  }
}
