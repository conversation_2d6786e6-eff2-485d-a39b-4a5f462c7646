import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Headers,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { PageGroup } from '../../entities/page-group.entity';
import { CreatePageGroupDto, UpdatePageGroupDto } from '../dtos/page-group.dto';
import { ConversationsFilter } from '../filters/conversations.filter';
import { CountConversationsFilter } from '../filters/count-conversations.filter';
import { PageGroupsFilter } from '../filters/page-groups.filter';
import { ConversationsService } from '../services/conversations.service';
import { PageGroupsService } from '../services/page-groups.service';
import { isEmpty, remove } from 'lodash';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import { AuthUser } from 'core/interfaces/auth-user.interface';

@Controller('page-groups')
@ApiTags('page-groups')
export class PageGroupsController {
  constructor(
    private pageGroupsService: PageGroupsService,
    private conversationsService: ConversationsService,
  ) {}

  @Get('')
  @Auth()
  async fetch(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Pagination() pagination: PaginationOptions,
    @Query() filter?: PageGroupsFilter,
  ) {
    const user: AuthUser = request.user;
    for (const profile of user.profiles) {
      const [dataAccessLevel, moduleInCharge] = profile;
      if (
        !isEmpty(moduleInCharge) &&
        moduleInCharge.includes(ModuleInCharge.carepage) &&
        dataAccessLevel === DataAccessLevel.personal
      ) {
        filter.userIds = [user.id];
      }
    }
    return this.pageGroupsService.findGroups(filter, pagination, headers, request);
  }

  @Get('/count-bots')
  @Auth()
  async countBots(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ){
    return this.pageGroupsService.findCountBot(request, headers)
  }

  @Post('')
  @Auth()
  async createGroup(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Body() body: CreatePageGroupDto,
  ): Promise<PageGroup> {
    return this.pageGroupsService.createGroup(body, request.user, headers);
  }

  @Put(':id')
  @Auth()
  async updateGroup(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Body() body: UpdatePageGroupDto,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<PageGroup> {
    return this.pageGroupsService.updateGroup(id, body, request.user);
  }

  @Get(':id')
  @Auth()
  async getGroup(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Param('id', ParseIntPipe) id: number,
    @Query('countUnreadConversations') countUnreadConversations: boolean,
  ) {
    return this.pageGroupsService.getGroup(id, countUnreadConversations, headers, request);
  }

  @Get(':id/count-unread-conversations')
  @Auth()
  async countPageGroupConversations(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.pageGroupsService.countPageGroupConversations(id);
  }

  @Get(':id/conversations')
  @Auth()
  async getGroupConversations(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Param('id', ParseIntPipe) id: number,
    @Query() filter?: ConversationsFilter,
  ) {
    filter.groupId = id;
    if (filter.filterByLastReason) {
      filter.lastReasonIds = filter.reasonIds;
      filter.excludeLastReasonIds = filter.excludeReasonIds;
    }
    return this.conversationsService.findConversations(filter, request.user.id, request.user);
  }

  @Get(':id/conversations/count-conversations')
  @Auth()
  async getGroupCountConversations(
    @Req() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Param('id', ParseIntPipe) id: number,
    @Query() filters?: CountConversationsFilter,
  ) {
    filters.groupIds = [id];
    return this.conversationsService.countConversations(filters);
  }

  @Post('seen-conversations')
  @Auth()
  async bulkSeenConversations(
    @Query() filter: ConversationsFilter,
    @Req() request: Record<string, any>,
    @Query('groupId') groupId?: number,
  ) {
    filter.groupId = groupId;
    return this.conversationsService.bulkSeenConversations(filter, request.user.id);
  }

  @Post('unseen-conversations')
  @Auth()
  async bulkUnseenConversations(
    @Query() filter: ConversationsFilter,
    @Req() request: Record<string, any>,
    @Query('groupId') groupId?: number,
  ) {
    filter.groupId = groupId;
    return this.conversationsService.bulkUnseenConversations(filter, request.user.id);
  }

  @Post(':id/sync-conversations')
  @Auth()
  async syncConversation(
    @Req() request,
    @Param('id') id: string,
    @Query('skipMessage') skipMessage: string,
    @Query('force') force: string,
  ): Promise<boolean> {
    if (!id) throw new BadRequestException();
    return this.pageGroupsService.syncPageGroupConversations(
      id,
      skipMessage == 'true',
      force === 'true',
    );
  }
}
