import { Body, Controller, Get, Param, Post, Put, Query, Request, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ConversationsService } from '../services/conversations.service';
import { ConversationsFilter } from '../filters/conversations.filter';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { CountConversationsFilter } from '../filters/count-conversations.filter';
import { remove } from 'lodash';
import { Pagination } from '../../../../../core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from '../../../../../core/decorators/pagination/pagination.model';
import * as moment from 'moment-timezone';

@Controller('pages/:id/conversations')
@ApiTags('page-conversations')
export class ConversationsController {
  constructor(private conversationsService: ConversationsService) {}

  @Get('')
  @Auth()
  async getConversations(
    @Request() request,
    @Param('id') id: string,
    @Query() filter?: ConversationsFilter,
  ) {
    filter.pageIds = [id];
    if (filter.filterByLastReason) {
      filter.lastReasonIds = remove(filter.reasonIds, r => r > 0);
      filter.excludeLastReasonIds = remove(filter.excludeReasonIds, r => r > 0);
    }
    return this.conversationsService.findConversations(filter, request.user.id, request.user);
  }

  @Get('count-conversations')
  async countConversations(@Param('id') id: string, @Query() filters?: CountConversationsFilter) {
    filters.pageIds = [id];
    return this.conversationsService.countConversations(filters);
  }

  @Get(':userId')
  @Auth()
  async getConversation(
    @Request() request,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Query('feedId') feedId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.getConversation(pageId, psid, feedId, [
      'user',
      'user.tags',
      'phones',
    ]);
  }

  @Get(':userId/avatar')
  async getConversationAvatar(
    @Request() request,
    // @Param('id') pageId: string,
    @Res() response,
    @Param('userId') userId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    const avatar = await this.conversationsService.getAvatar(pageId, psid);
    if (avatar) return response.redirect(avatar);
    return null;
  }

  @Put(':userId/seen')
  @Auth()
  async seenConversation(
    @Request() request,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Query('feedId') feedId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.seen(pageId, psid, true, request.user.id, feedId || '');
  }

  @Put(':userId/global-id')
  @Auth()
  async updateGlobalId(
    @Request() request,
    @Body('globalId') globalId: string,
    @Param('userId') userId: string,
  ) {
    return this.conversationsService.updateGlobalId(userId, globalId, request.user.id);
  }

  @Put(':userId/unseen')
  @Auth()
  async unseenConversation(
    @Request() request,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Query('feedId') feedId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.seen(pageId, psid, false, request.user.id, feedId || '');
  }

  @Get(':userId/phones')
  @Auth()
  async getConversationPhones(
    @Request() request,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.getConversationPhones(pageId, psid);
  }

  @Get(':userId/seenHistories')
  @Auth()
  async getConversationSeenHistories(
    // @Request() request,
    // @Param('id') id: string,
    @Param('userId') userId: string,
    @Query('feedId') feedId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.getSeenHistories(pageId, psid, feedId);
  }

  @Get(':userId/tagsHistories')
  @Auth()
  async getConversationTagsHistories(
    // @Request() request,
    // @Param('id') id: string,
    @Param('userId') userId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.getTagsHistories(pageId, psid);
  }

  @Get(':userId/histories')
  @Auth()
  async getConversationHistories(
    @Request() request,
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.getConversationHistories(pageId, psid);
  }

  @Get(':userId/referrals')
  @Auth()
  async getConversationReferrals(
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.getConversationReferrals(pageId, psid);
  }

  @Post(':userId/sync')
  @Auth()
  async syncConversation(
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
  ) {
    const [pageId, psid] = userId.split('_');
    return this.conversationsService.syncConversation(pageId, psid);
  }

  @Post(':userId/sync-v2')
  @Auth()
  async syncConversationV2(
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Query('days') days?: number,
  ) {
    const [pageId, psid] = userId.split('_');
    // Validate `days` parameter
    if (days !== undefined) {
      if (isNaN(days) || days <= 0) {
        return false;
      }
    } else {
      days = Number(days); // Default to 7 days
    }
    if (days > 7) days = 7;

    const xDaysAgo = new Date();
    xDaysAgo.setDate(xDaysAgo.getDate() - days);
    xDaysAgo.setHours(0, 0, 0, 0); // Start of the day
    const afterTime = xDaysAgo.getTime(); // Timestamp in milliseconds
    return this.conversationsService.syncConversationV2(pageId, afterTime);
  }

  @Post(':userId/sync-v3')
  @Auth()
  async syncConversationV3(
    // @Param('id') pageId: string,
    @Param('userId') userId: string,
    @Query('hours') hours?: number,
  ) {
    const [pageId, psid] = userId.split('_');
    // Validate `days` parameter
    if (hours !== undefined) {
      if (isNaN(hours) || hours <= 0) {
        return false;
      }
    } else {
      hours = Number(hours); // Default to 7 days
    }
    // if (days > 7) days = 7;

    return this.conversationsService.syncConversationV2(
      pageId,
      moment()
        .subtract(hours, 'hours')
        .valueOf(),
    );
  }
}
