import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { messageConnection } from 'core/constants/database-connection.constant';
import { groupBy, isEmpty, isNil, isUndefined, reduce, remove } from 'lodash';
import { Brackets, IsNull, Repository, SelectQueryBuilder } from 'typeorm';
import { ConversationOrder } from '../../entities/conversation-order.entity';
import { ConversationTagsHistory } from '../../entities/conversation-tags-history.entity';
import { Conversation } from '../../entities/conversation.entity';
import { ConversationsPhones } from '../../entities/conversations-phones.entity';
import { FanPage } from '../../entities/fanpage.entity';
import { SeenHistory } from '../../entities/seen-history.entity';
import { User } from '../../entities/user.entity';
import { SocketService } from '../../socket/socket.service';
import { ConversationsFilter } from '../filters/conversations.filter';
import { CountConversationsFilter } from '../filters/count-conversations.filter';
import { Tag } from '../../entities/tag.entity';
import { RawResponse } from 'core/raw/raw-response';
import { Feed } from '../../entities/feed.entity';
import { buildPaginator, PagingResult } from 'core/lib/cursor-pagination';
import { PageScopedUserCare } from '../../entities/page-scoped-user-care.entity';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';
import { PageScopedUserCareItem } from '../../entities/page-scoped-user-care-item.entity';
import { ConversationReferral } from '../../entities/conversation-referral.entity';
import { BotApiService } from './bot.service';
import axios from 'axios';
import { PaginationOptions } from '../../../../../core/decorators/pagination/pagination.model';
import { OrderStatus } from 'core/enums/order-status.enum';
import { CareStatus } from 'apps/facebook-bot/src/enums/care-status.enum';

import { AuthUser } from 'core/interfaces/auth-user.interface';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';

@Injectable()
export class ConversationsService {
  constructor(
    @InjectRepository(User, messageConnection)
    private userRepo: Repository<User>,
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
    @InjectRepository(FanPage, messageConnection)
    private fanPageRepository: Repository<FanPage>,
    @InjectRepository(ConversationsPhones, messageConnection)
    private conversationPhonesRepo: Repository<ConversationsPhones>,
    @InjectRepository(SeenHistory, messageConnection)
    private seenHistoriesRepo: Repository<SeenHistory>,
    @InjectRepository(ConversationTagsHistory, messageConnection)
    private cTagsHistoriesRepo: Repository<ConversationTagsHistory>,
    @InjectRepository(ConversationReferral, messageConnection)
    private cReferralsRepo: Repository<ConversationReferral>,
    @InjectRepository(ConversationOrder, messageConnection)
    private conversationOrdersRepo: Repository<ConversationOrder>,
    @InjectRepository(Tag, messageConnection)
    private tagsRepo: Repository<Tag>,
    @InjectRepository(PageScopedUserCare, messageConnection)
    private caresRepo: Repository<PageScopedUserCare>,
    @InjectRepository(PageScopedUser, messageConnection)
    private suRepo: Repository<PageScopedUser>,
    @InjectRepository(PageScopedUserCareItem, messageConnection)
    private suCareItemsRepo: Repository<PageScopedUserCareItem>,
    private socketGateway: SocketService,
    private amqpConnection: AmqpConnection,
    private botApiService: BotApiService,
  ) {}

  async updateGlobalId(psId: string, globalId: string, requestUserId: number) {
    const [pageId, scopedUserId] = psId.split('_');
    await this.conversationRepo.update(
      {
        scopedUserId,
        pageId,
        userGlobalId: IsNull(),
      },
      {
        userGlobalId: globalId,
        lastUpdatedBy: requestUserId,
      },
    );
    await this.suRepo.update(
      { id: psId, globalId: IsNull() },
      { globalId, updatedBy: requestUserId },
    );
    return true;
  }

  async seen(pageId: string, userId: string, seen: boolean, employeeId: number, feedId: string) {
    const conversation = await this.conversationRepo
      .createQueryBuilder('c')
      // .andWhere('c.pageId = :pageId', { pageId })
      .andWhere('c.scopedUserId = :userId', { userId })
      .andWhere('c.feedId = :feedId', { feedId })
      .leftJoin(
        'c.phones',
        'phones',
        `"phones"."page_id" = "c"."page_id"
            AND "phones"."scoped_user_id" = "c"."scoped_user_id"
            AND ("phones"."feed_id" = "c"."feed_id" OR  ("phones"."feed_id" IS NULL AND "c"."feed_id" = ''))`,
      )
      .addSelect(['phones.phone', 'phones.messageId', 'phones.offset', 'phones.length'])
      .leftJoinAndMapOne('c.page', FanPage, 'p', 'p.id = c.pageId')
      .leftJoinAndMapOne(
        'c.user',
        PageScopedUser,
        'u',
        'u.pageId = c.pageId AND u.scopedUserId = c.scopedUserId',
      )
      .leftJoinAndSelect('u.tags', 'tags')
      .leftJoin('u.cares', 'cares')
      .addSelect(['cares.id', 'cares.status', 'cares.createdAt', 'cares.scopedUserId'])
      .leftJoin('cares.items', 'ci')
      .addSelect(['ci.id', 'ci.reasonId', 'ci.createdAt'])
      .leftJoin('ci.reason', 'reason')
      .addSelect(['reason.id', 'reason.key'])
      .getOne();
    // if (conversation && !conversation.userGlobalId && conversation.feedId == '') {
    //   console.log('start find global id', conversation.scopedUserId);
    //   await this.amqpConnection.publish(
    //     'facebook-conversation-event',
    //     'update-conversation-global-id',
    //     {
    //       pageId,
    //       scopedUserId: userId,
    //     },
    //   );
    // }

    if (conversation && !conversation.snippet && conversation.feedId == '') {
      await this.amqpConnection.publish('facebook-conversation-event', 'fetch-conversation-by-id', {
        pageId: conversation.pageId,
        scopedUserId: conversation.scopedUserId,
      });
    }

    if (!conversation) throw new NotFoundException(`Conversation ${pageId}/${userId} not found`);
    const update = seen
      ? {
          lastSeenAt: new Date(),
          lastSeenBy: employeeId,
          unread: false,
          lastUpdatedBy: employeeId,
        }
      : { unread: true, lastUpdatedBy: employeeId };
    const query = this.conversationRepo
      .createQueryBuilder('c')
      .update(update)
      .where({
        pageId,
        scopedUserId: userId,
        feedId,
        // unread: seen
      });
    const result = await query.execute();
    // console.log(
    //   `seen Conversation ${pageId}/${userId} result.affected`,
    //   result.affected,
    //   query.getQueryAndParameters(),
    // );
    if (result.affected)
      try {
        conversation.unread = !seen;
        await this.socketGateway.emitConversations([conversation]);
      } catch (e) {}

    // Save seen histories
    if (update.lastSeenBy) {
      await this.seenHistoriesRepo.save({
        pageId,
        scopedUserId: userId,
        seenById: update.lastSeenBy,
        feedId,
      });
    }

    return plainToInstance(Conversation, { ...conversation, ...update });
  }

  async getAvatar(pageId: string, userId: string) {
    try {
      const api = await this.botApiService.getPageTokenApi(pageId);
      const res = await api({
        method: 'GET',
        url: `${userId}/picture`,
        params: {
          redirect: 'false',
          type: 'large',
        },
      });
      console.log('res', res);
      await this.amqpConnection.publish('facebook-conversation-event', 'save-user-info', {
        id: `${pageId}_${userId}`,
        avatar: res?.data?.url,
      });
      return res?.data?.url;
    } catch (e) {
      console.log('e', e);
      const u = await this.suRepo.findOne({
        where: {
          pageId,
          scopedUserId: userId,
        },
        select: ['globalId'],
      });
      try {
        const res = await axios.get(`https://graph.facebook.com/${u.globalId}/picture`, {
          params: {
            redirect: 'false',
            type: 'large',
            access_token: '2712477385668128%7Cb429aeb53369951d411e1cae8e810640',
          },
        });
        await this.amqpConnection.publish('facebook-conversation-event', 'save-user-info', {
          id: `${pageId}_${userId}`,
          avatar: res?.data?.data?.url,
        });
        return res.data?.data?.url;
      } catch (e) {
        return null;
      }
    }
  }

  async getConversation(pageId: string, userId: string, feedId = '', relations: string[] = []) {
    const qb = this.conversationRepo
      .createQueryBuilder('c')
      .innerJoin('c.page', 'page')
      .where({ pageId, scopedUserId: userId, feedId });
    if (relations) {
      for (const relation of relations) {
        switch (relation) {
          case 'user':
          case 'user.tags':
            if (qb.expressionMap.aliases.findIndex(alias => alias.name === 'u') < 0)
              qb.leftJoinAndMapOne(
                'c.user',
                PageScopedUser,
                'u',
                'u.pageId = c.pageId AND u.scopedUserId = c.scopedUserId',
              );
            if (relation === 'user.tags') qb.leftJoinAndSelect('u.tags', 'tags');
            break;
          case 'phones':
            qb.leftJoinAndMapMany(
              'c.phones',
              ConversationsPhones,
              'phones',
              `"phones"."page_id" = "c"."page_id"
                  AND "phones"."scoped_user_id" = "c"."scoped_user_id"
                  AND ("phones"."feed_id" = "c"."feed_id" OR  ("phones"."feed_id" IS NULL AND "c"."feed_id" = ''))`,
            );
          default:
            break;
        }
      }
    }
    const conversation = await qb.getOne();
    return conversation;
  }

  getConversationsQuery(
    filter: ConversationsFilter,
    user?: AuthUser,
  ): SelectQueryBuilder<Conversation> {
    const {
      tagIds,
      query,
      hasPhone,
      unread,
      lastSentByPage,
      groupId,
      getNumberOfUnread,
      pageIds,
      lastMessageFrom,
      lastMessageTo,
      hasOrder,
      assigned,
      assignedTo,
      notAssignedTo,
      usedToBeAssigned,
      status,
      isComment,
      createdAtFrom,
      createdAtTo,
      reasonIds,
      excludeReasonIds,
      lastReasonIds,
      excludeLastReasonIds,
      numberOfRepeats,
      orderStatuses,
      isBotEnabled,
      isHideComment,
      adIds,
      filterByLastReason,
      onlyComment,
    } = filter;

    const queryBuilder = this.conversationRepo
      .createQueryBuilder('c')
      .leftJoin('c.page', 'p', 'p.id = c.pageId');
    // .leftJoin(
    //   'page_scoped_users',
    //   'u',
    //   'c.pageId = u.pageId AND c.scopedUserId = u.scopedUserId',
    // );
    this.addConditionCareUserConversation(queryBuilder, user);

    if (pageIds) {
      queryBuilder.andWhere('c.pageId IN (:...pageIds)', { pageIds });
    }
    if (groupId) {
      queryBuilder.andWhere('p.groupId = :groupId', { groupId });
    }

    if (filterByLastReason) {
      if (!isEmpty(lastReasonIds)) {
        this.leftJoinLastCareItem(queryBuilder);
        // -1  = Chưa xử lý
        if (lastReasonIds.includes(-1)) {
          remove(lastReasonIds, id => id === -1);
          queryBuilder.andWhere(
            new Brackets(sqb => {
              sqb.orWhere(`lci.reason_id IN (:...lastReasonIds)`, { lastReasonIds });
              sqb.orWhere(`lci.reason_id IS NULL`);
            }),
          );
        } else {
          remove(lastReasonIds, id => id === -1);
          queryBuilder.andWhere(`lci.reason_id IN (:...lastReasonIds)`, { lastReasonIds });
        }
      }

      if (!isEmpty(excludeLastReasonIds)) {
        this.leftJoinLastCareItem(queryBuilder);
        // -1  = Chưa xử lý
        if (excludeLastReasonIds.includes(-1)) {
          const excludeLastReasonIdsNew = excludeLastReasonIds.map(value =>
            value === -1 ? null : value,
          );
          queryBuilder.andWhere(`lci.reason_id NOT IN (:...excludeLastReasonIdsNew)`, {
            excludeLastReasonIdsNew,
          });
        } else {
          remove(excludeLastReasonIds, id => id === -1);
          queryBuilder.andWhere(`lci.reason_id NOT IN (:...excludeLastReasonIds)`, {
            excludeLastReasonIds,
          });
        }
      }
    } else {
      if (reasonIds) {
        this.leftJoinScopedUserCareItems(queryBuilder);
        const noReasonId = remove(reasonIds, id => id === -1);
        queryBuilder
          .andWhere(
            new Brackets(sqb => {
              if (!isEmpty(reasonIds)) sqb.orWhere(`suci.reason_id IN (:...reasonIds)`);
              if (!isEmpty(noReasonId)) sqb.orWhere(`suci.reason_id IS NULL`);
            }),
          )
          .setParameters({ reasonIds });
      }

      if (!isEmpty(excludeReasonIds)) {
        remove(excludeReasonIds, id => id === -1);
        const sqb = this.getConversationsQuery({
          pageIds,
          groupId,
          reasonIds: excludeReasonIds,
        }).select(['c.page_id', 'c.scoped_user_id']);
        queryBuilder
          .andWhere(`(c.page_id, c.scoped_user_id) NOT IN (${sqb.getQuery()})`)
          .setParameters(sqb.getParameters());
      }
    }

    if (numberOfRepeats) {
      const subQb = this.caresRepo
        .createQueryBuilder('crs')
        .select('crs.scopedUserId', 'scoped_user_id')
        .addSelect('COUNT (crs.id)', 'cares_count')
        .groupBy('crs.scopedUserId');
      queryBuilder.leftJoin(
        `(${subQb.getQuery()})`,
        'crs',
        'c.scoped_user_id = crs.scoped_user_id AND crs.cares_count > :numberOfRepeats',
        { numberOfRepeats },
      );
    }

    if (!isNil(assigned) || assignedTo || notAssignedTo || usedToBeAssigned) {
      // if (usedToBeAssigned) {
      //   queryBuilder.leftJoin(
      //     ScopedUserCare,
      //     'cares',
      //     'cares.scoped_user_id = u.id AND cares.careUserId = :userId',
      //     { userId },
      //   );
      // }
      queryBuilder.andWhere(
        new Brackets(qb => {
          if (!isNil(assigned)) {
            if (assigned) {
              if (isEmpty(assignedTo)) qb.orWhere('c.careUserId IS NOT NULL');
            } else qb.orWhere('c.careUserId IS NULL');
          }
          if (assignedTo) {
            qb.orWhere('c.careUserId IN (:...assignedTo)', { assignedTo: assignedTo });
          }
          if (notAssignedTo) {
            qb.orWhere('c.careUserId NOT IN (:...notAssignedTo)', {
              notAssignedTo: notAssignedTo,
            });
          }
          // if (usedToBeAssigned) qb.orWhere('cares.id IS NOT NULL');
        }),
      );
    }
    if (status) {
      const careStatusKeys = Object.keys(CareStatus).filter(key => isNaN(Number(key)));
      if (status.length !== careStatusKeys.length)
        queryBuilder.andWhere('c.status IN (:...status)', { status });
    }
    if (!isNil(hasPhone)) {
      this.leftJoinConversationPhones(queryBuilder);
      if (hasPhone === false) queryBuilder.andWhere('phones.id IS NULL');
      if (hasPhone === true) queryBuilder.andWhere('phones.id IS NOT NULL');
    }
    if (!isNil(unread)) queryBuilder.andWhere('c.unread = :unread', { unread });
    if (lastMessageFrom) queryBuilder.andWhere('c.updatedAt >= :from', { from: lastMessageFrom });
    if (lastMessageTo) queryBuilder.andWhere('c.updatedAt < :to', { to: lastMessageTo });

    if (createdAtFrom)
      queryBuilder.andWhere('(c.createdAt IS NOT NULL AND c.createdAt >= :createdAtFrom)', {
        createdAtFrom,
      });
    if (createdAtTo)
      queryBuilder.andWhere('(c.createdAt IS NOT NULL AND c.createdAt < :createdAtTo)', {
        createdAtTo,
      });

    if (lastSentByPage === false)
      queryBuilder.andWhere('c.lastSentByPage = :lastSentByPage', {
        lastSentByPage,
      });

    if (onlyComment) {
      this.leftJoinFeeds(queryBuilder);
      queryBuilder.andWhere(`c.feed_id != ''`);
    }

    if (!isNil(isComment)) {
      if (isComment) {
        this.leftJoinFeeds(queryBuilder);
        queryBuilder
          .andWhere(`c.feed_id != ''`)
          .andWhere('(feed.hideComments IS NULL OR feed.hideComments = FALSE)');
      } else queryBuilder.andWhere(`c.feed_id = ''`);
    }

    if (!isNil(isHideComment)) {
      if (isHideComment) {
        this.leftJoinFeeds(queryBuilder);
        queryBuilder.andWhere('feed.hideComments = TRUE').andWhere(`c.feed_id != ''`);
      } else queryBuilder.andWhere(`c.feed_id = ''`);
    }

    if (!isEmpty(query)) {
      this.leftJoinConversationPhones(queryBuilder);
      this.leftJoinPageScopedUser(queryBuilder);

      queryBuilder.andWhere(`(u.name ~* :query OR phones.phone ~* :query)`, {
        query: filter.query.toLowerCase().trim(),
      });
      // .leftJoin(
      //   Message,
      //   'ms',
      //   `ms.id = phones.message_id
      //     AND ms.scoped_user_id = phones.scoped_user_id`,
      // )
      // .addSelect('(CASE WHEN ms.text IS NULL THEN c.snippet ELSE ms.text END)', 'c_snippet');
      // .andWhere('(u.name ~* :query OR m.timestamp IS NOT NULL)', {
      //   query: filters.query.toLowerCase().trim(),
      // })
      // .leftJoin(
      //   qb => {
      //     qb.from(Message, 'm')
      //       .select('scoped_user_id')
      //       .addSelect('MAX(timestamp)', 'timestamp')
      //       // .select(['scoped_user_id', 'text', 'timestamp', 'id'])
      //       .andWhere(`to_tsvector('simple', m."text") @@ to_tsquery('simple', :search)`, {
      //         search: query
      //           .toLowerCase()
      //           .split(' ')
      //           .join(' & '),
      //       })
      //       // .limit(1)
      //       .groupBy('scoped_user_id');
      //     if (pageIds)
      //       qb.andWhere('m.pageId IN (:...pageId)', {
      //         pageId: pageIds,
      //       });
      //     if (groupId)
      //       qb.leftJoin(FanPage, 'p', 'p.id = m.page_id').andWhere('p.group_id = :groupId', {
      //         groupId,
      //       });
      //     return qb;
      //   },
      //   'm',
      //   'm.scoped_user_id = c.scopedUserId',
      // )
      // .leftJoin(Message, 'ms', 'ms.timestamp = m.timestamp AND ms.scoped_user_id = m.scoped_user_id')
      // .addSelect('(CASE WHEN ms.text IS NULL THEN c.snippet ELSE ms.text END)', 'c_snippet');
      // .orderBy(
      //   '(CASE WHEN ms.text IS NULL THEN c.updated_at ELSE ms.timestamp END)',
      //   'DESC',
      // );
    }

    if (tagIds) {
      this.leftJoinPageScopedUser(queryBuilder);
      queryBuilder
        .leftJoin('page_scoped_users_tags', 'sut', 'u.id = sut.scoped_user_id')
        .andWhere(
          new Brackets(sqb => {
            const idx = tagIds.indexOf(-1);
            if (idx !== -1) {
              tagIds.splice(idx, 1);
              sqb.orWhere('sut.tag_id IS NULL');
            }
            if (!isEmpty(tagIds)) sqb.orWhere('sut.tag_id IN (:...tagIds)');
          }),
        )
        .setParameters({ tagIds });
    }
    if (getNumberOfUnread) {
      this.leftJoinPageScopedUser(queryBuilder);
      queryBuilder
        .leftJoin(
          'messages',
          'mes',
          'mes.page_id = c.page_id AND mes.scoped_user_id = c.scoped_user_id AND c.last_seen_at IS NOT NULL AND mes.timestamp > c.last_seen_at',
        )
        .addSelect('COUNT(mes.id) AS number_of_unread_messages')
        .addGroupBy('c.page_id, c.scoped_user_id, tags.id, phones.id, u.id');
    }
    if (query && getNumberOfUnread) queryBuilder.addGroupBy('ms.text');
    if (hasOrder) {
      this.leftJoinConversationOrders(queryBuilder);
      queryBuilder.andWhere('orders.order_id IS NOT NULL');
    }
    if (!isEmpty(orderStatuses)) {
      this.leftJoinConversationOrders(queryBuilder);
      queryBuilder.andWhere('orders.status IN (:...orderStatuses)', { orderStatuses });
    }
    if (!isNil(isBotEnabled))
      queryBuilder.andWhere('c.isBotEnabled = :isBotEnabled', { isBotEnabled });
    if (!isEmpty(adIds)) {
      queryBuilder.innerJoin(
        'conversation_referrals',
        'referrals',
        `c.page_id = referrals.page_id AND c.scoped_user_id = referrals.scoped_user_id AND referrals.source = 'ADS' AND referrals.ad_id IN (:...adIds)`,
        { adIds },
      );
    }
    return queryBuilder;
  }

  leftJoinPageScopedUser(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'u') > -1;
    if (hasJoined) return qb;

    qb.leftJoin(
      'page_scoped_users',
      'u',
      'c.pageId = u.pageId AND c.scopedUserId = u.scopedUserId',
    );
  }

  addConditionCareUserConversation(qb: SelectQueryBuilder<Conversation>, user: AuthUser) {
    if (!user) return qb;
    qb.andWhere(
      new Brackets(sqb => {
        for (const record of user.profiles) {
          const [dataAccessLevel, moduleInCharge] = record;
          sqb.orWhere(
            new Brackets(pQb => {
              if (
                !isEmpty(moduleInCharge) &&
                dataAccessLevel === DataAccessLevel.personal &&
                moduleInCharge.includes(ModuleInCharge.carepage)
              ) {
                pQb.where(`(c.care_user_id IS NULL OR c.care_user_id = ${user.id})`);
              }
            }),
          );
        }
      }),
    );
    return qb;
  }

  async findConversations(
    filter: ConversationsFilter = {},
    userId: number,
    user?: AuthUser,
  ): Promise<PagingResult<Conversation>> {
    const { limit, beforeCursor, afterCursor, sortByUnread, query } = filter;
    const paginationKeys: (keyof Conversation)[] = ['updatedAt'];
    if (sortByUnread) paginationKeys.unshift('unread');

    const paging = buildPaginator({
      entity: Conversation,
      alias: 'c',
      paginationKeys,
      paginationUniqueKey: 'scopedUserId',
      query: {
        limit,
        order: 'DESC',
        beforeCursor,
        afterCursor,
      },
    });
    const queryBuilder = this.getConversationsQuery(filter, user);
    const response = await paging.paginate(queryBuilder);
    if (isEmpty(response.data)) return response;

    const [scopedUserIds, keyPairs] = reduce(
      response.data,
      (prev, it) => {
        prev[0].push(`${it.pageId}_${it.scopedUserId}`);
        prev[1].push(`('${it.pageId}', '${it.scopedUserId}')`);
        return prev;
      },
      [[], []],
    );

    const hasOrdersSubQb = this.conversationOrdersRepo
      .createQueryBuilder('co')
      .select(`DISTINCT(co.page_id || '_' || co.fb_scoped_user_id)`, 'id')
      .where(`(co.page_id, co.fb_scoped_user_id) IN (${keyPairs.join(',')})`)
      .andWhere('co.status != :status', { status: OrderStatus.Draft });

    const [tags, cares, users, hasOrders, phones] = await Promise.all([
      this.tagsRepo
        .createQueryBuilder('t')
        .innerJoin(
          'page_scoped_users_tags',
          'sut',
          'sut.tag_id = t.id AND sut.scoped_user_id IN (:...rScopedUserIds)',
          { rScopedUserIds: scopedUserIds },
        )
        .select('t.id', 'id')
        .addSelect('t.name', 'name')
        .addSelect('sut.scoped_user_id', 'scoped_user_id')
        .getRawMany(),
      this.caresRepo
        .createQueryBuilder('cares')
        .select(['cares.id', 'cares.status', 'cares.createdAt', 'cares.scopedUserId'])
        .leftJoin('cares.items', 'ci')
        .addSelect(['ci.id', 'ci.reasonId', 'ci.createdAt'])
        .leftJoin('ci.reason', 'reason')
        .addSelect(['reason.id', 'reason.key', 'reason.ignoreDistribution'])
        .where('cares.scoped_user_id IN (:...rScopedUserIds)', { rScopedUserIds: scopedUserIds })
        .getMany(),
      this.suRepo.findByIds(scopedUserIds),
      hasOrdersSubQb.getRawMany(),
      this.conversationPhonesRepo
        .createQueryBuilder('c')
        .where(`(c.page_id,c.scoped_user_id) IN (${keyPairs.join(',')})`)
        .getMany(),
    ]);

    response.data = plainToInstance(Conversation, response.data);

    const tagsLookup = tags.reduce((prev: Record<string, Tag[]>, t) => {
      prev[t.scoped_user_id] = [...(prev[t.scoped_user_id] || []), plainToInstance(Tag, t)];
      return prev;
    }, {});

    const hasOrderLookup = groupBy(hasOrders, 'id');

    const usersLookup = users.reduce((prev: Record<string, PageScopedUser>, t) => {
      prev[t.id] = t;
      return prev;
    }, {});

    const caresLookup = cares.reduce((prev: Record<string, PageScopedUserCare[]>, c) => {
      prev[c.scopedUserId] = [...(prev[c.scopedUserId] || []), c];
      return prev;
    }, {});

    const phoneLookup = phones.reduce((prev, phone) => {
      const key = phone.pageId + '_' + phone.scopedUserId + '/' + String(phone.feedId || '');
      if (!prev[key]) {
        prev[key] = [phone];
        return prev;
      }

      prev[key].push(phone);
      return prev;
    }, {});

    let regex;
    if (!isEmpty(query)) {
      regex = new RegExp(query, 'gi');
    }
    response.data.forEach(i => {
      const suId = `${i.pageId}_${i.scopedUserId}`;
      if (usersLookup[suId]) {
        i.user = usersLookup[suId];
        if (tagsLookup[suId]) i.user.tags = tagsLookup[suId];
        if (caresLookup[suId]) i.user.cares = caresLookup[suId];
      }
      if (hasOrderLookup[suId]) {
        i.hasOrders = true;
      }
      i.phones = phoneLookup[suId + '/' + String(i.feedId)];

      // console.log('get conversations', i);
      // if (i && !i.userGlobalId && i.feedId == '') {
      //   console.log('get conversations scan global id', i);
      //   this.amqpConnection.publish(
      //     'facebook-conversation-event',
      //     'update-conversation-global-id',
      //     {
      //       pageId: i.pageId,
      //       scopedUserId: i.scopedUserId,
      //     },
      //   );
      // }
      // if (i && !i.snippet && i.feedId == '') {
      //   this.amqpConnection.publish('facebook-conversation-event', 'fetch-conversation-by-id', {
      //     pageId: i.pageId,
      //     scopedUserId: i.scopedUserId,
      //   });
      // }
      // if (regex && i.snippet) {
      //   const words = i.snippet?.split(' ')?.map(w => w.trim());
      //   const highlightAt = words.findIndex(w => regex.test(w));
      //   if (highlightAt < 0) {
      //     return;
      //   }
      //   words[highlightAt] = words[highlightAt].replace(regex, e => `<b>${e}</b>`);
      //   let replaced = false;
      //   if (highlightAt > 1) {
      //     replaced = true;
      //     words.splice(0, highlightAt - 1);
      //   }
      //   i.snippet = (replaced ? '...' : '') + words.join(' ');
      // }
      // i.page = pageLookup[i.pageId][0];
    });
    return response;
  }

  leftJoinFeeds(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'feed') > -1;
    if (hasJoined) return qb;

    qb.leftJoin(Feed, 'feed', `c.feedId <> '' AND c.feedId = feed.id`);
  }

  leftJoinConversationPhones(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'phones') > -1;
    if (hasJoined) return qb;

    qb.leftJoin(
      'c.phones',
      'phones',
      `"phones"."page_id" = "c"."page_id"
        AND "phones"."scoped_user_id" = "c"."scoped_user_id"
        AND ("phones"."feed_id" = "c"."feed_id" OR  ("phones"."feed_id" IS NULL AND "c"."feed_id" = ''))`,
    );
  }

  leftJoinConversationOrders(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'orders') > -1;
    if (hasJoined) return qb;

    qb.leftJoinAndMapMany(
      'c.orders',
      ConversationOrder,
      'orders',
      'orders.fb_scoped_user_id = c.scoped_user_id',
    );
  }

  leftJoinUserCares(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'cares') > -1;
    if (hasJoined) return qb;

    qb.leftJoin('u.cares', 'cares');
  }

  leftJoinCareItems(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'ci') > -1;
    if (hasJoined) return qb;

    qb.leftJoin('cares.items', 'ci');
  }

  leftJoinScopedUserCareItems(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'suci') > -1;
    if (hasJoined) return qb;
    this.leftJoinPageScopedUser(qb);
    qb.leftJoin(PageScopedUserCareItem, 'suci', 'suci.scoped_user_id = u.id');
  }

  leftJoinLastCareItem(qb: SelectQueryBuilder<Conversation>) {
    const aliases = qb.expressionMap.aliases;
    const hasJoined = aliases.findIndex(alias => alias.name === 'lci') > -1;
    if (hasJoined) return qb;
    this.leftJoinPageScopedUser(qb);
    qb.leftJoin(PageScopedUserCareItem, 'lci', 'lci.id = u.latest_care_item_id');
  }

  async getConversationPhones(pageId: string, userId: string): Promise<ConversationsPhones[]> {
    return await this.conversationPhonesRepo.find({
      where: { pageId, scopedUserId: userId },
    });
  }

  async getSeenHistories(
    pageId: string,
    scopedUserId: string,
    feedId: string,
  ): Promise<SeenHistory[]> {
    const subQb = this.seenHistoriesRepo
      .createQueryBuilder(`h`)
      .andWhere(`h.page_id =${pageId}`)
      .andWhere(`h.scoped_user_id =${scopedUserId}`)
      .select('h.id', 'id')
      .orderBy('h.created_at', 'DESC')
      .limit(5)
      .offset(0);

    await this.seenHistoriesRepo
      .createQueryBuilder('sh')
      .where(`id NOT IN(${subQb.getQuery()})`)
      .andWhere(
        `(page_id, scoped_user_id, feed_id) = (${pageId}, ${scopedUserId}, ${
          isUndefined(feedId) ? "''" : feedId
        })`,
      )
      .delete()
      .execute();
    return await this.seenHistoriesRepo.find({
      where: { pageId, scopedUserId },
      order: { createdAt: 'DESC' },
    });
  }

  async getTagsHistories(pageId: string, scopedUserId: string): Promise<ConversationTagsHistory[]> {
    return await this.cTagsHistoriesRepo.find({
      where: { pageId, scopedUserId },
      order: { createdAt: 'DESC' },
    });
  }

  async syncConversation(pageId: string, scopedUserId: string, skipMessage?: boolean) {
    await this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-conversation', {
      id: pageId,
      skipMessage,
      scopedUserId,
      force: true,
    });
    return true;
  }

  async syncConversationV2(pageId: string, afterTime: number) {
    await this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-conversation', {
      id: pageId,
      force: true,
      afterTime,
    });
    return true;
  }

  async getConversationReferrals(pageId: string, scopedUserId: string) {
    return this.cReferralsRepo.find({
      pageId,
      scopedUserId,
    });
  }

  async getConversationHistories(pageId: string, scopedUserId: string) {
    return [];
    // const conversation = await this.conversationRepo
    //   .createQueryBuilder('c')
    //   .select('c.userGlobalId')
    //   .andWhere('c.pageId = :pageId', { pageId })
    //   .andWhere('c.scopedUserId = :scopedUserId', { scopedUserId })
    //   .getOne();
    // const fbGlobalId = conversation?.userGlobalId;

    // const conversationLogsQb = this.logsRepo
    //   .createQueryBuilder('logs')
    //   .andWhere(
    //     `(logs.table_name = 'conversations' AND logs.record_id = :pageId AND logs.record_secondary_id = :scopedUserId)`,
    //     { pageId, scopedUserId },
    //   )
    //   .orWhere(
    //     `(logs.parent_table_name = 'conversations' AND logs.parent_id = :pageId AND logs.parent_secondary_id = :scopedUserId)`,
    //   );
    // if (fbGlobalId)
    //   conversationLogsQb.orWhere(
    //     `(logs.table_name = 'users_reports' AND logs.parent_id = :fbGlobalId)`,
    //     {
    //       fbGlobalId,
    //     },
    //   );
    // const conversationLogs = await conversationLogsQb.getMany();

    // // console.log(`conversation`, conversation);
    // if (!fbGlobalId) return conversationLogs;

    // const ordersQb = this.conversationOrdersRepo
    //   .createQueryBuilder('conversationOrder')
    //   .andWhere(`conversationOrder.pageId = :pageId`, { pageId })
    //   .andWhere(`conversationOrder.fbGlobalId = :fbGlobalId`, { fbGlobalId });
    // const orders = await ordersQb.getMany();
    // const orderIds = orders.map(order => order.orderId);

    // const { data: orderLogs } = await this.amqpConnection.request({
    //   exchange: 'order-service',
    //   routingKey: 'get-order-logs',
    //   payload: { ids: orderIds },
    //   timeout: 10000,
    // });

    // const { data: customerLogs } = await this.amqpConnection.request({
    //   exchange: 'order-service',
    //   routingKey: 'get-customer-logs',
    //   payload: { globalIds: [fbGlobalId] },
    //   timeout: 10000,
    // });

    // const logs = orderBy(
    //   [...conversationLogs, ...(orderLogs as SystemLog[]), ...(customerLogs as SystemLog[])],
    //   'createdAt',
    //   'desc',
    // );

    // return logs;
  }

  async countConversations(filter: CountConversationsFilter) {
    const { groupBy, isUnread, groupIds, pageIds } = filter;
    const qb = this.conversationRepo
      .createQueryBuilder('conversation')
      .select('COUNT(*)', 'count')
      .addSelect(`conversation.${groupBy}`, groupBy)
      .andWhere(`conversation.${groupBy} IS NOT NULL`)
      .groupBy(`conversation.${groupBy}`);
    if (pageIds) qb.andWhere('conversation.pageId IN (:...pageIds)', { pageIds });
    if (groupIds)
      qb.leftJoin(FanPage, 'pages', 'conversation.pageId = pages.id').andWhere(
        'pages.groupId IN (:...groupIds)',
        {
          groupIds,
        },
      );
    if (isUnread) qb.andWhere('conversation.unread = :isUnread', { isUnread });

    const data = await qb.getRawMany();
    return reduce(
      data,
      (prev, next) => {
        prev[next[`${groupBy}`]] = next.count;
        return prev;
      },
      {},
    );
  }

  async bulkSeenConversations(filter: ConversationsFilter, userId: number) {
    const { groupId, pageIds, createdAtFrom, createdAtTo } = filter;
    if (!groupId && isEmpty(pageIds))
      throw new BadRequestException('Page group id or Page ids must be provided');

    // const pagesQb = this.fanPageRepository.createQueryBuilder('p').select('p.id');
    // if (groupId) pagesQb.andWhere('p.groupId = :groupId', { groupId });
    // if (!isEmpty(pageIds)) pagesQb.andWhere('p.id IN (:...pageIds)', { pageIds });

    // const qb = this.conversationRepo
    //   .createQueryBuilder('c')
    //   .where('unread = TRUE')
    //   .andWhere(`page_id IN (${pagesQb.getQuery()})`)
    //   .setParameters(pagesQb.getParameters());
    // if (createdAtFrom) qb.andWhere('created_at >= :createdAtFrom', { createdAtFrom });
    // if (createdAtTo)
    //   qb.andWhere('(created_at < :createdAtTo OR updated_at < :createdAtTo)', { createdAtTo });

    filter.unread = true;
    const qb = this.getConversationsQuery(filter).select([
      'c.page_id',
      'c.scoped_user_id',
      'c.feed_id',
    ]);
    // .andWhere(`page_id IN (${pagesQb.getQuery()})`)
    // .setParameters(pagesQb.getParameters());

    const result = this.conversationRepo
      .createQueryBuilder()
      .update({
        lastSeenAt: new Date(),
        lastSeenBy: userId,
        unread: false,
        lastUpdatedBy: userId,
      })
      .where(`(page_id, scoped_user_id, feed_id) IN (${qb.getQuery()})`)
      .setParameters(qb.getParameters())
      .execute();
    return new RawResponse(result);
  }

  async bulkUnseenConversations(filter: ConversationsFilter, userId: number) {
    const { groupId, pageIds } = filter;
    if (!groupId && isEmpty(pageIds))
      throw new BadRequestException('Page group id or Page ids must be provided');

    filter.unread = false;
    const qb = this.getConversationsQuery(filter).select([
      'c.page_id',
      'c.scoped_user_id',
      'c.feed_id',
    ]);

    const result = this.conversationRepo
      .createQueryBuilder()
      .update({
        unread: true,
        lastUpdatedBy: userId,
      })
      .where(`(page_id, scoped_user_id, feed_id) IN (${qb.getQuery()})`)
      .setParameters(qb.getParameters())
      .execute();
    return new RawResponse(result);
  }
}
