import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { messageConnection } from 'core/constants/database-connection.constant';
import { FindConditions, IsNull, Not, Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import login, { IFbApi } from '../../facebook-api';
import { cloneDeep, isEmpty, shuffle, sortBy } from 'lodash';
import * as moment from 'moment';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import { ScopedUserPage } from '../../entities/scoped-user-page.entity';
import axios, { AxiosError } from 'axios';
import { FACEBOOK_API_BASE_URL } from '../../constants/fb-api-endpoints.constant';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { UserType } from '../../enums/user-type.enum';
import { ErrorCode } from '../../enums/error-code.enum';
import { UserSession } from '../../entities/user-sessions.entity';
import { UserActive, UserStatus } from '../../enums/user-status.enum';
import { Promise } from 'bluebird';

export interface IFbApiRequest {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, any>;
  data?: any;
  headers?: Record<string, any>;
}

export type FbTokenApi = ({ url, method, params, data, headers }: IFbApiRequest) => Promise<any>;

interface IFbError {
  error: {
    message: string;
    type: string;
    code: number;
    error_subcode: number;
    error_user_title: string;
    error_user_msg: string;
    fbtrace_id: string;
  };
}

export interface FbApi extends IFbApi {
  refreshBots: () => void;
}

type IFbApiKey = Array<keyof IFbApi>;

const excludesApis: (keyof IFbApi)[] = [
  'replyComment',
  'replyCommentPublic',
  'likeComment',
  'unlikeComment',
  // , 'duplicateAttachment', 'uploadAttachments', 'forwardAttachment'
];

const apiLimits: {
  [key in keyof IFbApi]?: number;
} = {
  duplicateAttachment: 3,
  uploadAttachments: 1,
  forwardAttachment: 1,
  replyComment: 1,
  replyCommentPublic: 1,
  likeComment: 1,
  unlikeComment: 1,
};

const delayFunctions: Record<string, number> = {
  getThreadList: 5,
};

@Injectable()
export class BotApiService {
  constructor(
    @InjectRepository(User, messageConnection)
    private uRepository: Repository<User>,
    @InjectRepository(UserSession, messageConnection)
    private usRepository: Repository<UserSession>,
    @InjectRepository(ScopedUserPage, messageConnection)
    private supRepository: Repository<ScopedUserPage>,
    @InjectRedis() private readonly redis: Redis,
    private amqpConnection: AmqpConnection,
  ) {}

  createApiExecFunction(apis: IFbApi[], bots: User[], pageId?: string) {
    const apiLookup: Record<keyof IFbApi, IFbApi> = {} as Record<keyof IFbApi, IFbApi>;
    let keys: (keyof IFbApi)[] = [];
    for (const api of apis) {
      apiLookup[api.getCurrentUserID()] = api;
      if (api && isEmpty(keys)) {
        keys = Object.keys(api) as IFbApiKey;
      }
    }
    return {
      execFunction: async (name: keyof IFbApi, ...params) => {
        let attempts = 120;
        const botGroupKey = `day-running-bot.${moment().format('yyMMDD')}`;
        while (attempts) {
          if (isEmpty(bots)) {
            throw { message: 'There is not bot available' };
          }
          let botCount = await this.redis.hgetall(botGroupKey);
          if (isEmpty(botCount)) {
            await this.redis.hset(
              botGroupKey,
              'expired',
              moment()
                .endOf('day')
                .valueOf(),
            );
            await this.redis.expire(botGroupKey, -moment().diff(moment().endOf('day'), 'seconds'));
            botCount = {};
          }
          const handleBots = sortBy(bots, bot =>
            Number(botCount[pageId ? `${pageId}.${bot.id}` : `${bot.id}`] || 0),
          );
          for (let i = 0; i < handleBots.length; i++) {
            const bot = handleBots[i];
            const blocking = await this.redis.get(`blocking-bot-${bot.id}-${pageId}-${name}`);
            if (blocking) {
              const botIndex = bots.findIndex(e => e.id === bot.id);
              bots.splice(botIndex, 1);
              if (isEmpty(bots)) {
                throw {
                  error: 3252001,
                  message: 'You’re Temporarily Blocked',
                };
              }
              continue;
            }
            const api = apiLookup[bot.id];
            if (!api || !api[name]) {
              continue;
            }
            const botKey = `running-bot-${bot.id}-${name}`;
            const limit = apiLimits[name] || 1;
            const [, [, running]] = await this.redis
              .multi()
              .set(botKey, 0, 'EX', 60, 'NX')
              .incr(botKey)
              .exec();
            // console.log(`API request ${bot.id} - ${pageId} -> ${name} : ${running}`);
            // const running = await this.redis.incr(botKey);
            if (running && Number(running) > limit) {
              await this.redis.decr(botKey);
              continue;
            }
            const key = pageId ? `${pageId}.${bot.id}` : `${bot.id}`;
            try {
              await this.redis
                .multi()
                .hincrby(botGroupKey, key, 1)
                .expire(botKey, 60)
                .exec();
              const res = await api[name](...params);
              return res;
            } catch (e) {
              if ([1357001, 3252001, 1357004].includes(e?.code || e?.error)) {
                await this.redis.hincrby(botGroupKey, key, 1000);
                const botIndex = bots.findIndex(e => e.id === bot.id);
                bots.splice(botIndex, 1);
                if ((e?.code || e?.error) == 3252001) {
                  await this.redis.set(
                    `blocking-bot-${bot.id}-${pageId}-${name}`,
                    1,
                    'EX',
                    60 * 60,
                  );
                }
                let shouldDeactive = (e?.code || e?.error) == 1357001;
                if ((e?.code || e?.error) == 1357004) {
                  const status = (await api['checkLoggedIn']()) as UserActive;
                  shouldDeactive = status !== UserActive.loggedIn;
                }
                if (shouldDeactive) {
                  try {
                    await this.usRepository.update(
                      {
                        user_id: bot.id,
                        ip: global.currentIp,
                      },
                      {
                        status: UserStatus.deactivate,
                      },
                    );
                    const key = `queue-refresh-bot-${bot.id}`;
                    const delay = 30 * 60 + Math.floor(Math.random() * 120);
                    const [, [, refreshing]] = await this.redis
                      .multi()
                      .set(key, 0, 'EX', delay, 'NX')
                      .incr(key)
                      .exec();
                    if (refreshing === 1) {
                      console.log('delay refresh', delay);
                      await this.amqpConnection.publish(
                        'delay-exchange',
                        'refresh-bot',
                        { id: bot.id, force: true },
                        {
                          headers: {
                            'x-delay': delay * 1000,
                          },
                        },
                      );
                    }
                  } catch (e) {}
                }
                if (isEmpty(bots)) {
                  throw e;
                }
                continue;
              }
              throw e;
            } finally {
              if (delayFunctions[name]) {
                setTimeout(() => this.redis.decr(botKey), delayFunctions[name] * 1000);
              } else {
                await this.redis.decr(botKey);
              }
            }
          }
          attempts--;
          await global.sleep(500);
        }
        throw { message: 'Bot is fulled' };
      },
      keys,
    };
  }

  async getApi(pageId: string, force = true): Promise<FbApi> {
    const sql = this.uRepository
      .createQueryBuilder('u')
      .innerJoin('u.pages', 'p', 'p.id = :pageId', { pageId })
      .innerJoinAndSelect('u.sessions', 's', 's.ip = :ip AND s.status = 1', {
        ip: global.currentIp,
      });
    if (!force) {
      sql.andWhere('u.isSubscriber = FALSE');
    }
    let bots = await sql.getMany();
    const rawBots = cloneDeep(bots);
    if (isEmpty(bots)) {
      throw {
        message: 'There is 0 bot for this page',
        pageId,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.FB_CHAT_0001,
      };
    }
    const apis: IFbApi[] = [];
    const funcs = [];

    for (let j = 0; j < bots.length; j++) {
      const i = bots[j];
      funcs.push(
        (async () => {
          try {
            const api = await Promise.race([
              login(
                {
                  appState: i.appState,
                  contexts: i.context,
                  password: i.password,
                  email: i.email || i.id,
                  code: i.twoFactor,
                  lastRefresh: i?.sessions?.[0]?.updatedAt,
                },
                { pageID: `${pageId}` },
              ),
              global.sleep(3000),
            ]);

            if (api) apis.push(api);
            else console.error('login bot timed out', { id: i.id, pageId, email: i.email });
          } catch (err) {
            console.error('cannot login bot', { id: i.id, pageId, email: i.email });
          }
        })(),
      );
    }

    await Promise.all(funcs);

    if (isEmpty(apis)) {
      throw {
        message: 'There is 0 bot that can be logged in',
        pageId,
      };
    }
    const { execFunction, keys } = this.createApiExecFunction(apis, bots, pageId);
    const api = {} as FbApi;
    for (const key of keys) {
      api[key as string] = (...params) => execFunction(key, ...params);
    }
    // api['refreshBots'] = () => {
    //   bots = rawBots;
    //   const { execFunction, keys } = this.createApiExecFunction(
    //     apis,
    //     bots,
    //     pageId,
    //   );
    //   for (const key of keys) {
    //     api[key as string] = (...params) => execFunction(key, ...params);
    //   }
    // };
    return api;
  }

  async getUserApi(userId: string, pageId?: string, force = false): Promise<IFbApi> {
    const sql = this.uRepository
      .createQueryBuilder('u')
      .innerJoinAndSelect('u.sessions', 's', 's.ip = :ip', {
        ip: global.currentIp,
      })
      .where('u.id = :userId', { userId });
    if (force) {
      sql.andWhere('s.status = 1');
    }
    const bot = await sql.getOne();
    if (!bot) {
      throw { message: 'Bot is not active', userId };
    }
    const api = await login(
      {
        appState: bot.appState,
        contexts: bot.context,
        password: bot.password,
        email: bot.email || bot.id,
        code: bot.twoFactor,
        lastRefresh: bot?.sessions?.[0]?.updatedAt,
      },
      { pageID: `${pageId}` },
    );
    const { execFunction, keys } = this.createApiExecFunction([api], [bot]);
    const api1 = {} as IFbApi;
    for (const key of keys) {
      api1[key as string] = (...params) => execFunction(key, ...params);
    }
    return api1;
  }

  async getPageTokens(id: string, appId?: string) {
    const condition: FindConditions<ScopedUserPage> = {
      pageId: id,
      accessToken: Not(IsNull()),
    };
    if (appId) {
      condition.appId = appId;
    }
    const sup = await this.supRepository.find(condition);
    return shuffle(sup);
  }

  async getAvailableBots(pageId: string) {
    const sql = this.uRepository
      .createQueryBuilder('u')
      .innerJoin('u.pages', 'p', 'p.id = :pageId', { pageId })
      .innerJoinAndSelect('u.sessions', 's', 's.ip = :ip AND s.status = 1', {
        ip: global.currentIp,
      });
    return sql.getMany();
  }

  async getPageTokenApi(id: string, appId?: string): Promise<FbTokenApi> {
    const accessTokens = await this.getPageTokens(id, appId);
    let token = accessTokens.pop();
    if (!token) {
      // await this.amqpConnection.publish(
      //   'facebook-bot',
      //   'refresh-page',
      //   {id},
      // );
      console.log('page token not found', id);
      throw {
        message: 'token not found',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.FB_CHAT_0002,
      };
    }
    const requestApi = async ({ url, method, params, data, headers }: IFbApiRequest) => {
      try {
        const response = await axios.request({
          method: method || 'GET',
          url,
          baseURL: FACEBOOK_API_BASE_URL,
          params: {
            access_token: token.accessToken,
            ...(params || {}),
          },
          data,
          headers,
        });
        return response.data;
      } catch (e) {
        const error = e as AxiosError;
        const errorData = error.response?.data as IFbError;
        if (
          [102, 190].includes(errorData?.error?.code) ||
          (error.response.status == 400 && typeof error.response?.data === 'string')
        ) {
          console.log('token expired', id, token);
          await this.supRepository.softDelete({ pageId: id, scopedUserId: token.scopedUserId });
          // await this.amqpConnection.publish(
          //   'facebook-bot',
          //   'refresh-bot',
          //   {id: token.scopedUserId},
          // );
          token = accessTokens.pop();
          if (token) {
            return requestApi({ url, method, params, data });
          }
        }
        console.log('request api error', error?.response?.config, id, errorData);
        throw errorData?.error || 'Internal error';
      }
    };
    return requestApi;
  }
}
