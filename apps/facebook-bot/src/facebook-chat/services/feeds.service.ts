import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { messageConnection } from 'core/constants/database-connection.constant';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Feed, IFeedAttachment } from '../../entities/feed.entity';
import { IFbFeed, IFbFeedAttachment } from '../../message/services/facebook-feed.service';
import {
  FACEBOOK_API_ENDPOINT,
  PANCAKE_API_ENDPOINT,
} from '../../constants/fb-api-endpoints.constant';
import { BotApiService } from './bot.service';
import { isEmpty, isNil, uniq } from 'lodash';
import { FeedsFilter } from '../filters/feeds.filter';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { FeedDto } from '../dtos/feed.dto';
import { plainToInstance } from 'class-transformer';
import { User } from '../../read-entities/identity-api/user.entity';
import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { IRmqMessage } from 'core/interfaces';
import axios from 'axios';
import * as moment from 'moment-timezone';
import { TYPE_DB } from '../../constants/message-parse.constants';
import * as FormData from 'form-data';
import StringUtils from 'core/utils/StringUtils';
import { Product } from '../../read-entities/catalog-api/product.entity';

@Injectable()
export class FeedsService {
  constructor(
    @InjectRepository(Feed, messageConnection)
    private feedRepository: Repository<Feed>,
    private botApiService: BotApiService,
    private amqpConnection: AmqpConnection,
  ) {}

  getFeedsQuery(filter: FeedsFilter): SelectQueryBuilder<Feed> {
    const qb = this.feedRepository
      .createQueryBuilder('f')
      .leftJoin('f.page', 'p')
      .addSelect(['p.id', 'p.name', 'p.marketerId']);
    const { pageIds, groupId, query, hideComments, productIds } = filter;
    if (pageIds) qb.andWhere('f.pageId IN (:...pageIds)', { pageIds });
    if (groupId) qb.andWhere('p.groupId = :groupId', { groupId });
    if (query) qb.andWhere('(p.name ~* :query OR f.id ~* :query OR f.message ~* :query)', { query });
    if (!isNil(hideComments)) {
      qb.andWhere('f.hide_comments = :hideComments', {hideComments})
      qb.andWhere('p.hide_comments = :hideComments', {hideComments})
    };

    if (!isEmpty(productIds)) {
      qb.andWhere('f.productId IN (:...productIds)', { productIds });
    }

    if (filter?.textOnly) {
      qb.andWhere('f.message IS NOT NULL');
    }

    let orderBy = 'f.updatedAt';
    let sortMode: 'ASC' | 'DESC' = 'DESC';

    if (!isNil(filter.orderBy)) {
      orderBy = `f.${filter.orderBy}`;
      if (!isNil(filter.sort)) {
        sortMode = `${filter.sort}` as 'ASC' | 'DESC';
      }
    }
    qb.orderBy(orderBy, sortMode);
    return qb;
  }

  async findFeeds(filter: FeedsFilter, pagination: PaginationOptions): Promise<[Feed[], number]> {
    const qb = this.getFeedsQuery(filter);

    if (pagination) qb.take(pagination.limit).skip(pagination.skip);
    const [feeds, count] = await qb.getManyAndCount();
    const marketerIds = feeds.map(f => f.page?.marketerId);
    const productIds = feeds.map(f => f.productId);

    let users: User[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'get-users-by-ids',
        payload: { ids: uniq(marketerIds) },
        timeout: 10000,
      });
      users = data as User[];
    } catch (error) {
      console.log(`error at map marketers to feeds`, error);
    }

    const usersLookup = users.reduce((prev: Record<string, User>, u) => {
      prev[String(u.id)] = u;
      return prev;
    }, {});

    const data = await this.amqpConnection.request({
      exchange: 'catalog-service-products',
      routingKey: 'find-products-by-ids',
      payload: { ids: uniq(productIds) },
      timeout: 10000,
    });

    const products = data.data as Product[];
    const hashMapProductByProductId = products.reduce((prev, item) => {
      prev[item.id] = item;
      return prev;
    }, {});

    for (const feed of feeds) {
      const marketer = usersLookup[feed.page.marketerId];
      if (marketer && feed.page) feed.page.marketer = marketer;

      const product = hashMapProductByProductId[feed.productId];
      feed.productName = product?.name
      feed.productSKU = product?.sku
    }

    return [feeds, count];
  }

  async findByIds(ids: string[]): Promise<Feed> {
    const qb = this.feedRepository
      .createQueryBuilder('f')
      .where('f.id IN (:...ids)', { ids })
      .leftJoin('f.page', 'p')
      .addSelect(['p.id', 'p.name', 'p.marketerId']);
    return qb.getOne();
  }

  async updateFeed(ids: string[], body: FeedDto): Promise<any> {
    const feed = await this.findByIds(ids);
    if (!feed) throw new NotFoundException(`Không tìm thấy bài viết ID ${ids}`);

    if (isEmpty(body)) return feed;

    const result = await this.feedRepository.update(ids, body);
    // if (result.affected) return plainToInstance(Feed, { ...feed, ...body });
    return {
      'affected': result.affected,
    };
  }

  parseFeedAttachment(attachments: IFbFeedAttachment[]): IFeedAttachment[] {
    return attachments.reduce((prev, item) => {
      if (isEmpty(item.subattachments?.data)) {
        prev.push({
          id: item.target?.id,
          type: item.media_type || item.type,
        });
      } else {
        for (const subattachment of item.subattachments.data) {
          prev.push({
            id: subattachment.target?.id,
            type: subattachment.media_type || item.type,
            mediaSrc: subattachment.media?.image?.src,
            targetUrl: subattachment.url || subattachment.target?.url,
            title: subattachment.title,
          });
        }
      }
      return prev;
    }, []);
  }

  handleFbFeed(feed: IFbFeed): Feed {
    const pageId: string = feed.id.split('_')[0];
    const attachments: IFeedAttachment[] = feed.attachments
      ? this.parseFeedAttachment(feed.attachments.data)
      : undefined;
    return {
      id: feed.id,
      attachments,
      createdAt: new Date(feed.created_time),
      updatedAt: new Date(feed.updated_time),
      message: feed.message,
      pageId,
    };
  }

  async getOne(id: string) {
    let feed = await this.feedRepository.findOne(id);
    if (feed) {
      return feed;
    }
    const idSplit = id.split('_');
    let pageId;
    if (idSplit[1]) {
      id = idSplit[1];
      pageId = idSplit[0];
    }
    const api = await this.botApiService.getPageTokenApi(pageId);
    const response = await api({
      url: FACEBOOK_API_ENDPOINT.ME(`${pageId}_${id}`),
      params: {
        fields:
          'attachments.limit(10){target,media_type,subattachments},message,created_time,updated_time',
      },
    });
    const feedRes: IFbFeed = response;
    feed = await this.feedRepository.save(this.handleFbFeed(feedRes));
    await this.amqpConnection.publish('message-service', 'attachments-in-feed', {
      feedId: feed.id,
    });
    return feed;
  }

  @RabbitRPC({
    exchange: 'message-service',
    routingKey: 'attachments-in-feed',
    queue: 'agbiz-process-attachments-in-feed',
    errorHandler: rmqErrorsHandler,
  })
  async processMessageAttachments({ feedId }) {
    if (!feedId) return new Nack(false);
    const feed = await this.feedRepository.findOne(feedId);
    if (!feed || isEmpty(feed.attachments)) return new Nack(false);

    feed.attachments = await Promise.all(
      feed.attachments.map(async att => {
        if (!att.mediaSrc) return att;
        const newMediaSrc = await this.uploadAttachmentToPancake(att.mediaSrc);
        if (newMediaSrc) {
          att.mediaSrc = newMediaSrc;
        }
        return att;
      }),
    );

    const result = await this.feedRepository.update(
      { id: feed.id },
      { attachments: feed.attachments },
    );
    console.log(`update feed attachment media src result`, result);

    return new Nack(false);
  }

  async uploadAttachmentToPancake(attachmentUrl: string): Promise<string | undefined> {
    try {
      const attResponse = await axios.get(attachmentUrl, {
        responseType: 'arraybuffer',
      });
      const buffer = Buffer.from(attResponse.data, 'binary');
      const filename =
        moment()
          .valueOf()
          .toString() +
        '.' +
        TYPE_DB[attResponse?.headers['content-type']]?.extensions[0];
      const form = new FormData();
      form.append('file', buffer, filename);
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      const response = await axios.request({
        method: 'POST',
        baseURL: 'https://pos.pages.fm/api/v1',
        url: PANCAKE_API_ENDPOINT.CONTENTS,
        params: {
          api_key: process.env.PANCAKE_API_KEY,
        },
        data: form,
        headers,
        timeout: 60000,
      });
      if (!response.data?.content_url)
        throw new Error(
          `Cannot upload attachment to pancake, response ${StringUtils.getString(response)}`,
        );

      return response.data.content_url;
    } catch (error) {
      console.log(`upload attachment to pancake error`, error?.stack ? error.stack : error);
      //console.log(`upload attachment to pancake error`, error);
    }
    return;
  }
}
