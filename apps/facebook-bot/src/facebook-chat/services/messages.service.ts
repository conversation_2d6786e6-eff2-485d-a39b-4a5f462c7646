import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bullmq';
import { plainToInstance } from 'class-transformer';
import { messageConnection } from 'core/constants/database-connection.constant';
import { isEmpty } from 'lodash';
import { IsNull, Not, Repository } from 'typeorm';
import { PageCursorOptions } from '../../../../../core/decorators/page-cursor/page-cursor.model';
import { rmqErrorsHandler } from '../../../../../core/handlers/rmq-errors.handler';
import { scopedUserCaresQueue } from '../../constants/bull-queue.constant';
import { FACEBOOK_API_ENDPOINT } from '../../constants/fb-api-endpoints.constant';
import { Attachment } from '../../entities/attachments.entity';
import { Message, parseAttachments } from '../../entities/message.entity';
import { TempMessage } from '../../entities/temp-message.entity';
import { User } from '../../entities/user.entity';
import { CareStatus } from '../../enums/care-status.enum';
import { FbMessagingTag, FbMessagingType } from '../../enums/fb-message.enum';
import { findAvailableKeys, IFbAttachment } from '../../facebook-api/functions/uploadAttachments';
import { MessagesFilter } from '../../message/filters/messages.filter';
import { SocketService } from '../../socket/socket.service';
import { AttachmentsService } from './attachments.service';
import { BotApiService } from './bot.service';
import { ConversationsService } from './conversations.service';
import { ConversationsPhones } from '../../entities/conversations-phones.entity';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';
import { SalePermission } from 'core/enums/sale-permissions';
import { CarePagePermission } from 'core/enums/sale-permissions/care-page-permission.enum';
import { ProfileType } from 'apps/identity-api/src/enums/profile-type.enum';
import { SaveMessageDto } from '../dtos/save-message.dto';
import { TAuthUserProfile } from 'core/interfaces/auth-user.interface';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import * as moment from 'moment';
import { FanPage } from '../../entities/fanpage.entity';
import OpenAIUtils from 'core/utils/OpenAIUtils';
import { OpenAI } from 'openai';
import { OpenAIModel, OpenAISafeTokens } from 'core/enums/open-ai.enum';
import { Conversation } from '../../entities/conversation.entity';
import { UpdateConversationSummaryDto } from '../dtos/update-conversation-summary.dto';
import { GetConversationSummaryCustomPromptDto } from '../dtos/get-convaersation-summary-custom-prompt.dto';

@Injectable()
export class MessagesService {
  constructor(
    @InjectRepository(User, messageConnection)
    private userRepo: Repository<User>,
    @InjectRepository(PageScopedUser, messageConnection)
    private suRepo: Repository<PageScopedUser>,
    @InjectRepository(Message, messageConnection)
    private messageRepository: Repository<Message>,
    @InjectRepository(TempMessage, messageConnection)
    private tempMessageRepository: Repository<TempMessage>,
    private conversationService: ConversationsService,
    private attachmentsService: AttachmentsService,
    private botService: BotApiService, // @InjectQueue('facebook-message') // private facebookMessageQueue: Queue,
    private amqpConnection: AmqpConnection,
    private socketGateway: SocketService,
    @InjectQueue(scopedUserCaresQueue)
    private suCaresQueue: Queue,
    @InjectRepository(FanPage, messageConnection)
    private fanPageRepository: Repository<FanPage>,
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
  ) {}

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'handle-temp-message',
    queue: 'handle-temp-message',
    errorHandler: rmqErrorsHandler,
  })
  async handleTempMessage(tempMessage: TempMessage) {
    tempMessage = plainToInstance(TempMessage, tempMessage);
    const message = await this.messageRepository.findOne({
      scopedUserId: tempMessage.userId,
      pageId: tempMessage.pageId,
      timestamp: tempMessage.timestamp,
    });
    console.log('message', message, tempMessage);
    if (message) {
      const result = await this.messageRepository.update(
        { id: message.id },
        {
          employeeId: tempMessage.senderId,
          raw: tempMessage.content?.raw,
        },
      );
      await this.tempMessageRepository.delete(tempMessage.id);

      if (result.affected) {
        // Xử lý khi sale gửi tin nhắn cuối cùng đi
        await this.amqpConnection.publish('facebook-bot', 'on-employee-sent-message', {
          scopedUserId: message.pageId + '_' + message.scopedUserId,
        });
      }

      return message;
    } else {
      try {
        const mes = await this.getTempMessage({
          userId: tempMessage.userId,
          pageId: tempMessage.pageId,
          timestamp: tempMessage.timestamp.getTime(),
        });
        if (mes) {
          const message = await this.messageRepository.save({
            id: mes.id,
            pageId: tempMessage.pageId,
            scopedUserId: tempMessage.userId,
            senderId: tempMessage.pageId,
            // recipientId: tempMessage.userId,
            text: mes.message,
            timestamp: tempMessage.timestamp,
            employeeId: tempMessage.senderId,
            // isEcho: true,
            raw: tempMessage.content?.raw,
            _attachments: parseAttachments(mes),
          });

          await this.socketGateway.emitFacebookMessage(plainToInstance(Message, message));
          if (!isEmpty(message.attachments))
            await this.amqpConnection.sendMessage('facebook-webhook-event', null, message, {
              routingKey: 'attachments-in-messages',
            });

          if (message?.employeeId) {
            // Xử lý khi sale gửi tin nhắn cuối cùng đi
            await this.amqpConnection.publish('facebook-bot', 'on-employee-sent-message', {
              scopedUserId: message.pageId + '_' + message.scopedUserId,
            });
          }
        }
      } catch (e) {
        console.log('handle temp message error', e);
      }
    }
    return new Nack(false);
  }

  async getTempMessage(tempMessage: { timestamp: number; pageId: string; userId: string }) {
    try {
      const api = await this.botService.getPageTokenApi(`${tempMessage.pageId}`);
      const res = await api({
        url: 'me/conversations',
        params: {
          fields: `messages.limit(100){message,attachments{mime_type,image_data,video_data,file_url},from,created_time}`,
          user_id: tempMessage.userId,
        },
      });
      const messageRes = (res?.data || [])[0]?.messages;
      if (!messageRes) {
        return new Nack(false);
      }
      const messages = messageRes?.data;
      if (messages) {
        const validMess = messages.filter(fbMes => {
          const time = new Date(fbMes.created_time);
          return time.getTime() === Math.floor(tempMessage.timestamp / 1000) * 1000;
        });
        const lastMessage = messages[messages.length];
        if (
          validMess.length === 0 &&
          new Date(lastMessage.created_time) < new Date(tempMessage.timestamp)
        ) {
          return;
        }
        if (validMess.length === 0) {
          return;
        }
        const mes = validMess[0];
        return mes;
      }
    } catch (e) {
      console.log('handle temp message error', e);
    }
  }

  // async initCheckTempMessageQueue() {
  //   const job = await this.facebookMessageQueue.add(
  //     'scan-temp-message',
  //     {},
  //     {
  //       attempts: 3,
  //       repeat: {
  //         cron: '*/1 * * * *',
  //         tz: 'Asia/Ho_Chi_Minh',
  //       },
  //       jobId: 'scan-temp-message',
  //       removeOnComplete: true,
  //       removeOnFail: false,
  //     },
  //   );
  //   console.log('job', job);
  // }

  async findTempMessages(userId: string) {
    const temp = await this.tempMessageRepository.find({ userId, content: Not(IsNull()) });
    return temp.map(i => i.content);
  }

  async find(
    paging: PageCursorOptions<Message>,
    pageId: string,
    userId: string,
    filters?: MessagesFilter,
  ) {
    const queryBuilder = this.messageRepository
      .createQueryBuilder('m')
      .leftJoinAndMapMany('m.phones', ConversationsPhones, 'p', 'p.messageId = m.id')
      .where('m.scopedUserId = :userId', { userId })
      .andWhere(`m.pageId = :pageId`, { pageId });

    const { type } = filters || {};
    if (type) queryBuilder.andWhere('m.type = :type', { type });

    return paging('m').paginate(queryBuilder);
  }
  async saveTempMessage(
    pageId: string,
    userId: string,
    data: SaveMessageDto,
    request: Record<string, any>,
  ) {
    const senderId = request.user.id;
    const message = await this.tempMessageRepository.save(
      plainToInstance(TempMessage, {
        id: data.mid,
        timestamp: new Date(data.timestamp),
        pageId,
        userId,
        senderId,
        content: {
          pageId,
          scopedUserId: userId,
          employeeId: senderId,
          senderId: pageId,
          timestamp: new Date(data.timestamp),
          id: data.mid,
        },
      } as TempMessage),
    );
    await this.amqpConnection.publish('facebook-bot', 'handle-temp-message', message);
    return message;
  }

  async send(
    pageId: string,
    userId: string,
    data: {
      files?: any[];
      body: string;
      clientId: string;
      attachments?: IFbAttachment[];
      attachmentIds?: number[];
      useToken?: boolean;
    },
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const senderId = request.user.id;
    const conversation = await this.conversationService.getConversation(pageId, userId, '', [
      'user',
    ]);

    const isCarePageManager = !!request.user.profiles.find((record: TAuthUserProfile) => {
      const [dataAccessLevel, moduleInCharge, , newPermissions, , profileType] = record;
      return (
        (isEmpty(moduleInCharge) ||
          profileType === ProfileType.leader ||
          dataAccessLevel === DataAccessLevel.parentAuthority) &&
        (BigInt(newPermissions[SalePermission.carePage]) & BigInt(CarePagePermission.process)) > 0
      );
    });

    if (senderId !== conversation.user?.careUserId && !isCarePageManager) {
      throw new ForbiddenException('Khách hàng này không ở trạng thái được phụ trách bởi bạn.');
    }

    let error;
    if (
      (!isEmpty(data.attachments) && !findAvailableKeys(data.attachments[0])) ||
      isEmpty(data.attachments) ||
      data.attachments?.length === 1
    ) {
      data.useToken = true;
    }
    if (data.files) {
      data.useToken = false;
    }
    const lastUserSents = await this.messageRepository
      .createQueryBuilder('m')
      // .where({
      //   scopedUserId: userId,
      //   isEcho: false,
      // })
      .where('m.scopedUserId = :userId', { userId })
      .andWhere('m.senderId = m.scopedUserId')
      .select('timestamp')
      .limit(200)
      .getRawMany();
    const lastUserSent = lastUserSents.sort(
      (p, n) => moment(n.timestamp).valueOf() - moment(p.timestamp).valueOf(),
    )[0];
    console.log('lastUserSent', lastUserSent);
    if (!lastUserSent?.timestamp) {
      data.useToken = false;
    }
    /*
    if (conversation.userGlobalId && !data.useToken) {
      const api = await this.botService.getApi(pageId);
      try {
        let attachments = data.attachments;
        if (data.files) {
          console.log(`upload attachments`, data.files.map(f => f.filename))
          attachments = await api.uploadAttachments(data.files);
        }
        if (!isEmpty(data.attachmentIds)) {
          attachments = await this.attachmentsService.getPageAttachments(
            data.attachmentIds.map(i => {
              const att = new Attachment();
              att.id = i;
              return att;
            }),
            pageId,
          );
        }
        const { messageID, timestamp } = await api.sendMessage(
          { body: data.body, attachment: attachments },
          conversation.userGlobalId,
        );
        const message = await this.tempMessageRepository.save(
          plainToInstance(TempMessage, {
            id: messageID,
            timestamp: new Date(timestamp),
            pageId,
            userId,
            senderId,
            content: {
              pageId,
              scopedUserId: userId,
              employeeId: senderId,
              senderId: pageId,
              timestamp: new Date(timestamp),
              text: data.body,
              id: messageID,
              _attachments: parseAttachments({
                attachments: attachments
                  ? {
                      data: attachments?.map(i => {
                        const key = findAvailableKeys(i);
                        return {
                          type: key?.replace('_id', '') || i.filetype,
                          payload: {
                            url: i.src,
                          },
                        };
                      }),
                    }
                  : undefined,
              }),
            },
          } as TempMessage),
        );
        message.clientId = data.clientId;
        await this.amqpConnection.publish('facebook-bot', 'handle-temp-message', message);
        return message;
      } catch (e) {
        if (e.error === 3252001) {
          e.message = 'You have been temporarily blocked';
          throw new BadRequestException(e);
        }
        if (e.error === 1404133) {
          e.message = 'Your site is unable to send messages for 24 hours';
          throw new BadRequestException(e);
        }
        if (e.error === 1545041) {
          e.message = 'This person is currently unavailable';
          throw new BadRequestException(e);
        }
        error = e.errorDescription?.__html;
      }
    }
    */
    const botApi = await this.botService.getPageTokenApi(
      pageId,
      process.env.FACEBOOK_APP_CLIENT_ID,
    );
    const messageType =
      lastUserSent?.timestamp &&
      (Date.now() - new Date(lastUserSent.timestamp).getTime() > 24 * 60 * 6e4
        ? FbMessagingType.MESSAGE_TAG
        : conversation.lastSentByPage
        ? FbMessagingType.UPDATE
        : FbMessagingType.RESPONSE);
    let messageTag;
    if (messageType === FbMessagingType.MESSAGE_TAG) {
      messageTag = FbMessagingTag.ACCOUNT_UPDATE;
    }
    const messages = [];
    if (data.body) {
      try {
        const date = new Date();
        const message = await botApi({
          method: 'POST',
          url: FACEBOOK_API_ENDPOINT.MESSAGES(),
          data: {
            messaging_type: messageType,
            tag: messageTag,
            recipient: {
              id: userId,
            },
            message: {
              text: data.body,
            },
          },
        });
        const mes = await this.messageRepository.save({
          id: message.message_id,
          scopedUserId: userId,
          pageId,
          employeeId: senderId,
          _attachments: parseAttachments(message),
          recipientId: userId,
          senderId: pageId,
          // isEcho: true,
          text: data.body,
          timestamp: date,
        });
        mes.clientId = data.clientId;
        messages.push(mes);
      } catch (e) {
        console.log('send() error1: ', e);
        if (e.code == 551) {
          throw new InternalServerErrorException(e);
        } else {
          // error = e.message;
          error = e;
        }
      }
    }
    if (data.attachments) {
      for (const attachment of data.attachments) {
        const key = findAvailableKeys(attachment) || 'src';
        try {
          const date = new Date();
          const message = await botApi({
            method: 'POST',
            url: FACEBOOK_API_ENDPOINT.MESSAGES(),
            data: {
              messaging_type: messageType,
              tag: messageTag,
              recipient: {
                id: userId,
              },
              message: {
                attachment: {
                  type: attachment.filetype || 'image',
                  payload:
                    key === 'src'
                      ? {
                          url: attachment[key],
                        }
                      : {
                          attachment_id: attachment[key],
                        },
                },
              },
            },
          });
          const mes = await this.messageRepository.save({
            id: message.message_id,
            scopedUserId: userId,
            pageId,
            employeeId: senderId,
            _attachments: parseAttachments({
              attachments: [
                {
                  type: attachment.filetype || 'image',
                  payload: {
                    url: attachment.src,
                  },
                },
              ],
            }),
            recipientId: userId,
            senderId: pageId,
            // isEcho: true,
            timestamp: date,
          });
          mes.clientId = data.clientId;
          messages.push(mes);
        } catch (e) {
          console.log('send() error2: ', e);
          if (e.code == 551) {
            throw new InternalServerErrorException(e);
          } else {
            error = e;
          }
        }
      }
    }

    if (!isEmpty(messages)) {
      try {
        await Promise.all(
          messages.map(message =>
            this.socketGateway.emitFacebookMessage(plainToInstance(Message, message)),
          ),
        );
      } catch (e) {
        console.log('send() emit message error', e);
      }
      return plainToInstance(Message, messages);
    }
    throw new BadRequestException(error || 'Không thể gửi tin nhắn');
  }

  /**
   * Xử lý khi nhận tin nhắn khách nhắn tới
   * @param payload
   * @returns
   */
  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'on-received-message',
    queue: 'bot-process-event-on-received-message',
    errorHandler: rmqErrorsHandler,
  })
  async onReceivedMessage(payload: { scopedUserId: string }) {
    const { scopedUserId } = payload;
    if (!scopedUserId) return new Nack(false);

    const scopedUser = await this.suRepo
      .createQueryBuilder('su')
      .where('su.id = :scopedUserId')
      .leftJoinAndSelect('su.currentCare', 'care')
      .setParameters({ scopedUserId })
      .getOne();

    if (!scopedUser) return new Nack(false);

    if (
      scopedUser.currentCare &&
      [
        CareStatus.no_response,
        CareStatus.in_process,
        CareStatus.confirmed,
        CareStatus.potential,
      ].includes(scopedUser.careStatus)
    )
      await this.amqpConnection.publish('facebook-bot', 'schedule-revoke-scoped-user', {
        scopedUserId: String(scopedUser.id),
        careStatus: scopedUser.careStatus,
        updatedAt: scopedUser.updatedAt,
        careId: scopedUser.currentCare?.id,
      });

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'on-employee-sent-message',
    queue: 'bot-process-event-on-employee-sent-message',
    errorHandler: rmqErrorsHandler,
  })
  async onEmployeeSentMessage(payload: { scopedUserId: string }) {
    const { scopedUserId } = payload;
    if (!scopedUserId) return new Nack(false);

    const scopedUser = await this.suRepo
      .createQueryBuilder('su')
      .where('su.id = :scopedUserId')
      .leftJoinAndSelect('su.currentCare', 'care')
      .setParameters({ scopedUserId })
      .getOne();

    if (!scopedUser) return new Nack(false);
    if ([CareStatus.confirmed].includes(scopedUser.careStatus))
      await this.cancelScheduledRevokeScopedUser({ scopedUser });

    if (
      scopedUser.currentCare &&
      [CareStatus.no_response, CareStatus.in_process, CareStatus.potential].includes(
        scopedUser.careStatus,
      )
    )
      await this.amqpConnection.publish('facebook-bot', 'schedule-revoke-scoped-user', {
        scopedUserId: String(scopedUser.id),
        careStatus: scopedUser.careStatus,
        updatedAt: scopedUser.updatedAt,
        careId: scopedUser.currentCare?.id,
      });

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'cancel-scheduled-revoke-scoped-user',
    queue: 'bot-process-cancel-scheduled-revoke-scoped-user',
    errorHandler: rmqErrorsHandler,
  })
  async cancelScheduledRevokeScopedUser(payload: {
    scopedUserId?: string;
    scopedUser?: PageScopedUser;
  }) {
    let { scopedUserId, scopedUser } = payload;
    if (!scopedUserId && !scopedUser) return new Nack(false);
    if (!scopedUser)
      scopedUser = await this.suRepo
        .createQueryBuilder('su')
        .where('su.id = :scopedUserId')
        .leftJoinAndSelect('su.currentCare', 'care')
        .setParameters({ scopedUserId })
        .getOne();
    if (!scopedUser) return new Nack(false);

    const lastMessage = await this.messageRepository
      .createQueryBuilder('m')
      .where('m.scopedUserId = :scopedUserId', { scopedUserId: scopedUser.scopedUserId })
      .andWhere('m.pageId = :pageId', { pageId: scopedUser.pageId })
      // .andWhere('m.type = :type', { type: MessageType.message })
      .orderBy('m.timestamp', 'DESC')
      .getOne();

    if (!lastMessage) return new Nack(false);

    /**
     * Cancel thu hồi khi trạng thái chăm sóc là [Không phản hồi] và tin nhắn cuối cùng là do khách nhắn
     * hoặc
     * Cancel thu hồi khi trạng thái chăm sóc là [Chốt || Đang xử lý] và tin nhắn cuối cùng là do sale nhắn
     */
    if (
      (scopedUser.careStatus === CareStatus.no_response && !lastMessage.employeeId) ||
      ([CareStatus.confirmed, CareStatus.in_process].includes(scopedUser.careStatus) &&
        lastMessage.employeeId)
    ) {
      const { currentCareId: careId } = scopedUser;

      const jobId = `scopedUserId-${scopedUserId}-${careId}`;
      const result = await (await this.suCaresQueue.getJob(jobId))?.remove();
      console.log(
        `result of cancel auto revoke no_response_confirm scoped user id ${scopedUserId}`,
        result,
      );
    }
    return new Nack(false);
  }

  async getConversationSummary(userId: string) {
    const [pageId, psid] = userId.split('_');
    const queryBuilder = this.messageRepository
      .createQueryBuilder('m')
      .leftJoinAndMapMany('m.phones', ConversationsPhones, 'p', 'p.messageId = m.id')
      .where('m.scopedUserId = :userId', { userId: psid })
      .andWhere(`m.pageId = :pageId`, { pageId });
    const messages = await queryBuilder.getMany();
    const msgPrompt = messages.map(message => {
      return {
        role: message.senderId === pageId ? 'staff' : 'customer',
        content: message.text,
      };
    });

    const conversationDetail = await this.conversationRepo.findOne({
      pageId,
      scopedUserId: psid,
      feedId: '',
    });
    if (conversationDetail?.lastMessageIdGetSummary === messages[0].id)
      return {
        lastSummary: conversationDetail?.lastSummary,
        lastSummaryAt: conversationDetail?.lastSummaryAt,
        lastSummaryBy: conversationDetail?.lastSummaryBy,
      };
    try {
      // Call to OpenAI
      let summary = '';
      const chunksConversation = OpenAIUtils.splitConversationByTokenLimit(
        msgPrompt,
        OpenAISafeTokens[OpenAIModel.GPT_4_o],
        OpenAIModel.GPT_4_o,
      );
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_API_URL,
      });
      const promises = chunksConversation.map(async chunk => {
        const conversation = chunk.map(m => `${m.role}: ${m.content}`).join('\n');
        const summaryRes = await openai.chat.completions.create({
          model: OpenAIModel.GPT_4_o,
          messages: [
            {
              role: 'system',
              content: `Bạn là một trợ lý AI đang phân tích cuộc hội thoại giữa một khách hàng (role: user) và nhân viên chăm sóc khách hàng (role: staff).
Hãy đọc toàn bộ nội dung hội thoại và tóm tắt theo cấu trúc rõ ràng sau:

### Overview:
- Tóm tắt ngắn gọn bối cảnh và nội dung chính của cuộc hội thoại. Không cần thêm mô tả "Cuộc hội thoại diễn ra giữa một khách hàng và nhân viên chăm sóc khách hàng."

### Concern:
- Liệt kê các vấn đề, yêu cầu hoặc mối quan tâm mà khách hàng đề cập trong hội thoại.
- Ghi rõ nếu có thông tin như: số điện thoại, mã đơn hàng, trạng thái đơn, v.v.

### Advise:
- Đưa ra gợi ý hoặc hành động phù hợp cho nhân viên xử lý tiếp theo.
- Có thể đề xuất phản hồi, chuyển tiếp đến bộ phận khác, hoặc giải pháp phù hợp với từng concern.

Lưu ý:
- Bỏ qua không cần phân tích các câu hội thoại không có nghĩa.
- Trả lời đúng theo định dạng trên, không viết thêm bất kỳ lời chào hay đoạn hội thoại nào.`,
            },
            { role: 'user', content: 'Dưới đây là nội dung cuộc hội thoại:\n\n' + conversation },
          ],
          temperature: 0.3,
        });
        return summaryRes.choices[0].message.content;
      });
      const res = await Promise.all(promises);
      if (promises.length > 1) {
        const systemPrompt = `Bạn là trợ lý thông minh, hãy đọc các đoạn tóm tắt nhỏ dưới đây và đưa ra 1 bản tổng hợp ngắn gọn, súc tích, giữ lại các ý chính quan trọng nhất. Tránh lặp ý.`;
        const userPrompt = `Dưới đây là các đoạn tóm tắt nhỏ:\n\n${res
          .map((s, i) => `### Tóm tắt ${i + 1}:\n${s}`)
          .join('\n\n')}\n\n--> Tổng kết cuối cùng theo cấu trúc rõ ràng sau:
            ### Overview:
            - Tóm tắt ngắn gọn bối cảnh và nội dung chính của cuộc hội thoại.

            ### Concern:
            - Liệt kê các vấn đề, yêu cầu hoặc mối quan tâm mà khách hàng đề cập trong hội thoại.
            - Ghi rõ nếu có thông tin như: số điện thoại, mã đơn hàng, trạng thái đơn, v.v.

            ### Advise:
            - Đưa ra gợi ý hoặc hành động phù hợp cho nhân viên xử lý tiếp theo.
            - Có thể đề xuất phản hồi, chuyển tiếp đến bộ phận khác, hoặc giải pháp phù hợp với từng concern.`;
        const completion = await openai.chat.completions.create({
          model: OpenAIModel.GPT_4_o,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt },
          ],
          temperature: 0.3,
          max_tokens: 500,
        });
        summary = completion.choices[0].message.content;
      } else {
        summary = res[0];
      }
      const currentDate = new Date();
      await this.conversationRepo.update(
        {
          pageId: conversationDetail.pageId,
          scopedUserId: conversationDetail.scopedUserId,
          feedId: '',
        },
        {
          lastMessageIdGetSummary: messages[0].id,
          lastSummary: summary,
          lastSummaryAt: currentDate,
          lastSummaryBy: -3,
        },
      );
      return {
        lastSummary: summary,
        lastSummaryAt: currentDate,
        lastSummaryBy: -3,
      };
    } catch (e) {
      console.log('getConversationSummary error', e);
      throw new BadRequestException('Không thể tóm tắt cuộc hội thoại');
    }
  }

  async updateConversationSummary(
    pageId: string,
    scopedUserId: string,
    data: UpdateConversationSummaryDto,
    userId: number,
  ) {
    const conversationDetail = await this.conversationRepo.findOne({
      pageId,
      scopedUserId,
      feedId: '',
    });
    if (!conversationDetail) return;
    const currentDate = new Date();
    await this.conversationRepo.update(
      {
        pageId,
        scopedUserId,
        feedId: '',
      },
      {
        lastSummary: `### Overview:
          ${data.overview}

          ### Concern:
          ${data.concern}

          ### Advise:
          ${data.advise}`,
        lastSummaryAt: currentDate,
        lastSummaryBy: userId,
      },
    );
    return data;
  }

  async getConversationSummaryWithCustomPrompt(
    userId: string,
    prompt: GetConversationSummaryCustomPromptDto,
  ) {
    const [pageId, psid] = userId.split('_');
    const queryBuilder = this.messageRepository
      .createQueryBuilder('m')
      .leftJoinAndMapMany('m.phones', ConversationsPhones, 'p', 'p.messageId = m.id')
      .where('m.scopedUserId = :userId', { userId: psid })
      .andWhere(`m.pageId = :pageId`, { pageId });
    const messages = await queryBuilder.getMany();
    const msgPrompt = messages.map(message => {
      return {
        role: message.senderId === pageId ? 'staff' : 'customer',
        content: message.text,
      };
    });

    const conversationDetail = await this.conversationRepo.findOne({
      pageId,
      scopedUserId: psid,
      feedId: '',
    });

    try {
      // Call to OpenAI
      let summary = '';
      const chunksConversation = OpenAIUtils.splitConversationByTokenLimit(
        msgPrompt,
        OpenAISafeTokens[OpenAIModel.GPT_4_o],
        OpenAIModel.GPT_4_o,
      );
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_API_URL,
      });
      const promises = chunksConversation.map(async chunk => {
        const conversation = chunk.map(m => `${m.role}: ${m.content}`).join('\n');
        const summaryRes = await openai.chat.completions.create({
          model: OpenAIModel.GPT_4_o,
          messages: [
            {
              role: 'system',
              content: prompt.prompt,
            },
            { role: 'user', content: 'Dưới đây là nội dung cuộc hội thoại:\n\n' + conversation },
          ],
          temperature: 0.3,
        });
        return summaryRes.choices[0].message.content;
      });
      const res = await Promise.all(promises);
      if (promises.length > 1) {
        const systemPrompt = `Bạn là trợ lý thông minh, hãy đọc các đoạn tóm tắt nhỏ dưới đây và đưa ra 1 bản tổng hợp ngắn gọn, súc tích, giữ lại các ý chính quan trọng nhất. Tránh lặp ý.`;
        const userPrompt = `Dưới đây là các đoạn tóm tắt nhỏ:\n\n${res
          .map((s, i) => `### Tóm tắt ${i + 1}:\n${s}`)
          .join('\n\n')}\n\n--> Tổng kết cuối cùng theo cấu trúc rõ ràng sau:
            ### Overview:
                ...
            ### Concern:
                ...
            ### Advise:
                ...`;
        const completion = await openai.chat.completions.create({
          model: OpenAIModel.GPT_4_o,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt },
          ],
          temperature: 0.3,
          max_tokens: 500,
        });
        summary = completion.choices[0].message.content;
      } else {
        summary = res[0];
      }
      const currentDate = new Date();
      await this.conversationRepo.update(
        {
          pageId: conversationDetail.pageId,
          scopedUserId: conversationDetail.scopedUserId,
          feedId: '',
        },
        {
          lastMessageIdGetSummary: messages[0].id,
          lastSummary: summary,
          lastSummaryAt: currentDate,
          lastSummaryBy: -3,
        },
      );
      return {
        lastSummary: summary,
        lastSummaryAt: currentDate,
        lastSummaryBy: -3,
      };
    } catch (e) {
      console.log('getConversationSummary error', e);
      throw new BadRequestException('Không thể tóm tắt cuộc hội thoại');
    }
  }
}
