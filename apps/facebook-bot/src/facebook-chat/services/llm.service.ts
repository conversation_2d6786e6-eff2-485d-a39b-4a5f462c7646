import { BadRequestException, Injectable } from '@nestjs/common';
import { OpenAI } from 'openai';
import {
  ChatCompletionMessageParam,
  ChatCompletionTool,
} from 'openai/src/resources/chat/completions';
import { isEmpty, sample } from 'lodash';
import { InjectModel } from '@nestjs/mongoose';
import { LlmDocument } from '../../schemas/llm-document.schema';
import { Model } from 'mongoose';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { OrderStatus } from '../../../../../core/enums/order-status.enum';
import { AiProductConfiguration } from '../../entities/ai-product-configuration.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { messageConnection } from '../../../../../core/constants/database-connection.constant';
import { In, Repository } from 'typeorm';
import {
  AiProductConfigurationDto,
  UpdateAiProductConfigurationDto,
} from '../dtos/ai-product-configuration.dto';
import { instanceToPlain, plainToInstance } from 'class-transformer';
import { AiConfigurationGroup } from '../../entities/ai-configuration-group.entity';
import {
  AiConfigurationGroupDto,
  UpdateAiConfigurationGroupDto,
} from '../dtos/ai-configuration-group.dto';
import { FanPage } from '../../entities/fanpage.entity';
import { AiProductImage } from '../../entities/ai-product-image.entity';
import { ConfigurationGroupsFilter } from '../filters/configuration-groups.filter';
import { PaginationOptions } from '../../../../../core/decorators/pagination/pagination.model';
import { ProductConfigsFilter } from '../../message/filters/product-configs.filter';
import { v4 as uuidv4 } from 'uuid';
import { AiProductDocumentDto } from '../dtos/ai-product-document.dto';

export interface ILlmCustomer {
  gender: 'MALE' | 'FEMALE' | 'OTHER' | undefined;
}

'LUÔN trả lời một cách lễ phép và lịch sự, luôn sử dụng từ "dạ" ở đầu câu trả lời và "ạ" ở cuối câu. Ví dụ: Dạ, giá sản phẩm là 120.000 ạ.';

export const SALE_PROMPTS = [
  "ALWAYS ask if function's parameter is unclear, and only execute one tool.",
  "ALWAYS use tool confirm_order to confirm customer's order when customer send phone number and address",
  'ALWAYS separate answer into many short paragraphs under 50 words if the content is too long.',
  'ALWAYS search for information in the provided tools (get_answer_info, confirm_order, ...) before answering user questions.',
  'ALWAYS remind users of all parameters for the function call.',
  'Always remain steadfast in the opinion provided about the product, even if the customer requests changes.',
  // 'DO NOT change the opinion provided about the product based on the customer\'s suggestion',
  'There is no need to include the product name when searching for information using the "get_answer_info" function',
];

export const PITCHING_PROMPTS = [
  "PLEASE retrieve product data using the 'get_answer_info' function and send persuasive messages to responding customers to drive sales, DO NOT utilize the 'handle_convince' function for this purpose.",
];

const stages = [
  {
    name: 'Introduction',
    content:
      'Start the conversation by introducing yourself and your company. Be polite and respectful while keeping the tone of the conversation professional. Your greeting should be welcoming/',
  },
  {
    name: 'Qualification',
    content:
      'Qualify the prospect by confirming if they are the right person to talk to regarding your product/service. Ensure that they have the authority to make purchasing decisions.',
  },
  {
    name: 'Value proposition',
    content:
      'Briefly explain how your product/service can benefit the prospect. Focus on the unique selling points and value proposition of your product/service that sets it apart from competitors.',
  },
  {
    name: 'Needs analysis',
    content:
      "Ask open-ended questions to uncover the prospect's needs and pain points. Listen carefully to their responses and take notes.",
  },
  {
    name: 'Solution presentation',
    content:
      "Based on the prospect's needs, present your product/service as the solution that can address their pain points.",
  },
  {
    name: 'Objection handling',
    content:
      'Address any objections that the prospect may have regarding your product/service. Be prepared to provide evidence or testimonials to support your claims.',
  },
  {
    name: 'Close',
    content:
      'Ask for the sale by proposing a next step. This could be a demo, a trial or a meeting with decision-makers. Ensure to summarize what has been discussed and reiterate the benefits.',
  },
  // {
  //   'name': 'End conversation',
  //   'content': 'The prospect has to leave to call, the prospect is not interested, or next steps where already determined by the sales agent.',
  // },
];

export const LLM_DEFAULT_PROMPTS = (
  { gender }: ILlmCustomer,
  initialData?: IMessageInitialData,
  isPitching = false,
): ChatCompletionMessageParam[] => {
  const productName = initialData.currentConfiguration?.name;
  console.log('isPitching', isPitching);
  const { vocative, language, extraPrompts } = initialData.groupConfiguration;
  let prompts = [
    // `Luôn giao tiếp bằng ngôn ngữ "${language}", không được sử dụng ngôn ngữ khác.`,
    // `Luôn tư vấn về sản phẩm${productName ? ` "${productName}" ` : ' '}để khách hàng dễ quyết định mua hàng`,
    // 'LUÔN LUÔN PHẢI tìm thông tin trong các tài liệu bằng tools (get_answer_info,confirm_order,...) được cung cấp trước khi trả lời câu hỏi của người dùng',
    // 'Luôn phải nhắc người dùng về tất cả các tham số cho lệnh gọi hàm',
    // 'Không được thay đổi thông tin trả lời theo lời khách',
    `ALWAYS answer in "${language}" only, DO NOT use other languages.`,
    'DO NOT response markdown formatting',
    "Keep your responses in short length to retain the user's attention",
    'DO NOT make up stories or use fake data to response',
    `ALWAYS provide advice about the"${
      productName ? ` "${productName}" ` : ' '
    }"product to help customers decide to purchase.`,
  ]
    .concat(!isPitching ? SALE_PROMPTS : PITCHING_PROMPTS)
    .concat(extraPrompts || []);
  console.log('extraPrompts', extraPrompts);
  if (!isEmpty(vocative)) {
    const customerVocatives = vocative[1].split('|');
    let customerVocative: string;
    if (gender === 'MALE') {
      customerVocative = customerVocatives[0];
    } else if (gender === 'FEMALE') {
      customerVocative = customerVocatives[1];
    } else {
      customerVocative = customerVocatives[2];
    }
    prompts.push(
      `Luôn trả lời với cách xưng hô "${vocative[0]}" cho chính mình và "${customerVocative}" cho người hỏi`,
    );
  }
  if (!isEmpty(initialData.currentConfiguration?.extraPrompts)) {
    prompts = prompts.concat(initialData.currentConfiguration.extraPrompts);
  }
  let systemPrompt = `\n-` + prompts.join('.\n-') + '.\n';
  let defaultPrompt = `As a sales representative, you should ALWAYS respond to customers and provide product advice according to the following requirements:`;
  const { extraConfig } = initialData.groupConfiguration;
  if (extraConfig?.saleStages) {
    extraConfig.type = 'sale';
  }
  if (extraConfig?.type === 'sale') {
    const { saleStages: stages, companyDescription, companyName } = extraConfig;
    if (companyName && companyDescription)
      defaultPrompt = `Never forget you work as a "sales representative".
You work at company named ${companyName}. ${companyName}'s business is the following: ${companyDescription}. You should ALWAYS respond to customers and provide product advice according to the following requirements:`;
    if (!isEmpty(stages))
      systemPrompt += `\nALWAYS think about at which conversation stage you are at before response or select function tool:
    ${stages.map((s, i) => `${i + 1}: ${s.name}: ${s.content}`).join('\n')}

You must respond according to the conversation history and the stage of the conversation you are at.`;
  }
  return [
    {
      role: 'system',
      content: defaultPrompt + systemPrompt,
    },
  ];
};

// export const LLM_DEFAULT_PROMPTS = ({ gender }: ILlmCustomer, productName: string): ChatCompletionMessageParam[] => [
//   {
//     role: 'system',
//     content:
//       `Luôn tư vấn về sản phẩm ${productName} để khách hàng dễ quyết định mua hàng.
//       Luôn trả lời với cách xưng hô "em" cho chính mình và "${genderMapper[gender] || 'anh/chị'}" cho người hỏi, KHÔNG ĐƯỢC xưng "bạn" với người hỏi.
//       LUÔN trả lời một cách lễ phép và lịch sự, luôn sử dụng từ "dạ" ở đầu câu trả lời và "ạ" ở cuối câu. Ví dụ: Dạ, giá sản phẩm là 120.000 ạ.
//       LUÔN LUÔN PHẢI tìm thông tin trong các tài liệu bằng tools (get_answer_info,confirm_order,...) được cung cấp trước khi trả lời câu hỏi của người dùng.
//       Luôn phải nhắc người dùng về tất cả các tham số cho lệnh gọi hàm.
//       Không được đưa ra giả định về những giá trị nào sẽ được đưa vào hàm. Yêu cầu làm rõ nếu yêu cầu của người dùng không rõ ràng.
//       Không được thay đổi thông tin trả lời theo lời khách.
//       `,
//   },
// ];

const tools = (config: IMessageInitialData): ChatCompletionTool[] => {
  let combos: string[];
  if (config.currentConfiguration) {
    combos = config.currentConfiguration.prices.map(i => i.name);
  }
  console.log('config.currentConfiguration', config.currentConfiguration);
  const defaultTools: ChatCompletionTool[] = [
    {
      type: 'function',
      function: {
        name: 'create_order',
        description: 'Tạo đơn hàng khi khách đồng ý hoặc muốn mua hàng',
        parameters: {
          type: 'object',
          properties: {
            phone: {
              type: 'string',
              description: 'Số điện thoại nhận hàng của khách',
            },
            address: {
              type: 'string',
              description: 'Địa chỉ nhận hàng của khách',
            },
            product: {
              type: 'string',
              enum: combos,
              description: 'Sản phẩm/combo khách muốn mua',
            },
          },
          required: ['phone', 'address', 'product'],
        },
      },
    },
    {
      type: 'function',
      function: {
        name: 'confirm_order',
        description:
          'Gửi lại tin nhắn xác nhận thông tin đơn hàng trước khi tạo đơn khi khách hàng nhắn số điện thoại và địa chỉ',
        parameters: {
          type: 'object',
          properties: {
            phone: {
              type: 'string',
              description: 'Số điện thoại nhận hàng của khách',
            },
            address: {
              type: 'string',
              description: 'Địa chỉ nhận hàng của khách',
            },
            product: {
              type: 'string',
              enum: combos,
              description: 'Sản phẩm/combo khách muốn mua',
            },
          },
          required: ['phone', 'address', 'product'],
        },
      },
    },
    {
      type: 'function',
      function: {
        name: 'get_answer_info',
        description:
          'Trả lời câu hỏi khách hàng hoặc tìm kiếm thông tin dựa trên thông tin sản phẩm',
        parameters: {
          type: 'object',
          properties: {
            info: {
              type: 'string',
              description:
                'Nội dung muốn hỏi, ví dụ: \n-Sản phẩm được sản xuất ở đâu. \n-Giá sản phẩm.',
            },
          },
          required: ['info'],
        },
      },
    },
    {
      type: 'function',
      function: {
        name: 'handle_convince',
        description: 'Thuyết phục khách mua hàng khi khách không muốn mua hoặc không quan tâm',
      },
    },
    {
      type: 'function',
      function: {
        name: 'handle_image',
        parameters: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['feedback', 'product'],
              description: 'Kiểu ảnh khách muốn nhận',
            },
          },
          required: ['type'],
        },
        description: 'Xử lý khi khách xin hình ảnh sản phẩm/feedback/...',
      },
    },
  ];
  if (config.groupConfiguration.productConfigurations.length > 1) {
    for (const defaultTool of defaultTools) {
      if (defaultTool.function?.parameters?.properties) {
        defaultTool.function.parameters.properties['config'] = {
          type: 'string',
          enum: config.groupConfiguration.productConfigurations.map(i => i.name),
          description: 'Sản phẩm khách đang quan tâm',
        };
        if (defaultTool.function.parameters.required) {
          (defaultTool.function.parameters.required as string[]).push('config');
        } else {
          defaultTool.function.parameters.required = ['config'];
        }
      }
    }
  }
  // if (!config.currentProductId || !config.currentConfiguration) {
  // }
  return defaultTools;
};

export interface IProductCombo {
  name: string;
  discount: number;
  shippingFee: number;
  products: {
    productId: number;
    quantity: number;
    price: number;
  }[];
}

export interface IBotOrder {
  pageId: string;
  isCreatedByAI?: boolean;
  products: {
    productId: number;
    quantity: number;
    price: number;
  }[];
  status: OrderStatus;
  carePageId: number;
  customerName: string;
  customerPhone: string;
  addressText: string;
  customerFbScopedUserId: string;
  shippingFee: number;
  discount?: number;
}

export interface IMessageInitialData {
  scopedUserId: string;
  pageId: string;
  botSaleId: number;
  groupConfiguration: AiConfigurationGroup;
  customerName: string;
  currentProductId?: number;
  currentConfiguration?: AiProductConfiguration;
}

@Injectable()
export class LlmService {
  private openAi = process.env.OPENAI_API_KEY
    ? new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: 'https://api.openai.com/v1',
      })
    : undefined;

  constructor(
    @InjectModel(LlmDocument.name) private llmDocumentModel: Model<LlmDocument>,
    private amqpConnection: AmqpConnection,
    @InjectRepository(AiProductConfiguration, messageConnection)
    private aiProductConfigurationRepository: Repository<AiProductConfiguration>,
    @InjectRepository(AiProductImage, messageConnection)
    private aiProductImageRepository: Repository<AiProductImage>,
    @InjectRepository(FanPage, messageConnection)
    private pageRepository: Repository<FanPage>,
    @InjectRepository(AiConfigurationGroup, messageConnection)
    private aiConfigurationGroupRepository: Repository<AiConfigurationGroup>,
  ) {}

  public async createBotOrder({
    data,
    countryId,
    companyId,
    userId,
    projectId,
  }: {
    data: IBotOrder;
    countryId: number;
    companyId: number;
    userId: number;
    projectId: number;
  }) {
    return await this.amqpConnection.publish('order-service', 'create-facebook-order', {
      data,
      countryId,
      companyId,
      userId,
      projectId,
    });
  }

  async completeMessage(messages: ChatCompletionMessageParam[], initData: IMessageInitialData) {
    if (initData.currentProductId && !initData.currentConfiguration) {
      initData.currentConfiguration = initData.groupConfiguration.productConfigurations.find(
        i => i.productId == initData.currentProductId,
      );
    }
    if (!initData.currentProductId && initData.currentConfiguration) {
      initData.currentProductId = initData.currentConfiguration.productId;
    }
    const initTools = tools(initData);
    console.log('initTools', JSON.stringify(initTools));

    const res = await this.openAi.chat.completions.create({
      model: 'gpt-4o-mini',
      messages,
      tools: initTools,
      tool_choice: 'auto',
      temperature: 0,
      user: `${initData.pageId}_${initData.scopedUserId}`,
      n: 1,
    });
    const responseMessage = res.choices[0]?.message;
    console.log('responseMessage', JSON.stringify(res.choices));
    const toolCall = responseMessage?.tool_calls?.[0];
    if (toolCall) {
      const functionName = toolCall.function.name;
      const functionToCall = this.functionMappers[functionName];
      const functionArgs = JSON.parse(toolCall.function.arguments);
      if (functionArgs?.config) {
        initData.currentConfiguration = initData.groupConfiguration.productConfigurations.find(
          i => functionArgs?.config == i.name,
        );
        initData.currentProductId = initData.currentConfiguration.productId;
      }
      const content = functionToCall ? await functionToCall(functionArgs, initData) : null;
      console.log('toolCall.function.name', functionName, functionArgs, content, toolCall.function);
      // if (functionName === 'create_order') {
      //   console.log('functionName', functionName);
      //   return content;
      // }
      if (!content) {
        return '';
      }
      if (typeof content !== 'string') {
        console.log('content', content);
        return content;
      }
      responseMessage.tool_calls = [toolCall];
      messages.push(responseMessage);
      messages.push({
        tool_call_id: toolCall.id,
        role: 'tool',
        content,
      });
      console.log('messages', messages);
      return this.completeMessage(messages, initData);
    } else {
      return responseMessage.content;
    }
  }

  async createIndex(data: LlmDocument[], productId: number) {
    try {
      // await this.llmDocumentModel.bulkWrite()
      await this.llmDocumentModel.deleteMany({
        productId,
      });
      for (const item of data) {
        const contentVector = await this.openAi.embeddings.create({
          input: item.content,
          model: 'text-embedding-3-small',
        });
        item.contentVector = contentVector.data[0].embedding;
      }
      return this.llmDocumentModel.insertMany(data);
    } catch (e) {
      console.log('e', e);
      throw e;
    }
  }

  async createConfigGroup(data: AiConfigurationGroupDto) {
    await this.handleConfigProductConfigs(data);
    return this.aiConfigurationGroupRepository.save(plainToInstance(AiConfigurationGroup, data));
  }

  async addConfigPages(id: number, pageIds: string[]) {
    const oldConfig = await this.aiConfigurationGroupRepository.findOne({
      where: { id },
      select: ['id', 'projectId', 'companyId', 'countryId'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    const { projectId, companyId, countryId } = oldConfig;
    const updated = await this.pageRepository
      .createQueryBuilder()
      .update()
      .set({
        aiConfigId: id,
      })
      .where({
        id: In(pageIds),
        projectId,
        companyId,
        countryId,
      })
      .returning(['id'])
      .execute();
    return updated.raw.map(i => i.id);
  }

  async removeConfigPages(id: number, pageIds: string[]) {
    const oldConfig = await this.aiConfigurationGroupRepository.findOne({
      where: { id },
      select: ['id', 'projectId', 'companyId', 'countryId'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    const updated = await this.pageRepository
      .createQueryBuilder()
      .update()
      .set({
        aiConfigId: null,
      })
      .where({
        id: In(pageIds),
        aiConfigId: id,
      })
      .returning(['id'])
      .execute();
    return updated.raw.map(i => i.id);
  }

  async updateConfigGroup(id: number, data: UpdateAiConfigurationGroupDto) {
    const oldConfig = await this.aiConfigurationGroupRepository.findOne({
      where: { id },
      select: ['id', 'projectId', 'companyId', 'countryId'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    data = { ...data, ...oldConfig };
    await this.handleConfigProductConfigs(data);
    const config = plainToInstance(AiConfigurationGroup, data);
    console.log('config', config);
    return this.aiConfigurationGroupRepository.save(config);
  }

  async getProductConfigs(pagination: PaginationOptions, filters: ProductConfigsFilter) {
    const { query, projectIds } = filters;
    const qb = this.aiProductConfigurationRepository
      .createQueryBuilder('pc')
      .skip(pagination.skip)
      .take(pagination.limit);
    if (!isEmpty(projectIds)) {
      qb.andWhere('pc.projectId IN(:...projectIds)', { projectIds });
    }
    if (query) {
      qb.andWhere('pc.name LIKE :query', { query: `%${query.replace(/%/g, '')}%` });
    }
    const configs = await qb.getMany();
    const configIds = configs.map(el => el.id);

    const ret = [];
    let images = [];
    if (configIds.length > 0) {
      images = await this.aiProductImageRepository
        .createQueryBuilder('ap')
        .where('ap.config_id IN (:...configIds)', { configIds })
        .getMany();
    }
    console.log('llm product config images', images);
    for (let i = 0; i < configs.length; i++) {
      const c = instanceToPlain(configs[i]);
      c.images = images.filter(el => el.configId === configs[i].id);
      ret.push(c);
    }

    return ret;
  }

  async getConfigGroups(pagination: PaginationOptions, filters: ConfigurationGroupsFilter) {
    const { query, projectId } = filters;
    const qb = this.aiConfigurationGroupRepository
      .createQueryBuilder('cg')
      .skip(pagination.skip)
      .take(pagination.limit);
    if (projectId) {
      qb.andWhere('cg.projectId = :projectId', { projectId });
    }
    if (query) {
      qb.andWhere('cg.name LIKE :query', { query: `%${query.replace(/%/g, '')}%` });
    }
    return qb.getMany();
  }

  async getConfigGroup(id: number) {
    const res = await this.aiConfigurationGroupRepository
      .createQueryBuilder('aiConfig')
      .where('aiConfig.id = :id', { id })
      .leftJoinAndSelect(
        'aiConfig.productConfigurations',
        'pConfig',
        'aiConfig.country_id = pConfig.country_id AND aiConfig.project_id = pConfig.project_id AND aiConfig.company_id = pConfig.company_id',
      )
      .unScope('pConfig')
      .getOne();
    return res;
  }

  async deleteConfigGroup(id: number) {
    const oldConfig = await this.aiConfigurationGroupRepository.findOne({
      where: { id },
      select: ['id'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    return !!(await this.aiConfigurationGroupRepository.delete(id)).affected;
  }

  async createProductConfig(data: AiProductConfigurationDto) {
    return this.aiProductConfigurationRepository.save(
      plainToInstance(AiProductConfiguration, data),
    );
  }

  async updateProductConfig(id: number, data: UpdateAiProductConfigurationDto) {
    const oldConfig = await this.aiProductConfigurationRepository.findOne({
      where: { id },
      select: ['id'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    const config = plainToInstance(AiProductConfiguration, data);
    config.id = id;
    return this.aiProductConfigurationRepository.save(config);
  }

  async delProductDoc(id: string, productId: number) {
    const oldConfig = await this.aiProductConfigurationRepository.findOne({
      where: { id: productId },
      select: ['id'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    const res = await this.llmDocumentModel.deleteOne({
      _id: id,
      productId,
    });
    return res.deletedCount;
  }

  async updateProductDoc(id: string, productId: number, data: AiProductDocumentDto) {
    const oldConfig = await this.aiProductConfigurationRepository.findOne({
      where: { id: productId },
      select: ['id'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    const vector = await this.openAi.embeddings.create({
      input: data.content,
      model: 'text-embedding-3-small',
    });
    data.contentVector = vector.data[0].embedding;
    const res = await this.llmDocumentModel.updateOne(
      {
        _id: id,
        productId,
      },
      data,
    );
    return res.modifiedCount;
  }

  async createProductDoc(productId: number, data: AiProductDocumentDto) {
    const oldConfig = await this.aiProductConfigurationRepository.findOne({
      where: { id: productId },
      select: ['id'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    // const vector = await this.openAi.embeddings.create({
    //   input: data.content,
    //   model: 'text-embedding-3-small',
    // });
    // data.contentVector = vector.data[0].embedding;
    data.productId = productId;
    data._id = uuidv4();
    const res = await this.llmDocumentModel.create(data);
    res.contentVector = [];
    return res.toJSON();
  }

  async getProductConfig(id: number) {
    return this.aiProductConfigurationRepository.findOne({ where: { id } });
  }

  async getProductConfigDocs(id: number) {
    const res = await this.llmDocumentModel.aggregate([
      {
        $match: {
          productId: {
            $eq: id,
          },
        },
      },
      {
        $project: {
          _id: 1,
          title: 1,
          content: 1,
        },
      },
    ]);
    return res;
  }

  async deleteProductConfig(id: number) {
    const oldConfig = await this.aiProductConfigurationRepository.findOne({
      where: { id },
      select: ['id'],
    });
    console.log('old config', oldConfig);
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    console.log('old config', oldConfig);
    return !!(await this.aiProductConfigurationRepository.delete(id)).affected;
  }

  async uploadConfigDocuments(id: number, data) {
    const oldConfig = await this.aiProductConfigurationRepository.findOne({
      where: { id },
      select: ['id'],
    });
    if (!oldConfig) {
      throw new BadRequestException('Cấu hình không tồn tại');
    }
    const docs = data.map((i, idx) => ({
      _id: uuidv4(),
      productId: id,
      title: i.title,
      content: i.text,
    }));
    console.log('docs', docs);
    return this.createIndex(docs, id);
  }

  // private async handleConfigPages(data: AiConfigurationGroupDto | UpdateAiConfigurationGroupDto) {
  //   if (!isEmpty(data.pageIds)) {
  //     data.pages = await this.pageRepository.findByIds(data.pageIds);
  //     const messages = [];
  //     for (const page of data.pages) {
  //       if (page.companyId !== data.companyId) {
  //         messages.push(`Page "${page.name}" không thuộc quản lý của bạn`);
  //       }
  //       if (page.projectId !== data.projectId) {
  //         messages.push(`Page "${page.name}" không thuộc cùng dự án`);
  //       }
  //       if (page.countryId != data.countryId) {
  //         messages.push(`Page "${page.name}" không thuộc cùng thị trường`);
  //       }
  //     }
  //     if (!isEmpty(messages)) {
  //       throw new BadRequestException(messages);
  //     }
  //   } else {
  //     data.productConfigurations = [];
  //   }
  // }

  private async handleConfigProductConfigs(
    data: AiConfigurationGroupDto | UpdateAiConfigurationGroupDto,
  ) {
    if (!isEmpty(data.productConfigIds)) {
      data.productConfigurations = await this.aiProductConfigurationRepository.findByIds(
        data.productConfigIds,
      );
      const messages = [];
      for (const productConfig of data.productConfigurations) {
        if (productConfig.companyId !== data.companyId) {
          messages.push(`Cấu hình "${productConfig.name}" không hợp lệ`);
        }
        if (productConfig.projectId !== data.projectId) {
          messages.push(`Cấu hình "${productConfig.name}" không thuộc cùng dự án`);
        }
        if (productConfig.countryId != data.countryId) {
          messages.push(`Cấu hình "${productConfig.name}" không thuộc cùng thị trường`);
        }
      }
      if (!isEmpty(messages)) {
        throw new BadRequestException(messages);
      }
    } else {
      data.productConfigurations = [];
    }
  }

  // async indexKnowledge(index: string, data: ILlmKnowledge[]) {
  //   for (const chunkData of chunk(data, 100)) {
  //     await this.elasticClient.bulk()
  //   }
  // }

  private createOrder = async (
    {
      phone,
      address,
      product,
    }: {
      phone: string;
      address: string;
      product: string;
    },
    data: IMessageInitialData,
  ) => {
    if (
      isEmpty(phone) ||
      isEmpty(address) ||
      isEmpty(product) ||
      phone === '0987654321' ||
      phone === '0123456789'
    ) {
      return (
        'Dạ để có thể hoàn tất đơn hàng gửi đến anh/chị nhanh nhất, anh/chị cho em xin thêm thông tin lên đơn hàng cho mình ạ:\n' +
        '- Số điện thoại\n' +
        '- Địa chỉ'
      );
    }

    const {
      scopedUserId,
      pageId,
      customerName,
      botSaleId,
      currentConfiguration: { companyId, projectId, countryId, prices },
    } = data;
    const combo = prices.find(i => i.name == product);
    console.log('đã lên đơn', {
      data: {
        customerFbScopedUserId: `${pageId}_${scopedUserId}`,
        carePageId: botSaleId,
        products: combo.products,
        discount: combo.discount,
        shippingFee: combo.shippingFee,
        pageId,
        status: OrderStatus.Draft,
        customerName,
        customerPhone: phone,
        addressText: address,
      },
      projectId,
      countryId,
      companyId,
      userId: botSaleId,
    });
    await this.createBotOrder({
      data: {
        customerFbScopedUserId: `${pageId}_${scopedUserId}`,
        carePageId: botSaleId,
        products: combo.products,
        discount: combo.discount,
        shippingFee: combo.shippingFee,
        pageId,
        status: OrderStatus.Draft,
        customerName,
        customerPhone: phone,
        addressText: address,
      },
      projectId,
      countryId,
      companyId,
      userId: botSaleId,
    });
    const totalPrice = combo.products.reduce(
      (acc, el) => acc + (Number(el.price * el.quantity) || 0),
      0,
    );
    const discount = combo.discount || 0;
    const shippingFee = combo.shippingFee || 0;
    const text = 'Dạ, em đã lên đơn cho anh/chị rồi ạ. Thông tin đơn hàng:\n';

    const textV2 =
      text +
      `Sản phẩm: ${combo.name}\n` +
      `Số điện thoại: ${phone}\n` +
      `Địa chỉ: ${address}\n` +
      (shippingFee ? `Phí ship: ${shippingFee}₫\n` : '') +
      (discount ? `Giảm giá: ${discount}₫\n` : '') +
      `Tổng: ${totalPrice - discount + shippingFee}₫`;

    return pageId === '432819869923623' ? textV2 : text;
  };

  private findDocument = async ({ info }: { info: string }, initData: IMessageInitialData) => {
    console.log('info', info);
    try {
      const questionVector = await this.openAi.embeddings.create({
        input: info,
        model: 'text-embedding-3-small',
      });
      // const res = await this.elasticClient.search({
      //   index: 'test_giac_hoi_vn_index',
      //   knn: {
      //     field: 'content_vector',
      //     query_vector: questionVector.data[0].embedding,
      //     k: 5,
      //     num_candidates: 10,
      //   }
      // });
      // return res.hits.hits[0]._source['text'];
      const res = await this.llmDocumentModel.aggregate([
        {
          $vectorSearch: {
            index: 'vector_index',
            path: 'contentVector',
            queryVector: questionVector.data[0].embedding, // Adjust the query vector as needed
            numCandidates: 6,
            limit: 5,
            filter: {
              productId: {
                $eq: initData.currentConfiguration.id,
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            title: 1,
            content: 1,
            score: {
              $meta: 'vectorSearchScore',
            },
          },
        },
        {
          $match: { score: { $gt: 0.5 } }, // Filter by minimum similarity score after vector search
        },
      ]);
      console.log('res', res);
      return res.map(i => i.content).join('\n');
    } catch (e) {
      console.log('e', e);
      return null;
    }
  };

  private handleConvince = async (args, initData: IMessageInitialData) => {
    try {
      // const res = await this.elasticClient.search({
      //   index: 'test_giac_hoi_vn_index',
      //   query: {
      //     bool: {
      //       must: {
      //         match: {
      //           title: 'convince'
      //         }
      //       }
      //     }
      //   }
      // });
      const res = await this.llmDocumentModel.find({
        productId: initData.currentConfiguration.id,
        title: {
          $in: ['convince', 'function', 'info'],
        },
      });
      return sample(res).content;
      // const hit = sample(res.hits.hits);
      // return hit._source['text'];
    } catch (e) {
      console.log('e', e);
      return null;
    }
  };

  private handleImage = async ({ type }, data: IMessageInitialData) => {
    if (!data.currentConfiguration) {
      return [];
    }
    console.log('data', data);
    const images = await this.aiProductImageRepository.find({
      configId: data.currentConfiguration.id,
      type,
    });
    return images.map(i => i.imageId);
    // return [101527, 101528, 101529, 101530, 101531, 101532];
  };

  private confirmOrder = async ({ phone, address, product }, data: IMessageInitialData) => {
    await this.createOrder({ phone, address, product }, data);
    return `Dạ, em xin phép xác nhận lại thông tin đơn hàng cho anh/chị ạ:
    - ${product}
    - SĐT: ${phone}
    - Địa chỉ: ${address}`;
  };

  private functionMappers: Record<
    string,
    (arg: Record<string, any>, initData: IMessageInitialData) => Promise<string | number[]>
  > = {
    create_order: this.createOrder,
    get_answer_info: this.findDocument,
    handle_convince: this.handleConvince,
    handle_image: this.handleImage,
    confirm_order: this.confirmOrder,
  };
}
