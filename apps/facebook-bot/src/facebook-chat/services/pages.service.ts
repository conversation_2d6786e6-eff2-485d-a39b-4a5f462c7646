import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { messageConnection } from 'core/constants/database-connection.constant';
import { getConnection, In, Repository, Not } from 'typeorm';
import { Conversation } from '../../entities/conversation.entity';
import { User } from '../../entities/user.entity';
import axios, { AxiosError } from 'axios';
import { ConversationTag } from '../../entities/conversation-tag.entity';
import { plainToInstance } from 'class-transformer';
import { chunk, difference, groupBy, isEmpty } from 'lodash';
import { ConversationsTags } from '../../entities/conversations-tags.entity';
import { BotApiService } from './bot.service';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq/lib/amqp/connection';
import { UserStatus } from '../../enums/user-status.enum';
import { IFbApi } from '../../facebook-api';
import { ScopedUserPage } from '../../entities/scoped-user-page.entity';
import { Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import {
  FACEBOOK_API_BASE_URL,
  FACEBOOK_API_ENDPOINT,
} from '../../constants/fb-api-endpoints.constant';
import { ScopedUser } from '../../entities/scoped-user.entity';
import { FanPage } from '../../entities/fanpage.entity';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { UserSession } from '../../entities/user-sessions.entity';
import { GET_STARTED_PAYLOAD } from '../../constants/facebook-bot.constant';
import { getRequestContext } from 'core/hooks/request-context.hook';

interface IPancakeTag {
  color: string;
  text: string;
  id: number;
}

@Injectable()
export class PagesService {
  constructor(
    @InjectRepository(User, messageConnection)
    private userRepo: Repository<User>,
    @InjectRepository(UserSession, messageConnection)
    private sessionRepository: Repository<UserSession>,
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
    @InjectRepository(ConversationTag, messageConnection)
    private conversationTagRepository: Repository<ConversationTag>,
    @InjectRepository(ConversationsTags, messageConnection)
    private conversationsTagRepository: Repository<ConversationsTags>,
    private botApiService: BotApiService,
    private readonly amqpConnection: AmqpConnection,
    @InjectRepository(FanPage, messageConnection)
    private fanPageRepository: Repository<FanPage>,
    @InjectRepository(ScopedUser, messageConnection)
    private suRepo: Repository<ScopedUser>,
    @InjectRepository(ScopedUserPage, messageConnection)
    private supRepo: Repository<ScopedUserPage>,
  ) {}

  async invite(pageId: string, userIds?: string[]) {
    const invited = [];
    const api = await this.botApiService.getApi(pageId);
    for (const userId of userIds) {
      const sent = await api.addAdminPage(userId, pageId);
      if (sent) {
        invited.push(userId);
      }
    }
    return invited;
  }

  async invitePageProfile(pageId: string, userIds?: string[]) {
    const invited = [];
    const api = await this.botApiService.getApi(pageId);
    for (const userId of userIds) {
      const sent = await api.inviteAdminProfilePlus(pageId, userId);
      if (sent) {
        invited.push(userId);
      }
    }
    return invited;
  }

  async addAdmin(pageId: string, userIds?: string[], max = 7) {
    const currentActive = await this.userRepo
      .createQueryBuilder('u')
      .innerJoin('user_page', 'up', 'up.page_id = :pageId AND u.id = up.user_id', { pageId })
      .innerJoin('u.sessions', 's', 's.status = 1 AND s.ip = :ip', {
        ip: global.currentIp,
      })
      .getMany();
    const activeBots = currentActive?.length || 0;
    const remain = max - activeBots;
    if (remain <= 0) {
      return { sents: [], accepts: [] };
    }
    const query = this.userRepo
      .createQueryBuilder('u')
      .innerJoinAndSelect('u.sessions', 's', 's.status = 1')
      .leftJoin('user_page', 'up2', 'up2.page_id = :pageId AND up2.user_id = u.id', { pageId })
      .andWhere('up2.page_id IS NULL');
    if (!isEmpty(userIds)) {
      query.andWhere('u.id IN (:...userIds)', { userIds });
    } else {
      query
        .limit(remain)
        .leftJoin(
          qb =>
            qb
              .from('user_page', 'up')
              .groupBy('up.user_id')
              .select('COUNT(*)', 'count')
              .addSelect('up.user_id', 'id'),
          'p',
          'p.id = u.id',
        )
        .addSelect('p.count', 'page_count')
        .orderBy('page_count', 'ASC', 'NULLS FIRST')
        .addOrderBy('random()');
    }
    const users = await query.getMany();
    const sents = [],
      accepts = [];
    for (const user of users) {
      try {
        const api = await this.botApiService.getApi(pageId);
        if (!api) {
          throw new BadRequestException('Không thể thêm tài khoản');
        }
        const userApi = await this.botApiService.getUserApi(user.id);
        const sent = await api.addAdminPage(user.id, pageId);
        console.log('sent', sent);
        console.log(`${user.name} received: ${sent}`);
        if (sent) {
          sents.push(user);
          const accept = await userApi.acceptAdminPage(pageId);
          console.log(`${user.name} accepted: ${accept}`);
          if (accept) {
            accepts.push(user);
            await getConnection(messageConnection)
              .createQueryBuilder()
              .insert()
              .into('user_page')
              .values({ user_id: user.id, page_id: pageId })
              .execute();
            if (user.token) {
              try {
                const pageRes = await axios.request({
                  method: 'GET',
                  url: FACEBOOK_API_ENDPOINT.ME(`${pageId}`),
                  baseURL: FACEBOOK_API_BASE_URL,
                  params: {
                    access_token: user.token,
                    fields: 'access_token,name',
                  },
                });
                const page = pageRes.data;
                await this.supRepo.save(
                  plainToInstance(ScopedUserPage, {
                    pageId,
                    scopedUserId: user.id,
                    accessToken: page?.access_token,
                    deletedAt: null,
                  }),
                );
              } catch (e) {
                console.error('update token error', e);
              }
            }
          }
        }
      } catch (e) {
        console.log('add admin error', e);
      }
    }
    return { sents, accepts };
  }

  async leaveAdmin(pageId: string, max = 7) {
    const currentActive = await this.userRepo
      .createQueryBuilder('u')
      .innerJoin('user_page', 'up', 'up.page_id = :pageId AND u.id = up.user_id', { pageId })
      .innerJoin('u.sessions', 's', 's.status = 1 AND s.ip = :ip', {
        ip: global.currentIp,
      })
      .getMany();
    const activeBots = currentActive?.length || 0;
    const remain = activeBots - max;
    if (remain <= 0) {
      return { removes: [] };
    }
    const query = this.userRepo
      .createQueryBuilder('u')
      .innerJoinAndSelect('u.sessions', 's', 's.status = 1')
      .innerJoin('user_page', 'up2', 'up2.page_id = :pageId AND up2.user_id = u.id', { pageId })
      // .where('u.isSubscriber = FALSE')
      .limit(remain)
      .leftJoin(
        qb =>
          qb
            .from('user_page', 'up')
            .groupBy('up.user_id')
            .select('COUNT(*)', 'count')
            .addSelect('up.user_id', 'id'),
        'p',
        'p.id = u.id',
      )
      .addSelect('p.count', 'page_count')
      .orderBy('page_count', 'DESC', 'NULLS FIRST')
      .addOrderBy('random()');
    const users = await query.getMany();
    const removes = [];
    for (const user of users) {
      const userApi = await this.botApiService.getUserApi(user.id);
      if (!userApi) {
        continue;
      }
      const left = await userApi.leaveAdminPage(pageId);
      console.log('left', left);
      if (left) {
        removes.push(user);
        await getConnection(messageConnection)
          .createQueryBuilder()
          .delete()
          .from('user_page')
          .where({ user_id: user.id, page_id: pageId })
          .execute();
        await this.supRepo.remove(
          plainToInstance(ScopedUserPage, {
            pageId,
            scopedUserId: user.id,
          }),
        );
      }
    }
    return { removes };
  }

  async syncGlobalId(pageId: string) {
    await this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-conversation', {
      id: pageId,
      skipMessage: true,
    });
    return true;
  }

  async syncConversation(pageId: string, skipMessage = false, force = false) {
    await this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-conversation', {
      id: pageId,
      skipMessage,
      force,
    });
    return true;
  }

  async syncFeeds(id?: string) {
    if (!id) {
      const pages = await this.supRepo
        .createQueryBuilder('s')
        .select('DISTINCT (s.page_id)', 'page_id')
        .where('s.access_token IS NOT NULL')
        .getRawMany();

      const chunkPages = chunk(pages, 2);
      for (const items of chunkPages) {
        await Promise.all(
          items.map(page =>
            this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-feed', {
              id: page.page_id,
            }),
          ),
        );
      }

      return pages;
    }
    await this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-feed', { id });
    return true;
  }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'sync-pancake-tags',
    queue: 'ag-sync-pancake-tags',
    errorHandler: rmqErrorsHandler,
  })
  async handleSyncTags({ pancakeToken, pageId }) {
    if (!pancakeToken || !pageId) {
      return;
    }
    const settings = await axios.get(`https://pages.fm/api/v1/pages/${pageId}/settings`, {
      params: {
        access_token: pancakeToken,
      },
    });
    const tags: IPancakeTag[] = settings.data?.settings?.tags;
    if (!tags) {
      return new Nack();
    }
    let updateTags: ConversationTag[] = [];
    const oldTags = await this.conversationTagRepository.find({
      name: In(tags.map(i => i.text)),
    });
    updateTags = tags
      .filter(i => oldTags.findIndex(ot => ot.name === i.text) < 0)
      .map(i => plainToInstance(ConversationTag, { name: i.text, color: i.color }));
    updateTags = await this.conversationTagRepository.save(updateTags);
    const handleTags = [...oldTags, ...updateTags];
    const handleTagLookup = groupBy(handleTags, 'name');
    const tagsLookup: Record<string, ConversationTag> = tags.reduce((prev, i) => {
      prev[i.id] = handleTagLookup[i.text][0];
      return prev;
    }, {});
    let conversations = [];
    let count = 0;
    const now = Math.floor(Date.now() / 1000);
    while (1) {
      const res = await axios.get(`https://pages.fm/api/v1/pages/${pageId}/conversations`, {
        params: {
          access_token: pancakeToken,
          tags: '%22ALL%22',
          current_count: count * 60,
          type: `DATE:0 - ${now}`,
        },
      });
      conversations = res.data.conversations;
      if (isEmpty(conversations)) {
        break;
      }
      const conversationsTags = [];
      count++;
      for (const conversation of conversations) {
        const { from_psid: scopedUserId, tags } = conversation;
        for (const tag of tags) {
          if (tagsLookup[tag] && scopedUserId) {
            conversationsTags.push({
              scoped_user_id: scopedUserId,
              page_id: pageId,
              tag_id: tagsLookup[tag].id,
            } as ConversationsTags);
          }
        }
      }
      try {
        await this.conversationsTagRepository.save(
          plainToInstance(ConversationsTags, conversationsTags),
        );
      } catch (e) {}
    }
  }

  async getAttachment(pageId: string, id: string) {
    const api = await this.botApiService.getPageTokenApi(pageId);
    const response = await api({
      url: FACEBOOK_API_ENDPOINT.ME(id),
      params: {
        fields: 'source',
      },
    });
    return response.source;
  }

  // async syncPancakeTags(userId: string, pageId: string) {
  //   try {
  //     const api = await this.botApiService.getUserApi(userId, pageId);
  //     const pancakeToken = await api.getPancakeToken();
  //     if (!pancakeToken) {
  //       return false;
  //     }
  //     await this.amqpConnection.publish(
  //       'facebook-conversation-event',
  //       'sync-pancake-tags',
  //       {
  //         pageId,
  //         pancakeToken,
  //       },
  //     );
  //     return true;
  //   } catch (e) {
  //     console.log(e);
  //   }
  // }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'add-admin-page',
    queue: 'bot-queue-add-admin-page',
    errorHandler: rmqErrorsHandler,
  })
  async addAdminPage({ pageId }) {
    return {};
    // return this.addAdmin(pageId);
  }

  // async reSubscribePages(id: string, force = false) {
  //   try {
  //     const api = await this.botApiService.getUserApi(id);
  //     const token = await api.getCrawlToken();
  //     console.log('subscribe token', token);
  //     const page = await axios.request({
  //       method: 'GET',
  //       url: `/me/accounts`,
  //       baseURL: FACEBOOK_API_BASE_URL,
  //       params: {
  //         access_token: token,
  //         fields: 'is_webhooks_subscribed',
  //         limit: 100,
  //       },
  //     });
  //     const notSubscribedPages = page.data.data
  //       .filter(i => force || !i.is_webhooks_subscribed)
  //       .map(i => i.id);
  //     if (isEmpty(notSubscribedPages)) {
  //       throw new BadRequestException('page already subscribed');
  //     }
  //     const res = await this.fanPageRepository.update(
  //       {
  //         id: In(notSubscribedPages),
  //       },
  //       {
  //         isSubscribed: false,
  //         isSyncConversation: false,
  //       },
  //     );
  //
  //     await this.amqpConnection.publish('facebook-bot', 'handle-subscribe-page-with-token', {
  //       pageIds: notSubscribedPages,
  //       token,
  //       userId: id,
  //       resubscribe: true,
  //     });
  //     return res.affected;
  //   } catch (e) {
  //     console.log(e);
  //     throw new BadRequestException(e);
  //   }
  // }

  async subscribe(pageIds: string[], id?: string) {
    pageIds = difference(pageIds, []);

    if (isEmpty(pageIds)) {
      return false;
    }
    if (id) {
      try {
        const api = await this.botApiService.getUserApi(id);
        const token = await api.getCrawlToken();
        await this.updatePageToken(token, process.env.FACEBOOK_APP_CLIENT_ID, `${id}`);
        console.log('subscribe page token', token);
        if (token) {
          await this.amqpConnection.publish('facebook-bot', 'handle-subscribe-page-with-token', {
            // pageIds: pageIds,
            token,
            userId: id,
          });
        }
      } catch (e) {
        console.log('subscribe page error', e);
      }
      return { id };
    }
    // const userPages = await getConnection(messageConnection)
    //   .createQueryBuilder()
    //   .from('user_page', 'up')
    //   .where('up.page_id IN (:...pageIds)', { pageIds })
    //   .innerJoin(
    //     'user_sessions',
    //     'us',
    //     'us.ip = :ip AND us.status = 1 AND us.user_id = up.user_id',
    //     {
    //       ip: global.currentIp,
    //     },
    //   )
    //   .innerJoin('fanpage', 'p', 'p.id = up.page_id AND p.is_subscribed = FALSE')
    //   .select('up.page_id, up.user_id')
    //   .getRawMany();
    // const topUsers = {};
    // const pageGroups = reduce(
    //   userPages,
    //   (prev, item) => {
    //     prev[item.page_id] = prev[item.page_id] || [];
    //     prev[item.page_id].push(item.user_id);
    //     topUsers[item.user_id] = (topUsers[item.user_id] || 0) + 1;
    //     return prev;
    //   },
    //   {},
    // );
    // const topGroup = {};
    // for (const pageId of pageIds) {
    //   topGroup[pageId] = orderBy(pageGroups[pageId], i => topUsers[i])[0];
    // }
    // const pages = {};
    // for (const pageId of Object.keys(topGroup)) {
    //   pages[topGroup[pageId]] = [...(pages[topGroup[pageId]] || []), pageId];
    // }
    // for (const userId of Object.keys(pages)) {
    //   if (!userId) {
    //     continue;
    //   }
    //   try {
    //     const api = await this.botApiService.getUserApi(userId);
    //     const token = await api.getCrawlToken();
    //     console.log('subscribe page token', token);
    //     if (token) {
    //       // const subscribed = await this.subscribeByUserToken(token, pages[userId]);
    //       // subscribedPages.concat(...subscribed);
    //
    //       await this.amqpConnection.publish('facebook-bot', 'handle-subscribe-page-with-token', {
    //         pageIds: pages[userId],
    //         token,
    //         userId,
    //       });
    //     }
    //   } catch (e) {
    //     console.log('subscribe page error', e);
    //   }
    // }
    return true;
    // const api = await this.botApiService.getApi(pageId);
    // const token = await api.getCrawlToken();
    // console.log('token', token);
    // return axios.post(process.env.FB_SUBSCRIBE, {
    //   token
    // });
    // return api.getCurrentUserID();
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'handle-subscribe-page-with-token',
    queue: 'bot-queue-handle-subscribe-page-with-token',
    errorHandler: rmqErrorsHandler,
  })
  async subscribeByUserToken({
    token,
    pageIds,
    userId,
  }: {
    token: string;
    pageIds: string[];
    userId: string;
  }) {
    const responses = [];
    // try {
    //   const user = await this.userRepo.findOne(userId);
    //   if (!user) {
    //     console.log('subscribe user token error', 'can not get user info');
    //     return;
    //   }
    // } catch (e) {
    //   console.log('subscribe user token error', e);
    //   return;
    // }
    let after = null;
    while (1) {
      try {
        // const userRes = await axios.request({
        //   method: 'GET',
        //   url: FACEBOOK_API_ENDPOINT.ME(),
        //   baseURL: FACEBOOK_API_BASE_URL,
        //   params: {
        //     access_token: token,
        //   },
        // });
        // const uId = userRes.data.id;
        const pageResponse = await axios.request({
          method: 'GET',
          url: FACEBOOK_API_ENDPOINT.ME() + '/accounts',
          baseURL: FACEBOOK_API_BASE_URL,
          params: {
            access_token: token,
            limit: 1000,
            after,
            fields: 'access_token,id',
          },
        });
        for (const page of pageResponse.data?.data) {
          const pageToken = page?.access_token;
          const pageId = page?.id;
          console.log(`subscribe page ${page.id}`, page, pageToken);
          try {
            // const runningBots = await getConnection(messageConnection)
            //   .createQueryBuilder()
            //   .from('scoped_user_pages', 'up')
            //   .where('up.page_id = :id AND up.scoped_user_id != :uId', {id: page.id, uId})
            //   .select('up.scoped_user_id')
            //   .getRawMany();
            // console.log('running bots', runningBots);
            const shouldCrawl = false;
            // if (isEmpty(runningBots)) {
            //   shouldCrawl = true;
            // }
            // const lastMessage = await this.conversationRepo.findOne({
            //   where: {
            //     pageId,
            //     feedId: '',
            //     lastSentByPage: false,
            //     updatedAt: LessThan(new Date(Date.now() - 30000))
            //   },
            //   order: {
            //     updatedAt: 'DESC',
            //   },
            //   select: ['updatedAt'],
            // });
            const subscribeRes = await axios.post(process.env.FB_SUBSCRIBE, {
              token: pageToken,
            });
            console.log(`subscribe page ${pageId} subscribed`, subscribeRes.data);
            const res = await this.fanPageRepository.update(
              {
                id: pageId,
                isSubscribed: false,
              },
              { isSubscribed: true },
            );

            // await this.supRepo.save(
            //   plainToInstance(ScopedUserPage, {
            //     pageId,
            //     scopedUserId: userId,
            //     accessToken: pageToken,
            //   }),
            // );

            // if (res.affected || shouldCrawl)
            //   await this.amqpConnection.publish(
            //     'facebook-conversation-event',
            //     'fetch-page-conversation',
            //     {
            //       id: pageId,
            //       force: true,
            //       // afterTime: lastMessage?.updatedAt,
            //       skipComment: !res.affected,
            //     },
            //   );
            responses.push(subscribeRes.data);
          } catch (e) {
            console.log(`subscribe page ${pageId} error`, (e as AxiosError).response.data);
          }
        }
        after = pageResponse.data?.paging?.cursors?.after;
        if (!pageResponse.data?.paging?.next) {
          break;
        }
        await global.sleep(200);
      } catch (error) {
        console.log(`error`, error?.response?.data || error);
        if (error?.response?.data?.error) throw error?.response?.data?.error;
      }
    }
    return responses;
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'handle-new-pages',
    queue: 'bot-queue-handle-new-pages',
    errorHandler: rmqErrorsHandler,
  })
  async handleNewPages({ pageIds, id }) {
    if (isEmpty(pageIds)) {
      return new Nack(false);
    }
    // await this.amqpConnection.publish('facebook-bot', 'handle-subscribe-page', {
    //   pageIds,
    // });
    // for (const pageId of pageIds) {
    //   await this.syncConversation(pageId);
    // }
    return this.subscribe(pageIds, id);
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'handle-subscribe-page',
    queue: 'bot-queue-handle-subscribe-page',
    errorHandler: rmqErrorsHandler,
  })
  async handleSubscribe({ pageIds, id }) {
    return this.subscribe(pageIds, id);
  }

  async handleSubscribeV1({ pageId }) {
    const users = await this.userRepo
      .createQueryBuilder('u')
      .innerJoin('user_page', 'up', 'up.user_id = u.id')
      .where('up.page_id = :pageId', { pageId })
      .innerJoinAndSelect('u.sessions', 's', 's.status = 1 AND s.ip = :ip', {
        ip: global.currentIp,
      })
      // .andWhere('u.isSubscriber = TRUE')
      .getMany();
    for (const user of users) {
      try {
        const api = await this.botApiService.getUserApi(user.id);
        const token = user.sessions[0]?.token;
        console.log('token', token);
        if (!token) {
          continue;
        }
        const pageRes = await axios.request({
          method: 'GET',
          url: FACEBOOK_API_ENDPOINT.ME(`${pageId}`),
          baseURL: FACEBOOK_API_BASE_URL,
          params: {
            access_token: token,
            fields: 'access_token,name',
          },
        });
        const page = pageRes.data;
        const subscribed = await axios.request({
          method: 'GET',
          url: FACEBOOK_API_ENDPOINT.SUBSCRIBE(),
          baseURL: FACEBOOK_API_BASE_URL,
          params: {
            access_token: page.access_token,
            fields: 'id,subscribed_fields',
          },
        });
        try {
          await axios.request({
            method: 'DELETE',
            url: FACEBOOK_API_ENDPOINT.MESSENGER_PROFILE(),
            baseURL: FACEBOOK_API_BASE_URL,
            params: {
              access_token: page.access_token,
            },
            data: {
              fields: ['get_started'],
            },
          });
          await axios.request({
            method: 'POST',
            url: FACEBOOK_API_ENDPOINT.MESSENGER_PROFILE(),
            baseURL: FACEBOOK_API_BASE_URL,
            params: {
              access_token: page.access_token,
            },
            data: {
              get_started: {
                payload: GET_STARTED_PAYLOAD,
              },
            },
          });
        } catch (e) {
          console.log('get started error', e);
        }
        if (subscribed.data?.data) {
          const app = subscribed.data?.data.find(i => i.id === process.env.FACEBOOK_APP_CLIENT_ID);
          if (
            app &&
            app.subscribed_fields?.every(r =>
              [
                'messages',
                'messaging_postbacks',
                'message_reads',
                'message_echoes',
                'message_reactions',
                'messaging_referrals',
                'feed',
              ].includes(r),
            )
          ) {
            break;
          }
        }
        await axios.request({
          method: 'POST',
          url: FACEBOOK_API_ENDPOINT.SUBSCRIBE(),
          baseURL: FACEBOOK_API_BASE_URL,
          params: {
            access_token: page.access_token,
            subscribed_fields:
              'messages,messaging_postbacks,message_reads,message_echoes,message_reactions,messaging_referrals,feed',
          },
        });

        await this.syncConversation(pageId);
      } catch (e) {
        console.log('subscribe errored', e);
      }
    }
    return new Nack();
  }

  async updatePageToken(
    oAuthToken: string,
    appId: string,
    id?: string,
    companyId?: number,
    creatorId?: number,
  ): Promise<{
    user: ScopedUser;
    pages: FanPage[];
  }> {
    let response;
    try {
      response = await axios.request({
        method: 'GET',
        url: FACEBOOK_API_ENDPOINT.ME(),
        baseURL: FACEBOOK_API_BASE_URL,
        params: {
          access_token: oAuthToken,
          fields: 'name,email,id',
        },
      });
    } catch (error) {
      if (error.response?.data?.error?.code === 190) {
        throw error.response?.data?.error;
      }
      console.log(`error`, error.response.data);
    }
    if (!response?.data?.id) {
      throw { message: 'can not resolve token' };
    }
    let accounts = [];
    let after = undefined;
    while (1) {
      try {
        const pageResponse = await axios.request({
          method: 'GET',
          url: FACEBOOK_API_ENDPOINT.ME() + '/accounts',
          baseURL: FACEBOOK_API_BASE_URL,
          params: {
            access_token: oAuthToken,
            limit: 1000,
            after,
          },
        });
        accounts = accounts.concat(pageResponse.data?.data);
        after = pageResponse.data?.paging?.cursors?.after;
        if (!pageResponse.data?.paging?.next) {
          break;
        }
        await global.sleep(200);
      } catch (error) {
        console.log(`error`, error?.response?.data || error);
        if (error?.response?.data?.error) throw error?.response?.data?.error;
      }
    }
    console.log('accounts', accounts);
    const userId = response.data.id;
    const rawPages = accounts.filter(
      i =>
        !i?.tasks ||
        i?.tasks?.includes('MODERATE') ||
        i?.tasks?.includes('PROFILE_PLUS_MODERATE') ||
        i?.tasks?.includes('PROFILE_PLUS_MESSAGING'),
    );
    // await this.subscribe(rawPages.map(i => i.id));
    // if (!skipSubscribe) {
    //   for (const rawPage of rawPages) {
    //     try {
    //       await axios.request({
    //         method: 'POST',
    //         url: FACEBOOK_API_ENDPOINT.SUBSCRIBE(),
    //         baseURL: FACEBOOK_API_BASE_URL,
    //         params: {
    //           access_token: rawPage.access_token,
    //           subscribed_fields:
    //             'messages,messaging_postbacks,message_reads,message_echoes,message_reactions,messaging_referrals,feed',
    //         },
    //       });
    //     } catch (error) {
    //       console.log(`error`, error);
    //     }
    //   }
    // }
    if (getRequestContext().data.req.query) getRequestContext().data.req.query.update = true;

    const existPages = await this.fanPageRepository.findByIds(rawPages.map(i => i.id));
    const newPages = rawPages.filter(i => !existPages.find(j => j.id === i.id));
    const uPages = newPages.map(item => {
      const page = plainToInstance(FanPage, {
        id: item.id,
        name: item.name,
        deletedAt: null,
        updatedAt: new Date(),
        marketerId: creatorId,
        companyId,
      });
      return page;
    });

    const updateOldPages = existPages.map(item => {
      const page = plainToInstance(FanPage, {
        id: item.id,
        name: item.name,
        updatedAt: new Date(),
        deletedAt: null,
      });
      return page;
    });

    const [pages] = await Promise.all([
      this.fanPageRepository.save(uPages),
      this.fanPageRepository.save(updateOldPages),
    ]);

    // console.log('save', uPages);
    const user = plainToInstance(ScopedUser, {
      ...response.data,
      access_token: oAuthToken,
      id: userId,
      appId,
      globalId: id,
      creatorId,
      companyId,
    });

    const updateFields = [
      'app_id',
      'name',
      'short_name',
      'avatar',
      'access_token',
      'global_id',
      'company_id',
      'updated_by',
    ];

    if (creatorId) {
      updateFields.push('creator_id');
    }

    const result = await this.suRepo
      .createQueryBuilder('su')
      .insert()
      .values(user)
      .orUpdate(updateFields, ['id'])
      .execute();
    console.log(`insert scoped user result`, result);

    const mPages: ScopedUserPage[] = rawPages.map(page => ({
      scopedUserId: userId,
      pageId: page.id,
      accessToken: page.access_token,
      deletedAt: null,
      appId,
    }));

    console.log('save pages', mPages);

    await this.supRepo.save(plainToInstance(ScopedUserPage, mPages));
    if (appId === process.env.FACEBOOK_APP_CLIENT_ID) {
      await this.amqpConnection.publish('facebook-bot', 'handle-subscribe-page-with-token', {
        token: oAuthToken,
        userId: id,
      });
    } else {
      await this.amqpConnection.publish('facebook-bot', 'handle-new-pages', {
        pageIds: pages.map(i => i.id),
        id,
      });
    }

    // clean all old pages by scoped_user_id and page id not in mPages
    // todo check company_id also
    const connectedPageIds = mPages.map(i => i.pageId);
    if (connectedPageIds?.length > 0) {
      await this.supRepo.softDelete({
        scopedUserId: userId,
        pageId: Not(In(connectedPageIds)),
      });
    }

    return { user, pages };
  }

  async updateUserPages(
    api: IFbApi,
    companyId: number,
    token?: string,
    attempts = 3,
    skipToken = false,
  ) {
    console.log('init token', token);
    const id = await api.getCurrentUserID();
    if (!skipToken && !token) {
      token = await api.getInstagramToken();
      await this.amqpConnection.publish('facebook-bot', 'handle-instagram-token', { id });
      console.log('updated token', token);
    }
    let saveUser: User, session: UserSession;
    if (token) {
      let user, pages;
      try {
        const res = await this.updatePageToken(token, '380032743914781', `${id}`);
        user = res?.user;
        pages = res?.pages;
      } catch (e) {
        console.log('update page token error', e);
        throw e;
      }
      session = plainToInstance(UserSession, {
        user_id: String(id),
        token,
        ip: global.currentIp,
        status: UserStatus.active,
      } as UserSession);
      saveUser = plainToInstance(User, {
        id: String(id),
        email: user.email,
        companyId,
      } as User);
      const oldPages = await this.fanPageRepository.findByIds(pages.map(i => i.pageId));
      const oldPageLookup: Record<string, FanPage> = oldPages.reduce((prev, item) => {
        prev[item.id] = item;
        return prev;
      }, {});
      saveUser.pages = pages.map(i => {
        if (oldPageLookup[i.pageId]) {
          return oldPageLookup[i.pageId];
        }
        const page = new FanPage();
        page.id = i.id;
        page.name = i.name;
        page.companyId = companyId;
        return page;
      });
    } else {
      // session = plainToInstance(UserSession, {
      //   user_id: String(id),
      //   ip: global.currentIp,
      //   status: UserStatus.active,
      //   token: null,
      // } as UserSession);
      // saveUser = plainToInstance(User, {
      //   id: String(id),
      //   companyId
      // } as User);
      // const pages = await api.getPageList();
      // const oldPages = await this.fanPageRepository.findByIds(
      //   pages.map(i => i.id),
      // );
      // const oldPageLookup: Record<string, FanPage> = oldPages.reduce(
      //   (prev, item) => {
      //     prev[item.id] = item;
      //     return prev;
      //   },
      //   {},
      // );
      // saveUser.pages = pages.map(i => {
      //   if (oldPageLookup[i.id]) {
      //     return oldPageLookup[i.id];
      //   }
      //   const page = new FanPage();
      //   page.id = i.id
      //   page.name = i.name;
      //   page.companyId = companyId;
      //   return page;
      // });
    }
    console.log('session', session);
    const ses = await this.sessionRepository.save(session);
    const user = await this.userRepo.save(saveUser);
    user.sessions = [ses];
    return user;
  }
}

/* const ignoredHHLivePages = [
  '437579409435406',
  '167409039792999',
  '436961746157810',
  '396631830204381',
  '462968943555956',
  '424619884068455',
  '102275536230269',
  '107958952322386',
  '107668624987415',
  '102533265956722',
  '105602178929665',
  '106761639109756',
  '340273505847149',
  '435619759625098',
  '105217422598776',
  '357723314090582',
  '107718834980671',
  '320240084516918',
  '111228065326127',
  '108672941959645',
  '100875095674168',
  '102406572578440',
  '348401995034246',
  '109557668126909',
  '102494288852945',
  '109030718180182',
  '317775544763123',
  '106074782512135',
  '363102046893286',
  '399494676582029',
  '433955756464690',
  '264115823440602',
  '452701031252755',
  '442995598894776',
  '357083480827181',
  '429459510252680',
  '424120824109951',
  '366928349847216',
  '445785091948733',
  '452043294652472',
  '366160679920605',
  '110293705023850',
  '113906445056428',
  '105338625222287',
  '369482322920519',
  '431399093387641',
  '461868223668430',
  '380830028442571',
  '381748765028388',
  '410878922113100',
  '363696963502801',
  '100548066406141',
  '385628487968786',
  '106719621750586',
  '120884880950776',
  '104964539291105',
  '413352068520842',
  '100236462448072',
  '123017977562214',
  '106714979114438',
  '380890965116540',
  '436775416184388',
  '414905598368341',
  '370029476201725',
  '102177309091834',
  '407314012464180',
  '103074045739490',
  '111393434881020',
  '440295862493371',
  '420492771148320',
  '396245640244377',
  '430628473458691',
  '357679100772349',
  '341566992382912',
  '392109930641840',
  '364207183435253',
  '443347552186285',
  '376816088855598',
  '416896538163732',
  '449876534866394',
]; */
