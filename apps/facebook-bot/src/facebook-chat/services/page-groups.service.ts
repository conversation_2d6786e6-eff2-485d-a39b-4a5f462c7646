import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, ForbiddenException, Injectable, NotFoundException, UnauthorizedException, } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { messageConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import { groupBy, isEmpty, isNil, pick } from 'lodash';
import { Brackets, Repository, getConnection } from 'typeorm';
import FilterUtils from '../../../../../core/utils/FilterUtils';
import { Conversation } from '../../entities/conversation.entity';
import { FanPage } from '../../entities/fanpage.entity';
import { GroupUser } from '../../entities/group-user.entity';
import { PageGroup } from '../../entities/page-group.entity';
import { User } from '../../read-entities/identity-api/user.entity';
import { CreatePageGroupDto, UpdatePageGroupDto } from '../dtos/page-group.dto';
import { PageGroupsFilter } from '../filters/page-groups.filter';

@Injectable()
export class PageGroupsService {
  constructor(
    @InjectRepository(PageGroup, messageConnection)
    private pageGroupRepository: Repository<PageGroup>,
    @InjectRepository(FanPage, messageConnection)
    private pagesRepository: Repository<FanPage>,
    private redisCache: RedisCacheService,
    private amqpConnection: AmqpConnection,
  ) {}

  async findGroups(
    filter: PageGroupsFilter = {},
    pagination?: PaginationOptions,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<[PageGroup[], number]> {
    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return [[], 0];
    const {
      name,
      productIds,
      userIds,
      getUnreadConversationCount,
      countProjects,
      countProducts,
      companyId,
    } = filter;
    const qb = this.pageGroupRepository
      .createQueryBuilder('g')
      .leftJoin('g.pages', 'p')
      .addSelect(['p.id', 'p.name'])
      .leftJoin('g.users', 'u')
      .addSelect(['u.groupId', 'u.userId'])
      .leftJoin('g.configurationGroup', 'cg')
      .addSelect(['cg.id', 'cg.name'])
      .orderBy('g.isActive', 'DESC')
      .addOrderBy('g.createdAt', 'ASC');

    if (pagination) qb.take(pagination.limit).skip(pagination.skip);
    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      // const projectIds = headers['project-ids']?.split(',');
      if (!isEmpty(countryIds)) qb.andWhere('g.countryId IN (:...countryIds)', { countryIds });
      // if (!isEmpty(projectIds)) qb.andWhere('g.projectId IN (:...projectIds)', { projectIds });
    }
    if (projectIds) qb.andWhere('p.projectId IN (:...projectIds)', { projectIds });
    if (companyId) qb.andWhere('g.companyId = :companyId', { companyId });
    if (name) {
      qb.andWhere(
        new Brackets(sqb =>
          sqb
            .where('g.name ~* :query')
            .orWhere('CAST(p.id AS TEXT) = :query')
            .orWhere('p.name ~* :query'),
        ),
      ).setParameters({ query: name });
    }
    // if (productIds) qb.andWhere('g.productId IN (:...productIds)', { productIds });
    if (userIds)
      qb.innerJoin(GroupUser, 'gu', 'g.id = gu.groupId AND gu.userId IN (:...userIds)', {
        userIds,
      });

    if (getUnreadConversationCount) {
      qb.leftJoin(
        `(
          SELECT
            COUNT (*) AS count, p.group_id
          FROM
            conversations
          LEFT JOIN fanpage p ON p.id = page_id
          WHERE
            unread = TRUE
            AND group_id IS NOT NULL
          GROUP BY
            group_id
          )`,
        'conversations',
        'conversations.group_id = g.id',
      );
      qb.addSelect('conversations.count', 'unread_conversations');
      // qb.addGroupBy('g.id, p.id, unread_conversations');
    }
    if (countProjects || countProducts) {
      const countSubQb = this.pagesRepository
        .createQueryBuilder('p')
        .select('p.groupId', 'group_id')
        .addSelect('COUNT (DISTINCT p.project_id)', 'projects_count')
        .addSelect('COUNT (DISTINCT p.product_id)', 'products_count')
        .where('p.groupId IS NOT NULL')
        .andWhere('p.companyId = :companyId')
        .groupBy('p.groupId')
        .setParameters({ companyId });
      qb.leftJoin(`(${countSubQb.getQuery()})`, 'gc', 'g.id = gc.group_id')
        .addSelect('gc.projects_count', 'projects_count')
        .addSelect('gc.products_count', 'products_count');
    }

    const [groups, count] = await qb.getManyAndCount();
    await this.mapGroups(groups);
    return [groups, count];
  }

  async mapGroups(groups: PageGroup[]) {
    const { userIds } = groups.reduce(
      (prev, it) => {
        for (const userId of it.users) {
          if (prev.userIds.indexOf(userId) === -1) prev.userIds.push(userId);
        }
        return prev;
      },
      { userIds: [] },
    );

    const [{ data: users }] = await Promise.all([
      this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'get-users-by-ids',
        payload: { ids: userIds },
        timeout: 10000,
      }),
    ]);

    const usersLookup = (users as User[]).reduce((prev: Record<string, User>, item) => {
      prev[item.id] = item;
      return prev;
    }, {});

    for (const group of groups) {
      for (const item of group.users) {
        const user = usersLookup[item.userId];
        if (user) item.user = user;
      }
    }
  }

  async findCountBot(request: Record<string, any>, headers: Record<string, any>){
    const {companyId} = request.user
    const sql = this.pagesRepository.createQueryBuilder('page');
    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      const projectIds = headers['project-ids']?.split(',');
      if (!isEmpty(countryIds))
        sql.andWhere('page.countryId IN (:...countryIds)', {countryIds});
      if (!isEmpty(projectIds))
        sql.andWhere('page.projectId IN (:...projectIds)', {projectIds});
    }
    if (companyId) sql.andWhere('page.companyId = :companyId', {companyId});
    sql.andWhere('page.groupId IS NOT NULL').select(['page.id', 'page.name', 'page.groupId']);
    sql.leftJoin(
      '(SELECT COUNT(DISTINCT u.id) as total, page_id FROM "user_page" LEFT JOIN "user" u ON u.id = user_page.user_id INNER JOIN user_sessions s ON s.user_id = u.id AND  s.ip = :ip AND s.status = 1 GROUP BY page_id)',
      'up',
      '"up".page_id = "page".id',
      {
        ip: global.currentIp,
      },
    );
    sql.addSelect('up.total as total_user');
    const result =  await sql.getMany();
    return groupBy(result, 'groupId')
  }

  async createGroup(
    body: CreatePageGroupDto,
    user: AuthUser,
    headers?: Record<string, string>,
  ): Promise<PageGroup> {
    if (!headers?.['country-ids']) throw new ForbiddenException('country-ids is required');
    const { id: uid, companyId } = user;
    const countryId = headers?.['country-ids'];

    let group = plainToInstance(PageGroup, {
      ...body,
      countryId: Number(countryId),
      companyId,
    });

    group.pages = await this.pagesRepository.findByIds(body.pageIds);
    for (const page of group.pages) {
      if (page.countryId !== group.countryId)
        throw new BadRequestException(`Các page không cùng quốc gia`);
      if (page.groupId) throw new BadRequestException(`Page ${page.name} đã thuộc nhóm khác`);
    }
    if (body.userIds)
      group.users = body.userIds.map(userId => plainToInstance(GroupUser, { userId }));

    const connection = getConnection(messageConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      group = await queryRunner.manager.save(group);

      await queryRunner.commitTransaction();
      await this.redisCache.delWithPrefix('fanpage.');
      return group;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      if (e?.driverError) throw new BadRequestException(e?.driverError);
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async updateGroup(id: number, body: UpdatePageGroupDto, user: AuthUser): Promise<PageGroup> {
    const now = new Date();

    const oldGroup = await this.pageGroupRepository.findOne({
      where: { id },
      relations: ['users'],
    });
    if (!oldGroup) throw new NotFoundException('Group not found');

    let group = plainToInstance(PageGroup, {
      ...oldGroup,
      ...pick(body, ['name', 'configurationGroupId', 'isActive', 'botStartAt', 'botEndAt']),
      updatedAt: now,
    });
    if (body.pageIds) {
      group.pages = await this.pagesRepository.findByIds(body.pageIds);
      for (const page of group.pages) {
        if (page.countryId !== group.countryId)
          throw new BadRequestException(`Các page không cùng quốc gia`);
        if (page.groupId && page.groupId !== id)
          throw new BadRequestException(`Page ${page.name} đã thuộc nhóm khác`);
      }
    }

    let groupUsers: GroupUser[] = [];
    let removeGroupUsers: GroupUser[] = [];
    if (body.userIds) {
      const existingUserIds = oldGroup.users.map(it => it.userId);
      groupUsers = body.userIds.reduce((prev, userId) => {
        const groupUser = plainToInstance(GroupUser, {
          groupId: id,
          userId,
        });

        // update existing
        if (existingUserIds.includes(userId)) {
          prev.push(groupUser);
          return prev;
        }

        // add new item
        prev.push(groupUser);
        return prev;
      }, []);

      // remove items that do not exist in DTO
      removeGroupUsers = oldGroup.users.reduce((prev, item) => {
        if (!body.userIds.includes(item.userId)) prev.push(item);
        return prev;
      }, []);
    }

    const connection = getConnection(messageConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      group = await queryRunner.manager.save(group);
      if (!isNil(body.userIds)) group.users = await queryRunner.manager.save(groupUsers);
      if (!isEmpty(removeGroupUsers)) await queryRunner.manager.remove(removeGroupUsers);
      await queryRunner.commitTransaction();
      await this.redisCache.delWithPrefix('fanpage.');

      if (oldGroup.isActive !== group.isActive && group.isActive === false) {
        await this.amqpConnection.publish(
          'facebook-bot',
          'revoke-scoped-users-on-page-group-disabled',
          {
            pageGroupId: id,
            isActive: group.isActive,
            updatedAt: group.updatedAt,
          },
        );
      }

      return group;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      if (e?.driverError) throw new BadRequestException(e?.driverError);
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async getGroup(
    id: number,
    countUnreadConversations?: boolean,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const qb = this.pageGroupRepository
      .createQueryBuilder('g')
      .leftJoin('g.users', 'u')
      .addSelect(['u.groupId', 'u.userId'])
      .andWhere('g.id = :id', { id })
      .andWhere('g.companyId = :companyId', { companyId })
      .leftJoinAndSelect('g.pages', 'pages');
    if (countUnreadConversations) {
      qb.leftJoin(
        `(
          SELECT
            COUNT (*) AS count, p.group_id
          FROM
            conversations
          LEFT JOIN fanpage p ON p.id = page_id
          WHERE
            unread = TRUE
            AND group_id = :id
          GROUP BY
            group_id
          )`,
        'c',
        'c.group_id = g.id',
      );
      qb.addSelect('c.count', 'unread_conversations');
    }

    const group = await qb.getOne();
    if (group) await this.mapGroupUser(group);
    return group;
  }

  async mapGroupUser(group: PageGroup) {
    const userIds = group.users.reduce((prev, it) => {
      if (prev.indexOf(it) === -1) prev.push(it);
      return prev;
    }, []);

    const { data: users } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: userIds },
      timeout: 10000,
    });

    const usersLookup = (users as User[]).reduce((prev: Record<string, User>, item) => {
      prev[item.id] = item;
      return prev;
    }, {});

    for (const item of group.users) {
      const user = usersLookup[item.userId];
      if (user) item.user = user;
    }
  }

  async countPageGroupConversations(id: number) {
    const qb = this.pageGroupRepository
      .createQueryBuilder('g')
      .where('g.id = :id', { id })
      .leftJoin('g.pages', 'pages')
      .leftJoin(Conversation, 'conversations', 'conversations.pageId = pages.id')
      .andWhere('conversations.unread = TRUE')
      .select('COUNT (*)', 'count');
    return qb.getRawOne();
  }

  async syncPageGroupConversations(groupId: string, skipMessage = false, force = false) {
    const pages = await this.pagesRepository
      .createQueryBuilder('p')
      .where('p.group_id = :groupId', { groupId })
      .select(['p.id'])
      .getMany();
    if (isEmpty(pages)) throw new BadRequestException('This group has no pages');

    await Promise.all(
      pages.map(page =>
        this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-conversation', {
          id: page.id,
          skipMessage,
          force,
        }),
      ),
    );
    return true;
  }
}
