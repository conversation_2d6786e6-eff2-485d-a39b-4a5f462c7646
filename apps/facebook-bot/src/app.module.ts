import { BullModule } from '@nestjs/bull';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { messageConnection } from 'core/constants/database-connection.constant';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { MessageModule } from './message/message.module';
import { BaseAuthModule } from '../../../core/auth/auth.module';
import { RmqHandlerModule } from './rmq-handler/rmq-handler.module';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { QueueUIModule } from './queue-ui/queue-ui.module';
import { FacebookChatModule } from './facebook-chat/facebook-chat.module';
import { SocketModule } from './socket/socket.module';
import { AppController } from './app.controller';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from 'core/filters/all-exception.filter';
import { getRmqHost } from 'core/utils/loadEnv';
import { MongooseModule } from '@nestjs/mongoose';
import { LoggerMiddleware } from 'core/middlewares/logger.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        'apps/facebook-bot/' + (process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env'),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      logging: ['error'],
      maxQueryExecutionTime: 30000,
      entities: [__dirname + '/entities/*.entity{ .ts,.js}'],
      name: messageConnection,
      ...(process.env.DATABASE_USERNAME_REPLICATE
        ? {
            replication: {
              master: {
                host: process.env.DATABASE_HOST,
                port: parseInt(process.env.DATABASE_PORT),
                username: process.env.DATABASE_USERNAME,
                password: process.env.DATABASE_PASSWORD,
                database: process.env.DATABASE_MESSAGE,
              },
              slaves: [
                {
                  host: process.env.DATABASE_HOST_REPLICATE,
                  port: parseInt(process.env.DATABASE_PORT_REPLICATE),
                  username: process.env.DATABASE_USERNAME_REPLICATE,
                  password: process.env.DATABASE_PASSWORD_REPLICATE,
                  database: process.env.DATABASE_MESSAGE,
                },
              ],
            },
          }
        : {
            host: process.env.DATABASE_HOST,
            port: parseInt(process.env.DATABASE_PORT),
            username: process.env.DATABASE_USERNAME,
            password: process.env.DATABASE_PASSWORD,
            database: process.env.DATABASE_MESSAGE,
          }),
    }),
    RedisModule.forRoot({
      config: {
        password: process.env.REDIS_PASSWORD,
        db: Number(process.env.REDIS_DB_NUMBER),
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
        keyPrefix: `cache-${process.env.REDIS_DB}`,
      },
    }),
    BullModule.forRoot({
      redis: {
        password: process.env.REDIS_PASSWORD,
        db: Number(process.env.REDIS_DB_NUMBER),
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
      },
      prefix: `agbiz-campaign-queue-${process.env.REDIS_DB}`,
    }),
    // BotModule,
    MessageModule,
    QueueUIModule,
    BaseAuthModule.forRoot(),
    SocketModule,
    FacebookChatModule,
    RmqHandlerModule,
    RabbitMQModule.forRootAsync(RabbitMQModule, {
      useFactory: () => ({
        uri: getRmqHost(),
        prefetchCount: 20,
        registerHandlers: !process.env.CONSUMER || process.env.CONSUMER == 'true' ? true : false,
        exchanges: [
          {
            name: 'delay-exchange',
            type: 'x-delayed-message',
            options: {
              arguments: {
                'x-delayed-type': 'direct',
              },
            },
          },
          {
            name: 'message-campaign-inbox',
            type: 'direct',
          },
          {
            name: 'facebook-webhook-event',
            type: 'direct',
          },
          {
            name: 'facebook-conversation-event',
            type: 'direct',
          },
          {
            name: 'facebook-bot',
            type: 'direct',
          },
          {
            name: 'rmq-errors',
            type: 'direct',
          },
          {
            name: 'message-service',
            type: 'direct',
          },
        ],
        channels: {
          webhook: {
            prefetchCount: 100,
          },
        },
      }),
    }),
    MongooseModule.forRoot(process.env.MONGO_URI),
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    if (process.env.NODE_ENV !== 'production') {
      consumer.apply(LoggerMiddleware).forRoutes('*');
    }
  }
}
