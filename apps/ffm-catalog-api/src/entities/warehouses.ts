import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';
import { Expose, Type } from 'class-transformer';
import { DateTransform } from '../../../../core/decorators/date-transform/date-transform.decorator';
import StringUtils from '../../../../core/utils/StringUtils';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import {
  WarehouseHandoverWorkflow,
  WarehouseStatus,
  WarehouseType,
} from '../catalog/enum/warehouse-type.enum';
import { Product } from './product.entity';
import { PurchaseOrder } from './purchase-order.entity';
import { StockInventory } from './stock-inventory.entity';
import { WarehouseClientAllocation } from './warehouse-client-allocation.entity';
import { SenderInformation } from './sender-information.entity';
import { InventoryManagement } from './inventory-management.entity';
import { StockTransfer } from './stock-transfer.entity';
import { WarehouseSession } from './warehouse-session.entity';
import { VirtualColumn } from 'core/decorators/virtual-column.decorator';

@Entity({
  name: 'slot_warehouses',
  database: process.env.DATABASE_CATALOG,
})
@Unique('UQ_DISPLAY_WAREHOUSE', ['displayId'])
export class SlotWarehouses {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  @Expose()
  id: string;

  @Column('character varying', { name: 'name', nullable: true, length: 255 })
  @Expose()
  name: string | null;

  @Column('uuid', { name: 'user_id', nullable: true })
  @Expose()
  userId: string | null;

  @Column('character varying', { name: 'post_code', nullable: true })
  @Expose()
  postCode: string | null;

  @Column('boolean', { name: 'is_removed', nullable: true })
  @Expose()
  isRemoved: boolean | null;

  @Column('character varying', { name: 'creator_id', nullable: true })
  @Expose()
  creatorId: string | null;

  @Column('int4', { name: 'position', nullable: true, array: true })
  @Expose()
  position: number[] | null;

  @Column('varchar', {
    name: 'user_ids',
    nullable: true,
    array: true,
  })
  @Expose()
  userIds: string[] | null;

  @Column('character varying', {
    name: 'display_id',
    nullable: true,
    length: 255,
  })
  @Index({ unique: true, where: 'display_id IS NOT NULL' })
  @Expose()
  displayId: string | null;

  @Column('character varying', {
    name: 'phone_number',
    nullable: true,
    length: 255,
  })
  @Expose()
  phoneNumber: string | null;

  @Column('character varying', { name: 'address', nullable: true, length: 255 })
  @Expose()
  address: string | null;

  @Column('character varying', {
    name: 'full_address',
    nullable: true,
    length: 255,
  })
  @Expose()
  fullAddress: string | null;

  @Column('integer', { name: 'country_code', nullable: true })
  @Expose()
  countryCode: number | null;

  @Column('timestamp without time zone', { name: 'inserted_at' })
  @Expose()
  @DateTransform()
  insertedAt: Date;

  @Column('timestamp without time zone', { name: 'updated_at' })
  @Expose()
  @DateTransform()
  updatedAt: Date;

  @Column('character varying', {
    name: 'commune_id',
    nullable: true,
    length: 255,
  })
  @Expose()
  communeId: string | null;

  @Column({
    name: 'district_id',
    nullable: true,
    length: 255,
  })
  @Expose()
  districtId: string | null;

  @Column({
    name: 'province_id',
    nullable: true,
    length: 255,
  })
  @Expose()
  provinceId: string | null;

  @Expose()
  get addressSplit() {
    return this.fullAddress ? this.fullAddress.split(',').map(i => i.trim()) : '';
  }

  @Column('varchar', { name: 'type', nullable: true, length: 255 })
  @Expose()
  @EnumTransform(WarehouseType)
  type: WarehouseType;

  @Column('varchar', { name: 'location_lat', nullable: true, length: 255 })
  @Expose()
  lat: string;

  @Column('varchar', { name: 'location_long', nullable: true, length: 255 })
  @Expose()
  long: string;

  // @ManyToMany(() => Product, (prod) => prod.warehouses, { nullable: true})
  // products: Product[];

  @OneToMany(
    () => PurchaseOrder,
    prod => prod.warehouse,
    { nullable: true },
  )
  @Type(() => PurchaseOrder)
  @Expose()
  po?: PurchaseOrder[];

  @OneToMany(
    () => StockInventory,
    prod => prod.warehouse,
    { nullable: true },
  )
  @Type(() => StockInventory)
  @Expose()
  stocks?: StockInventory[];

  @Column({
    name: 'last_editor_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  lastEditorId?: string;

  @Column({
    name: 'biz_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  bizId?: string;

  @OneToMany(
    () => WarehouseClientAllocation,
    wcl => wcl.warehouse,
    { nullable: true },
  )
  @Type(() => WarehouseClientAllocation)
  @Expose()
  clients?: WarehouseClientAllocation[];

  @Column({
    name: 'gst_no',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  gstNo?: string;

  @Column({
    name: 'is_on_sender',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isOnSender?: boolean;

  @OneToMany(
    () => SenderInformation,
    si => si.warehouse,
    { nullable: true, cascade: true },
  )
  @Type(() => SenderInformation)
  @Expose()
  senderInformations?: SenderInformation[];

  @Column({
    name: 'status',
    type: 'integer',
    default: WarehouseStatus.active,
  })
  @EnumTransform(WarehouseStatus)
  @Expose()
  status?: number;

  @OneToMany(
    () => InventoryManagement,
    prod => prod.warehouse,
    { nullable: true },
  )
  @Type(() => InventoryManagement)
  @Expose()
  inventories?: InventoryManagement[];

  @OneToMany(
    () => StockTransfer,
    prod => prod.recipientWarehouse,
    { nullable: true },
  )
  @Type(() => StockTransfer)
  @Expose()
  recipientWarehouse?: StockTransfer[];

  @OneToMany(
    () => StockTransfer,
    prod => prod.senderWarehouse,
    { nullable: true },
  )
  @Type(() => StockTransfer)
  @Expose()
  senderWarehouse?: StockTransfer[];

  @Column({ type: 'integer', name: 'return_warehouse_id', nullable: true })
  @Expose()
  returnWarehouseId?: number;

  @OneToMany(
    () => WarehouseSession,
    ws => ws.warehouse,
    { nullable: true },
  )
  @Type(() => WarehouseSession)
  @Expose()
  warehouseSessions?: WarehouseSession[];

  @Column({
    name: 'handover_workflow',
    type: 'integer',
    default: WarehouseHandoverWorkflow.SingleOrder,
  })
  @EnumTransform(WarehouseHandoverWorkflow)
  @Expose()
  handoverWorkflow?: number;

  @VirtualColumn('prefix')
  @Expose()
  prefix?: string;

  @Column({
    name: 'is_return_default',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isReturnDefault?: boolean;
}
