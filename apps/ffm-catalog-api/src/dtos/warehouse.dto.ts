import { ArrayNotEmpty, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EnumTransform } from '../../../../core/decorators/enum-transform.decorator';
import {
  WarehouseHandoverWorkflow,
  WarehouseStatus,
  WarehouseType,
} from '../catalog/enum/warehouse-type.enum';
import { Type } from 'class-transformer';
import { WarehouseSessionDto } from './warehouse-session.dto';

export class SenderInformationDto {
  @ApiProperty()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  senderName?: string;

  @ApiProperty()
  @IsNotEmpty()
  senderPhone?: string;

  @ApiProperty()
  @IsNotEmpty()
  senderAddress?: string;

  @ApiProperty()
  @IsNotEmpty()
  senderProvince?: string;

  @ApiProperty()
  @IsNotEmpty()
  senderDistrict?: string;

  @ApiProperty()
  @IsOptional()
  senderPostCode?: string;

  @ApiProperty()
  @IsOptional()
  senderWard?: string;

  @ApiProperty()
  @IsOptional()
  senderProvinceId?: string;

  @ApiProperty()
  @IsOptional()
  senderDistrictId?: string;

  @ApiProperty()
  @IsOptional()
  senderWardId?: string;

  @IsOptional()
  @ApiProperty()
  creatorId: number;

  @IsOptional()
  @ApiProperty()
  lastUpdatedBy: number;

  @IsNotEmpty()
  @ApiProperty()
  warehouseId: number;

  @IsNotEmpty()
  @ApiProperty()
  carrierId: number;
}
export class WarehouseDto {
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @ApiProperty()
  phoneNumber: string;

  @IsNotEmpty()
  @ApiProperty()
  fullAddress: string;

  @IsNotEmpty()
  @ApiProperty()
  provinceId: string;

  @IsNotEmpty()
  @ApiProperty()
  districtId: string;

  @ApiProperty()
  @IsOptional()
  displayId?: string;

  @IsNotEmpty()
  @ApiProperty()
  countryCode: string;

  @IsNotEmpty()
  @ApiProperty()
  @IsEnum(WarehouseType)
  @EnumTransform(WarehouseType)
  type: WarehouseType;

  @ApiProperty()
  @IsOptional()
  lat: string;

  @ApiProperty()
  @IsOptional()
  long: string;

  @ApiProperty()
  @IsOptional()
  communeId?: string;

  @ApiProperty()
  @IsOptional()
  postCode?: string;

  @ApiProperty()
  @IsOptional()
  gstNo?: string;

  @ApiProperty({
    isArray: true,
  })
  // @IsNotEmpty()
  @IsOptional()
  // @ArrayNotEmpty({ message: 'userIds là bắt buộc' })
  userIds?: string[];

  @ApiProperty()
  @IsOptional()
  isOnSender?: boolean;

  @IsOptional()
  @ApiProperty({
    type: SenderInformationDto,
    isArray: true,
  })
  @Type(() => SenderInformationDto)
  senderInformations: SenderInformationDto[];

  @IsNotEmpty()
  @ApiProperty({ type: 'enum', enum: WarehouseStatus })
  @IsEnum(WarehouseStatus)
  @EnumTransform(WarehouseStatus)
  status: WarehouseStatus;

  @IsOptional()
  @ApiProperty()
  returnWarehouseId?: number;

  @IsOptional()
  @ApiProperty()
  whSessions?: WarehouseSessionDto[];

  @IsNotEmpty()
  @ApiProperty({ type: 'enum', enum: WarehouseHandoverWorkflow })
  @IsEnum(WarehouseHandoverWorkflow)
  @EnumTransform(WarehouseHandoverWorkflow)
  handoverWorkflow: WarehouseHandoverWorkflow;

  @ApiProperty()
  @IsOptional()
  isReturnDefault?: boolean;
}

export class UpdateWarehouseUser {
  @IsNotEmpty()
  @ApiProperty()
  id: number;

  @IsNotEmpty()
  @ApiProperty({ required: false })
  @IsOptional()
  oldIds?: string[];

  @IsNotEmpty()
  @ApiProperty({ required: false })
  @IsOptional()
  newIds?: string[];
}
