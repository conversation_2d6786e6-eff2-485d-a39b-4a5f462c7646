/* eslint-disable prefer-const */
import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass, plainToInstance } from 'class-transformer';
import { catalogConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { CountryID } from 'core/enums/carrier-code.enum';
import { UserType } from 'core/enums/user-type.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { objToEnum } from 'core/utils';
import {
  cloneDeep,
  compact,
  differenceBy,
  findIndex,
  floor,
  isEmpty,
  isNil,
  min,
  reduce,
  split,
  uniq,
} from 'lodash';
import xlsx from 'node-xlsx';
import { $enum } from 'ts-enum-util';
import { Brackets, EntityManager, In, Not, Repository } from 'typeorm';
import { VariantDto } from '../../dtos/product-variation.dto';
import { ProductComboDto } from '../../dtos/product-combo.dto';
import { ProductsDto, ProductUpdateStatusDto } from '../../dtos/products.dto';
import { AttributesValue } from '../../entities/attributes-value.entity';
import { Attributes } from '../../entities/attributes.entity';
import { Category } from '../../entities/category.entity';
import { InventoryManagement } from '../../entities/inventory-management.entity';
import { Logs } from '../../entities/logs.entity';
import { ProductComboVariant } from '../../entities/product-combo-variant.entity';
import { ProductVariation } from '../../entities/product-variation.entity';
import { Product } from '../../entities/product.entity';
import { Users } from '../../read-entities/identity-entities/Users';
import { AttributeStatus } from '../enum/attribute-status.enum';
import { ProductStatus } from '../enum/product-status.enum';
import { FilterProduct, FilterVariant } from '../filters/product.filter';
import { TypeSortProduct } from '../enum/product-sort.enum';
import { ProductAdditional } from '../../enums/product-additional.enum';
import * as ExcelJS from 'exceljs';
import * as moment from 'moment-timezone';
import { saveImageToExcel } from 'core/utils/ImageUtils';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product, catalogConnection)
    private productsRepository: Repository<Product>,
    @InjectRepository(ProductVariation, catalogConnection)
    private variantRepository: Repository<ProductVariation>,
    @InjectRepository(Attributes, catalogConnection)
    private attrRepository: Repository<Attributes>,
    @InjectRepository(AttributesValue, catalogConnection)
    private avRepository: Repository<AttributesValue>,
    @InjectRepository(ProductComboVariant, catalogConnection)
    private pcvRepository: Repository<ProductComboVariant>,
    @InjectRepository(Logs, catalogConnection)
    private logRepository: Repository<Logs>,
    @InjectRepository(Category, catalogConnection)
    private categoryRepository: Repository<Category>,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  @RabbitRPC({
    exchange: 'ffm-catalog-service',
    routingKey: 'ffm-order-find-product',
    queue: 'queue-ffm-order-find-product',
    errorHandler: rmqErrorsHandler,
  })
  async findProduct(payload) {
    console.log('🐔 payload', 'ffm-order-find-product', payload);
    if ((!payload?.ids && !payload?.sku) || !payload?.countryId) {
      return new Nack();
    }
    const { ids, user, sku, clientId, countryId } = payload;

    const query = this.variantRepository
      .createQueryBuilder('variants')
      .select([
        'variants.id',
        'variants.isDangerous',
        'variants.prefix',
        'variants.productId',
        'variants.size',
        'variants.sku',
        'variants.status',
        'variants.weight',
        'properties.id',
        'properties.name',
        'properties.status',
        'attributes.id',
        'attributes.name',
        'attributes.status',
        'productVariant.categoryId',
        'productVariant.clientId',
        'productVariant.description',
        'productVariant.id',
        'productVariant.isCombo',
        'productVariant.link',
        'productVariant.name',
        'productVariant.prefix',
        'productVariant.script',
        'productVariant.sku',
        'productVariant.status',
        'productVariant.chineseName',
        'variantCombo.id',
        'variantCombo.prefix',
        'variantCombo.productId',
        'variantCombo.size',
        'variantCombo.sku',
        'variantCombo.status',
        'variantCombo.weight',
        'variantCombo.isDangerous',
        'variantInfo.name',
        'variantInfo.id',
        'variantInfo.prefix',
        'variantInfo.sku',
        'variantInfo.link',
        'variantInfo.chineseName',
        'variantComboProperties.id',
        'variantComboProperties.name',
        'variantComboProperties.status',
        'variantComboattributes.id',
        'variantComboattributes.name',
        'variantComboattributes.status',
      ])
      .leftJoin('variants.product', 'productVariant')
      .leftJoinAndSelect('productVariant.combo', 'combo', `combo.status = :statusCombo`, {
        statusCombo: ProductStatus.active,
      })
      .leftJoin('combo.variant', 'variantCombo')
      .leftJoin('variantCombo.product', 'variantInfo')
      .leftJoin('variantCombo.properties', 'variantComboProperties')
      .leftJoin('variantComboProperties.attributes', 'variantComboattributes')

      .leftJoin('variants.properties', 'properties')
      .leftJoin('properties.attributes', 'attributes')
      .where('variants.countryId = :countryId', { countryId });
    if (!isEmpty(ids)) {
      query.andWhere('variants.id IN (:...ids)', { ids });
      query.andWhere('variants.bizId = :bizId', { bizId: user?.companyId });
    }
    if (!isEmpty(sku)) {
      query.andWhere('variants.sku IN (:...sku)', { sku });
      if (clientId) query.andWhere('variants.clientId = :clientId', { clientId });
    }

    const products = await query.getMany();
    // console.log('🐔  ~ ProductsService ~ findProduct ~ query:', products);
    return products;
  }

  async find(
    request,
    pagination: PaginationOptions,
    query: FilterProduct,
    header,
  ): Promise<[Product[], number]> {
    const {
      productIds,
      cateIds,
      status,
      clientId,
      clientIds,
      additional,
      sortBy,
      sortType,
    } = query;
    let { countryId } = query;

    if (!!header['country-ids']) {
      countryId = header['country-ids'];
    } else {
      throw new BadRequestException();
    }

    let users: Users[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user',
        payload: {
          filters: { type: UserType.customer, countries: countryId },
          pagination,
          header: {},
          request: { user: { companyId: request?.user?.companyId } },
        },
        timeout: 10000,
      });
      users = data as Users[];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    // Subquery to find product IDs matching the criteria
    const subQuery = this.productsRepository
      .createQueryBuilder('p')
      .select('DISTINCT p.id')
      .leftJoin('p.variants', 'v')
      .leftJoin('p.combo', 'c')
      .leftJoin('c.variant', 'cv')
      .leftJoin('p.category', 'cat');

    // Apply all filter conditions to the subquery
    subQuery.where('p.bizId = :bizId', { bizId: request?.user?.companyId });
    subQuery.andWhere('p.countryId = :countryId', { countryId });

    if (!isNil(status) && !!status[0]) {
      subQuery.andWhere('p.status IN (:...status)', { status });
    } else {
      subQuery.andWhere('p.status != :deleteStatus', {
        deleteStatus: ProductStatus.delete,
      });
    }

    if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      subQuery.andWhere(
        new Brackets(qb => {
          qb.where('p.creatorId = :creatorId', {
            creatorId: request?.user?.id,
          }).orWhere('p.clientId = :clientId', { clientId: request?.user?.id });
        }),
      );
    }

    if (!!clientId) {
      subQuery.andWhere('p.clientId = :clientId', { clientId });
    }

    if (!!clientIds) {
      subQuery.andWhere('p.clientId IN (:...clientIds)', { clientIds });
    }

    subQuery.andWhere('p.clientId IN (:...userIds)', {
      userIds: users?.map((item: Users) => item?.id),
    });

    if (!!cateIds) {
      subQuery.andWhere('cat.id IN (:...cateIds)', { cateIds });
    }

    if (additional) {
      subQuery.andWhere(`ARRAY[${additional}] && p.additional`);
    }

    if (!!productIds) {
      subQuery.andWhere('p.id IN (:...productIds)', {
        productIds: productIds.split(',').map(item => Number(item)),
      });
    }

    if (query?.name) {
      const searchTerms = query.name.split(';').map(term => term.trim());
      subQuery.andWhere(
        new Brackets(qb => {
          searchTerms.forEach((term, index) => {
            const condition = new Brackets(innerQb => {
              innerQb
                .where('p.name ILIKE :name' + index, { ['name' + index]: `%${term}%` })
                .orWhere('p.sku ILIKE :sku' + index, { ['sku' + index]: `%${term}%` })
                .orWhere('v.sku ILIKE :varSku' + index, { ['varSku' + index]: `%${term}%` })
                .orWhere('cv.sku ILIKE :comboSku' + index, { ['comboSku' + index]: `%${term}%` });
                // .orWhere("CONCAT(p.prefix, '-', p.sku) ILIKE :prefixSku" + index, {
                //   ['prefixSku' + index]: `%${term}%`,
                // })
                // .orWhere("CONCAT(v.prefix, '-', v.sku) ILIKE :prefixVarSku" + index, {
                //   ['prefixVarSku' + index]: `%${term}%`,
                // })
                // .orWhere("CONCAT(cv.prefix, '-', cv.sku) ILIKE :prefixComboSku" + index, {
                //   ['prefixComboSku' + index]: `%${term}%`,
                // });
            });
            qb.orWhere(condition);
          });
        }),
      );
    }

    if (!isNil(query?.isCombo)) {
      subQuery.andWhere('p.isCombo = :isCombo', { isCombo: query.isCombo });
      if (query?.isCombo) {
        subQuery.andWhere('c.status != :deleteStatus', {
          deleteStatus: ProductStatus.delete,
        });
      }
    }

    if (!isEmpty(query?.variantIds)) {
      subQuery.andWhere('v.id IN (:...variantIds)', { variantIds: query?.variantIds });
    }

    // Main query to get all products with their complete variants
    const queryBuilder = this.productsRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.variants', 'variants')
      .leftJoinAndSelect('variants.properties', 'properties')
      .leftJoinAndSelect('product.combo', 'combo')
      .leftJoin('combo.variant', 'combo_variant')
      .leftJoinAndSelect('product.category', 'category')
      .where(`product.id IN (${subQuery.getQuery()})`)
      .setParameters(subQuery.getParameters());

    // Apply sorting
    if (sortBy === TypeSortProduct.category) {
      queryBuilder.orderBy('category.name', sortType || 'ASC');
    } else if (sortBy === TypeSortProduct.client) {
      const userMap = new Map(users.map(user => [user.id, user.name]));
      const clientNameCase = `CASE CAST(product.clientId AS TEXT) ${Array.from(userMap.entries())
        .map(([id, name]) => `WHEN '${id}' THEN '${name}'`)
        .join(' ')} ELSE '' END`;

      queryBuilder.addSelect(clientNameCase, 'client_name');
      queryBuilder.orderBy('client_name', sortType || 'ASC');
    } else {
      queryBuilder.orderBy(`product.${sortBy || 'updatedAt'}`, sortType || 'DESC');
    }

    // Get count using a separate count query with the same filtering
    const countQuery = this.productsRepository
      .createQueryBuilder('product')
      .select('COUNT(DISTINCT product.id)', 'count')
      .where(`product.id IN (${subQuery.getQuery()})`)
      .setParameters(subQuery.getParameters());

    const countResult = await countQuery.getRawOne();
    const count = parseInt(countResult?.count || '0', 10);

    // Apply pagination to main query
    queryBuilder.take(pagination?.limit).skip(pagination?.skip);

    const result = await queryBuilder.getMany();
    return [result, count];
  }
  async findAllProducts(query: FilterVariant, request, header): Promise<Product[]> {
    const { name, names, extraData } = query;
    const { companyId } = request.user;
    const countryId = header['country-ids'];
    if (!companyId) throw new UnauthorizedException();
    const sql = await this.productsRepository
      .createQueryBuilder('product')
      .where('product.bizId =:companyId', { companyId })
      .andWhere('product.countryId =:countryId', { countryId })
      .andWhere('product.isCombo !=:isCombo', { isCombo: true })
      .andWhere('product.status = 1')
      .select(['product.id', 'product.name', 'product.sku', 'product.prefix', 'product.clientId']);

    if (name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('product.name ILIKE :name', { name: `%${name}%` }).orWhere(
            "CONCAT(product.prefix, '-', product.sku) ILIKE :name",
            {
              name: `%${name}%`,
            },
          );
        }),
      );
    }

    if (extraData && names) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('product.sku IN (:...names)', {
            names,
          }).orWhere("CONCAT(product.prefix, '-', product.sku) IN (:...names)", { names });
        }),
      );
      sql
        .leftJoinAndSelect('product.variants', 'variants')
        .leftJoinAndSelect('variants.properties', 'properties')
        .leftJoinAndSelect('product.attributes', 'attributes')
        .leftJoinAndSelect('attributes.properties', 'attributes_properties');
    }
    return sql.getMany();
  }
  async findAllVariants(
    query: FilterVariant,
    pagination: PaginationOptions,
    request,
    header,
  ): Promise<ProductVariation[]> {
    const { companyId } = request.user;
    const { clientIds, ids, name, names, extraData } = query;
    const countryId = header['country-ids'];
    // console.log(request);
    if (!companyId) throw new UnauthorizedException();
    const sql = this.variantRepository
      .createQueryBuilder('var')
      .leftJoinAndSelect('var.product', 'product');
    if (extraData) {
      sql
        .leftJoinAndSelect('product.combo', 'combo')
        .leftJoinAndSelect('combo.variant', 'variantCombo');
    } else {
      sql
        .leftJoin('product.combo', 'combo')
        .leftJoin('combo.variant', 'variantCombo')
        .select([
          'var.id',
          'var.name',
          'var.sku',
          'var.prefix',
          'product.id',
          'product.name',
          'product.sku',
          'product.prefix',
          'var.is_dangerous',
          'var.clientId',
        ]);
    }

    sql
      .where('product.bizId =:companyId', { companyId })
      .andWhere('product.countryId =:countryId', { countryId });
    // .andWhere('product.isCombo !=:isCombo', { isCombo: true })

    if (clientIds) sql.andWhere('var.clientId IN (:...clientIds)', { clientIds });

    if (ids) sql.andWhere('var.id IN (:...ids)', { ids });

    if (name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('var.sku ILIKE :name', { name: `%${name}%` })
            .orWhere('product.sku ILIKE :name', { name: `%${name}%` })
            .orWhere('variantCombo.sku ILIKE :name', { name: `%${name}%` })
            .orWhere('product.description ILIKE :name', { name: `%${name}%` })
            .orWhere('product.name ILIKE :name', { name: `%${name}%` })
            .orWhere('variantCombo.name ILIKE :name', { name: `%${name}%` })
            .orWhere('var.name ILIKE :name', { name: `%${name}%` })
            .orWhere('variantCombo.sku ILIKE :name', { name: `%${name}%` });
            // .orWhere("CONCAT(var.prefix, '-', var.sku) ILIKE :name", { name: `%${name}%` })
            // .orWhere("CONCAT(variantCombo.prefix, '-', variantCombo.sku) ILIKE :name", {
            //   name: `%${name}%`,
            // })
            // .orWhere("CONCAT(product.prefix, '-', product.sku) ILIKE :name", {
            //   name: `%${name}%`,
            // });
        }),
      );
    }

    if (names) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('var.sku IN (:...names)', { names })
            .orWhere('product.sku IN (:...names)', { names })
            .orWhere('variantCombo.sku IN (:...names)', { names });
            // .orWhere("CONCAT(var.prefix, '-', var.sku) IN (:...names)", { names })
            // .orWhere("CONCAT(variantCombo.prefix, '-', variantCombo.sku) IN (:...names)", { names })
            // .orWhere("CONCAT(product.prefix, '-', product.sku) IN (:...names)", { names });
        }),
      );
    }
    // console.log('🐔  ~ ProductsService ~ findAllVariants ~ sql:', sql.getQueryAndParameters());
    // pagination
    sql.take(pagination?.limit).skip(pagination?.skip);

    return sql.getMany();
  }

  async findVariant(
    request,
    pagination: PaginationOptions,
    query: FilterProduct,
    header,
  ): Promise<[ProductVariation[], number]> {
    let { countryId } = query;

    if (!!header['country-ids']) {
      countryId = header['country-ids'];
    } else {
      throw new BadRequestException();
    }

    let users: Users[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user',
        payload: {
          filters: { type: UserType.customer, countries: countryId },
          pagination,
          header: {},
          request: { user: { companyId: request?.user?.companyId } },
        },
        timeout: 10000,
      });
      users = data as Users[];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    const sql = this.variantRepository.createQueryBuilder('var');

    if (!query?.allStatus) {
      sql.where('var.status = :stt', { stt: ProductStatus.active });
      sql.andWhere('product.status = :stt', { stt: ProductStatus.active });
    }

    sql
      .leftJoinAndSelect('var.product', 'product')
      .leftJoinAndSelect('var.properties', 'properties')
      .leftJoinAndSelect('properties.attributes', 'attributes');

    if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      sql.andWhere('(product.client_id = :creatorId OR product.creator_id = :creatorId)', {
        creatorId: request?.user?.id,
      });
    }

    if (!query?.getAll) sql.andWhere('product.isCombo != :isCombo', { isCombo: true });

    sql.andWhere({
      bizId: request?.user?.companyId,
    });
    sql.andWhere({
      countryId,
    });

    if (query?.name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('var.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.description ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.name ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('var.name ILIKE :name', { name: `%${query?.name}%` });
            // .orWhere("CONCAT(var.prefix, '-', var.sku) ILIKE :name", { name: `%${query?.name}%` })
            // .orWhere("CONCAT(product.prefix, '-', product.sku) ILIKE :name", {
            //   name: `%${query?.name}%`,
            // });
        }),
      );
    }

    if (!!query?.clientId)
      sql.andWhere('product.clientId = :clientId', { clientId: query?.clientId });

    if (!!query?.names) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('var.sku in (:...names)', { names: query?.names })
            // .orWhere("CONCAT(var.prefix, '-', var.sku) in (:...names)", { names: query?.names })
            .orWhere('product.name in (:...names)', { names: query?.names })
            .orWhere('product.sku in (:...names)', { names: query?.names });
            // .orWhere("CONCAT(product.prefix, '-', product.sku) in (:...names)", {
            //   names: query?.names,
            // });
        }),
      );
    }

    if (!!query?.clientIds) {
      sql.andWhere('product.clientId in (:...clientIds)', { clientIds: query?.clientIds });
    }

    sql.andWhere('product.clientId in (:...cIds)', { cIds: users?.map((item: Users) => item?.id) });

    if (query.variantIds) sql.andWhereInIds(query.variantIds);
    if (!!query.productIds) {
      sql.andWhere('product.id in (:...pIds)', {
        pIds: query.productIds.split(',')?.map(item => {
          return item;
        }),
      });
    } else {
      sql.skip(pagination?.skip).take(pagination?.limit);
    }

    sql.orderBy('var.createdAt', 'DESC');
    // console.log(sql.getQueryAndParameters());
    return sql.getManyAndCount();
  }

  async searchVariant(
    request,
    pagination: PaginationOptions,
    query: FilterProduct,
    header,
  ): Promise<[ProductVariation[], number]> {
    const { clientId, warehouseId } = query;
    if (!clientId || !warehouseId)
      throw new BadRequestException('WarehouseId, ClientId is required');

    let { countryId } = query;

    if (!!header['country-ids']) {
      countryId = header['country-ids'];
    } else {
      throw new BadRequestException();
    }

    let users: Users[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user',
        payload: {
          filters: { type: UserType.customer, countries: countryId },
          pagination,
          header: {},
          request: { user: { companyId: request?.user?.companyId } },
        },
        timeout: 10000,
      });
      users = data as Users[];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    // const subQuery = this.stRepository
    //   .createQueryBuilder('si')
    //   .select([
    //     'si.variant_id as id',
    //     'SUM(si.stocked_phy_sellable) as stock'
    //   ])
    //   .andWhere(`si.warehouse_id = ${warehouseId}`)
    //   .groupBy('si.variant_id');

    const sql = this.variantRepository.createQueryBuilder('var');
    sql.addSelect(
      'CASE WHEN "siJoin".stocked_phy_sellable > 0 THEN "siJoin".stocked_phy_sellable ELSE 0 END as stock',
    );
    sql.where('var.status = :stt', { stt: ProductStatus.active });
    sql.andWhere('product.status = :stt', { stt: ProductStatus.active });
    sql
      .leftJoinAndSelect('var.product', 'product')
      .leftJoinAndSelect('var.properties', 'properties')
      .leftJoinAndSelect('properties.attributes', 'attributes')
      .leftJoinAndSelect('product.combo', 'combo')
      .leftJoinAndSelect('combo.variant', 'variant')
      .leftJoinAndSelect('variant.product', 'products')
      .leftJoinAndSelect('variant.properties', 'pp')
      .leftJoinAndSelect('pp.attributes', 'attr');

    sql.andWhere({
      bizId: request?.user?.companyId,
    });

    sql.andWhere({
      countryId,
    });

    sql.andWhere('product.clientId in (:...clientIds)', {
      clientIds: users?.map((item: Users) => item?.id),
    });

    if (query?.isNormal) sql.andWhere('product.isCombo != :isCombo', { isCombo: true });

    if (query?.name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('var.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.name ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('var.name ILIKE :name', { name: `%${query?.name}%` });
            // .orWhere("CONCAT(var.prefix, '-', var.sku) ILIKE :name", { name: `%${query?.name}%` })
            // .orWhere("CONCAT(product.prefix, '-', product.sku) ILIKE :name", {
            //   name: `%${query?.name}%`,
            // });
        }),
      );
    }

    sql.andWhere('product.clientId = :clientId', { clientId: query?.clientId });

    if (query?.names) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('var.sku in (:...names)', { names: query?.names })
            // .orWhere("CONCAT(var.prefix, '-', var.sku) in (:...names)", { names: query?.names })
            .orWhere('product.name in (:...names)', { names: query?.names })
            .orWhere('product.sku in (:...names)', { names: query?.names });
            // .orWhere("CONCAT(product.prefix, '-', product.sku) in (:...names)", {
            //   names: query?.names,
            // });
        }),
      );
    }

    if (query.variantIds) sql.andWhereInIds(query.variantIds);
    if (!!query.productIds) {
      sql.andWhere('product.id in (:...pIds)', {
        pIds: query.productIds.split(',')?.map(item => {
          return item;
        }),
      });
    } else {
      sql.skip(pagination?.skip).take(pagination?.limit);
    }

    sql.orderBy('var.createdAt', 'DESC');
    sql.addOrderBy('var.productId', 'DESC');

    // sql.leftJoin(
    //   '(' + subQuery.getQuery() + ')',
    //   'siJoin',
    //   '"var".id = "siJoin".id',
    // )
    sql.leftJoin(
      InventoryManagement,
      'siJoin',
      '("var".id = "siJoin".variant_id AND siJoin.warehouse_id = :warehouseId)',
      { warehouseId },
    );
    // .andWhere(`siJoin.warehouse_id = ${warehouseId}`);

    const data = await sql.getManyAndCount();

    if (data?.[0]?.length > 0 && !query?.isNormal) {
      const ids = [];
      data?.[0]?.forEach((item: ProductVariation) => {
        if (item?.product?.isCombo) ids.push(item?.product?.id);
      });
      if (ids?.length > 0) {
        console.log('🐔  ~ ProductsService ~ ids:', ids);
        const mQuery = this.pcvRepository.createQueryBuilder('pcv');
        mQuery.select(
          'CASE WHEN "si".stocked_phy_sellable > 0 THEN "si".stocked_phy_sellable ELSE 0 END as stock',
        );
        mQuery.addSelect('pcv.qty as qty');
        mQuery.addSelect('pcv.id_product as id');
        mQuery.addSelect('pcv.id_product_variation as variant');
        mQuery.leftJoin(
          InventoryManagement,
          'si',
          '("pcv".id_product_variation = "si".variant_id AND si.warehouse_id = :warehouseId)',
          { warehouseId },
        );
        mQuery.where('pcv.id_product in (:...ids)', { ids: compact(ids) });
        mQuery.andWhere('pcv.status = :stt', { stt: ProductStatus.active });
        const inventoryCombo = await mQuery.getRawMany();
        // console.log('🐔  ~ ProductsService ~ inventoryCombo:', inventoryCombo);
        if (ids?.length > 0) {
          const res = data?.[0]?.map((item: ProductVariation) => {
            if (item?.product?.isCombo) {
              const itemInventory = [];
              inventoryCombo?.forEach((val: any) => {
                if (val?.id == item?.productId) {
                  itemInventory.push(val?.stock / val?.qty);
                }
              });
              item.stock = itemInventory?.includes(0) ? 0 : floor(min(compact(itemInventory)));
            }
            return item;
          });
          data[0] = res;
        }
      }
    }

    return data;
  }

  async findOne(id, header, request): Promise<Product> {
    const countryId = header['country-ids'];
    const cate = await this.productsRepository.findOne(id, {
      where: qb => {
        qb.where({
          status: Not(ProductStatus.delete),
        }).andWhere({ countryId });

        qb.andWhere({
          bizId: request?.user?.companyId,
        });

        if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        }
      },
      relations: [
        'variants',
        'variants.properties',
        'category',
        'attributes',
        'attributes.properties',
      ],
    });

    if (cate?.attributes) {
      cate.attributes = cate.attributes.filter(
        attribute => attribute.status === AttributeStatus.active,
      );

      cate.attributes.forEach(attribute => {
        attribute.properties = attribute.properties.filter(
          property => property.status === AttributeStatus.active,
        );
      });
    }
    return cate;
  }

  async findCombo(id, header, request): Promise<Product> {
    const countryId = header['country-ids'];
    const cate = await this.productsRepository.findOne(id, {
      where: qb => {
        qb.where({
          status: Not(ProductStatus.delete),
        })
          // .andWhere('Product__variants.status = :stt', { stt: ProductStatus.active })
          // .andWhere('Product__combo.status != :sttDelete', {
          //   sttDelete: ProductStatus.delete,
          // })
          .andWhere({ countryId });

        if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        }

        qb.andWhere({
          bizId: request?.user?.companyId,
        });
      },
      relations: [
        'variants',
        'variants.properties',
        'combo',
        'category',
        // 'warehouses',
        'combo.variant',
        'combo.variant.properties',
        'combo.variant.properties.attributes',
        'combo.variant.product',
      ],
    });
    return cate;
  }

  async findAttributes(id): Promise<Attributes[]> {
    const pp = await this.avRepository
      .createQueryBuilder('av')
      .select('av.attributes_id', 'id')
      .leftJoin('av.variations', 'v')
      .where('v.productId = :productId', { productId: id })
      .getRawMany();

    if (!pp || pp.length < 1) return [];

    return await this.attrRepository
      .createQueryBuilder('a')
      .leftJoinAndSelect('a.properties', 'p')
      .where('a.status = :stt', { stt: ProductStatus.active })
      .andWhere('p.status = :stt', { stt: ProductStatus.active })
      .andWhere('a.id IN (:...attrIds)', {
        attrIds: pp.map(item => {
          return item?.id;
        }),
      })
      .getMany();
  }

  async create(data: ProductsDto, id: string, request, header): Promise<Product> {
    const countryId = header['country-ids'];
    // attributes
    let attributes = [],
      savedAtts = [];
    if (data?.attributes && data?.attributes.length > 0) {
      let attParams = [];
      for (const att of data?.attributes) {
        let cate = new Attributes();
        cate.name = att.name;
        cate.status = AttributeStatus.active;
        cate.bizId = request?.user?.companyId;
        cate.creatorId = request?.user?.id;

        att?.data?.forEach(e => {
          let val = new AttributesValue();
          val.attributes = cate;
          val.status = AttributeStatus.active;
          if (e?.name) {
            val.name = e?.name ?? '';
            attParams.push(val);
          }
        });
        if (attParams.length <= 0)
          throw new BadRequestException('Giá trị của thuộc tính không để trống');
      }
      savedAtts = await this.avRepository.save(attParams);

      const attIdArray = uniq(
        savedAtts.map(item => {
          return item?.attributesId;
        }),
      );

      attributes = await this.attrRepository.find({
        where: {
          id: In(attIdArray),
        },
      });
    }

    // variant
    const product = {
      ...data,
      bizId: request?.user?.companyId,
      countryId,
      attributes,
    };
    product.creatorId = id;
    product.lastEditorId = id;

    const params = [];
    data?.variations.forEach(e => {
      if (e?.sku) {
        const variant = {
          ...plainToClass(VariantDto, e),
          bizId: request?.user?.companyId,
          clientId: data?.clientId,
          countryId,
        };

        if (savedAtts?.length > 0) {
          e?.properties.forEach((property: any) => {
            const matchingAttribute = savedAtts.find(
              savedAtt =>
                savedAtt.attributes.name === property.name &&
                savedAtt.name === property.data?.[0]?.name,
            );

            if (matchingAttribute) {
              property.id = matchingAttribute.id;
            }
          });
        }
        variant.properties = e?.properties;
        variant.product = product;
        variant.creatorId = id;
        variant.lastEditorId = id;
        params.push(variant);
      }
    });
    if (params.length <= 0) throw new BadRequestException('Biến thể của sản phẩm không để trống');
    const variant = await this.variantRepository.save(params).catch(err => {
      if (err?.driverError) {
        let _sku = split(err?.driverError?.detail, '=(', 2);
        _sku = split(_sku[1], ',', 1);
        throw new BadRequestException(
          err?.driverError?.constraint == 'UQ_SKU_PRODUCT'
            ? { code: 'PD_0004', sku: _sku[0] ?? '' }
            : err?.driverError?.constraint == 'UQ_SKU_VARIANT'
            ? { code: 'PD_0005', sku: _sku[0] ?? '' }
            : err?.driverError?.detail,
        );
      }
      return err;
    });

    console.log('🐔  ~ ProductsService ~ create ~ variant:', variant);

    const newAttributes = await this.attrRepository.find({
      where: {
        product: { id: variant[0]?.productId },
        status: AttributeStatus.active,
      },
      relations: ['properties'],
    });

    const logs: Logs[] = [];
    // added
    newAttributes.forEach(attribute => {
      const listProperties =
        attribute.properties
          .filter(x => x.status == AttributeStatus.active)
          .map(x => String(x.name)) ?? [];
      const afterChangesNew = [];
      afterChangesNew.push(attribute.name, listProperties.join(', '));
      if (!isEmpty(listProperties)) {
        logs.push({
          action: 'Added Properties',
          changes: afterChangesNew,
          afterChanges: afterChangesNew,
          beforeChanges: [],
          tableName: 'product',
          parentTableName: null,
          recordId: variant[0]?.productId,
          creatorId: request?.user?.id,
          parentId: null,
          childrenId: null,
        });
      }
    });

    // added variant
    variant.forEach(item => {
      if (!item?.properties) item.properties = [];
      const afterChanges =
        item?.properties.flatMap(x => {
          if (x.data && Array.isArray(x.data) && x.data.length > 0) {
            return x.data.map(d => String(d.name));
          }
          return [String(x.name)];
        }) ?? [];
      if (!isEmpty(afterChanges)) {
        logs.push({
          action: 'Added Variant Properties',
          changes: afterChanges,
          afterChanges: afterChanges,
          beforeChanges: [],
          tableName: 'product_variation',
          parentTableName: 'product',
          recordId: item?.id.toString(),
          creatorId: request?.user?.id,
          parentId: item?.productId.toString(),
          childrenId: null,
        });
      }
    });
    logs.push({
      action: 'Created Product',
      changes: [],
      afterChanges: [],
      beforeChanges: [],
      tableName: 'product',
      parentTableName: null,
      recordId: variant[0]?.productId,
      creatorId: request?.user?.id,
      parentId: null,
      childrenId: null,
    } as Logs);

    await this.logRepository.save(logs).catch(err => {
      if (err?.driverError) console.log(err?.driverError?.detail);
      return err;
    });

    if (data.categoryId) {
      await this.amqpConnection.publish('identity-service-roles', 'add-client-category', {
        id: data?.clientId,
        categoryId: data?.categoryId,
      });
    }

    return plainToClass(Product, product);
  }

  async createCombo(data: ProductComboDto, id: string, request, header): Promise<Product> {
    const countryId = header['country-ids'];
    const prod = {
      ...data,
      bizId: request?.user?.companyId,
      countryId,
    };

    prod.isCombo = true;
    prod.creatorId = id;
    prod.lastEditorId = id;

    if (data?.isCombo && data?.productCombo && data?.productCombo.length > 0)
      prod.combo = plainToInstance(
        ProductComboVariant,
        data?.productCombo.map(item => {
          return {
            ...item,
          };
        }),
      );

    const params = [];
    data?.variations.forEach(e => {
      const variant = {
        ...plainToClass(VariantDto, e),
        bizId: request?.user?.companyId,
        clientId: data?.clientId,
        countryId,
      };
      variant.name = prod.name;
      variant.sku = prod?.sku;
      variant.weight = data?.weight;
      variant.product = prod;

      variant.creatorId = id;
      variant.lastEditorId = id;

      params.push(variant);
    });
    if (params.length <= 0) throw new BadRequestException('Product cannot be empty in combo');

    const variant = await this.variantRepository.save(params).catch(err => {
      if (err?.driverError) {
        let _sku = split(err?.driverError?.detail, '=(', 2);
        _sku = split(_sku[1], ',', 1);
        throw new BadRequestException(
          err?.driverError?.constraint == 'UQ_SKU_PRODUCT'
            ? { code: 'PD_0004', sku: _sku[0] ?? '' }
            : err?.driverError?.constraint == 'UQ_SKU_VARIANT'
            ? { code: 'PD_0005', sku: _sku[0] ?? '' }
            : err?.driverError?.detail,
        );
      }
      return err;
    });

    await this.logRepository
      .save({
        action: 'Created Combo',
        changes: [],
        afterChanges: [],
        beforeChanges: [],
        tableName: 'product',
        parentTableName: null,
        recordId: variant[0]?.productId,
        creatorId: request?.user?.id,
        parentId: null,
        childrenId: null,
      } as Logs)
      .catch(err => {
        if (err?.driverError) console.log(err?.driverError?.detail);
        return err;
      });

    return plainToClass(Product, prod);
  }

  async update(data: ProductsDto, id, request, header): Promise<Product> {
    const queryRunner = this.productsRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const logs: Logs[] = [];
      if (data?.remove && data?.remove?.length > 0) {
        for (const item of data?.remove as { variantId: number }[]) {
          const deletedVariant = await this.removeVariant(
            item?.variantId,
            request,
            queryRunner.manager,
          );
          if (!deletedVariant.properties) deletedVariant.properties = [];
          const afterChanges =
            deletedVariant?.properties
              .filter(x => x.status == AttributeStatus.active)
              .map(x => String(x.name)) ?? [];
          if (!isEmpty(afterChanges)) {
            logs.push({
              action: 'Removed Variant Properties',
              changes: afterChanges,
              afterChanges: [],
              beforeChanges: [],
              tableName: 'product_variation',
              parentTableName: 'product',
              recordId: deletedVariant?.id.toString(),
              creatorId: request?.user?.id,
              parentId: deletedVariant?.productId.toString(),
              childrenId: null,
            });
          }
        }
      }

      const countryId = header['country-ids'];
      const product = await queryRunner.manager.findOne(Product, id, {
        where: qb => {
          qb.where({
            status: Not(ProductStatus.delete),
            bizId: request?.user?.companyId,
          });

          if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
            qb.andWhere([
              {
                creatorId: request?.user?.id,
              },
              {
                clientId: request?.user?.id,
              },
            ]);
          }
        },
        relations: ['variants', 'variants.properties'],
      });

      const oldVariants = cloneDeep(product.variants);

      const oldAttributes = await queryRunner.manager.find(Attributes, {
        where: {
          product: { id },
          status: AttributeStatus.active,
        },
        relations: ['properties'],
      });

      // attributes
      let attributes = [],
        savedAtts = [];
      if (data?.attributes && data?.attributes.length > 0) {
        let attParams = [];
        for (const att of data?.attributes) {
          let cate;
          if (att?.id) {
            cate = await queryRunner.manager.findOne(Attributes, att?.id);
          } else {
            cate = new Attributes();
            cate.bizId = request?.user?.companyId;
            cate.creatorId = request?.user?.id;
          }
          cate.name = att.name;
          cate.status = AttributeStatus.active;

          await Promise.all(
            att?.data?.map(async e => {
              let val;
              if (e?.id) {
                val = await queryRunner.manager.findOne(AttributesValue, e?.id);
              } else {
                val = new AttributesValue();
              }
              val.attributes = cate;
              val.status = AttributeStatus.active;
              if (e?.name) {
                val.name = e?.name ?? '';
                attParams.push(val);
              }
            }),
          );
          if (attParams.length <= 0)
            throw new BadRequestException('Giá trị của thuộc tính không để trống');
        }

        // Disable all attributes and their properties for the given product
        const attributesToDisable = await queryRunner.manager
          .createQueryBuilder(Attributes, 'attributes')
          .leftJoin('attributes.properties', 'properties')
          .addSelect(['properties.id', 'properties.status'])
          .where('attributes.productId = :productId', { productId: product.id })
          .getMany();

        for (const attribute of attributesToDisable) {
          attribute.status = AttributeStatus.disable;
          for (const property of attribute.properties) {
            property.status = AttributeStatus.disable;
          }
          await queryRunner.manager.save(AttributesValue, attribute.properties);
        }

        await queryRunner.manager.save(Attributes, attributesToDisable);

        // save new attributes or update
        savedAtts = await queryRunner.manager.save(AttributesValue, attParams);

        const attIdArray = uniq(
          savedAtts.map(item => {
            return item?.attributesId;
          }),
        );

        attributes = await queryRunner.manager.find(Attributes, {
          where: {
            id: In(attIdArray),
          },
        });
      }

      // variant
      const prod = {
        ...product,
        ...data,
        bizId: request?.user?.companyId,
        attributes,
        status: data?.status != product?.status ? data?.status : product?.status,
      };

      prod.lastEditorId = request?.user?.id;
      prod.id = id;

      delete prod.createdAt;
      delete prod.updatedAt;

      const params: any = cloneDeep(product.variants);
      data?.variations.forEach(e => {
        if (e?.sku) {
          const variant = {
            ...plainToInstance(VariantDto, e),
            bizId: request?.user?.companyId,
            clientId: data?.clientId,
            countryId,
          };

          if (savedAtts?.length > 0) {
            e?.properties.forEach((property: any) => {
              if (!property?.id) {
                const matchingAttribute = savedAtts.find(
                  savedAtt =>
                    savedAtt.attributes.name === property.name &&
                    savedAtt.name === property.data?.[0]?.name,
                );

                if (matchingAttribute) {
                  property.id = matchingAttribute.id;
                }
              }
            });
          }

          variant.status =
            data?.status != product?.status ? data?.status : e?.status ?? ProductStatus.active;
          if (data?.status == ProductStatus.disable) variant.status = ProductStatus.disable;
          variant.product = prod;
          variant.priceRetail = e?.priceRetail ?? 0;
          variant.properties = e?.properties;

          if (e?.id) {
            variant.sku = e?.sku;
            variant.id = e?.id;
            variant.lastEditorId = request?.user?.id;

            const keyVariant = findIndex(params, { id: e?.id });
            if (!!params[keyVariant]) params[keyVariant] = variant;
          } else {
            variant.creatorId = request?.user?.id;
            variant.lastEditorId = request?.user?.id;
            params.push(variant);
          }
        }
      });

      if (params.length <= 0)
        throw new BadRequestException('Biến thể của sản phẩm không được để trống');

      const update = await queryRunner.manager.save(ProductVariation, params).catch(err => {
        if (err?.driverError) {
          let _sku = split(err?.driverError?.detail, '=(', 2);
          _sku = split(_sku[1], ',', 1);
          throw new BadRequestException(
            err?.driverError?.constraint == 'UQ_SKU_PRODUCT'
              ? { code: 'PD_0004', sku: _sku[0] ?? '' }
              : err?.driverError?.constraint == 'UQ_SKU_VARIANT'
              ? { code: 'PD_0005', sku: _sku[0] ?? '' }
              : err?.driverError?.detail,
          );
        }
        return err;
      });

      const newAttributes = await queryRunner.manager.find(Attributes, {
        where: {
          product: { id },
          status: AttributeStatus.active,
        },
        relations: ['properties'],
      });

      // added
      const addedAttributes = differenceBy(newAttributes, oldAttributes, 'id');
      addedAttributes.forEach(attribute => {
        const listProperties =
          attribute.properties
            .filter(x => x.status == AttributeStatus.active)
            .map(x => String(x.name)) ?? [];
        const afterChangesNew = [];
        afterChangesNew.push(attribute.name, listProperties.join(', '));
        if (!isEmpty(listProperties)) {
          logs.push({
            action: 'Added Properties',
            changes: afterChangesNew,
            afterChanges: afterChangesNew,
            beforeChanges: [],
            tableName: 'product',
            parentTableName: null,
            recordId: id,
            creatorId: request?.user?.id,
            parentId: null,
            childrenId: null,
          });
        }
      });
      // changed
      for (const item of oldAttributes) {
        const changeAttributes = newAttributes.find(x => x.id == item.id);
        if (changeAttributes) {
          const beforeChangesList =
            item.properties
              .filter(x => x.status == AttributeStatus.active)
              .map(x => String(x.name)) ?? [];
          const beforeChanges = [];
          beforeChanges.push(item.name, beforeChangesList.join(', '));
          const afterChangesList =
            changeAttributes.properties
              .filter(x => x.status == AttributeStatus.active)
              .map(x => String(x.name)) ?? [];
          const afterChanges = [];
          afterChanges.push(changeAttributes.name, afterChangesList.join(', '));
          const changes =
            beforeChangesList.length > afterChangesList.length
              ? beforeChangesList.filter(item => !afterChangesList.includes(item))
              : afterChangesList.filter(item => !beforeChangesList.includes(item));
          if (!isEmpty(changes) || item.name != changeAttributes.name) {
            logs.push({
              action: 'Changed Properties',
              changes: afterChanges,
              afterChanges,
              beforeChanges,
              tableName: 'product',
              parentTableName: null,
              recordId: id,
              creatorId: request?.user?.id,
              parentId: null,
              childrenId: null,
            });
          }
        } else {
          // removed
          const listProperties =
            item.properties
              .filter(x => x.status == AttributeStatus.active)
              .map(x => String(x.name)) ?? [];
          const removedAttribute = [];
          removedAttribute.push(item.name, listProperties.join(', '));
          if (!isEmpty(listProperties)) {
            logs.push({
              action: 'Removed Properties',
              changes: removedAttribute,
              afterChanges: [],
              beforeChanges: [],
              tableName: 'product',
              parentTableName: null,
              recordId: id,
              creatorId: request?.user?.id,
              parentId: null,
              childrenId: null,
            });
          }
        }
      }
      if (update) {
        // added variant
        const addedVariants: any = differenceBy(update, oldVariants, 'id');
        addedVariants.forEach(item => {
          if (!item?.properties) item.properties = [];
          const afterChanges =
            item?.properties.flatMap(x => {
              if (x.data && Array.isArray(x.data) && x.data.length > 0) {
                return x.data.map(d => String(d.name));
              }
              return [String(x.name)];
            }) ?? [];
          if (!isEmpty(afterChanges)) {
            logs.push({
              action: 'Added Variant Properties',
              changes: afterChanges,
              afterChanges: afterChanges,
              beforeChanges: [],
              tableName: 'product_variation',
              parentTableName: 'product',
              recordId: item?.id.toString(),
              creatorId: request?.user?.id,
              parentId: item?.productId.toString(),
              childrenId: null,
            });
          }
        });
        // change variant properties
        for (const item of oldVariants) {
          const changeVariation = update.find(x => x.id == item.id);
          const beforeChanges =
            item.properties
              .filter(x => x.status == AttributeStatus.active)
              .map(x => String(x.name)) ?? [];
          const afterChanges =
            changeVariation.properties.flatMap(x => {
              if (x.data && Array.isArray(x.data) && x.data.length > 0) {
                return x.data.map(d => String(d.name));
              }
              return [String(x.name)];
            }) ?? [];
          const changes =
            beforeChanges.length > afterChanges.length
              ? beforeChanges.filter(item => !afterChanges.includes(item))
              : afterChanges.filter(item => !beforeChanges.includes(item));
          if (!isEmpty(changes)) {
            logs.push({
              action: 'Changed Variant Properties',
              changes: afterChanges,
              afterChanges,
              beforeChanges,
              tableName: 'product_variation',
              parentTableName: 'product',
              recordId: item?.id.toString(),
              creatorId: request?.user?.id,
              parentId: item?.productId.toString(),
              childrenId: null,
            });
          }
        }
      }

      if (!isEmpty(logs)) {
        await queryRunner.manager.save(Logs, logs).catch(err => {
          if (err?.driverError) console.log(err?.driverError?.detail);
          return err;
        });
      }

      if (data.categoryId != product?.categoryId) {
        await this.amqpConnection.publish('identity-service-roles', 'add-client-category', {
          id: data?.clientId,
          categoryId: data?.categoryId,
        });
      }

      await queryRunner.commitTransaction();
      return plainToClass(Product, product);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateStatus(data: ProductUpdateStatusDto, id, request): Promise<Product> {
    const product = await this.productsRepository.findOne(id, {
      where: qb => {
        qb.where({
          status: Not(ProductStatus.delete),
          bizId: request?.user?.companyId,
        });
        if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        }
      },
      relations: ['variants'],
      // relations: ['variants', 'combo'],
    });

    if (!data) throw new BadRequestException('Not found product');

    if (product.variants && product.variants.length > 0) {
      const variantIds = product.variants.map(item => item.id);
      await this.variantRepository.update(
        { id: In(variantIds) },
        {
          status: data?.status,
          lastEditorId: request?.user?.id,
        },
      );
    }

    // if (product.combo && product.combo.length > 0) {
    //   const comboVariantIds = product.combo.map(item => item.variantId);
    //   await this.pcvRepository.update(
    //     { variantId: In(comboVariantIds) },
    //     {
    //       status: data?.status,
    //       lastEditorId: request?.user?.id,
    //     },
    //   );
    // }

    await this.productsRepository.update(id, {
      status: data?.status,
      lastEditorId: request?.user?.id,
    });

    return product;
  }

  async updateCombo(data: ProductComboDto, id, request): Promise<Product> {
    const queryRunner = this.productsRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (data?.remove && data?.remove.length > 0) {
        for (const item of data?.remove) {
          await this.deleteProductCombo(item, request, queryRunner.manager);
        }
      }

      const product = await queryRunner.manager.findOne(Product, id, {
        where: qb => {
          qb.where({
            status: Not(ProductStatus.delete),
            bizId: request?.user?.companyId,
          });
          if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
            qb.andWhere([
              {
                creatorId: request?.user?.id,
              },
              {
                clientId: request?.user?.id,
              },
            ]);
          }
        },
        relations: [
          'variants',
          'variants.properties',
          'combo',
          'combo.variant',
          'combo.variant.properties',
          'combo.variant.properties.attributes',
          'combo.variant.product',
        ],
      });

      const lookUpVariant = [];
      product?.combo.forEach(el => {
        if (!lookUpVariant[el?.variantId]) lookUpVariant[el.variantId] = el;
      });

      const prod = {
        ...product,
        ...data,
        bizId: request?.user?.companyId,
        // status: data?.status != product?.status ? data?.status : product?.status,
      };

      prod.lastEditorId = request?.user?.id;
      prod.id = id;

      delete prod.createdAt;
      delete prod.updatedAt;

      if (!!prod?.variants[0]) {
        const combo = [],
          logData = [];
        if (prod.isCombo && data?.productCombo && data?.productCombo.length > 0) {
          data?.productCombo.forEach(e => {
            const pcv = new ProductComboVariant();
            pcv.productId = id;
            pcv.qty = e?.qty;
            pcv.price = e?.variant?.price ?? 0;
            pcv.status = e?.status ?? ProductStatus.active;
            // pcv.status =
            //   data?.status != product?.status ? data?.status : e?.status ?? ProductStatus.active;
            // if (data?.status == ProductStatus.disable) pcv.status = ProductStatus.disable;
            pcv.variantId = e?.variant?.id;

            pcv.creatorId = request?.user?.id;
            pcv.lastEditorId = request?.user?.id;

            combo.push(pcv);
            const variantDetail = lookUpVariant[e?.variant?.id];

            if (
              !variantDetail ||
              variantDetail?.qty != e?.qty ||
              variantDetail?.status != e?.status
            ) {
              logData.push({
                action: !variantDetail ? 'Added Product in combo' : 'Changed Product in combo',
                changes: [
                  'Product Name',
                  e?.variant?.name,
                  'SKU',
                  `${e?.variant?.sku}`,
                  'Attributes',
                  e?.variant?.properties
                    .map(i => {
                      if (i.status.toString() == 'active')
                        return `${i?.attributes?.name}: ${i?.name}`;
                    })
                    ?.join(','),
                  'Quantity',
                  pcv.qty,
                  'Selling Price',
                  pcv?.price,
                  'Status',
                  pcv.status === 1 ? 'Active' : 'Inactive',
                ],
                afterChanges: [
                  'Product Name',
                  e?.variant?.name,
                  'SKU',
                  e?.variant?.sku,
                  'Attributes',
                  e?.variant?.properties
                    .map(i => {
                      if (i.status.toString() == 'active')
                        return `${i?.attributes?.name}: ${i?.name}`;
                    })
                    ?.join(','),
                  'Quantity',
                  pcv.qty,
                  'Selling Price',
                  pcv?.price,
                  'Status',
                  pcv.status === 1 ? 'Active' : 'Inactive',
                ],
                beforeChanges: !variantDetail
                  ? []
                  : [
                      'Product Name',
                      `${variantDetail?.variant?.product?.name} (${variantDetail?.variant?.product?.prefix}-${variantDetail?.variant?.product?.sku})`,
                      'SKU',
                      `${variantDetail?.variant?.product?.prefix}-${variantDetail?.variant?.product?.sku}`,
                      'Attributes',
                      variantDetail?.variant?.properties
                        .map(i => {
                          if (i.status == 1) return `${i?.attributes?.name}: ${i?.name}`;
                        })
                        ?.join(','),
                      'Quantity',
                      variantDetail?.qty,
                      'Selling Price',
                      variantDetail?.price,
                      'Status',
                      variantDetail?.status === 1 ? 'Active' : 'Inactive',
                    ],
                tableName: 'product',
                parentTableName: null,
                recordId: id,
                creatorId: request?.user?.id,
                parentId: null,
                childrenId: null,
              });
            }
          });
        }

        prod.combo = combo;
        prod.lastEditorId = request?.user?.id;

        const variation = {
          ...prod?.variants[0],
          name: data?.name,
          priceRetail: data?.price,
          weight: data?.weight,
          bizId: request?.user?.companyId,
          clientId: data?.clientId,
          sku: data?.sku,
          lastEditorId: request?.user?.id,
        };

        await queryRunner.manager.save(ProductVariation, variation).catch(err => {
          if (err?.driverError) {
            let _sku = split(err?.driverError?.detail, '=(', 2);
            _sku = split(_sku[1], ',', 1);
            throw new BadRequestException(
              err?.driverError?.constraint == 'UQ_SKU_PRODUCT'
                ? { code: 'PD_0004', sku: _sku[0] ?? '' }
                : err?.driverError?.constraint == 'UQ_SKU_VARIANT'
                ? { code: 'PD_0005', sku: _sku[0] ?? '' }
                : err?.driverError?.detail,
            );
          }
          return err;
        });

        await queryRunner.manager.save(Product, prod).catch(err => {
          let _sku = split(err?.driverError?.detail, '=(', 2);
          _sku = split(_sku[1], ',', 1);
          if (err?.driverError)
            throw new BadRequestException(
              err?.driverError?.constraint == 'UQ_SKU_PRODUCT'
                ? { code: 'PD_0004', sku: _sku[0] ?? '' }
                : err?.driverError?.constraint == 'UQ_SKU_VARIANT'
                ? { code: 'PD_0005', sku: _sku[0] ?? '' }
                : err?.driverError?.detail,
            );
          return err;
        });
        console.log('logs', logData);

        if (logData?.length > 0) {
          await queryRunner.manager.save(Logs, logData).catch(err => {
            if (err?.driverError) console.log(err?.driverError?.detail);
            return err;
          });
        }
      }

      await queryRunner.commitTransaction();
      return plainToClass(Product, prod);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateAttribute(productId: number, request) {
    const product = await this.productsRepository.findOne(productId, {
      where: qb => {
        qb.where({
          status: Not(ProductStatus.delete),
          bizId: request?.user?.companyId,
        });

        if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        }
      },
      relations: ['variants', 'variants.properties', 'attributes', 'attributes.properties'],
    });

    if (!product) throw new BadRequestException('Not found product');

    const attIds = [];
    if (!isEmpty(product?.attributes)) {
      for (const attribute of product.attributes) {
        if (attribute.status === AttributeStatus.active) {
          attIds.push(attribute.id);
        }
      }
    }

    for (const variant of product.variants) {
      for (const property of variant.properties) {
        if (attIds.includes(property.attributesId)) {
          continue;
        }
        // Nếu property.attributesId không nằm trong attIds thì clone attribute và properties
        const attNeedClone = await this.attrRepository.findOne({
          where: {
            id: property.attributesId,
          },
          relations: ['properties'],
        });
        if (attNeedClone) {
          let savedAttribute;
          // Clone attribute
          const existedAttribute = await this.attrRepository.findOne({
            where: {
              name: attNeedClone.name,
              product: { id: product.id },
              status: AttributeStatus.active,
            },
          });
          if (existedAttribute) {
            savedAttribute = existedAttribute;
          } else {
            const newAttribute = new Attributes();
            newAttribute.bizId = attNeedClone.bizId;
            newAttribute.creatorId = attNeedClone.creatorId;
            newAttribute.name = attNeedClone.name;
            newAttribute.status = AttributeStatus.active;
            newAttribute.product = product; // gán vào product hiện tại nếu cần

            // Lưu attribute mới
            savedAttribute = await this.attrRepository.save(newAttribute);
          }

          // Clone property
          const newProp = new AttributesValue();
          newProp.name = property.name;
          newProp.status = property.status;
          newProp.attributes = savedAttribute;
          const savedProperties = await this.avRepository.save(newProp);

          // Gán lại property mới vào variant
          property.attributesId = savedAttribute.id;
          // property.attributes = savedAttribute;
          property.id = savedProperties?.id; // gán id property mới (nếu cần)
          property.name = savedProperties?.name;
        }
      }
      // Lưu lại variant với property mới
      await this.variantRepository.save(variant);
    }
    return { success: true };
  }

  async delete(id, request): Promise<Product> {
    const data = await this.productsRepository.findOne({
      where: qb => {
        qb.where({
          id,
          bizId: request?.user?.companyId,
        });

        if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        }
      },
      relations: ['variants'],
    });
    if (!data) throw new BadRequestException('Không tìm thấy sản phẩm này');
    if (!!data) data.status = ProductStatus.disable;
    data.lastEditorId = request?.user?.id;

    await this.productsRepository.save(data);
    return data;
  }

  async hideVariant(id, request) {
    const data = await this.variantRepository.findOne({
      where: qb => {
        qb.where({
          id,
          bizId: request?.user?.companyId,
        });

        if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere(
            '(ProductVariation__product.client_id = :creatorId OR ProductVariation__product.creator_id = :creatorId)',
            {
              creatorId: request?.user?.id,
            },
          );
        }
      },
      relations: ['product'],
    });

    if (!data) throw new BadRequestException('Không tìm thấy sản phẩm này');
    data.status =
      data.status == ProductStatus.active ? ProductStatus.disable : ProductStatus.active;
    data.lastEditorId = request?.user?.id;

    await this.variantRepository.save(data);
    return data;
  }

  async removeVariant(id, request, manager: EntityManager) {
    const mQuery = manager.createQueryBuilder(ProductVariation, 'v');
    mQuery
      .andWhere({
        id,
        bizId: request?.user?.companyId,
      })
      .leftJoinAndSelect('v.product', 'product')
      .leftJoinAndSelect('v.properties', 'properties')
      .leftJoinAndSelect('v.combo', 'combo')
      .leftJoinAndSelect('combo.product', 'prod')
      .leftJoinAndSelect('prod.variants', 'variants');

    if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      mQuery.andWhere('product.clientId = :creatorId OR product.creatorId = :creatorId', {
        creatorId: request?.user?.id,
      });
    }
    const data = await mQuery.getOne();

    if (!data) throw new BadRequestException('Không tìm thấy sản phẩm cần loại bỏ');

    if (!!data?.combo && data?.combo?.length > 0) {
      throw new BadRequestException({
        code: 'PD_0014',
        message: 'Không thể gỡ SKU này do đang được sử dụng trong sản phẩm combo khác',
      });
    }

    let order: any = {};
    try {
      const res = await this.amqpConnection.request({
        exchange: 'ffm-order-external',
        routingKey: 'get-one-order-2-1',
        payload: { id: null, user: request?.user, variants: [id] },
        timeout: 10000,
      });
      if (!!res?.data) order = res?.data;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        code: 'PD_0003',
        message: 'Sản phẩm đã tồn tại trong đơn hàng',
      });
    }

    if (!!order?.id) {
      throw new BadRequestException({
        code: 'PD_0003',
        message: 'Sản phẩm đã tồn tại trong đơn hàng',
      });
    }

    await manager.delete(ProductVariation, id);

    return data;
  }

  async hideProductCombo(data, request) {
    const [prod, variant] = await Promise.all([
      this.pcvRepository.findOne({
        where: qb => {
          qb.where({
            variantId: data?.variantId,
            productId: data?.productId,
          });
        },
      }),
      this.variantRepository
        .createQueryBuilder('var')
        .leftJoinAndSelect('var.product', 'product')
        .leftJoinAndSelect('var.properties', 'properties')
        .where('var.id =:id', { id: data?.variantId })
        .getOne(),
    ]);

    if (!prod) throw new BadRequestException('Không tìm thấy sản phẩm này trong combo');
    const oldStatus = prod.status;
    prod.status =
      prod.status == ProductStatus.active ? ProductStatus.disable : ProductStatus.active;
    prod.lastEditorId = request?.user?.id;

    const product = await this.productsRepository.findOne({
      where: qb => {
        qb.where({
          id: data?.productId,
          bizId: request?.user?.companyId,
        });
        if (request?.user?.type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        }
      },
      relations: ['variants'],
    });
    if (!product) throw new BadRequestException('Không tìm thấy sản phẩm này trong combo');
    await this.pcvRepository.save(prod);

    await this.logRepository
      .save({
        action: 'Changed Product',
        changes: [
          'Product Name',
          variant?.product?.name,
          'SKU',
          variant?.sku,
          'Variant',
          variant?.properties
            .map(i => {
              if (i.status == ProductStatus.active) return `${i?.attributes?.name}: ${i?.name}`;
            })
            ?.join(','),
          'Quantity',
          prod?.qty,
          'Retail Price',
          prod?.price,
          'Status',
          prod.status,
        ],
        afterChanges: [
          'Product Name',
          variant?.product?.name,
          'SKU',
          variant?.sku,
          'Variant',
          variant?.properties
            .map(i => {
              if (i.status == ProductStatus.active) return `${i?.attributes?.name}: ${i?.name}`;
            })
            ?.join(','),
          'Quantity',
          prod?.qty,
          'Retail Price',
          prod?.price,
          'Status',
          prod.status,
        ],
        beforeChanges: [
          'Product Name',
          variant?.product?.name,
          'SKU',
          variant?.sku,
          'Variant',
          variant?.properties
            .map(i => {
              if (i.status == ProductStatus.active) return `${i?.attributes?.name}: ${i?.name}`;
            })
            ?.join(','),
          'Quantity',
          prod?.qty,
          'Retail Price',
          prod?.price,
          'Status',
          oldStatus,
        ],
        tableName: 'product',
        parentTableName: null,
        recordId: data?.productId,
        creatorId: request?.user?.id,
        parentId: null,
        childrenId: null,
      } as Logs)
      .catch(err => {
        if (err?.driverError) console.log(err?.driverError?.detail);
        return err;
      });

    return prod;
  }

  async deleteProductCombo(data, request, manager: EntityManager) {
    const [productVariantCombo, variantData] = await Promise.all([
      manager
        .createQueryBuilder(ProductComboVariant, 'pcv')
        .andWhere({
          variantId: data?.variantId,
          productId: data?.productId,
        })
        .leftJoinAndSelect('pcv.product', 'product')
        .leftJoinAndSelect('product.variants', 'variants')
        .getOne(),
      manager
        .createQueryBuilder(ProductVariation, 'var')
        .leftJoinAndSelect('var.product', 'product')
        .leftJoinAndSelect('var.properties', 'properties')
        .where('var.id =:id', { id: data?.variantId })
        .getOne(),
    ]);

    if (!productVariantCombo)
      throw new BadRequestException('Không tìm thấy sản phẩm cần loại bỏ trong combo');

    await manager
      .save(Logs, {
        action: 'Deleted SKU in combo',
        changes: [],
        afterChanges: [],
        beforeChanges: ['SKU', `${variantData?.prefix}-${variantData?.sku}`],
        tableName: 'product',
        parentTableName: null,
        recordId: data?.productId,
        creatorId: request?.user?.id,
        parentId: null,
        childrenId: null,
      } as Logs)
      .catch(err => {
        if (err?.driverError) console.log(err?.driverError?.detail);
        return err;
      });

    return await manager
      .createQueryBuilder()
      .delete()
      .from(ProductComboVariant)
      .where('variantId = :variantId', { variantId: data?.variantId })
      .andWhere('productId = :productId', { productId: data?.productId })
      .execute();
  }

  async getSQLQuery(query: FilterProduct, request, header) {
    const countryId = header['country-ids'];
    let { name, clientId, clientIds } = query;
    const { type, companyId, id } = request?.user;

    const sql = this.productsRepository.createQueryBuilder('product');

    clientId = type == $enum(UserType).getKeyOrDefault(UserType.customer, null) ? id : clientId;

    if (clientId) sql.andWhere('product.clientId = :clientId', { clientId });

    if (!!clientIds) sql.andWhere('product.clientId IN (:...clientIds)', { clientIds });

    if (companyId) sql.andWhere('product.bizId = :companyId', { companyId });
    if (countryId) sql.andWhere('product.countryId = :countryId', { countryId });

    sql.leftJoinAndSelect('product.variants', 'variants');
    sql.leftJoinAndSelect('variants.properties', 'properties');
    sql.leftJoinAndSelect('product.combo', 'combo');
    sql.leftJoinAndSelect('combo.variant', 'variantCombo');
    sql.leftJoinAndSelect('variantCombo.properties', 'variantComboProperties');

    return sql;
  }

  async exportExcel(query: FilterProduct, request, header) {
    const sql = await this.getSQLQuery(query, request, header);
    if (!isNil(query?.isCombo))
      sql.andWhere({
        isCombo: query.isCombo,
      });
    sql.leftJoinAndSelect('product.category', 'category');
    sql.leftJoinAndSelect('properties.attributes', 'attributes');
    sql.leftJoinAndSelect('variantComboProperties.attributes', 'comboAttributes');
    sql.leftJoinAndSelect('variantCombo.product', 'productDetail');
    if (!isEmpty(query?.ids))
      sql.andWhere('product.id in (:...ids)', {
        ids: query?.ids,
      });
    sql.orderBy('product.id', 'DESC');
    // sql.andWhere('product.status = :status', {status: ProductStatus.active});

    const result = await sql.getMany();

    let userIds = [];
    for (const item of result) {
      for (const element of item?.variants) {
        userIds.push(Number(element?.creatorId), Number(element?.clientId));
      }
    }
    userIds = uniq(userIds);
    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: userIds },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Product Information');
    let headerColumns = [
      { header: 'No.', key: 'no' },
      { header: 'Image', key: 'image', width: 20 },
      { header: 'Country Code', key: 'countryCode', width: 15 },
      { header: 'Client Code', key: 'clientCode', width: 15 },
      { header: 'Client Name', key: 'clientName', width: 25 },
      { header: 'Product ID', key: 'productId', width: 25 },
      { header: 'Product Name', key: 'productName', width: 25 },
      { header: 'Product Name by Language', key: 'productNameByLanguage', width: 25 },
      { header: 'Product Status', key: 'productStatus', width: 15 },
      { header: 'Category ID', key: 'categoryId', width: 20 },
      { header: 'Category Name', key: 'categoryName', width: 20 },
      { header: 'Type', key: 'type', width: 10 },
      { header: 'Available Variants', key: 'availableVariants', width: 20 },
      { header: 'Additional', key: 'additional', width: 25 },
      { header: 'Declared Value', key: 'declaredValue', width: 15 },
      { header: 'Product Link', key: 'productLink', width: 25 },
      { header: 'Description', key: 'description', width: 25 },
      { header: 'Variant SKU', key: 'variantSKU', width: 25 },
      { header: 'Variant Barcode', key: 'variantBarcode', width: 25 },
      { header: 'Variant Attributes', key: 'variantAttributes', width: 25 },
      { header: 'Variant Import Price', key: 'variantImportPrice', width: 20 },
      { header: 'Variant Selling Price', key: 'variantSellingPrice', width: 20 },
      { header: 'Variant Dimension (cm)', key: 'variantDimension', width: 20 },
      { header: 'Variant Weight (gr)', key: 'variantWeight', width: 15 },
      { header: 'Total Weight (gr)', key: 'totalWeight', width: 15 },
      { header: 'Combo Selling Price', key: 'comboSellingPrice', width: 20 },
      { header: 'Variant Status', key: 'variantStatus', width: 15 },
      { header: 'Created', key: 'created', width: 25 },
    ];

    if (!query?.image) {
      headerColumns = headerColumns.filter(item => item.key !== 'image');
    }

    worksheet.columns = headerColumns;

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
    });

    let i = 1;
    let imagesArray = [];
    const imageColumn = 2;
    for (let index = 0; index < result.length; index++) {
      const product = result[index];

      if (product?.isCombo == true) {
        const activeVariants = product?.combo.filter(item => item?.status == ProductStatus.active);
        const availableVariants = `${activeVariants.length}/${product?.combo.length}`;
        for (let index = 0; index < product?.combo.length; index++) {
          const element = product?.combo[index];
          const item = element?.variant;
          const arrProperties = [];
          for (const property of item?.properties) {
            arrProperties.push(`${property?.attributes?.name}: ${property.name}`);
          }
          const additional = [];
          if (item?.product?.additional?.includes(ProductAdditional.dangerousGood))
            additional.push('Dangerous');
          if (item?.product?.additional?.includes(ProductAdditional.fragileGood))
            additional.push('Fragile');
          const image = !!item?.images ? item?.images[0] : null;
          worksheet.addRow({
            no: i,
            countryCode: product?.countryId,
            clientCode: item?.clientId ? userLookup[item?.clientId]?.displayId : null,
            clientName: item?.clientId ? userLookup[item?.clientId]?.name : null,
            productId: product?.originSku,
            productName: product?.name,
            productNameByLanguage: item?.product?.chineseName
              ? 'CN-' + item?.product?.chineseName
              : '',
            productStatus:
              $enum(ProductStatus).getKeyOrDefault(product?.status, null) == 'active'
                ? 'Active'
                : 'Inactive',
            categoryId: !!product?.category ? product?.category?.displayId : '',
            categoryName: !!product?.category ? product?.category?.name : '',
            type: product?.isCombo ? 'Combo' : 'Normal',
            availableVariants: availableVariants,
            additional: additional.length > 0 ? additional.join(', ') : '',
            declaredValue: product?.declaredValue,
            productLink: product?.link ?? '',
            description: product?.description ?? '',
            variantSKU: item?.originSku,
            variantBarcode: item?.barcode ?? '',
            variantAttributes: arrProperties.join('; '),
            variantImportPrice: item?.priceImport,
            variantSellingPrice: item?.priceRetail,
            variantDimension: item?.size ? item?.size.join(' x ') : '',
            variantWeight: item?.weight,
            totalWeight: product?.variants[0]?.weight,
            comboSellingPrice: product?.variants[0]?.priceRetail,
            variantStatus:
              $enum(ProductStatus).getKeyOrDefault(item?.status, null) == 'active'
                ? 'Active'
                : 'Inactive',
            created: item?.createdAt
              ? moment(item?.createdAt)
                  .tz('Asia/Ho_Chi_Minh')
                  .format('DD/MM/yyyy HH:mm')
              : '',
          });
          if (image && query?.image) {
            imagesArray.push({ rowIndex: i + 1, image });
          }
          i++;
        }
      } else {
        const activeVariants = product?.variants.filter(
          item => item?.status == ProductStatus.active,
        );
        const availableVariants = `${activeVariants.length}/${product?.variants.length}`;
        const additional = [];
        if (product?.additional?.includes(ProductAdditional.dangerousGood))
          additional.push('Dangerous');
        if (product?.additional?.includes(ProductAdditional.fragileGood))
          additional.push('Fragile');
        const image = !!product?.covers ? product?.covers[0] : null;
        for (const item of product?.variants) {
          const arrProperties = [];
          for (const element of item?.properties) {
            arrProperties.push(`${element?.attributes?.name}: ${element.name}`);
          }
          worksheet.addRow({
            no: i,
            countryCode: product?.countryId,
            clientCode: item?.clientId ? userLookup[item?.clientId]?.displayId : null,
            clientName: item?.clientId ? userLookup[item?.clientId]?.name : null,
            productId: product?.originSku,
            productName: product?.name,
            productNameByLanguage: product?.chineseName ? 'CN-' + product?.chineseName : '',
            productStatus:
              $enum(ProductStatus).getKeyOrDefault(product?.status, null) == 'active'
                ? 'Active'
                : 'Inactive',
            categoryId: !!product?.category ? product?.category?.displayId : '',
            categoryName: !!product?.category ? product?.category?.name : '',
            type: product?.isCombo ? 'Combo' : 'Normal',
            availableVariants: availableVariants,
            additional: additional.length > 0 ? additional.join(', ') : '',
            declaredValue: product?.declaredValue,
            productLink: product?.link ?? '',
            description: product?.description ?? '',
            variantSKU: `${item?.sku}`,
            variantBarcode: item?.barcode ?? '',
            variantAttributes: arrProperties.join('; '),
            variantImportPrice: item?.priceImport,
            variantSellingPrice: item?.priceRetail,
            variantDimension: item?.size ? item?.size.join(' x ') : '',
            variantWeight: item?.weight,
            totalWeight: item?.weight,
            comboSellingPrice: '',
            variantStatus:
              $enum(ProductStatus).getKeyOrDefault(item?.status, null) == 'active'
                ? 'Active'
                : 'Inactive',
            created: item?.createdAt
              ? moment(item?.createdAt)
                  .tz('Asia/Ho_Chi_Minh')
                  .format('DD/MM/yyyy HH:mm')
              : '',
          });
          if (image && query?.image) {
            imagesArray.push({ rowIndex: i + 1, image });
          }
          i++;
        }
      }
    }

    if (imagesArray.length > 0) {
      await saveImageToExcel(imagesArray, workbook, worksheet, imageColumn);
    }

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async detailHistories(id: any, request: any): Promise<any> {
    const sql = this.logRepository.createQueryBuilder('log');

    sql.select('log.recordId as id');
    sql.addSelect('log.changes as changes');
    sql.addSelect('log.beforeChanges as before');
    sql.addSelect('log.afterChanges as after');
    sql.addSelect('log.table_name as table');
    sql.addSelect('log.action as action');
    sql.addSelect('log.creatorId as uid');
    sql.addSelect('log.createdAt as time');

    sql.where('(log.action != :status OR (log.action = :status))', { status: 'STATUS' });

    sql.andWhere(
      '((log.tableName = :tableName and log.recordId = :id) OR (log.tableName = :tableItemName and log.parentId = :parentId))',
      {
        insert: 'INSERT',
        tableName: 'product',
        id,
        tableItemName: 'product_variation',
        parentId: id,
      },
    );

    sql.orderBy('log.createdAt', 'DESC');

    const [data, categories, variants, product] = await Promise.all([
      sql.getRawMany(),
      this.categoryRepository
        .createQueryBuilder('c')
        .where('c.bizId = :bizId', { bizId: request?.user?.companyId })
        .getMany(),
      // this.avRepository
      //   .createQueryBuilder('av')
      //   .leftJoin('av.attributes', 'attributes')
      //   .addSelect(['attributes.name'])
      //   .where('attributes.bizId = :bizId', { bizId: request?.user?.companyId })
      //   .getMany(),
      this.variantRepository
        .createQueryBuilder('')
        .select(['id', 'sku', 'prefix'])
        .where('id_product = :id', { id })
        .getRawMany(),
      this.productsRepository
        .createQueryBuilder('pd')
        .where('pd.id = :id', { id })
        .getOne(),
    ]);
    // console.log(222, product);
    const typeProduct = product?.isCombo ? 'Combo' : 'Product';
    let userIds = [];
    for (const item of data) {
      const clientIdIndex = item?.changes?.indexOf('client_id') + 1;
      const clientId = clientIdIndex > 0 ? item?.changes[Number(clientIdIndex)] : null;
      userIds.push(clientId);
    }
    userIds = userIds.concat(data?.map(x => x.uid));
    let users: Users[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user',
        payload: {
          filters: { ids: uniq(userIds) },
          pagination: {},
          header: {},
          request: { user: { companyId: request?.user?.companyId } },
        },
        timeout: 10000,
      });
      users = data as Users[];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }
    const userLookup = [];
    const categoriesLookup = [];
    // const avLookup = [];
    // const aLookup = [];
    const variantLookup = [];

    users.forEach((user: Users) => {
      userLookup[user?.id] = `${!!user?.displayId ? `${user?.displayId} | ` : ''}${user?.name}`;
    });
    categories.forEach((item: any) => {
      categoriesLookup[item?.id] = `${item?.displayId} | ${item?.name}`;
    });
    // av.forEach((item: any) => {
    //   avLookup[item?.id] = `${item?.name}`;
    //   aLookup[item?.id] = `${item?.attributes?.name}`;
    // });
    variants.forEach((item: any) => {
      variantLookup[item?.id] = `${item?.prefix}-${item?.sku}`;
    });

    const newData = [];
    const productLogMapping = {
      description: 'Description',
      status: 'Status',
      category_id: 'Category',
      name: 'Product Name',
      price_retail: 'Selling Price',
      sku: 'SKU',
      weight: 'Weight (gr)',
      declared_value: 'Declared Value',
      cover: 'Cover',
    };
    if (product?.isCombo) {
      for (const item of data) {
        if (item.table == 'product_variation' && item.action == 'INSERT') continue;
        let action = '';
        const actor = userLookup[item?.uid] ?? '-';
        if (item.table == 'product' && item.action == 'INSERT') {
          // action = 'Added Product in combo';
          // newData.push({
          //   ...item,
          //   changes: {},
          //   before: {},
          //   actor,
          //   action,
          // });
        } else if (item?.action == 'STATUS' && item.before) {
          newData.push({
            ...item,
            changes: {
              Status:
                objToEnum(ProductStatus)[item?.changes[0]] === 'active'
                  ? 'Active'
                  : objToEnum(ProductStatus)[item?.changes[0]] === 'disable'
                  ? 'Inactive'
                  : objToEnum(ProductStatus)[item?.changes[0]],
            },
            before: {
              Status:
                objToEnum(ProductStatus)[item?.before[0]] === 'active'
                  ? 'Active'
                  : objToEnum(ProductStatus)[item?.before[0]] === 'disable'
                  ? 'Inactive'
                  : objToEnum(ProductStatus)[item?.before[0]],
            },
            actor,
            action: 'Changed Combo information',
          });
        } else if (
          item?.action == 'Changed Product' ||
          item?.action == 'Deleted SKU in combo' ||
          item?.action == 'Added Product in combo' ||
          item?.action == 'Changed Product in combo'
        ) {
          const dataBefore = {},
            dataAfter = {};

          for (let i = 0; i < item?.before?.length; i += 2) {
            const key = item?.before[i];
            const value = item?.before[i + 1] == null ? 'N/A' : item?.before[i + 1];
            dataBefore[key] = value;
          }
          for (let i = 0; i < item?.after?.length; i += 2) {
            const key = item?.after[i];
            const value = item?.after[i + 1] == null ? 'N/A' : item?.after[i + 1];
            dataAfter[key] = value;
          }

          newData.push({
            ...item,
            changes: dataAfter,
            before: dataBefore,
            after: dataAfter,
            actor,
            action: item?.action,
          });
        } else if (item.action == 'UPDATE' || (item?.action == 'STATUS' && item.before)) {
          action = 'Changed Combo information';
          let before = [];
          const prefixAfterIndex =
            item?.action == 'DELETE'
              ? item?.before?.indexOf('prefix') + 1
              : item?.after?.indexOf('prefix') + 1;
          const prefixAfter =
            prefixAfterIndex > 0 &&
            (item?.action == 'DELETE'
              ? item?.before[Number(prefixAfterIndex)]
              : item?.after[Number(prefixAfterIndex)]);
          const prefixBeforeIndex = item?.before?.indexOf('prefix') + 1;
          const prefixBefore = prefixBeforeIndex > 0 && item?.before[Number(prefixBeforeIndex)];

          for (let i = 0; i < item?.before?.length; i += 2) {
            const key = item?.before[i];
            const value = item?.before[i + 1] == null ? 'N/A' : item?.before[i + 1];
            before[key] = value;
          }

          for (let i = 0; i < item?.changes?.length; i += 2) {
            const key = item?.changes[i];
            const value = item?.changes[i + 1] == null ? 'N/A' : item?.changes[i + 1];
            const beforeValue = item?.before[i + 1] == null ? 'N/A' : item?.before[i + 1];

            if (item.table == 'product_variation') {
              // if (key == 'sku') {
              //   newData.push({
              //     ...item,
              //     changes: {
              //       [productLogMapping[key]]: `${prefixAfter}-${value}`,
              //     },
              //     before: { [productLogMapping[key]]: `${prefixBefore}-${before[key]}` },
              //     actor,
              //     action,
              //   });
              // }
              if (['name', 'price_retail', 'weight']?.includes(key)) {
                newData.push({
                  ...item,
                  changes: {
                    [productLogMapping[key]]: value,
                  },
                  before: { [productLogMapping[key]]: before[key] },
                  actor,
                  action,
                });
              }
            }
            if (item.table == 'product') {
              if (['category_id', 'description', 'sku', 'declared_value', 'cover']?.includes(key)) {
                newData.push({
                  ...item,
                  changes: {
                    [productLogMapping[key]]:
                      key == 'category_id' ? categoriesLookup[value] ?? 'N/A' : value,
                  },
                  before: {
                    [productLogMapping[key]]:
                      key == 'category_id' ? categoriesLookup[before[key]] ?? 'N/A' : beforeValue,
                  },
                  actor,
                  action: key == 'description' ? "Changed Combo's description" : action,
                });
              }
            }
          }
        } else if (item?.action == 'Created Combo') {
          newData.push({
            ...item,
            changes: {},
            before: {},
            actor,
            action: 'Created Combo',
          });
        }
      }
    } else {
      if (!isEmpty(data)) {
        for (const item of data) {
          if (item?.changes?.length == 2 && item?.changes[0] == 'last_editor_id') continue;
          if (item?.action == 'UPDATE' && item?.table == 'product') {
            if (item?.changes?.includes('category_id')) {
              const idx = item.changes.indexOf('category_id');
              if (idx !== -1 && item.changes.length > idx + 1) {
                newData.push({
                  ...item,
                  changes: ['category_id', item.changes[idx + 1]],
                  before: ['category_id', item.before[idx + 1]],
                  after: [],
                });
              }
              item.changes.splice(idx, 2);
              item.before.splice(idx, 2);
            }
          }
          newData.push(item);
        }
        for (const item of newData) {
          const prefixAfterIndex =
            item?.action == 'DELETE'
              ? item?.before?.indexOf('prefix') + 1
              : item?.after?.indexOf('prefix') + 1;
          const prefixAfter =
            prefixAfterIndex > 0 &&
            (item?.action == 'DELETE'
              ? item?.before[Number(prefixAfterIndex)]
              : item?.after[Number(prefixAfterIndex)]);
          const prefixBeforeIndex = item?.before?.indexOf('prefix') + 1;
          const prefixBefore = prefixBeforeIndex > 0 && item?.before[Number(prefixBeforeIndex)];
          if (item?.action == 'STATUS') {
            item.changes = {
              Status:
                objToEnum(ProductStatus)[item?.changes[0]] === 'active'
                  ? 'Active'
                  : objToEnum(ProductStatus)[item?.changes[0]] === 'disable'
                  ? 'Inactive'
                  : objToEnum(ProductStatus)[item?.changes[0]],
            };
            if (!item.before || Object.keys(item.before).length === 0) {
              item.action = 'Added Variant';
              item.before = {};
            } else {
              item.action =
                item?.table == 'product' ? 'Changed Product information' : 'Changed Variant';
              item.before = {
                Status:
                  objToEnum(ProductStatus)[item?.before[0]] === 'active'
                    ? 'Active'
                    : objToEnum(ProductStatus)[item?.before[0]] === 'disable'
                    ? 'Inactive'
                    : objToEnum(ProductStatus)[item?.before[0]],
              };
            }
            item.actor = userLookup[item?.uid] ?? '-';
          } else if (item?.action == 'Changed Properties') {
            item.changes = { [item?.changes[0]]: item?.changes[1] };
            item.before = { [item?.before[0]]: item?.before[1] };
          } else if (item?.action == 'Added Properties' || item?.action == 'Removed Properties') {
            item.changes = { [item?.changes[0]]: item?.changes[1] };
          } else if (item?.action == 'Changed Variant Properties') {
            item.changes = { Attributes: `{${item?.changes.join(', ')}}` };
            item.before = { Attributes: `{${item?.before.join(', ')}}` };
            item.action = 'Changed Variant';
          } else if (
            item?.action == 'Added Variant Properties' ||
            item?.action == 'Removed Variant Properties'
          ) {
            item.changes = { Attributes: `{${item?.changes.join(', ')}}` };
            item.action =
              item?.action == 'Added Variant Properties' ? 'Added Variant' : 'Removed Variant';
          } else {
            const obj = {};
            const beforeObj = {};

            for (let i = 0; i < item?.changes?.length; i += 2) {
              const key = item?.changes[i];
              const value = item?.changes[i + 1] == null ? 'N/A' : item?.changes[i + 1];
              obj[key] = value;
              if (key == 'category_id') {
                obj['Category'] = categoriesLookup[value] ?? 'N/A';
                delete obj['category_id'];
              }
              if (key == 'sku') {
                obj['Original product code'] = `${prefixAfter}-${value}`;
                delete obj['sku'];
              }
              if (key == 'name') {
                obj['Product name'] = value;
                delete obj['name'];
              }
              if (key == 'is_dangerous') {
                // obj['Dangerous Good'] = value == 't' ? 'Yes' : 'No';
                delete obj['is_dangerous'];
              }
              if (key == 'client_id') {
                item?.table == 'product' && (obj['Customer'] = userLookup[value] ?? '-');
                delete obj['client_id'];
              }
              if (key == 'last_editor_id') {
                delete obj['last_editor_id'];
              }
              if (key == 'declared_value') {
                obj['Declared Value'] = value;
                delete obj['declared_value'];
              }
              if (key == 'price_import') {
                obj['Import Price'] = value;
                delete obj['price_import'];
              }
              if (key == 'price_retail') {
                obj['Selling Price'] = value;
                delete obj['price_retail'];
              }
              if (key == 'additional') {
                let result = [];
                if (value.includes(ProductAdditional.dangerousGood)) {
                  result.push('Dangerous Good');
                }
                if (value.includes(ProductAdditional.fragileGood)) {
                  result.push('Fragile Good');
                }

                if (result.length > 0) {
                  obj['Additional'] = `{${result.join(', ')}}`;
                } else {
                  obj['Additional'] = value;
                }
                delete obj['additional'];
              }
              if (key == 'barcode') {
                obj['Barcode'] = value;
                delete obj['barcode'];
              }
              if (key == 'size') {
                obj['Dimensions (cm)'] = value ? value.split(',').join(' x ') : '';
                delete obj['size'];
              }
              if (key == 'weight') {
                obj['Weight (gr)'] = value;
                delete obj['weight'];
              }
              if (key == 'description') {
                obj['Description'] = value;
                delete obj['description'];
              }
              if (key == 'link') {
                obj['Link'] = value;
                delete obj['link'];
              }
              if (key == 'cover') {
                obj['Cover'] = value;
                delete obj['cover'];
              }
              if (item?.table == 'product_variation') {
                delete obj['Display name'];
                delete obj['image'];
                delete obj['biz_id'];
                delete obj['qty_good'];
                delete obj['qty_total'];
                delete obj['country_id'];
                delete obj['id_product'];
                delete obj['qty_damaged'];
                delete obj['id'];
                delete obj['created_at'];
                delete obj['status'];
                delete obj['creator_id'];
                delete obj['updated_at'];
                delete obj['Product name'];
              }
              delete obj['prefix'];
              delete obj['categories'];
            }
            for (let i = 0; i < item?.before?.length; i += 2) {
              const key = item?.before[i];
              const value = item?.before[i + 1] == null ? 'N/A' : item?.before[i + 1];
              beforeObj[key] = value;
              if (key == 'category_id') {
                beforeObj['Category'] = categoriesLookup[value] ?? 'N/A';
                delete beforeObj['category_id'];
              }
              if (key == 'sku') {
                beforeObj['Original product code'] = `${prefixBefore || prefixAfter}-${value}`;
                delete beforeObj['sku'];
              }
              if (key == 'name') {
                beforeObj['Product name'] = value;
                delete beforeObj['name'];
              }
              if (key == 'is_dangerous') {
                // beforeObj['Dangerous Good'] = value == 't' ? 'Yes' : 'No';
                delete beforeObj['is_dangerous'];
              }
              if (key == 'client_id') {
                item?.table == 'product' && (beforeObj['Customer'] = userLookup[value] ?? '-');
                delete beforeObj['client_id'];
              }
              if (key == 'last_editor_id') {
                delete beforeObj['last_editor_id'];
              }
              if (key == 'declared_value') {
                beforeObj['Declared Value'] = value;
                delete beforeObj['declared_value'];
              }
              if (key == 'price_import') {
                beforeObj['Import Price'] = value;
                delete beforeObj['price_import'];
              }
              if (key == 'price_retail') {
                beforeObj['Selling Price'] = value;
                delete beforeObj['price_retail'];
              }
              if (key == 'additional') {
                let result = [];
                if (value.includes(ProductAdditional.dangerousGood)) {
                  result.push('Dangerous Good');
                }
                if (value.includes(ProductAdditional.fragileGood)) {
                  result.push('Fragile Good');
                }

                if (result.length > 0) {
                  beforeObj['Additional'] = `{${result.join(', ')}}`;
                } else {
                  beforeObj['Additional'] = value;
                }
                delete beforeObj['additional'];
              }
              if (key == 'barcode') {
                beforeObj['Barcode'] = value;
                delete beforeObj['barcode'];
              }
              if (key == 'size') {
                beforeObj['Dimensions (cm)'] = value ? value.split(',').join(' x ') : '';
                delete beforeObj['size'];
              }
              if (key == 'weight') {
                beforeObj['Weight (gr)'] = value;
                delete beforeObj['weight'];
              }
              if (key == 'description') {
                beforeObj['Description'] = value;
                delete beforeObj['description'];
              }
              if (key == 'link') {
                beforeObj['Link'] = value;
                delete beforeObj['link'];
              }
              if (key == 'cover') {
                beforeObj['Cover'] = value;
                delete beforeObj['cover'];
              }
              if (item?.table == 'product_variation') {
                delete beforeObj['Display name'];
                delete beforeObj['image'];
                delete beforeObj['biz_id'];
                delete beforeObj['qty_good'];
                delete beforeObj['qty_total'];
                delete beforeObj['country_id'];
                delete beforeObj['id_product'];
                delete beforeObj['qty_damaged'];
                delete beforeObj['id'];
                delete beforeObj['created_at'];
                delete beforeObj['status'];
                delete beforeObj['creator_id'];
                delete beforeObj['updated_at'];
                delete beforeObj['Product name'];
              }
              delete beforeObj['prefix'];
              delete beforeObj['categories'];
            }
            item.changes = obj;
            item.before = beforeObj;
          }
          item.actor = userLookup[item?.uid] ?? '-';

          if (item?.table == 'product_variation') {
            item.variantSku = variantLookup[item?.id];
          }

          if (item?.action == 'UPDATE') {
            if (item?.table == 'product') {
              item.action = `Changed ${typeProduct} information`;
            }
            if (item?.table == 'product_variation') {
              item.action = 'Changed Variant';
            }
          }
          if (item?.action == 'INSERT') {
            if (item?.table == 'product') {
              item.action = `Created ${typeProduct} information`;
            }
            if (item?.table == 'product_variation') {
              item.action = 'Added Variant';
            }
          }
          if (item?.action == 'DELETE') {
            if (item?.table == 'product') {
              item.action = `Removed ${typeProduct} information`;
            }
            if (item?.table == 'product_variation') {
              item.action = 'Removed Variant';
            }
          }
        }
      }
    }

    const mergedData = [];
    const seen = new Map();

    for (const item of newData) {
      if (item.table === 'product_variation' && item.time && item.action === 'Changed Variant') {
        // Allow merging if time difference <= 3 seconds
        const itemTime = new Date(item.time).getTime();
        let foundKey = null;
        for (const [key, mergedItem] of seen.entries()) {
          if (
            key.startsWith(`changed_${item.id}_${item.table}_`) &&
            key.endsWith(`_${item.action}`) &&
            Math.abs(new Date(mergedItem.time).getTime() - itemTime) <= 3000
          ) {
            foundKey = key;
            break;
          }
        }
        if (!foundKey) {
          const key = `changed_${item.id}_${item.table}_${item.time}_${item.action}`;
          const mergedItem = { ...item, changes: { ...item.changes }, before: { ...item.before } };
          seen.set(key, mergedItem);
          mergedData.push(mergedItem);
        } else {
          const mergedItem = seen.get(foundKey);
          Object.assign(mergedItem.changes, item.changes);
          Object.assign(mergedItem.before, item.before);
        }
      } else if (
        item.table === 'product_variation' &&
        item.time &&
        item.action === 'Added Variant'
      ) {
        if (item.changes && item.changes['Product name']) {
          delete item.changes['Product name'];
        }
        // Allow merging if time difference <= 3 seconds
        const itemTime = new Date(item.time).getTime();
        let foundKey = null;
        for (const [key, mergedItem] of seen.entries()) {
          if (
            key.startsWith(`added_${item.id}_${item.table}_`) &&
            key.endsWith(`_${item.action}`) &&
            Math.abs(new Date(mergedItem.time).getTime() - itemTime) <= 3000
          ) {
            foundKey = key;
            break;
          }
        }
        if (!foundKey) {
          const key = `added_${item.id}_${item.table}_${item.time}_${item.action}`;
          const mergedItem = { ...item, changes: { ...item.changes }, before: { ...item.before } };
          seen.set(key, mergedItem);
          mergedData.push(mergedItem);
        } else {
          const mergedItem = seen.get(foundKey);
          Object.assign(mergedItem.changes, item.changes);
          Object.assign(mergedItem.before, item.before);
        }
      } else if (
        item.table === 'product_variation' &&
        item.time &&
        item.action === 'Removed Variant'
      ) {
        // Remove 'Product name' from changes if present
        if (item.changes && item.changes['Product name']) {
          delete item.changes['Product name'];
        }
        // Allow merging if time difference <= 3 seconds
        const itemTime = new Date(item.time).getTime();
        let foundKey = null;
        for (const [key, mergedItem] of seen.entries()) {
          if (
            key.startsWith(`removed_${item.id}_${item.table}_`) &&
            key.endsWith(`_${item.action}`) &&
            Math.abs(new Date(mergedItem.time).getTime() - itemTime) <= 3000
          ) {
            foundKey = key;
            break;
          }
        }
        if (!foundKey) {
          const key = `removed_${item.id}_${item.table}_${item.time}_${item.action}`;
          const mergedItem = { ...item, changes: { ...item.changes }, before: { ...item.before } };
          seen.set(key, mergedItem);
          mergedData.push(mergedItem);
        } else {
          const mergedItem = seen.get(foundKey);
          // Merge all data into before
          Object.assign(mergedItem.before, item.before, item.changes, mergedItem.changes);
          mergedItem.changes = {};
        }
      } else {
        mergedData.push(item);
      }
    }

    newData.length = 0;
    newData.push(...mergedData);

    return newData;
  }
}
