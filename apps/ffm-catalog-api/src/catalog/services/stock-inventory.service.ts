/* eslint-disable @typescript-eslint/no-floating-promises */
import {
  AmqpConnection,
  defaultNackError<PERSON><PERSON><PERSON>,
  <PERSON>ck,
  RabbitRPC,
} from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeRevertStatus } from 'apps/ffm-order-api/src/enums/type-update-order-status.enum';
import { plainToClass, plainToInstance } from 'class-transformer';
import { CARRIER_CONFIGURATION_BY_COUNTRIES } from 'core/constants/carrier';
import { catalogConnection, orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { CarrierType, CountryID } from 'core/enums/carrier-code.enum';
import { StockInventoryName } from 'core/enums/inventory-ffm.enum';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { SystemIdEnum, UserType } from 'core/enums/user-type.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import HistoryLogUtils from 'core/utils/HistoryLogUtils';
import {
  compact,
  concat,
  find,
  findIndex,
  head,
  intersection,
  isEmpty,
  isNil,
  isNull,
  omit,
  orderBy,
  reduce,
  reverse,
  sortBy,
  startCase,
  trim,
  uniq,
} from 'lodash';
import * as moment from 'moment-timezone';
import xlsx from 'node-xlsx';
import { $enum } from 'ts-enum-util';
import { Brackets, getConnection, In, Repository } from 'typeorm';
import { StockInventoryDto, StockInventoryItemDto } from '../../dtos/stock-inventory.dto';
import { InventoryLineItem } from '../../entities/inventory-line-item.entity';
import { InventoryLogs } from '../../entities/inventory-logs.entity';
import { InventoryManagement } from '../../entities/inventory-management.entity';
import { Logs } from '../../entities/logs.entity';
import { ProductComboVariant } from '../../entities/product-combo-variant.entity';
import { ProductVariation } from '../../entities/product-variation.entity';
import { PurchaseOrder } from '../../entities/purchase-order.entity';
import { StockInventoryItem } from '../../entities/stock-inventory-item.entity';
import { StockInventory } from '../../entities/stock-inventory.entity';
import { StockTakingItem } from '../../entities/stock-taking-item.entity';
import { StockTaking } from '../../entities/stock-taking.entity';
import { StockTransfer } from '../../entities/stock-transfer.entity';
import { SlotWarehouses } from '../../entities/warehouses';
import { Users } from '../../read-entities/identity-entities/Users';
import { OrderProduct } from '../../read-entities/order-entities/order-product.entity';
import { Order } from '../../read-entities/order-entities/order.entity';
import { ProductStatus } from '../enum/product-status.enum';
import {
  ExportType,
  InventoryType,
  SortType,
  StockInventoryFilterColumns,
  StockInventoryType,
  TypeInventory,
  TypePurpose,
} from '../enum/stock-inventory-type.enum';
import { StockTakingStatus } from '../enum/stock-taking-status.enum';
import { StockTransferPurpose, StockTransferType } from '../enum/stock-transfer.enum';
import { WarehouseType } from '../enum/warehouse-type.enum';
import { FilterFluctuation, FilterFluctuationDetail } from '../filters/filterFluctuation.filter';
import { FilterStock } from '../filters/filterStock.filter';
import { AwsS3 } from 'core/utils/AwsS3';
import { MailerService } from '@nestjs-modules/mailer';
import * as ExcelJS from 'exceljs';
import { saveImageToExcel } from 'core/utils/ImageUtils';

const inventoryItemCheckConstraints = [
  'CHECK_phy_awaiting_reimport',
  'CHECK_phy_awaiting_allocated',
  'CHECK_phy_in_progress',
  'UQ_STOCK_TYPE_ORDER',
];
const stockCheckConstraints = [
  'CHK_27bd0cab06306dd45db177eff5',
  'CHK_ccaf4c29eec7314728ca42c843',
  'CHK_3b017ee6640c6338c4cdb796b2',
  'CHK_a402933cf70911b4ca0f27e2bd',
  'CHK_2d6b01a939513719a27ea0faa7',
  'CHK_f34c4d29d3e4141e023132b8d0',
  'CHK_43ce615c60d316373ea3556f38',
];

export const CLIENT_ENABLE_ZNS = 1239;

export interface IChangeWarehouse {
  order: {
    id: number;
    displayId: string;
    status: OrderFFMStatus;
    warehouseId: number;
  };
  variantIds: number[];
  user: Record<string, any>;
  newWarehouseId: number;
  products: IOrderProduct[];
  stillChangeWarehouse?: boolean;
  updatedAt?: number;
  checkUniq?: boolean;
}

export interface IOrderProduct {
  quantity: number;
  id: number;
  productId: number;
}

export interface IReImportOrder {
  id: number;
  countryId: number;
  orderId: number;
  warehouseId: number;
  orderDisplayId: string;
  products: IReImportOrderItem[];
}

export interface IReImportOrderItem {
  id?: number;
  orderId: number;
  itemId: number;
  productId: number;
  productName?: string;
  sku?: string;
  good: number;
  damaged: number;
  damagedBy3pl: number;
  lost: number;
  lostBy3pl: number;
  needed: number;
}

export interface IReImportOrderPayload {
  reImport: IReImportOrder;
  user: Record<string, any>;
  currentStatus: OrderFFMStatus;
  nextStatus: OrderFFMStatus;
  type: ReImportTypeEnum;
  productReimports?: IReImportOrderItem[];
  updatedAt: number;
}

export interface IOrderInventory {
  id: number;
  user: Record<string, any>;
  oldStatus: OrderFFMStatus;
  newStatus: OrderFFMStatus;
  lastUpdateStatus: Date;
  typeUpdateStatus?: any;
  reason?: string;
  revert?: boolean;
  callBackStatus?: boolean;
  isRequestQueue?: boolean;
  rollBackStatus?: boolean;
  revertType?: TypeRevertStatus;
  rmqErrorAttempts?: number;
  updatedAt?: number;
  isSyncToPartner?: boolean;
  saveNewStatus?: boolean;
  isReimport?: boolean;
  isPriorityStockInventory?: boolean;
}

export interface IStockImport {
  status: number;
  message: string;
  variantId?: string;
  code?: string;
}

export enum ReImportTypeEnum {
  Cancel = 1,
  Return = 2,
}

export interface IDataInventory {
  warehouse: string;
  clientId: number;
  warehouseId: number;
  productName: string;
  // productCover: string[];
  images: string[];
  productSku: string;
  variantId: number;
  sku: string;
  category: string;
  aggImported: number;
  aggExported: number;
  inboundReserved: number;
  sellable: number;
  currentSellable: number;
  currentDamaged: number;
  aggDamaged: number;
  stockedPhySellable?: number;
  aggLost: number;
  aggPhyStocked: number;
  statusSKU: number | string;
  statusProduct: number | string;
  lastSales: Date;
  id?: number;
  productId?: number;
  goodOutShelf?: number;
  phyGood?: number;
  inProgress?: number;
  awReimport?: number;
  isAlreadyExit?: boolean;
}

export interface IDataFluctuationInventory {
  warehouse: string;
  clientId: number;
  warehouseId: number;
  productName: string;
  productCover: string;
  variantId: number;
  sellableBegin: number;
  sellableEnding: number;
  damagedBegin: number;
  damagedEnding: number;
  sku: string;
  category: string;
  importSellableSupplier: number;
  importSellableReimport: number;
  importSellableStocktaking: number;
  importSellableTransfer: number;
  importSellableOthers: number;
  exportSellableSupplier: number;
  exportSellableSales: number;
  exportSellableStocktaking: number;
  exportSellableTransfer: number;
  exportSellableOthers: number;

  importDamagedSupplier: number;
  importDamagedReimport: number;
  importDamagedStocktaking: number;
  importDamagedTransfer: number;
  importDamagedOthers: number;
  exportDamagedSupplier: number;
  exportDamagedClearance: number;
  exportDamagedStocktaking: number;
  exportDamagedTransfer: number;
  exportDamagedOthers: number;

  importLostOthers: number;
  exportLostOthers: number;
  id?: number;
}

export interface IDataFluctuationInventoryLog {
  inventory: string;
  purpose: string;
  reference: string;
  actor: string;
  inventoryLineId: string;
  companyId: number;
  variantId: number;
  warehouseId: number;
  warehouseName: string;
  id?: number;
}

@Injectable()
export class StockInventoryService {
  constructor(
    @InjectRepository(InventoryManagement, catalogConnection)
    private imRepository: Repository<InventoryManagement>,
    @InjectRepository(InventoryLogs, catalogConnection)
    private logsRepository: Repository<InventoryLogs>,
    @InjectRepository(InventoryLineItem, catalogConnection)
    private iltRepository: Repository<InventoryLineItem>,
    @InjectRepository(StockInventoryItem, catalogConnection)
    private stiRepository: Repository<StockInventoryItem>,
    @InjectRepository(StockInventory, catalogConnection)
    private stRepository: Repository<StockInventory>,
    @InjectRepository(StockTaking, catalogConnection)
    private stkRepository: Repository<StockTaking>,
    @InjectRepository(PurchaseOrder, catalogConnection)
    private poRepository: Repository<PurchaseOrder>,
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    @InjectRepository(ProductVariation, catalogConnection)
    private variantRepository: Repository<ProductVariation>,
    @InjectRepository(ProductComboVariant, catalogConnection)
    private pcvRepository: Repository<ProductComboVariant>,
    private readonly amqpConnection: AmqpConnection,
    private readonly mailerService: MailerService,
  ) {}

  async findAll(
    pagination: PaginationOptions,
    query: FilterStock,
    request,
    header,
  ): Promise<[StockInventory[], number]> {
    const sql = await this.getSQLQuery(query, request, header);

    if (query.status) sql.andWhere('product.status = :status', { status: query.status });
    if (pagination) sql.take(pagination.limit).skip(pagination.skip);
    const result = await sql.getManyAndCount();

    let whIds = [];
    if (!isNil(header)) {
      whIds = header['warehouse-ids']?.split(',');
    }
    if (!!query?.warehouseId) whIds = [query?.warehouseId];
    let lookup = {};
    const proIds = (result[0] ?? [])?.map((item: StockInventory) => item?.variantId);
    if (proIds?.length > 0 && whIds?.length > 0) {
      let products: any = {};
      try {
        const res = await this.amqpConnection.request({
          exchange: 'ffm-order-external',
          routingKey: 'count-product-in-order-2-1',
          payload: { ids: proIds, companyId: request?.user?.companyId, warehouseIds: whIds },
          timeout: 10000,
        });
        products = res?.data;

        lookup = products.reduce((acc, { id, status, warehouse_id, quantity }) => {
          const key = `${id}_${warehouse_id}_${status}`;
          if (!acc[key]) {
            acc[key] = 0;
          }
          acc[key] += quantity;
          return acc;
        }, {});
      } catch (error) {
        console.log(error);
      }
    }

    if (result[0]?.length > 0)
      result[0] = result[0]?.map((item: StockInventory) => {
        const sellable =
          (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.AwaitingCollection}`] ??
            0) +
          (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Reconfirm}`] ?? 0) +
          (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Confirmed}`] ?? 0);
        return {
          ...item,
          phyInProgress:
            lookup?.[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Collecting}`] ?? 0,
          phyAwaitingAllocated:
            lookup?.[
              `${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Awaiting3PLPickup}`
            ] ?? 0,
          actualStockedPhy: Number(item?.stockedPhySellable) + sellable,
          phyAwaitingReimport:
            lookup?.[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Returned}`] ?? 0,
        };
      });

    return result;
  }

  async findFluctuationInventory(
    pagination: PaginationOptions,
    query: FilterFluctuation,
    request,
    header,
  ): Promise<[IDataFluctuationInventory[], number]> {
    const [sql, countQuery, startEndingQuery] = await this.getSQLQueryFluctuationInventory(
      query,
      request,
      header,
      false,
    );

    sql.addSelect('ilt.warehouseId', 'warehouse_id');

    if (query?.sortBy && query?.sortType) {
      sql.addOrderBy(query?.sortBy, query?.sortType);
    }

    sql.addGroupBy('st.updatedAt');
    sql.addOrderBy('st.updatedAt', 'DESC');

    if (pagination) {
      // sql.take(pagination.limit).skip(pagination.skip);
      sql.offset(pagination.skip).limit(pagination.limit);
    }

    const [res, countData, maxMinData] = await Promise.all([
      sql.getRawMany(),
      countQuery.getRawMany(),
      startEndingQuery.getRawMany(),
    ]);

    const lookupVariants = {};
    if (maxMinData?.length > 0)
      maxMinData?.forEach((item: any) => {
        if (!lookupVariants[`${item?.warehouse_id}`]) lookupVariants[`${item?.warehouse_id}`] = {};
        if (!lookupVariants[`${item?.warehouse_id}`][`${item?.variant_id}`])
          lookupVariants[`${item?.warehouse_id}`][`${item?.variant_id}`] = item;
      });
    return [
      res?.map((it: any) => {
        return {
          warehouse: it?.warehouse_name,
          warehouseId: it?.warehouse_id,
          clientId: it?.variant_client_id,
          productName: it?.product_name,
          productCover: it?.product_cover,
          variantId: it?.ilt_variant_id,
          sellableBegin: lookupVariants?.[it?.warehouse_id]?.[it?.ilt_variant_id]?.sellable_begin,
          sellableEnding: lookupVariants?.[it?.warehouse_id]?.[it?.ilt_variant_id]?.sellable_end,
          damagedBegin: lookupVariants?.[it?.warehouse_id]?.[it?.ilt_variant_id]?.damaged_begin,
          damagedEnding: lookupVariants?.[it?.warehouse_id]?.[it?.ilt_variant_id]?.damaged_end,
          sku: it?.sku,
          category: it?.category_name,
          importSellableSupplier: it?.import_sellable_supplier,
          importSellableReimport: it?.import_sellable_reimport,
          importSellableStocktaking: it?.import_sellable_stocktaking,
          importSellableTransfer: it?.import_sellable_transfer,
          importSellableOthers: it?.import_sellable_others,
          exportSellableSupplier: it?.export_sellable_supplier,
          exportSellableSales: it?.export_sellable_sales,
          exportSellableStocktaking: it?.export_sellable_stocktaking,
          exportSellableTransfer: it?.export_sellable_transfer,
          exportSellableOthers: it?.export_sellable_others,
          importDamagedSupplier: it?.import_damaged_supplier,
          importDamagedReimport: it?.import_damaged_reimport,
          importDamagedStocktaking: it?.import_damaged_stocktaking,
          importDamagedTransfer: it?.import_damaged_transfer,
          importDamagedOthers: it?.import_damaged_others,
          exportDamagedSupplier: it?.export_damaged_supplier,
          exportDamagedClearance: it?.export_damaged_clearance,
          exportDamagedStocktaking: it?.export_damaged_stocktaking,
          exportDamagedTransfer: it?.export_damaged_transfer,
          exportDamagedOthers: it?.export_damaged_others,
          importLostOthers: it?.import_lost,
          exportLostOthers: it?.export_lost,
        } as IDataFluctuationInventory;
      }),
      res?.length,
    ];
  }

  async fluctuationInventoryLogs(
    pagination: PaginationOptions,
    query: FilterFluctuationDetail,
    request,
    header,
  ): Promise<[InventoryLogs[], number]> {
    const sql = await this.getSQLQueryFluctuationInventoryLog(query, request, header);
    sql.addSelect('logs.*');
    sql.addSelect('warehouse.name', 'warehouse_name');
    sql.groupBy('logs.id');
    sql.addGroupBy('warehouse.name');
    sql.orderBy('logs.createdAt', 'DESC');
    if (pagination) {
      sql.take(pagination.limit).skip(pagination.skip);
    }

    const res = await sql.getManyAndCount();
    return res;
  }

  async fluctuationInventorySummary(
    pagination: PaginationOptions,
    query: FilterFluctuation,
    request,
    header,
  ): Promise<any> {
    const [sql, countQuery, subQuery] = await this.getSQLQueryFluctuationInventory(
      query,
      request,
      header,
      true,
    );
    sql.select('COUNT(DISTINCT ilt.variant_id)', 'SKUs');
    sql.addSelect('COUNT(DISTINCT product.name)', 'products');
    sql.addSelect('COUNT(DISTINCT warehouse.name)', 'warehouses');
    sql.addSelect('COUNT(DISTINCT variant.clientId)', 'clients');
    sql.addSelect('COUNT(DISTINCT category.name)', 'categories');
    sql.addSelect('SUM ( ilt.stocked_phy_sellable )', 'stocked_phy_sellable');
    sql.addSelect('SUM ( ilt.import_sellable_supplier )', 'import_sellable_supplier');
    sql.addSelect('SUM ( ilt.import_sellable_reimport )', 'import_sellable_reimport');
    sql.addSelect('SUM ( ilt.import_sellable_stocktaking )', 'import_sellable_stocktaking');
    sql.addSelect('SUM ( ilt.import_sellable_transfer )', 'import_sellable_transfer');
    sql.addSelect('SUM ( ilt.import_sellable_others )', 'import_sellable_others');
    sql.addSelect('SUM ( ilt.export_sellable_supplier )', 'export_sellable_supplier');
    sql.addSelect('SUM ( ilt.export_sellable_sales )', 'export_sellable_sales');
    sql.addSelect('SUM ( ilt.export_sellable_stocktaking )', 'export_sellable_stocktaking');
    sql.addSelect('SUM ( ilt.export_sellable_transfer )', 'export_sellable_transfer');
    sql.addSelect('SUM ( ilt.export_sellable_others )', 'export_sellable_others');

    sql.addSelect('SUM ( ilt.import_damaged_supplier )', 'import_damaged_supplier');
    sql.addSelect('SUM ( ilt.import_damaged_reimport )', 'import_damaged_reimport');
    sql.addSelect('SUM ( ilt.import_damaged_stocktaking )', 'import_damaged_stocktaking');
    sql.addSelect('SUM ( ilt.import_damaged_transfer )', 'import_damaged_transfer');
    sql.addSelect('SUM ( ilt.import_damaged_others )', 'import_damaged_others');
    sql.addSelect('SUM ( ilt.export_damaged_supplier )', 'export_damaged_supplier');
    sql.addSelect('SUM ( ilt.export_damaged_clearance )', 'export_damaged_clearance');
    sql.addSelect('SUM ( ilt.export_damaged_stocktaking )', 'export_damaged_stocktaking');
    sql.addSelect('SUM ( ilt.export_damaged_transfer )', 'export_damaged_transfer');
    sql.addSelect('SUM ( ilt.export_damaged_others )', 'export_damaged_others');
    sql.addSelect('SUM ( ilt.import_lost )', 'import_lost');
    sql.addSelect('SUM ( ilt.export_lost )', 'export_lost');

    const subQR = await getConnection(catalogConnection)
      .createQueryBuilder()
      .from(`(${subQuery.getQuery()})`, 'subq')
      .leftJoin(InventoryLineItem, 'beginTx', '(beginTx.id = subq.minid)')
      .leftJoin(InventoryLineItem, 'endTx', '(endTx.id = subq.maxid)')
      .setParameters(subQuery.getParameters())
      .addSelect('SUM("beginTx".current_sellable)', 'sellable_begin')
      .addSelect(
        'SUM("endTx".current_sellable + "endTx".import_sellable_supplier + "endTx".import_sellable_reimport + "endTx".import_sellable_stocktaking + "endTx".import_sellable_transfer + "endTx".import_sellable_others - "endTx".export_sellable_supplier - "endTx".export_sellable_sales - "endTx".export_sellable_stocktaking - "endTx".export_sellable_transfer - "endTx".export_sellable_others)',
        'end_sellable',
      )
      .addSelect('SUM("beginTx".current_damaged)', 'damaged_begin')
      .addSelect(
        'SUM("endTx".current_damaged + "endTx".import_damaged_supplier + "endTx".import_damaged_reimport + "endTx".import_damaged_stocktaking + "endTx".import_damaged_transfer + "endTx".import_damaged_others - "endTx".export_damaged_supplier - "endTx".export_damaged_clearance - "endTx".export_damaged_stocktaking - "endTx".export_damaged_transfer - "endTx".export_damaged_others)',
        'end_damaged',
      );

    const [data, summary] = await Promise.all([sql.getRawOne(), subQR.getRawOne()]);

    return {
      SKUs: data?.SKUs,
      products: data?.products,
      warehouses: data?.warehouses,
      clients: data?.clients,
      categories: data?.categories,
      sellableBegin: summary?.sellable_begin,
      sellableEnding: summary?.end_sellable,
      damagedBegin: summary?.damaged_begin,
      damagedEnding: summary?.end_damaged,
      importSellableSupplier: data?.import_sellable_supplier,
      importSellableReimport: data?.import_sellable_reimport,
      importSellableStocktaking: data?.import_sellable_stocktaking,
      importSellableTransfer: data?.import_sellable_transfer,
      importSellableOthers: data?.import_sellable_others,
      exportSellableSupplier: data?.export_sellable_supplier,
      exportSellableSales: data?.export_sellable_sales,
      exportSellableStocktaking: data?.export_sellable_stocktaking,
      exportSellableTransfer: data?.export_sellable_transfer,
      exportSellableOthers: data?.export_sellable_others,
      importDamagedSupplier: data?.import_damaged_supplier,
      importDamagedReimport: data?.import_damaged_reimport,
      importDamagedStocktaking: data?.import_damaged_stocktaking,
      importDamagedTransfer: data?.import_damaged_transfer,
      importDamagedOthers: data?.import_damaged_others,
      exportDamagedSupplier: data?.export_damaged_supplier,
      exportDamagedClearance: data?.export_damaged_clearance,
      exportDamagedStocktaking: data?.export_damaged_stocktaking,
      exportDamagedTransfer: data?.export_damaged_transfer,
      exportDamagedOthers: data?.export_damaged_others,
      importLostOthers: data?.import_lost,
      exportLostOthers: data?.export_lost,
    };
  }

  async fluctuationInventoryLogSummary(
    pagination: PaginationOptions,
    query: FilterFluctuationDetail,
    request,
    header,
  ): Promise<any> {
    const sql = await this.getSQLQueryFluctuationInventoryLog(query, request, header);
    sql.select('COUNT(DISTINCT logs.variant_id)', 'variants');
    sql.addSelect('COUNT(DISTINCT logs.actor)', 'actors');
    sql.addSelect('COUNT(DISTINCT logs.warehouse_id)', 'warehouses');
    sql.addSelect('COUNT(DISTINCT logs.inventory)', 'inventories');
    sql.addSelect('COUNT(DISTINCT logs.purpose)', 'remarks');
    sql.addSelect('SUM(logs.quantity)', 'quantity');
    sql.addSelect(`COUNT(DISTINCT TO_CHAR(logs.created_at, 'dd/mm/yyyy'))`, 'days');

    const res = await sql.getRawOne();

    return res;
  }

  async getSQLQueryFluctuationInventoryLog(query: FilterFluctuationDetail, request, header) {
    const { from, to, clientId, variantId, inventory, purpose } = query;
    if (!variantId) throw new BadRequestException('variantId is required');
    const { warehouses, type, companyId, isAdmin, id } = request?.user;

    let warehouseIds =
      query?.warehouseIds?.length > 0 ? query?.warehouseIds?.map(it => Number(it)) : [];

    const sql = this.logsRepository.createQueryBuilder('logs');
    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null) || !companyId)
      throw new ForbiddenException();

    sql.andWhere('logs.variantId = :variantId', { variantId });
    sql.andWhere('logs.companyId = :companyId', { companyId });

    if (from) sql.andWhere('logs.createdAt >= :from', { from });
    if (to) sql.andWhere('logs.createdAt <= :to', { to });
    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');
      if (!isEmpty(whIds)) {
        warehouseIds = intersection(
          warehouseIds,
          whIds?.map(it => Number(it)),
        );
      }
    }
    if (clientId) {
      sql.andWhere('product.clientId = :clientId', { clientId });
    }

    if (purpose?.length > 0)
      sql.andWhere('logs.purpose IN (:...purpose)', { purpose: uniq(purpose) });
    if (inventory?.length > 0)
      sql.andWhere('logs.inventory IN (:...inventory)', { inventory: uniq(inventory) });

    if (
      !isAdmin &&
      !!warehouses &&
      warehouses?.length > 0 &&
      type != $enum(UserType).getKeyOrDefault(UserType.customer, null)
    ) {
      warehouseIds = intersection(
        warehouseIds,
        warehouses?.map(it => Number(it)),
      );
    }

    // console.log(222, warehouseIds);

    if (warehouseIds?.length > 0)
      sql.andWhere('logs.warehouseId in (:...warehouseIds)', { warehouseIds: uniq(warehouseIds) });

    sql.leftJoin('logs.inventoryLineItem', 'lineItem');
    sql.leftJoin('lineItem.inventory', 'st');
    sql.leftJoin('st.variant', 'variant');
    sql.leftJoin('st.warehouse', 'warehouse');
    sql.leftJoin('variant.product', 'product');

    return sql;
  }

  async getSQLQueryFluctuationInventory(
    query: FilterFluctuation,
    request,
    header,
    summary?: boolean,
  ) {
    const { from, to, clientIds, variantIds, categoryIds } = query;
    let warehouseIds =
      query?.warehouseIds?.length > 0 ? query?.warehouseIds?.map(it => Number(it)) : [];

    const { warehouses, type, companyId, isAdmin, id } = request?.user;
    const sql = this.iltRepository.createQueryBuilder('ilt');
    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null))
      throw new ForbiddenException();

    if (variantIds?.length > 0) sql.andWhere('st.variantId IN (:...variantIds)', { variantIds });

    if (companyId) sql.andWhere('st.companyId = :companyId', { companyId });

    if (clientIds?.length > 0) sql.andWhere('product.clientId IN (:...clientIds)', { clientIds });
    if (categoryIds?.length > 0)
      sql.andWhere('product.categoryId IN (:...categoryIds)', { categoryIds });

    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');
      const countryIds = header['country-ids']?.split(',');

      if (!isEmpty(whIds))
        warehouseIds = intersection(
          warehouseIds,
          whIds?.map(it => Number(it)),
        );
      if (!isEmpty(countryIds))
        sql.andWhere('warehouse.countryCode IN (:...countryIds)', { countryIds });
    }

    if (
      !isAdmin &&
      !!warehouses &&
      warehouses?.length > 0 &&
      type != $enum(UserType).getKeyOrDefault(UserType.customer, null)
    ) {
      warehouseIds = intersection(
        warehouseIds,
        warehouses?.map(it => Number(it)),
      );
    }
    if (warehouseIds?.length > 0)
      sql.andWhere('st.warehouseId IN (:...warehouseIds)', { warehouseIds: uniq(warehouseIds) });
    if (from) sql.andWhere('ilt.createdAt >= :from', { from });
    if (to) sql.andWhere('ilt.createdAt <= :to', { to });

    sql.leftJoin('ilt.inventory', 'st');
    sql.leftJoin('st.warehouse', 'warehouse');
    sql.leftJoin('st.variant', 'variant');
    sql.leftJoin('variant.product', 'product');
    sql.leftJoin('product.category', 'category');

    const subQuery = sql.clone();
    const countQuery = sql.clone();

    subQuery.addGroupBy('ilt.variantId');
    subQuery.addGroupBy('ilt.warehouseId');
    subQuery.select('ilt.variantId', 'variantid');
    subQuery.addSelect('ilt.warehouseId', 'warehouseid');
    subQuery.addSelect('MAX ( ilt.ID )', 'maxid');
    subQuery.addSelect('MIN ( ilt.ID )', 'minid');

    if (summary) return [sql, null, subQuery];

    countQuery.select('ilt.variantId');
    countQuery.addGroupBy('ilt.variantId');
    countQuery.addGroupBy('ilt.warehouseId');
    const startEndingQuery = countQuery.clone();

    startEndingQuery.leftJoin(
      '(' + subQuery.getQuery() + ')',
      'subq',
      '(subq.variantId = ilt.variantId AND subq.warehouseid = ilt.warehouseId)',
    );
    startEndingQuery.leftJoin(InventoryLineItem, 'beginTx', '(beginTx.id = subq.minid)');
    startEndingQuery.leftJoin(InventoryLineItem, 'endTx', '(endTx.id = subq.maxid)');

    startEndingQuery.select('ilt.variantId', 'variant_id');
    startEndingQuery.addSelect('ilt.warehouseId', 'warehouse_id');
    startEndingQuery.addSelect('beginTx.currentSellable', 'sellable_begin');
    startEndingQuery.addSelect('beginTx.currentDamaged', 'damaged_begin');
    startEndingQuery.addSelect(
      '(endTx.currentSellable + endTx.importSellableSupplier + endTx.importSellableReimport + endTx.importSellableStocktaking + endTx.importSellableTransfer + endTx.importSellableOthers - endTx.exportSellableSupplier - endTx.exportSellableSales - endTx.exportSellableStocktaking - endTx.exportSellableTransfer - endTx.exportSellableOthers)',
      'sellable_end',
    );
    startEndingQuery.addSelect(
      '(endTx.currentDamaged + endTx.importDamagedSupplier + endTx.importDamagedReimport + endTx.importDamagedStocktaking + endTx.importDamagedTransfer + endTx.importDamagedOthers - endTx.exportDamagedSupplier - endTx.exportDamagedClearance - endTx.exportDamagedStocktaking - endTx.exportDamagedTransfer - endTx.exportDamagedOthers)',
      'damaged_end',
    );
    startEndingQuery.addGroupBy('beginTx.currentSellable');
    startEndingQuery.addGroupBy('beginTx.currentDamaged');

    startEndingQuery.addGroupBy('endTx.currentSellable');
    startEndingQuery.addGroupBy('endTx.currentDamaged');
    startEndingQuery.addGroupBy('endTx.exportSellableSupplier');
    startEndingQuery.addGroupBy('endTx.exportSellableSales');
    startEndingQuery.addGroupBy('endTx.exportSellableStocktaking');
    startEndingQuery.addGroupBy('endTx.exportSellableTransfer');
    startEndingQuery.addGroupBy('endTx.exportSellableOthers');
    startEndingQuery.addGroupBy('endTx.exportDamagedSupplier');
    startEndingQuery.addGroupBy('endTx.exportDamagedClearance');
    startEndingQuery.addGroupBy('endTx.exportDamagedStocktaking');
    startEndingQuery.addGroupBy('endTx.exportDamagedTransfer');
    startEndingQuery.addGroupBy('endTx.exportDamagedOthers');

    startEndingQuery.addGroupBy('endTx.importSellableSupplier');
    startEndingQuery.addGroupBy('endTx.importSellableReimport');
    startEndingQuery.addGroupBy('endTx.importSellableStocktaking');
    startEndingQuery.addGroupBy('endTx.importSellableTransfer');
    startEndingQuery.addGroupBy('endTx.importSellableOthers');
    startEndingQuery.addGroupBy('endTx.importDamagedSupplier');
    startEndingQuery.addGroupBy('endTx.importDamagedReimport');
    startEndingQuery.addGroupBy('endTx.importDamagedStocktaking');
    startEndingQuery.addGroupBy('endTx.importDamagedTransfer');
    startEndingQuery.addGroupBy('endTx.importDamagedOthers');

    sql.select('ilt.variantId');
    sql.addSelect('warehouse.name');
    sql.addSelect('warehouse.id');
    // sql.addSelect(`CONCAT(variant.prefix, '-', variant.sku)`, 'sku');
    sql.addSelect(`variant.sku`, 'sku');
    sql.addSelect('variant.clientId');
    sql.addSelect('product.name');
    sql.addSelect('product.covers');
    sql.addSelect('category.name');

    sql.addSelect('SUM ( ilt.stocked_phy_sellable )', 'stocked_phy_sellable');
    sql.addSelect('SUM ( ilt.import_sellable_supplier )', 'import_sellable_supplier');
    sql.addSelect('SUM ( ilt.import_sellable_reimport )', 'import_sellable_reimport');
    sql.addSelect('SUM ( ilt.import_sellable_stocktaking )', 'import_sellable_stocktaking');
    sql.addSelect('SUM ( ilt.import_sellable_transfer )', 'import_sellable_transfer');
    sql.addSelect('SUM ( ilt.import_sellable_others )', 'import_sellable_others');
    sql.addSelect('SUM ( ilt.export_sellable_supplier )', 'export_sellable_supplier');
    sql.addSelect('SUM ( ilt.export_sellable_sales )', 'export_sellable_sales');
    sql.addSelect('SUM ( ilt.export_sellable_stocktaking )', 'export_sellable_stocktaking');
    sql.addSelect('SUM ( ilt.export_sellable_transfer )', 'export_sellable_transfer');
    sql.addSelect('SUM ( ilt.export_sellable_others )', 'export_sellable_others');

    sql.addSelect('SUM ( ilt.import_damaged_supplier )', 'import_damaged_supplier');
    sql.addSelect('SUM ( ilt.import_damaged_reimport )', 'import_damaged_reimport');
    sql.addSelect('SUM ( ilt.import_damaged_stocktaking )', 'import_damaged_stocktaking');
    sql.addSelect('SUM ( ilt.import_damaged_transfer )', 'import_damaged_transfer');
    sql.addSelect('SUM ( ilt.import_damaged_others )', 'import_damaged_others');
    sql.addSelect('SUM ( ilt.export_damaged_supplier )', 'export_damaged_supplier');
    sql.addSelect('SUM ( ilt.export_damaged_clearance )', 'export_damaged_clearance');
    sql.addSelect('SUM ( ilt.export_damaged_stocktaking )', 'export_damaged_stocktaking');
    sql.addSelect('SUM ( ilt.export_damaged_transfer )', 'export_damaged_transfer');
    sql.addSelect('SUM ( ilt.export_damaged_others )', 'export_damaged_others');
    sql.addSelect('SUM ( ilt.import_lost )', 'import_lost');
    sql.addSelect('SUM ( ilt.export_lost )', 'export_lost');
    sql.addGroupBy('ilt.variantId');
    sql.addGroupBy('ilt.warehouseId');
    sql.addGroupBy('variant.clientId');
    sql.addGroupBy('variant.prefix');
    sql.addGroupBy('variant.sku');
    sql.addGroupBy('warehouse.name');
    sql.addGroupBy('warehouse.id');
    sql.addGroupBy('product.name');
    sql.addGroupBy('product.covers');
    sql.addGroupBy('category.name');

    return [sql, countQuery, startEndingQuery];
  }

  async findAllV2(
    pagination: PaginationOptions,
    query: FilterStock,
    request,
    header,
  ): Promise<[IDataInventory[], number]> {
    const countryIds = header['country-ids']?.split(',');
    const companyId = request?.user?.companyId;
    const queryVariant = this.variantRepository
      .createQueryBuilder('variant')
      .leftJoin('variant.product', 'product')
      .andWhere('product.clientId = :clientId', { clientId: query?.clientId })
      .andWhere('product.country_id IN (:...countryIds)', { countryIds })
      .andWhere('product.biz_id = :companyId', { companyId })
      .andWhere('product.isCombo is false')
      .select('product.id')
      .addSelect('variant.id')
      .addSelect('variant.sku')
      .addSelect('variant.prefix')
      .addSelect('variant.images')
      .addSelect('product.prefix')
      .addSelect('product.sku')
      .addSelect('product.name')
      // .addSelect('product.covers')
      .addSelect('product.status')
      .addSelect('variant.status')
      .addSelect('product.clientId')
      .leftJoin('product.category', 'category')
      .addSelect('category.name')
      .addSelect('category.id')
      .take(pagination.limit ?? 10);

    if (query.status == ProductStatus.active) {
      queryVariant.andWhere('variant.status = :status', { status: query.status });
      queryVariant.andWhere('product.status = :status', { status: query.status });
    } else if (query.status == ProductStatus.disable) {
      queryVariant.andWhere('variant.status = :status', { status: query.status });
    }
    const sql = await this.getSQLQueryInventory(query, request, header);
    if (query?.name)
      queryVariant.andWhere(
        new Brackets(qb => {
          qb.where('variant.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.name ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('variant.name ILIKE :name', { name: `%${query?.name}%` });
          // .orWhere("CONCAT(variant.prefix, '-', variant.sku) ILIKE :name", {
          //   name: `%${query?.name}%`,
          // })
          // .orWhere("CONCAT(product.prefix, '-', product.sku) ILIKE :name", {
          //   name: `%${query?.name}%`,
          // });
        }),
      );
    sql.addSelect('product.id');
    sql.addSelect('product.sku');
    sql.addSelect('product.prefix');
    if (pagination) sql.take(pagination.limit).skip(pagination.skip);
    const [result, variants] = await Promise.all([
      sql.getManyAndCount(),
      query?.clientId &&
      query?.warehouseId &&
      Number(StockTransferPurpose?.[query?.typePurpose]) == StockTransferPurpose.stockTransferred
        ? queryVariant?.getMany()
        : [],
    ]);
    // console.log('🐔  ~ StockInventoryService ~ variants:', variants);

    let whIds = [];
    if (!isNil(header)) {
      whIds = header['warehouse-ids']?.split(',');
    }
    if (!!query?.warehouseId) whIds = [query?.warehouseId];
    let lookup = {};
    let lookupAwReimport = {};
    const lookupInventoryItem = {};
    const proIds = [],
      inventoryIds = [],
      lookupVariant = [];

    (result?.[0] ?? [])?.forEach((item: InventoryManagement) => {
      proIds.push(item?.variantId);
      inventoryIds.push(item?.id);
      lookupVariant[item?.variantId] = item;
    });

    const [productCombos, lastChanges] = await Promise.all([
      proIds?.length > 0
        ? this.pcvRepository
            .createQueryBuilder('pcv')
            .select('variants.id', 'id')
            .leftJoin('pcv.product', 'product')
            .leftJoin('product.variants', 'variants')
            .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: proIds })
            .andWhere('pcv.status = :status', { status: ProductStatus.active })
            .getRawMany()
        : [],
      inventoryIds?.length > 0
        ? this.iltRepository
            .createQueryBuilder('ilt')
            .select('ilt.createdAt', 'day')
            .addSelect('ilt.inventoryId', 'id')
            .where(
              `ilt.id IN (${this.iltRepository
                .createQueryBuilder('ilt')
                .select('max(ilt.id)')
                .where(`ilt.inventoryId IN (${inventoryIds?.join(',')})`)
                .andWhere(`ilt.type = ${InventoryType.order}`)
                .groupBy('ilt.inventoryId')
                .getQuery()})`,
            )
            .getRawMany()
        : [],
    ]);

    lastChanges?.forEach((item: any) => {
      if (!lookupInventoryItem?.[item?.id]) lookupInventoryItem[item?.id] = item;
    });

    if (proIds?.length > 0 && whIds?.length > 0) {
      let products: any = {};
      let dataAwReimport: any = {};
      try {
        const res: { data: { products; dataAwReimport } } = await this.amqpConnection.request({
          exchange: 'ffm-order-external',
          routingKey: 'inbound-reserved-order-2-1',
          payload: {
            ids: proIds,
            companyId: request?.user?.companyId,
            warehouseIds: whIds,
            comboIds:
              compact(productCombos)?.length > 0 ? productCombos?.map((it: any) => it?.id) : [],
          },
          timeout: 10000,
        });
        products = res?.data?.products;
        dataAwReimport = res?.data?.dataAwReimport;

        lookupAwReimport = !isEmpty(dataAwReimport)
          ? dataAwReimport?.reduce((acc, { id, warehouse_id, quantity }) => {
              const key = `${id}_${warehouse_id}`;
              if (!acc[key]) {
                acc[key] = 0;
              }
              acc[key] += quantity;
              return acc;
            }, {})
          : [];
        lookup = products.reduce((acc, { id, status, warehouse_id, quantity }) => {
          const key = `${id}_${warehouse_id}_${status}`;
          if (!acc[key]) {
            acc[key] = 0;
          }
          acc[key] += quantity;
          return acc;
        }, {});
      } catch (error) {
        console.log(error);
      }
    }

    const dataInventory: IDataInventory[] = [];
    if (variants?.length > 0) {
      for (const item of variants) {
        const it = item as ProductVariation;
        if (lookupVariant[it?.id]) {
          const value = await this.parseResultItemInventoryV2(
            lookupVariant[it?.id],
            lookup,
            lookupInventoryItem,
            lookupAwReimport,
          );
          dataInventory.push(value);
        } else {
          dataInventory.push({
            warehouse: '',
            clientId: Number(it?.product?.clientId),
            warehouseId: Number(query?.warehouseId),
            productName: it?.product?.name,
            // productCover: it?.product?.covers,
            images: it?.images,
            productSku: `${it?.product?.sku}`,
            variantId: Number(it?.id),
            productId: Number(it?.product?.id),
            sku: `${it?.sku}`,
            category: it?.product?.category?.name,
            aggImported: 0,
            aggExported: 0,
            inboundReserved: 0,
            sellable: 0,
            stockedPhySellable: 0,
            aggDamaged: 0,
            currentDamaged: 0,
            currentSellable: 0,
            aggLost: 0,
            aggPhyStocked: 0,
            statusSKU: $enum(ProductStatus).getKeyOrDefault(it?.status, null),
            statusProduct: $enum(ProductStatus).getKeyOrDefault(it?.product?.status, null),
            lastSales: null,
            id: -it?.id,
            goodOutShelf: 0,
            inProgress: 0,
            phyGood: 0,
            awReimport: 0,
            isAlreadyExit: false,
          } as IDataInventory);
        }
      }
    } else {
      for (const item of result?.[0]) {
        const value = await this.parseResultItemInventoryV2(
          item,
          lookup,
          lookupInventoryItem,
          lookupAwReimport,
        );
        dataInventory.push(value);
      }
    }
    let resultDataInventory = [];
    if (
      [StockInventoryFilterColumns.lastSales, StockInventoryFilterColumns.awReimport].includes(
        query?.typeSort,
      )
    ) {
      const lastSalesData = dataInventory.filter(x => !isNull(x.lastSales));
      const notLastSalesData = dataInventory.filter(x => isNull(x.lastSales));
      if (query?.sort == SortType.DESC) {
        resultDataInventory = orderBy(
          lastSalesData,
          [StockInventoryFilterColumns[query?.typeSort]],
          ['desc'],
        ).concat(notLastSalesData);
      } else {
        resultDataInventory = notLastSalesData.concat(
          orderBy(
            lastSalesData,
            [StockInventoryFilterColumns[query?.typeSort]],
            ['asc'],
          ) as IDataInventory[],
        );
      }
    } else {
      resultDataInventory = dataInventory;
    }
    return [resultDataInventory, result?.[1]];
  }

  async parseResultItemInventoryV2(
    item: InventoryManagement,
    lookup: any,
    lookupInventoryItem: any,
    lookupAwReimport: any,
  ) {
    const inProgress =
      (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.AwaitingCollection}`] ??
        0) +
      (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Reconfirm}`] ?? 0) +
      (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Confirmed}`] ?? 0);

    const goodOutShelf =
      (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Collecting}`] ?? 0) +
      (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Awaiting3PLPickup}`] ?? 0) +
      (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Canceled}`] ?? 0);

    const inboundReserved = Number(goodOutShelf) + Number(inProgress);
    const sellable = item?.sellableImported - item?.sellableExported - inboundReserved;
    const phyGood = Number(sellable) + Number(inProgress);
    const value: IDataInventory = {
      warehouse: item?.warehouse?.name,
      clientId: Number(item?.variant?.clientId),
      warehouseId: Number(item?.warehouse?.id),
      productName: item?.variant?.product?.name,
      // productCover: item?.variant?.product?.covers,
      images: item?.variant?.images,
      productSku: `${item?.variant?.product?.sku}`,
      variantId: Number(item?.variantId),
      productId: Number(item?.variant?.product?.id),
      sku: `${item?.variant?.sku}`,
      category: item?.variant?.product?.category?.name,
      aggImported: item?.sellableImported,
      aggExported: item?.sellableExported,
      inboundReserved,
      sellable,
      stockedPhySellable: item?.stockedPhySellable,
      aggDamaged: item?.aggDamaged,
      currentDamaged: item?.aggDamaged,
      currentSellable: sellable,
      aggLost: item?.aggLost,
      aggPhyStocked: item?.aggPhyStocked,
      statusSKU: $enum(ProductStatus).getKeyOrDefault(item?.variant?.status, null),
      statusProduct: $enum(ProductStatus).getKeyOrDefault(item?.variant?.product?.status, null),
      lastSales: lookupInventoryItem?.[item?.id]?.day ?? null,
      id: item?.id,
      goodOutShelf,
      inProgress,
      phyGood,
      awReimport: lookupAwReimport[`${item?.variantId}_${item?.warehouseId}`] ?? 0,
      isAlreadyExit: true,
    };
    return value;
  }

  async exportExcel(query: FilterStock, request, header) {
    const sql = await this.getSQLQuery(query, request, header);
    sql.orderBy('st.id', 'ASC');
    if (query?.status) sql.andWhere('product.status = :status', { status: query?.status });
    let result = await sql.getMany();

    let whIds = [];
    if (!isNil(header)) {
      whIds = header['warehouse-ids']?.split(',');
    }
    if (!!query?.warehouseId) whIds = [query?.warehouseId];
    const proIds = (result ?? [])?.map((item: StockInventory) => item?.variantId);
    const lookup = [];
    if (proIds?.length > 0 && whIds?.length > 0) {
      let products: any = {};
      try {
        const res = await this.amqpConnection.request({
          exchange: 'ffm-order-external',
          routingKey: 'count-product-in-order-2-1',
          payload: { ids: proIds, companyId: request?.user?.companyId, warehouseIds: whIds },
          timeout: 10000,
        });
        products = res?.data;
        products.forEach((item: any) => {
          if (!lookup[item?.id])
            lookup[item?.id] = {
              [OrderFFMStatus.Collecting]: 0,
              [OrderFFMStatus.Awaiting3PLPickup]: 0,
              [OrderFFMStatus.Confirmed]: 0,
              [OrderFFMStatus.Reconfirm]: 0,
              [OrderFFMStatus.AwaitingCollection]: 0,
              [OrderFFMStatus.Returned]: 0,
            };
          lookup[item?.id][item?.status] += item?.quantity;
        });
        // console.log(lookup);
      } catch (error) {
        console.log(error);
      }
    }
    if (result?.length > 0)
      result = result?.map((item: StockInventory) => {
        // console.log(item);
        const sellable =
          (lookup?.[item?.variantId]?.[OrderFFMStatus.AwaitingCollection] ?? 0) +
          (lookup?.[item?.variantId]?.[OrderFFMStatus.Reconfirm] ?? 0) +
          (lookup?.[item?.variantId]?.[OrderFFMStatus.Confirmed] ?? 0);
        return {
          ...item,
          phyInProgress: lookup?.[item?.variantId]?.[OrderFFMStatus.Collecting] ?? 0,
          phyAwaitingAllocated: lookup?.[item?.variantId]?.[OrderFFMStatus.Awaiting3PLPickup] ?? 0,
          actualStockedPhy: Number(item?.stockedPhySellable) + sellable,
          phyAwaitingReimport: lookup?.[item?.variantId]?.[OrderFFMStatus.Returned] ?? 0,
        };
      });

    let userIds = [];
    for (const item of result) {
      userIds.push(item?.variant?.product?.clientId);
      userIds.push(item?.variant?.product?.creatorId);
    }
    userIds = uniq(userIds);
    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: userIds },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    const columns = [
      'N.O',
      'Warehouse Name',
      'Client',
      'Product name',
      'SKU',
      'Category',
      'Agg. New Imported',
      'Actual Stocked Phy.',
      'Stocked Phy. Sellable',
      'Phy. In-Progress',
      'Phy. Awaiting Allocated',
      'Phy. Awaiting Reimport',
      'Agg. Actual Good',
      'Agg. Damaged',
      'Agg. Lost',
      'Agg. Stocked Qtt',
      'Created Date',
      'Creator',
    ];
    const data: unknown[][] = [columns];

    for (let index = 0; index < result.length; index++) {
      const item = result[index];
      const rows = [...Array(1)].map(() => Array(columns.length).fill(null));

      try {
        rows[0][0] = index + 1;
        rows[0][1] = item.warehouse.name;
        rows[0][2] = item?.variant?.product?.clientId
          ? userLookup[item?.variant?.product?.clientId]?.name
          : null;
        rows[0][3] = item?.variant?.product?.name;
        rows[0][4] = `${item?.variant?.prefix}-${item?.variant?.sku}`;
        rows[0][5] = item?.variant?.product?.categories?.[0] || 'Other';
        rows[0][6] = item?.aggNewImported?.toLocaleString();
        rows[0][7] = item?.actualStockedPhy?.toLocaleString();
        rows[0][8] = item?.stockedPhySellable?.toLocaleString();
        rows[0][9] = item?.phyInProgress?.toLocaleString();
        rows[0][10] = item?.phyAwaitingAllocated?.toLocaleString();
        rows[0][11] = item?.phyAwaitingReimport?.toLocaleString();
        rows[0][12] = item?.aggActualGood?.toLocaleString();
        rows[0][13] = item?.aggDamaged?.toLocaleString();
        rows[0][14] = item?.aggLost?.toLocaleString();
        rows[0][15] = item?.aggStockedQtt?.toLocaleString();
        rows[0][16] = moment(item.createdAt).format('DD/MM/YY');
        rows[0][17] = item?.variant?.product?.creatorId
          ? userLookup[item?.variant?.product?.creatorId]?.name
          : null;

        data.push(...rows);
      } catch (error) {
        console.log(`error at`, index, result);
        console.log(`error reason`, error);
      }
    }

    const sheetOptions = {
      '!cols': new Array(columns?.length).fill({ wch: 20 }),
    };
    const buffer = xlsx.build([{ name: 'Warehouse inventory', data, options: {} }], {
      sheetOptions,
    }); // Returns a buffer
    return buffer;
  }

  async exportExcelV2(query: FilterStock, request, header) {
    const sql = await this.getSQLQueryInventory(query, request, header);
    // sql.distinctOn(['st.id'])
    sql.addSelect('st.stockedPhySellable');
    sql.addSelect('variant.countryId');
    // sql.leftJoin('st.detail', 'detail', 'detail.inventoryId = st.id AND detail."type" = :type', {
    //   type: InventoryType.order,
    // });
    // sql.addSelect('detail.id');
    // sql.addSelect('detail.createdAt');
    // sql.addSelect('detail.updatedAt');
    // sql.orderBy('st.id')
    // sql.addOrderBy('detail.createdAt', 'DESC');
    const sqlLastItem = sql.clone();
    sqlLastItem
      .select('st.id', 'id')
      .leftJoin('st.detail', 'detail', 'detail.inventoryId = st.id AND detail."type" = :type', {
        type: InventoryType.order,
      });
    sqlLastItem.addSelect('max(detail.createdAt)', 'day');
    sqlLastItem.groupBy('st.id');

    const [result, listItem] = await Promise.all([sql.getMany(), sqlLastItem.getRawMany()]);

    const lookupOrders = [];
    listItem?.forEach((item: any) => {
      if (!lookupOrders?.[item?.id]) lookupOrders[item?.id] = item?.day;
    });

    const inventoryIds = result?.map(x => x.id);
    const items = await this.iltRepository
      .createQueryBuilder('ilt')
      .select('ilt.createdAt', 'day')
      .addSelect('ilt.inventoryId', 'id')
      .where(
        `ilt.id IN (${this.iltRepository
          .createQueryBuilder('ilt')
          .select('max(ilt.id)')
          .where(`ilt.inventoryId IN (${inventoryIds?.join(',')})`)
          .andWhere(`ilt.type = ${InventoryType.order}`)
          .groupBy('ilt.inventoryId')
          .getQuery()})`,
      )
      .getRawMany();
    const lookupInventoryItem = {};
    items?.forEach((item: any) => {
      if (!lookupInventoryItem?.[item?.id]) lookupInventoryItem[item?.id] = item;
    });
    let whIds = [];
    if (!isNil(header)) {
      whIds = header['warehouse-ids']?.split(',');
    }
    if (!!query?.warehouseId) whIds = [query?.warehouseId];
    let lookup = {};
    let lookupAwReimport = {};
    const userIds = [];
    const proIds = [];
    const lastResult = orderBy(result, ['updatedAt'], ['desc']);
    for (const item of result) {
      userIds.push(item?.variant?.clientId);
      proIds.push(item?.variantId);
    }

    const productCombos =
      proIds?.length > 0
        ? await this.pcvRepository
            .createQueryBuilder('pcv')
            .select('variants.id', 'id')
            .leftJoin('pcv.product', 'product')
            .leftJoin('product.variants', 'variants')
            .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: proIds })
            .andWhere('pcv.status = :status', { status: ProductStatus.active })
            .getRawMany()
        : [];

    if (proIds?.length > 0 && whIds?.length > 0) {
      let products: any = {};
      let dataAwReimport: any = {};
      try {
        const res: { data: { products; dataAwReimport } } = await this.amqpConnection.request({
          exchange: 'ffm-order-external',
          routingKey: 'inbound-reserved-order-2-1',
          payload: {
            ids: proIds,
            companyId: request?.user?.companyId,
            warehouseIds: whIds,
            comboIds: productCombos?.length > 0 ? productCombos?.map((it: any) => it?.id) : [],
          },
          timeout: 10000,
        });
        products = res?.data?.products;
        dataAwReimport = res?.data?.dataAwReimport;

        lookupAwReimport = dataAwReimport?.reduce((acc, { id, warehouse_id, quantity }) => {
          const key = `${id}_${warehouse_id}`;
          if (!acc[key]) {
            acc[key] = 0;
          }
          acc[key] += quantity;
          return acc;
        }, {});
        lookup = products.reduce((acc, { id, status, warehouse_id, quantity }) => {
          const key = `${id}_${warehouse_id}_${status}`;
          if (!acc[key]) {
            acc[key] = 0;
          }
          acc[key] += quantity;
          return acc;
        }, {});
      } catch (error) {
        console.log(error);
      }
    }

    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: uniq(userIds) },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    let columns = [
      'N.O',
      'Image',
      'Market',
      'Warehouse',
      'Client',
      'Product name',
      'SKU',
      'Category',
      'Sellable Imported',
      'Sellable Exported',
      'Inbound Reversed',
      'Returned Aw Re-import',
      'Sellable',
      'Agg. Damaged',
      'Agg. Lost',
      'Agg. Stocked Qtt',
      'SKU status',
      'Last Sales Time',
      'ID',
      'VariantID',
      'WarehouseID',
      'Stock',
      'StockSellable',
    ];

    if (!query.image) {
      columns = columns.filter(col => col !== 'Image');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Warehouse inventory');

    worksheet.columns = columns.map(header => ({ header, width: 20 }));
    const imagesArray = [];
    for (let index = 0; index < lastResult.length; index++) {
      const item = lastResult[index];
      const inboundReserved =
        (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.AwaitingCollection}`] ??
          0) +
        (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Reconfirm}`] ?? 0) +
        (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Collecting}`] ?? 0) +
        (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Awaiting3PLPickup}`] ??
          0) +
        (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Returned}`] ?? 0) +
        (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Canceled}`] ?? 0) +
        (lookup[`${item?.variantId}_${item?.warehouseId}_${OrderFFMStatus.Confirmed}`] ?? 0);

      const sellable = item?.sellableImported - item?.sellableExported - inboundReserved;
      try {
        const rowData = [
          index + 1,
          '', // Image column (to be filled later)
          CountryID[item?.variant?.countryId],
          item.warehouse.name,
          item?.variant?.clientId ? userLookup[item?.variant?.clientId]?.name : null,
          '', // Image column (to be filled later if needed)
          item?.variant?.product?.name,
          `${item?.variant?.sku}`,
          item?.variant?.product?.category?.name,
          Number(item?.sellableImported),
          Number(item?.sellableExported),
          Number(inboundReserved),
          !isNaN(lookupAwReimport[`${item?.variantId}_${item?.warehouseId}`])
            ? lookupAwReimport[`${item?.variantId}_${item?.warehouseId}`]
            : 0,
          Number(sellable),
          Number(item?.aggDamaged),
          Number(item?.aggLost),
          Number(item?.aggPhyStocked),
          $enum(ProductStatus).getKeyOrDefault(item?.variant?.status, null),
          lookupOrders?.[item?.id] ? moment(lookupOrders?.[item?.id]).format('DD/MM/YYYY') : '',
          item?.id,
          item?.variantId,
          item?.warehouseId,
          Number(item?.stockedPhySellable),
          Number(sellable),
        ];

        if (!query?.image) {
          rowData.splice(3, 1);
        }

        worksheet.addRow(rowData);

        // Add image if available
        if (item?.variant?.images && item?.variant?.images.length > 0 && query?.image) {
          imagesArray.push({
            image: item?.variant?.images[0],
            rowIndex: index + 2,
          });
        }
      } catch (error) {
        console.log(`error at`, index, result);
        console.log(`error reason`, error);
      }
    }

    // Add images to the worksheet
    const imageColumn = 2;
    if (imagesArray.length > 0) {
      await saveImageToExcel(imagesArray, workbook, worksheet, imageColumn);
    }

    worksheet.getRow(1).font = { bold: true };
    worksheet.autoFilter = {
      from: { row: 1, column: 1 },
      to: { row: 1, column: columns.length },
    };

    return await workbook.xlsx.writeBuffer();
  }

  async fluctuationInventoryExportLogs(query: FilterFluctuationDetail, request, header) {
    const sql = await this.getSQLQueryFluctuationInventoryLog(query, request, header);
    sql.addSelect('logs.*');
    sql.addSelect('warehouse.name', 'warehouse_name');
    // sql.addSelect(`CONCAT(variant.prefix, '-', variant.sku)`, 'sku');
    sql.addSelect(`variant.sku`, 'sku');
    const res = await sql.getMany();

    const columns = [
      'Selected time',
      'SKU',
      'Actor',
      'Warehouse',
      'Inventory',
      'Purpose',
      'Quantity',
      'Reference',
      'Datetime',
    ];
    const data: any[][] = [columns];
    for (let index = 0; index < res.length; index++) {
      const item = res[index];
      const rows = [...Array(1)].map(() => Array(columns.length).fill(null));
      try {
        rows[0][0] = `${moment(query?.from)?.format('DD/MM/YYYY')}-${moment(query?.to)?.format(
          'DD/MM/YYYY',
        )}`;
        rows[0][1] = item?.sku;
        rows[0][2] = item?.actor;
        rows[0][3] = item?.warehouseName;
        rows[0][4] = startCase(item?.inventory);
        rows[0][5] = item?.purpose;
        rows[0][6] = item?.quantity;
        rows[0][7] = item?.reference;
        rows[0][8] = item?.createdAt;
        data.push(...rows);
      } catch (error) {
        console.log(`error at`, index, data);
        console.log(`error reason`, error);
      }
    }
    const sheetOptions = {
      '!cols': new Array(columns?.length).fill({ wch: 20 }),
    };
    const buffer = xlsx.build(
      [{ name: `LogInventory-${moment()?.valueOf()}`, data, options: {} }],
      {
        sheetOptions,
      },
    ); // Returns a buffer
    return buffer;
  }

  async exportFluctuationExcel(query: FilterFluctuation, request, header) {
    const [sql, countQuery, startEndingQuery] = await this.getSQLQueryFluctuationInventory(
      query,
      request,
      header,
      false,
    );
    const [result, maxMinData] = await Promise.all([
      sql.getRawMany(),
      startEndingQuery.getRawMany(),
    ]);

    const lookupVariants = {};
    if (maxMinData?.length > 0)
      maxMinData?.forEach((item: any) => {
        if (!lookupVariants[`${item?.warehouse_id}`]) lookupVariants[`${item?.warehouse_id}`] = {};
        if (!lookupVariants[`${item?.warehouse_id}`][`${item?.variant_id}`])
          lookupVariants[`${item?.warehouse_id}`][`${item?.variant_id}`] = item;
        console.log(item);
      });

    const userIds = [];
    for (const item of result) {
      userIds.push(Number(item?.variant_client_id));
    }

    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: uniq(userIds) },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[Number(item.id)] = item;
        return prev;
      },
      {},
    );

    // console.log(222, userLookup, result);

    let columns = [
      'Date range',
      'Warehouse',
      'Client',
      'Image',
      'Product name',
      'SKU',
      'Category',
      'Beginning',
      'Import Sellable (Supplier)',
      'Import Sellable (Reimport)',
      'Import Sellable (Stocktaking)',
      'Import Sellable (Transfer)',
      'Import Sellable (Others)',
      'Export Sellable (Supplier)',
      'Export Sellable (Sale)',
      'Export Sellable (Stocktaking)',
      'Export Sellable (Transfer)',
      'Export Sellable (Others)',
      'Ending',
    ];

    let columnDamagedLosts = [
      'Date range',
      'Warehouse',
      'Client',
      'Image',
      'Product name',
      'SKU',
      'Category',
      'Beginning',
      'Import Damaged (Supplier)',
      'Import Damaged (Reimport)',
      'Import Damaged (Stocktaking)',
      'Import Damaged (Transfer)',
      'Import Damaged (Others)',
      'Export Damaged (Supplier)',
      'Export Damaged (Clearance)',
      'Export Damaged (Stocktaking)',
      'Export Damaged (Transfer)',
      'Export Damaged (Others)',
      'Import Lost',
      'Export Lost',
      'Ending',
    ];

    if (!query?.image) {
      columns = columns.filter(col => col !== 'Image');
      columnDamagedLosts = columnDamagedLosts.filter(col => col !== 'Image');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(
      `FI-${query?.export == ExportType.sellable ? 'Sellable' : 'Damaged,Lost'}`,
    );

    // Set columns
    worksheet.columns = (query?.export == ExportType.sellable
      ? columns
      : columnDamagedLosts
    ).map(header => ({ header, width: 20 }));

    const imagesArray = [];
    for (let index = 0; index < result.length; index++) {
      const item = result[index];
      console.log(item);
      const row: any[] = [];
      try {
        row[0] = `${moment(query?.from)?.format('DD/MM/YYYY')}-${moment(query?.to)?.format(
          'DD/MM/YYYY',
        )}`;
        row[1] = item.warehouse_name;
        row[2] = item?.variant_client_id ? userLookup[Number(item?.variant_client_id)]?.name : null;
        // row[3] = item?.product_cover;
        let startIdx = query?.image ? 4 : 3;
        row[startIdx] = item?.product_name;
        row[startIdx + 1] = `${item?.sku}`;
        row[startIdx + 2] = item?.category_name;

        if (query?.export == ExportType.sellable) {
          row[startIdx + 3] = lookupVariants[`${item?.warehouse_id}`]?.[
            `${item?.variant_id}`
          ]?.sellable_begin?.toLocaleString();
          row[startIdx + 4] = item?.import_sellable_supplier?.toLocaleString();
          row[startIdx + 5] = item?.import_sellable_reimport?.toLocaleString();
          row[startIdx + 6] = item?.import_sellable_stocktaking?.toLocaleString();
          row[startIdx + 7] = item?.import_sellable_transfer?.toLocaleString();
          row[startIdx + 8] = item?.import_sellable_others?.toLocaleString();
          row[startIdx + 9] = item?.export_sellable_supplier?.toLocaleString();
          row[startIdx + 10] = item?.export_sellable_sales?.toLocaleString();
          row[startIdx + 11] = item?.export_sellable_stocktaking?.toLocaleString();
          row[startIdx + 12] = item?.export_sellable_transfer?.toLocaleString();
          row[startIdx + 13] = item?.export_sellable_others?.toLocaleString();
          row[startIdx + 14] = lookupVariants[`${item?.warehouse_id}`]?.[
            `${item?.variant_id}`
          ]?.sellable_end?.toLocaleString();
        } else {
          row[startIdx + 3] = lookupVariants[`${item?.warehouse_id}`]?.[
            `${item?.variant_id}`
          ]?.damaged_begin?.toLocaleString();
          row[startIdx + 4] = item?.import_damaged_supplier?.toLocaleString();
          row[startIdx + 5] = item?.import_damaged_reimport?.toLocaleString();
          row[startIdx + 6] = item?.import_damaged_stocktaking?.toLocaleString();
          row[startIdx + 7] = item?.import_damaged_transfer?.toLocaleString();
          row[startIdx + 8] = item?.import_damaged_others?.toLocaleString();
          row[startIdx + 9] = item?.export_damaged_supplier?.toLocaleString();
          row[startIdx + 10] = item?.export_damaged_clearance?.toLocaleString();
          row[startIdx + 11] = item?.export_damaged_stocktaking?.toLocaleString();
          row[startIdx + 12] = item?.export_damaged_transfer?.toLocaleString();
          row[startIdx + 13] = item?.export_damaged_others?.toLocaleString();
          row[startIdx + 14] = item?.import_lost?.toLocaleString();
          row[startIdx + 15] = item?.export_lost?.toLocaleString();
          row[startIdx + 16] = lookupVariants[`${item?.warehouse_id}`]?.[
            `${item?.variant_id}`
          ]?.damaged_end?.toLocaleString();
        }
        worksheet.addRow(row);
        if (!!item?.product_cover && query?.image) {
          imagesArray.push({
            image: item?.product_cover[0],
            rowIndex: index + 2,
          });
        }
      } catch (error) {
        console.log(`error at`, index, item);
        console.log(`error reason`, error);
      }
    }

    // Add images to the worksheet
    const imageColumn = 4;
    if (imagesArray.length > 0) {
      await saveImageToExcel(imagesArray, workbook, worksheet, imageColumn);
    }

    // Set header row bold
    worksheet.getRow(1).font = { bold: true };

    // Auto filter
    worksheet.autoFilter = {
      from: {
        row: 1,
        column: 1,
      },
      to: {
        row: 1,
        column: worksheet.columnCount,
      },
    };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getSQLQuery(query: FilterStock, request, header) {
    const {
      name,
      warehouseId,
      cateId,
      filterBy,
      filterRangeFrom,
      filterRangeTo,
      clientIds,
      variantIds,
      warehouseIds,
    } = query;
    let { clientId } = query;
    const { warehouses, type, companyId, isAdmin, id } = request?.user;
    const sql = this.stRepository.createQueryBuilder('st');
    sql.andWhere(
      '((st.aggNewImported <> 0 OR st.stockedPhySellable <> 0 OR st.phyAwaitingReimport <> 0 OR st.aggActualGood <> 0 OR st.aggDamaged <> 0 OR st.aggLost <> 0) OR (st.stockedPhySellable = 0 AND st.phyAwaitingReimport = 0 AND st.aggActualGood = 0 AND st.aggDamaged = 0 AND st.aggLost = 0 AND (st.createdAt != st.updatedAt)))',
    );
    // sql.andWhere('st.stocked_phy_sellable > 0' );
    // sql.andWhere('st.last_editor_id IS NOT NULL' );
    // sql.andWhere('st.item_id IS NOT NULL' );
    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      sql.andWhere('warehouse.type = :typeWarehouse', { typeWarehouse: WarehouseType.main });
    }

    if (warehouseId) sql.andWhere('st.warehouseId = :warehouseId', { warehouseId });

    clientId = type == $enum(UserType).getKeyOrDefault(UserType.customer, null) ? id : clientId;
    // console.log(clientId, type);

    if (clientId) sql.andWhere('product.clientId = :clientId', { clientId });

    if (variantIds?.length > 0) sql.andWhere('st.variantId IN (:...variantIds)', { variantIds });

    if (clientIds?.length > 0) sql.andWhere('product.clientId IN (:...clientIds)', { clientIds });

    if (warehouseIds?.length > 0)
      sql.andWhere('st.warehouseId IN (:...warehouseIds)', { warehouseIds });

    if (cateId) sql.andWhere(':cateId = ANY (product.categories)', { cateId });

    if (companyId) sql.andWhere('st.bizId = :companyId', { companyId });

    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');
      const countryIds = header['country-ids']?.split(',');

      if (!isEmpty(whIds)) sql.andWhere('st.warehouseId IN (:...whIds)', { whIds });
      if (!isEmpty(countryIds))
        sql.andWhere('warehouse.countryCode IN (:...countryIds)', { countryIds });
      if (!isEmpty(countryIds))
        sql.andWhere('product.country_id IN (:...countryIds)', { countryIds });
      if (!isEmpty(countryIds))
        sql.andWhere('variant.country_id IN (:...countryIds)', { countryIds });
    }

    if (
      !isAdmin &&
      !!warehouses &&
      warehouses?.length > 0 &&
      type != $enum(UserType).getKeyOrDefault(UserType.customer, null)
    ) {
      sql.andWhere('st.warehouseId in (:...warehouses)', { warehouses });
    }

    if (name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('variant.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.name ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('variant.name ILIKE :name', { name: `%${query?.name}%` });
          // .orWhere("CONCAT(variant.prefix, '-', variant.sku) ILIKE :name", {
          //   name: `%${query?.name}%`,
          // })
          // .orWhere("CONCAT(product.prefix, '-', product.sku) ILIKE :name", {
          //   name: `%${query?.name}%`,
          // });
        }),
      );
    }

    if (filterBy) {
      if (!isNil(filterRangeFrom)) {
        sql.andWhere(`st.${filterBy} >= ${filterRangeFrom}`);
      }
      if (!isNil(filterRangeTo)) {
        sql.andWhere(`st.${filterBy} <= ${filterRangeTo}`);
      }
    }

    sql.leftJoinAndSelect('st.warehouse', 'warehouse');
    sql.leftJoinAndSelect('st.variant', 'variant');
    sql.leftJoinAndSelect('variant.properties', 'properties');
    sql.leftJoinAndSelect('variant.product', 'product');

    return sql;
  }

  async getSQLQueryInventory(query: FilterStock, request, header) {
    const {
      name,
      warehouseId,
      cateId,
      filterBy,
      filterRangeFrom,
      filterRangeTo,
      clientIds,
      variantIds,
      warehouseIds,
      skus,
      categoryIds,
      getAllCountry,
    } = query;
    let { clientId } = query;
    const { warehouses, type, companyId, isAdmin, id } = request?.user;
    const sql = this.imRepository.createQueryBuilder('st');
    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      sql.andWhere('warehouse.type = :typeWarehouse', { typeWarehouse: WarehouseType.main });
    }

    if (warehouseId) sql.andWhere('st.warehouseId = :warehouseId', { warehouseId });

    clientId = type == $enum(UserType).getKeyOrDefault(UserType.customer, null) ? id : clientId;
    // console.log(clientId, type);

    if (clientId) sql.andWhere('product.clientId = :clientId', { clientId });

    if (variantIds?.length > 0) sql.andWhere('st.variantId IN (:...variantIds)', { variantIds });

    if (clientIds?.length > 0) sql.andWhere('product.clientId IN (:...clientIds)', { clientIds });

    if (warehouseIds?.length > 0)
      sql.andWhere('st.warehouseId IN (:...warehouseIds)', { warehouseIds });

    if (cateId) sql.andWhere('product.categoryId = :cateId', { cateId });
    if (categoryIds) sql.andWhere('product.categoryId IN (:...categoryIds)', { categoryIds });

    if (companyId) sql.andWhere('st.companyId = :companyId', { companyId });

    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');
      const countryIds = header['country-ids']?.split(',');

      if (!isEmpty(whIds) && !getAllCountry)
        sql.andWhere('st.warehouseId IN (:...whIds)', { whIds });
      if (!isEmpty(countryIds) && !getAllCountry)
        sql.andWhere('warehouse.countryCode IN (:...countryIds)', { countryIds });
    }

    // if (
    //   !isAdmin &&
    //   !!warehouses &&
    //   warehouses?.length > 0 &&
    //   type != $enum(UserType).getKeyOrDefault(UserType.customer, null)
    // ) {
    //   sql.andWhere('st.warehouseId in (:...warehouses)', { warehouses });
    // }

    if (name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('variant.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.name ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('product.sku ILIKE :name', { name: `%${query?.name}%` })
            .orWhere('variant.name ILIKE :name', { name: `%${query?.name}%` });
          // .orWhere("CONCAT(variant.prefix, '-', variant.sku) ILIKE :name", {
          //   name: `%${query?.name}%`,
          // })
          // .orWhere("CONCAT(product.prefix, '-', product.sku) ILIKE :name", {
          //   name: `%${query?.name}%`,
          // });
        }),
      );
    }

    if (skus) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('variant.sku IN (:...skus)', { skus });
          // .orWhere('product.sku IN (:...skus)', { skus })
          // .orWhere("CONCAT(variant.prefix, '-', variant.sku) IN (:...skus)", { skus });
          // .orWhere("CONCAT(product.prefix, '-', product.sku) IN (:...skus)", { skus });
        }),
      );
    }

    if (filterBy) {
      switch (filterBy) {
        case StockInventoryFilterColumns.aggDamaged:
          if (!isNil(filterRangeFrom)) {
            sql.andWhere(
              `st.importDamagedOthers +
          st.importDamagedReimport +
          st.importDamagedStocktaking +
          st.importDamagedSupplier +
          st.importDamagedTransfer -
          st.exportDamagedClearance -
          st.exportDamagedOthers -
          st.exportDamagedStocktaking -
          st.exportDamagedSupplier -
          st.exportDamagedTransfer >= :filterRangeFrom`,
              { filterRangeFrom },
            );
          }
          if (!isNil(filterRangeTo)) {
            sql.andWhere(
              `st.importDamagedOthers +
          st.importDamagedReimport +
          st.importDamagedStocktaking +
          st.importDamagedSupplier +
          st.importDamagedTransfer -
          st.exportDamagedClearance -
          st.exportDamagedOthers -
          st.exportDamagedStocktaking -
          st.exportDamagedSupplier -
          st.exportDamagedTransfer <= :filterRangeTo`,
              { filterRangeTo },
            );
          }
          break;
        case StockInventoryFilterColumns.aggLost:
          if (!isNil(filterRangeFrom)) {
            sql.andWhere(
              `st.importLost -
            st.exportLost >= :filterRangeFrom`,
              { filterRangeFrom },
            );
          }
          if (!isNil(filterRangeTo)) {
            sql.andWhere(
              `st.importLost -
            st.exportLost <= :filterRangeTo`,
              { filterRangeTo },
            );
          }
          break;

        case StockInventoryFilterColumns.aggImported:
          if (!isNil(filterRangeFrom)) {
            sql.andWhere(
              `st.importSellableSupplier + st.importSellableReimport + st.importSellableStocktaking + st.importSellableTransfer +
            st.importSellableOthers >= :filterRangeFrom`,
              { filterRangeFrom },
            );
          }
          if (!isNil(filterRangeTo)) {
            sql.andWhere(
              `st.importSellableSupplier + st.importSellableReimport + st.importSellableStocktaking + st.importSellableTransfer +
            st.importSellableOthers <= :filterRangeTo`,
              { filterRangeTo },
            );
          }
          break;
        case StockInventoryFilterColumns.aggExported:
          if (!isNil(filterRangeFrom)) {
            sql.andWhere(
              `st.exportSellableSupplier + st.exportSellableSales + st.exportSellableStocktaking + st.exportSellableTransfer +
              st.exportSellableOthers >= :filterRangeFrom`,
              { filterRangeFrom },
            );
          }
          if (!isNil(filterRangeTo)) {
            sql.andWhere(
              `st.exportSellableSupplier + st.exportSellableSales + st.exportSellableStocktaking + st.exportSellableTransfer +
              st.exportSellableOthers <= :filterRangeTo`,
              { filterRangeTo },
            );
          }
          break;
        case StockInventoryFilterColumns.aggPhyStocked:
          if (!isNil(filterRangeFrom)) {
            sql.andWhere(
              `st.importSellableSupplier + st.importSellableReimport + st.importSellableStocktaking + st.importSellableTransfer +
            st.importSellableOthers - st.exportSellableSupplier - st.exportSellableSales - st.exportSellableStocktaking - st.exportSellableTransfer -
              st.exportSellableOthers + (st.importDamagedOthers +
          st.importDamagedReimport +
          st.importDamagedStocktaking +
          st.importDamagedSupplier +
          st.importDamagedTransfer -
          st.exportDamagedClearance -
          st.exportDamagedOthers -
          st.exportDamagedStocktaking -
          st.exportDamagedSupplier -
          st.exportDamagedTransfer) >= :filterRangeFrom`,
              { filterRangeFrom },
            );
          }
          if (!isNil(filterRangeTo)) {
            sql.andWhere(
              `st.importSellableSupplier + st.importSellableReimport + st.importSellableStocktaking + st.importSellableTransfer +
            st.importSellableOthers - st.exportSellableSupplier - st.exportSellableSales - st.exportSellableStocktaking - st.exportSellableTransfer -
              st.exportSellableOthers + (st.importDamagedOthers +
          st.importDamagedReimport +
          st.importDamagedStocktaking +
          st.importDamagedSupplier +
          st.importDamagedTransfer -
          st.exportDamagedClearance -
          st.exportDamagedOthers -
          st.exportDamagedStocktaking -
          st.exportDamagedSupplier -
          st.exportDamagedTransfer) <= :filterRangeTo`,
              { filterRangeTo },
            );
          }
          break;
        case StockInventoryFilterColumns.stockedPhySellable:
          if (!isNil(filterRangeFrom))
            sql.andWhere(`st.stockedPhySellable >= :filterRangeFrom`, { filterRangeFrom });
          if (!isNil(filterRangeTo))
            sql.andWhere(`st.stockedPhySellable <= :filterRangeTo`, { filterRangeTo });
          break;
      }
    }

    sql.leftJoin('st.warehouse', 'warehouse');
    sql.addSelect('warehouse.name');
    sql.addSelect('warehouse.displayId');
    sql.addSelect('warehouse.id');
    sql.leftJoin('st.variant', 'variant');
    sql.addSelect('variant.sku');
    sql.addSelect('variant.prefix');
    sql.addSelect('variant.status');
    sql.addSelect('variant.id');
    sql.addSelect('variant.clientId');
    sql.addSelect('variant.images');
    // sql.leftJoinAndSelect('variant.properties', 'properties');
    sql.leftJoin('variant.product', 'product');
    sql.addSelect('product.name');
    // sql.addSelect('product.covers');
    sql.addSelect('product.status');
    sql.leftJoin('product.category', 'category');
    sql.addSelect('category.name');
    sql.addSelect('category.id');

    if (query.status == ProductStatus.active) {
      sql.andWhere('variant.status = :status', { status: query.status });
      sql.andWhere('product.status = :status', { status: query.status });
    } else if (query.status == ProductStatus.disable) {
      sql.andWhere('variant.status = :status', { status: query.status });
    }
    sql.addSelect('st.*');
    // sql.leftJoin('st.detail', 'detail', 'detail.inventoryId = st.id AND detail."type" = :type', {
    //   type: InventoryType.order,
    // });
    // sql.addSelect('detail.id');
    // sql.addSelect('detail.createdAt');
    // sql.addSelect('detail.updatedAt');
    sql.addSelect(
      `(
      st.importSellableSupplier + st.importSellableReimport + st.importSellableStocktaking + st.importSellableTransfer + st.importSellableOthers
    )`,
      'sellable_imported',
    );
    sql.addSelect(
      `(
      st.exportSellableSupplier + st.exportSellableSales + st.exportSellableStocktaking + st.exportSellableTransfer + st.exportSellableOthers
    )`,
      'sellable_exported',
    );
    sql.addSelect(
      `(
      st.importDamagedOthers + st.importDamagedReimport + st.importDamagedStocktaking + st.importDamagedSupplier + st.importDamagedTransfer -
      st.exportDamagedClearance - st.exportDamagedOthers - st.exportDamagedStocktaking - st.exportDamagedSupplier - st.exportDamagedTransfer
    )`,
      'agg_damaged',
    );
    sql.addSelect(
      `(
      st.importSellableSupplier + st.importSellableReimport + st.importSellableStocktaking + st.importSellableTransfer + st.importSellableOthers - 
      st.exportSellableSupplier - st.exportSellableSales - st.exportSellableStocktaking - st.exportSellableTransfer - st.exportSellableOthers + 
      (st.importDamagedOthers + st.importDamagedReimport + st.importDamagedStocktaking + st.importDamagedSupplier + st.importDamagedTransfer -
      st.exportDamagedClearance - st.exportDamagedOthers - st.exportDamagedStocktaking - st.exportDamagedSupplier - st.exportDamagedTransfer)
    )`,
      'agg_phy_stocked',
    );
    sql.addSelect(`(st.importLost - st.exportLost)`, 'agg_lost');
    if (
      query?.sort &&
      query?.typeSort &&
      ![StockInventoryFilterColumns.lastSales, StockInventoryFilterColumns.awReimport].includes(
        query?.typeSort,
      )
    ) {
      sql.addOrderBy(
        query?.typeSort == StockInventoryFilterColumns.stockedPhySellable
          ? 'st.stockedPhySellable'
          : query?.typeSort,
        query.sort == SortType.DESC ? 'DESC' : 'ASC',
      );
    }
    sql.addOrderBy('st.updatedAt', 'DESC');

    return sql;
  }

  async exportData(data: StockInventory[]) {
    console.log(data);
    return;
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'return-order-stock',
    queue: 'return-order-change-stock',
    errorHandler: rmqErrorsHandler,
  })
  async returnOrderChangeStockInventory(payload) {
    console.log('payload', 'return-order-stock', payload);
    if (!payload?.id) {
      return new Nack();
    }
    const { id, user, countryId, status, orderStatus, type } = payload;
    const { warehouses, companyId } = user;
    if (!warehouses || !companyId) {
      return new Nack();
    }

    let data: any = {};
    try {
      const res = await this.amqpConnection.request({
        exchange: 'ffm-order-external',
        routingKey: 'get-return-order-2-1',
        payload: { user, id },
        timeout: 20000,
      });
      data = res?.data;
      // console.log('re-import-data', data);
    } catch (error) {
      console.log(payload?.id, 'amqpConnection failed', error);
      // return new Nack();
      throw new Error('');
    }

    if (!data) {
      console.log(payload?.id, 'not found order');
      return new Nack();
    }

    // if(!warehouses.includes(data?.warehouseId?.toString()) && !isAdmin){
    //   console.log('return-order-stock-inventory-error',"Not found warehouse");
    //   return new Nack();
    // }

    const ids = [],
      varIds = [];

    data?.products.forEach((item: any) => {
      ids.push(item?.id);
      varIds.push(item?.productId);
    });

    const products: any[] = [];

    // get product in order
    if (varIds?.length > 0) {
      const lstProd = await this.variantRepository.find({
        where: qb => {
          qb.andWhere({
            id: In(varIds),
          }).andWhere({
            countryId,
          });
        },
        relations: ['product', 'product.combo'],
      });

      // export combo => single product
      data?.products?.forEach((item: any) => {
        const prod = find(lstProd, { id: item?.productId });

        if (prod?.product?.isCombo) {
          prod?.product?.combo?.forEach((el: any) => {
            products.push({
              id: item?.id,
              quantity: el?.qty,
              good: el?.qty * item?.good,
              damaged: el?.qty * item?.damaged + el?.qty * item?.damagedBy3pl,
              lost: el?.qty * item?.lost + el?.qty * item?.lostBy3pl,
              needed: el?.qty * item?.needed,
              productId: el?.variantId,
            });
          });
        } else {
          products.push({
            id: item?.id,
            quantity: 1,
            good: item?.good,
            damaged: item?.damaged + item?.damagedBy3pl,
            lost: item?.lost + item?.lostBy3pl,
            needed: item?.needed,
            productId: item?.productId,
          });
        }
      });
    }
    if (products?.length <= 0) {
      console.log(payload?.id, 'not found product');
      return new Nack();
    }

    const inventories = await this.stRepository.find({
      where: qb => {
        qb.where({
          variantId: In(products?.map((item: any) => item?.productId)),
          warehouseId: data?.warehouseId,
          bizId: companyId,
        });
      },
    });

    const inventoryItems = await this.stiRepository.find({
      where: qb => {
        qb.where({
          parentId: data?.id,
          variantId: In(products?.map((item: any) => item?.productId)),
          type: StockInventoryType.returned,
          itemId: In(ids),
        });
      },
    });

    const params = [],
      totalStock = [];

    for (const e of products) {
      const valid = find(inventoryItems, function(o: StockInventoryItem) {
        return o?.itemId == e?.id && o?.variantId == e?.productId;
      });
      const st = find(inventories, function(o: StockInventory) {
        return o?.variantId == e?.productId;
      });

      let stock = new StockInventory();
      if (!st) {
        stock.estimate = 0;
        stock.availableDamaged = 0;
        stock.availableGood = 0;
        stock.good = 0;
        stock.damaged = 0;
        stock.stockedPhySellable = 0;
        stock.aggActualGood = 0;
        stock.aggDamaged = 0;
        stock.aggNewImported = 0;
        stock.aggStockedQtt = 0;
        stock.saleOut = 0;
        stock.return = 0;
        stock.variantId = e?.productId;
        stock.warehouseId = data?.warehouseId;
        stock.bizId = companyId;

        stock = await this.stRepository.save(stock).catch(err => {
          if (err?.driverError)
            console.log('error-order-stock-inventory', err?.driverError?.detail);
          throw err;
        });
        if (!stock?.id) throw new Error('');
      } else {
        stock = { ...st };
      }

      const item = new StockInventoryItemDto();
      if (!valid) {
        item.parentId = data?.id;
        item.itemId = e?.id;
        item.type =
          type == ReImportTypeEnum.Return
            ? StockInventoryType.reImportOrderReturned
            : StockInventoryType.returned;
        item.estimate = e?.needed;
        item.lastEditorId = data?.lastUpdatedBy;

        item.stockedPhySellable = e?.good;
        item.aggDamaged = 0;
        item.aggLost = 0;
        item.aggActualGood = 0;
        item.phyAwaitingReimport = -e?.needed;
        item.aggNewImported = 0;
        item.aggStockedQtt = -(e?.lost ?? 0);

        if (orderStatus == OrderFFMStatus.Canceled) {
          if (status == OrderFFMStatus.LostByWH) {
            // Lost -> LostByWH
            item.aggActualGood = e?.good ?? 0;
            item.aggDamaged = e?.damaged ?? 0;
            item.aggLost = e?.lost ?? 0;
          } else if (status == OrderFFMStatus.DamagedByWH) {
            // Damaged -> DamagedByWH
            item.aggActualGood = e?.good ?? 0;
            item.aggDamaged = e?.damaged ?? 0;
          }
        }
        if (orderStatus == OrderFFMStatus.InReturn) {
          if (status == OrderFFMStatus.ReturnedStocked) {
            item.aggActualGood = e?.good ?? 0;
            item.aggStockedQtt = e?.good ?? 0;
          } else if ([OrderFFMStatus.ReturnedDamagedBy3PL]?.includes(status)) {
            item.aggDamaged = e?.damaged ?? 0;
            item.phyAwaitingReimport = 0;
            item.aggStockedQtt = e?.damaged;
          } else if ([OrderFFMStatus.DamagedByWH]?.includes(status)) {
            item.aggActualGood = e?.good ?? 0;
            item.aggDamaged = e?.damaged ?? 0;
            item.phyAwaitingReimport = 0;
            item.aggStockedQtt = e?.damaged + e?.good;
            item.aggLost = e?.lost ?? 0;
          } else if ([OrderFFMStatus.LostBy3PL, OrderFFMStatus.LostByWH]?.includes(status)) {
            item.aggActualGood = e?.good ?? 0;
            item.aggDamaged = e?.damaged ?? 0;
            item.aggLost = e?.lost ?? 0;
            item.phyAwaitingReimport = 0;
            item.aggStockedQtt = e?.damaged + e?.good;
          }
        }
        if (orderStatus == OrderFFMStatus.Returned) {
          if ([OrderFFMStatus.ReturnedDamagedBy3PL, OrderFFMStatus.DamagedByWH]?.includes(status)) {
            item.aggActualGood = -(e?.lost + e?.damaged);
            item.aggDamaged = e?.damaged ?? 0;
            item.aggLost = e?.lost ?? 0;
            item.aggStockedQtt = e?.lost;
          } else if ([OrderFFMStatus.LostBy3PL, OrderFFMStatus.LostByWH]?.includes(status)) {
            item.aggActualGood = -(e?.lost + e?.damaged);
            item.aggDamaged = e?.damaged ?? 0;
            item.aggLost = e?.lost ?? 0;
            item.aggStockedQtt = e?.lost;
          }
        }
      }

      item.variantId = e?.productId;
      item.stockInId = stock?.id;
      params.push(item);
      totalStock[stock?.id] = stock.stockedPhySellable + e?.good;
    }

    // console.log('re-import-data', params);

    // const result = { code: null, message: '' };
    if (params?.length > 0) {
      const connection = getConnection(catalogConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        for (const item of params) {
          if (!!item?.id) {
            await queryRunner.manager.update(
              StockInventoryItem,
              { id: item?.id },
              omit(item, ['created_at', 'id', 'updated_at']),
            );
          } else {
            await queryRunner.manager.insert(
              StockInventoryItem,
              plainToInstance(StockInventoryItem, item),
            );
          }
        }
        await queryRunner.commitTransaction();
      } catch (e) {
        await queryRunner.rollbackTransaction();
        console.log('error - Loi khi cap nhat', payload?.id);
        console.log('error - Loi khi cap nhat', e);
        // result.code = StockInventoryErrorCodeType.failed;
        // result.message = 'Trừ tồn không thành công!';
        return {
          status: 500,
          message: e?.driverError?.detail ?? e?.driverError?.error,
        };
      } finally {
        await queryRunner.release();
      }
      const updatedAt = moment()?.valueOf();
      for (const item of params) {
        if (!!totalStock?.[item?.stockInId] && totalStock?.[item?.stockInId] > 0)
          await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
            id: item?.variantId,
            user: {
              companyId,
            },
            warehouseId: data?.warehouseId,
            quantity: totalStock?.[item?.stockInId] ?? 0,
            updatedAt,
          });
      }
    }
    return {
      status: 200,
      message: 'success',
    };
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'purchase-order-stock',
    queue: 'purchase-order-change-stock',
    errorHandler: rmqErrorsHandler,
  })
  async poChangeStockInventory(payload) {
    console.log('payload', 'purchase-order-stock', payload);

    if (!payload?.id) {
      return new Nack();
    }
    const { id, user } = payload;

    const data = await this.poRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
        });
      },
      relations: ['items'],
    });
    // Cộng tồn từ phiếu nhập kho
    const { warehouses, companyId, isAdmin } = user;

    if (!warehouses || !companyId) {
      return new Nack();
    }

    if (!warehouses.includes(data?.warehouseId) && !isAdmin) {
      console.log('update-stock-inventory-error', 'Not found warehouse');
      return new Nack();
    }
    if (!data) {
      console.log('update-stock-inventory-error', 'Not found PO');
      return new Nack();
    }

    const ids = [],
      varIds = [];

    data?.items.forEach(item => {
      ids.push(item?.id);
      varIds.push(item?.variantId);
    });

    // Check tồn của sản phẩm theo kho

    const inventories = await this.stRepository.find({
      where: qb => {
        qb.where({
          variantId: In(varIds),
          warehouseId: data?.warehouseId,
          bizId: companyId,
        });
      },
    });

    // Check xem phiếu nhập đã từng nhập vào kho chưa
    // Nếu mới thì insert ko thì update sl nhập kho vào tồn

    const inventoryItems = await this.stiRepository.find({
      where: qb => {
        qb.where({
          parentId: data?.id,
          type: StockInventoryType.imported,
          itemId: In(ids),
        });
      },
    });

    const params = [],
      totalStock = [];

    for (const e of data?.items) {
      const valid = find(inventoryItems, function(o) {
        return o?.itemId == e?.id;
      });
      const st = find(inventories, function(o) {
        return o?.variantId == e?.variantId;
      });
      let stock = new StockInventory();
      if (!st) {
        stock.estimate = 0;
        stock.availableDamaged = 0;
        stock.availableGood = 0;
        stock.good = 0;
        stock.damaged = 0;
        stock.stockedPhySellable = 0;
        stock.aggActualGood = 0;
        stock.aggDamaged = 0;
        stock.aggNewImported = 0;
        stock.aggStockedQtt = 0;
        stock.saleOut = 0;
        stock.return = 0;
        stock.variantId = e?.variantId;
        stock.warehouseId = data?.warehouseId;
        stock.bizId = companyId;

        stock = await this.stRepository.save(stock).catch(err => {
          if (err?.driverError)
            console.log('error-order-stock-inventory', err?.driverError?.detail);
          throw err;
        });
        if (!stock?.id) throw new Error('');
      } else {
        stock = { ...st };
      }

      let item = new StockInventoryItemDto();
      if (!valid) {
        stock.stockedPhySellable += e?.good;
        item.parentId = data?.id;
        item.itemId = e?.id;
        item.type = StockInventoryType.imported;
        item.estimate = e?.estimate;
        item.lastEditorId = data.lastEditorId;

        item.stockedPhySellable = e?.good;
        item.aggActualGood = e?.good;
        item.aggDamaged = e?.damaged;

        item.aggNewImported = item.aggActualGood + item.aggDamaged;
        item.aggStockedQtt = item.aggActualGood + item.aggDamaged;
      } else {
        stock.stockedPhySellable += e?.good - valid?.good;

        delete valid.createdAt;
        delete valid.updatedAt;

        item = {
          ...valid,
          estimate: e?.estimate,
          lastEditorId: data.lastEditorId,
        };

        item.stockedPhySellable = e?.good;
        item.aggActualGood = e?.good;
        item.aggDamaged = e?.damaged;

        item.aggNewImported = item.aggActualGood + item.aggDamaged;
        item.aggStockedQtt = item.aggActualGood + item.aggDamaged;
      }

      item.variantId = e?.variantId;
      item.stockInId = stock?.id;
      params.push(item);
      totalStock[stock?.id] = stock.stockedPhySellable;
    }
    // console.log(params);

    if (params?.length > 0) {
      const connection = getConnection(catalogConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        for (const item of params) {
          if (!!item?.id) {
            await queryRunner.manager.update(
              StockInventoryItem,
              { id: item?.id },
              omit(item, ['created_at', 'id', 'updated_at']),
            );
          } else {
            await queryRunner.manager.insert(
              StockInventoryItem,
              plainToInstance(StockInventoryItem, item),
            );
          }
        }
        await queryRunner.commitTransaction();
      } catch (e) {
        await queryRunner.rollbackTransaction();
        console.log('error - Loi khi cap nhat', payload?.id);
        console.log('error - Loi khi cap nhat', e);
        return new Nack();
      } finally {
        await queryRunner.release();
      }
    }

    // if(params.length>0) await this.stiRepository.save(params).catch(err => {
    //   if (err?.driverError)
    //     console.log('update-stock-inventory-error',err?.driverError?.detail);
    //     throw err;
    // });
    const updatedAt = moment()?.valueOf();
    for (const item of params) {
      if (!!totalStock?.[item?.stockInId] && totalStock?.[item?.stockInId] > 0)
        await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
          id: item?.variantId,
          user: {
            companyId,
          },
          warehouseId: data?.warehouseId,
          quantity: totalStock?.[item?.stockInId] ?? 0,
          updatedAt,
        });
    }

    return new Nack();
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'stock-taking-stock',
    queue: 'stock-taking-change-stock',
    errorHandler: rmqErrorsHandler,
  })
  async stockTakingChangeStockInventory(payload) {
    console.log('payload', 'stock-taking-stock', payload);
    if (!payload?.id) {
      return new Nack();
    }
    const { id, user } = payload;

    // update tồn kho từ phiếu kiểm kho

    const { companyId } = user;

    const data = await this.stkRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
        });
      },
      relations: ['items'],
    });

    if (!data) return new Nack();
    const ids = [],
      varIds = [];

    data?.items.forEach(item => {
      ids.push(item?.id);
      varIds.push(item?.variantId);
    });

    const inventories = await this.stRepository.find({
      where: qb => {
        qb.where({
          variantId: In(varIds),
          warehouseId: data?.warehouseId,
          bizId: companyId,
        });
      },
    });

    const inventoryItems = await this.stiRepository.find({
      where: qb => {
        qb.where({
          parentId: data?.id,
          // variantId: In(varIds),
          type: StockInventoryType.stockTaking,
          itemId: In(ids),
        });
      },
    });

    const params = [];

    data?.items.forEach((e: StockTakingItem) => {
      const valid = find(inventoryItems, function(o) {
        return o?.itemId == e?.id;
      });

      if (!valid) {
        const st = find(inventories, function(o) {
          return o?.variantId == e?.variantId;
        });
        let stock = new StockInventoryDto();
        if (!st) {
          stock.variantId = e?.variantId;
          stock.warehouseId = data?.warehouseId;
          stock.bizId = companyId;
        } else {
          delete st.createdAt;
          delete st.updatedAt;
          stock = { ...st };
        }

        stock.lastEditorId = data.lastEditorId;

        const item = new StockInventoryItemDto();
        item.stock = stock;
        item.parentId = data?.id;
        item.itemId = e?.id;
        item.type = StockInventoryType.stockTaking;
        item.lastEditorId = data.lastEditorId;
        item.stockedPhySellable = e?.sellable - e?.inventorySellable;
        item.aggActualGood = e?.good - e?.inventoryGood;
        item.aggDamaged = e?.damaged - e?.inventoryDamaged;
        item.aggStockedQtt = item.aggActualGood + item.aggDamaged;
        // item.variantId = e?.variantId;
        params.push(item);
      }
    });

    if (params.length > 0)
      await this.stiRepository.save(params).catch(err => {
        if (err?.driverError)
          console.log('error-stock-taking-stock-inventory', err?.driverError?.detail);
        if (!!data?.id) this.stkRepository.update(data?.id, { status: StockTakingStatus.checking });
        throw err;
      });
    if (!!data?.id) this.stkRepository.update(data?.id, { status: StockTakingStatus.done });
    return new Nack();
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'order-changed-stock',
    queue: 'queue-order-changed-stock',
    errorHandler: rmqErrorsHandler,
  })
  async orderChangeStockInventory(payload) {
    console.log('payload', 'order-changed-stock', payload);

    if (!payload?.id) {
      console.log('missed id');
      return new Nack();
    }
    const {
      id,
      user,
      oldStatus,
      newStatus,
      typeUpdateStatus,
      lastUpdateStatus,
      reason,
      revert,
      revertType,
      rmqErrorAttempts,
      isUpdateStatus3PL,
    } = payload;
    const callBackStatus = payload?.callBackStatus == false ? false : true;

    if (!oldStatus || !newStatus) {
      console.log('newStatus oldStatus');
      return new Nack();
    }

    // update tồn kho từ đơn hàng
    const { companyId } = user;

    let data: any = {};
    try {
      const res = await this.amqpConnection.request({
        exchange: 'ffm-order-external',
        routingKey: 'get-one-order-2-1',
        payload: { user, id },
        timeout: 20000,
      });
      data = res?.data;
    } catch (error) {
      console.log(payload?.id, 'amqpConnection failed', error);
      // return new Nack();
      throw new Error('');
    }

    if (!data) {
      console.log(payload?.id, 'not found order');
      return new Nack();
    }

    const ORDER_STATUS_BEFORE_EXPORT = [
      OrderFFMStatus.Confirmed,
      OrderFFMStatus.Reconfirm,
      OrderFFMStatus.AwaitingCollection,
      OrderFFMStatus.Collecting,
      OrderFFMStatus.Awaiting3PLPickup,
      OrderFFMStatus.PickedUp3PL,
    ];

    const ORDER_STATUS_IN_TRANSIT = [
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.Delivered,
      OrderFFMStatus.AwaitingReturn,
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedCompleted,
      OrderFFMStatus.DeliveredCompleted,
      OrderFFMStatus.DamagedCompleted,
      OrderFFMStatus.LostCompleted,
    ];

    // DVVC khong co API thi trang thai don o 3PL ko tru ton
    const validCarrier = find(CARRIER_CONFIGURATION_BY_COUNTRIES[data?.countryId], {
      code: data?.lastCarrier?.carrier?.code,
    });
    if (
      !!validCarrier &&
      validCarrier?.type == CarrierType.manual &&
      ORDER_STATUS_IN_TRANSIT.includes(newStatus)
    ) {
      console.log(payload?.id, 'carrier manual');
      return new Nack();
    }

    const ids = [],
      varIds = [],
      prodInOd = [];

    data?.products.forEach((item: OrderProduct) => {
      ids.push(item?.id);
      varIds.push(item?.productId);
      prodInOd[item?.productId] = item;
    });

    const products: OrderProduct[] = [];

    // get product in order
    if (varIds?.length > 0) {
      // export combo => single product
      data?.products?.forEach((item: OrderProduct) => {
        if (item?.productDetail.product?.isCombo) {
          item?.productDetail?.product?.combo?.forEach((el: ProductComboVariant) => {
            products.push(
              plainToClass(OrderProduct, {
                id: item?.id,
                quantity: item?.quantity * el?.qty,
                productId: el?.variantId,
              }),
            );
          });
        } else {
          products.push(
            plainToClass(OrderProduct, {
              id: item?.id,
              quantity: item?.quantity,
              productId: item?.productId,
            }),
          );
        }
      });
    }
    if (products?.length <= 0) {
      console.log(payload?.id, 'not found product');
      return new Nack();
    }

    const inventories = await this.stRepository.find({
      where: qb => {
        qb.where({
          variantId: In(products?.map((item: OrderProduct) => item?.productId)),
          warehouseId: data?.warehouseId,
          bizId: companyId,
        });
      },
    });

    const inventoryItems = await this.stiRepository.find({
      where: qb => {
        qb.where({
          parentId: data?.id,
          variantId: In(products?.map((item: OrderProduct) => item?.productId)),
          type: StockInventoryType.order,
          itemId: In(ids),
        });
      },
    });

    const params = [],
      totalStock = [];
    let reConfirmOrderAwaitingStock = false;

    // insert | update inventory
    for (const e of products) {
      const exitInventory = find(inventories, function(o: StockInventory) {
        return o?.variantId == e?.productId;
      });
      let stock = new StockInventory();
      if (!exitInventory) {
        stock.estimate = 0;
        stock.availableDamaged = 0;
        stock.availableGood = 0;
        stock.good = 0;
        stock.damaged = 0;
        stock.saleOut = 0;
        stock.return = 0;
        stock.variantId = e?.productId;
        stock.warehouseId = data?.warehouseId;
        stock.bizId = companyId;
        stock.stockedPhySellable = 0;
        stock.aggActualGood = 0;
        stock.aggDamaged = 0;
        stock.aggNewImported = 0;
        stock.aggStockedQtt = 0;
        stock.aggLost = 0;
        stock.phyAwaitingReimport = 0;
        stock.phyAwaitingAllocated = 0;
        stock.phyInProgress = 0;

        stock = await this.stRepository.save(stock).catch(err => {
          if (err?.driverError)
            console.log('error-order-stock-inventory', err?.driverError?.detail);
          throw err;
        });
        if (!stock?.id) throw new Error('');
      } else {
        stock = { ...exitInventory };
      }

      const exitItemInventory = find(inventoryItems, function(o: StockInventoryItem) {
        return o?.itemId == e?.id && o?.variantId == e?.productId && o?.stockInId == stock?.id;
      });

      let item = new StockInventoryItem();

      if (!!exitItemInventory) {
        delete exitItemInventory.createdAt;
        delete exitItemInventory.updatedAt;

        item = {
          ...exitItemInventory,
          lastEditorId: Number(user?.id) > 0 ? user?.id : data?.lastUpdatedBy,
        };
      } else {
        item.parentId = data?.id;
        item.itemId = e?.id;
        item.lastEditorId = Number(user?.id) > 0 ? user?.id : data?.lastUpdatedBy;
        item.type = StockInventoryType.order;
        item.stockedPhySellable = 0;
        item.aggActualGood = 0;
        item.aggDamaged = 0;
        item.aggNewImported = 0;
        item.aggStockedQtt = 0;
        item.aggLost = 0;
        item.phyAwaitingReimport = 0;
        item.phyAwaitingAllocated = 0;
        item.phyInProgress = 0;
      }

      if (!!revert) {
        if (
          revertType == TypeRevertStatus.api &&
          [OrderFFMStatus.New, OrderFFMStatus.Reconfirm]?.includes(newStatus)
        ) {
          if ([OrderFFMStatus.New]?.includes(newStatus)) {
            item.phyInProgress = 0;
            item.stockedPhySellable = 0;
            item.phyAwaitingAllocated = 0;
          }
        }

        if (
          revertType == TypeRevertStatus.bulkRevertStatus &&
          [
            OrderFFMStatus.Delivered,
            OrderFFMStatus.Returned,
            OrderFFMStatus.InDelivery,
            OrderFFMStatus.InReturn,
            OrderFFMStatus.Awaiting3PLPickup,
          ]?.includes(newStatus)
        ) {
          if ([OrderFFMStatus.InDelivery, OrderFFMStatus.InReturn].includes(newStatus)) {
            if ([OrderFFMStatus.LostByWH, OrderFFMStatus.LostBy3PL].includes(oldStatus)) {
              item.phyAwaitingReimport = 0;
              item.aggActualGood = -e?.quantity;
              item.aggStockedQtt = -e?.quantity;
              item.phyAwaitingAllocated = 0;
              item.phyInProgress = 0;
              item.stockedPhySellable = -e?.quantity;
              item.aggLost = 0;
              item.aggDamaged = 0;
            }
            if (
              [OrderFFMStatus.DamagedByWH, OrderFFMStatus.ReturnedDamagedBy3PL].includes(oldStatus)
            ) {
              item.phyAwaitingReimport = 0;
              item.aggActualGood = -e?.quantity;
              item.aggStockedQtt = -e?.quantity;
              item.phyAwaitingAllocated = 0;
              item.phyInProgress = 0;
              item.stockedPhySellable = -e?.quantity;
              item.aggLost = 0;
              item.aggDamaged = 0;
            }
          } else if (newStatus == OrderFFMStatus.Awaiting3PLPickup) {
            item.phyAwaitingAllocated = e?.quantity;
            item.aggLost = 0;
            item.phyAwaitingReimport = 0;
            item.aggActualGood = 0;
            item.aggStockedQtt = 0;
            item.phyInProgress = 0;
            item.aggDamaged = 0;
            item.aggNewImported = 0;
          } else if (newStatus == OrderFFMStatus.Returned) {
            item.phyAwaitingReimport = e?.quantity;
            item.stockedPhySellable = -e?.quantity;
            item.aggActualGood = 0;
            item.aggStockedQtt = 0;
            item.phyAwaitingAllocated = 0;
            item.phyInProgress = 0;
            item.aggLost = 0;
            item.aggDamaged = 0;
          } else if (newStatus == OrderFFMStatus.Delivered) {
            item.phyAwaitingReimport = 0;
            item.aggActualGood = -e?.quantity;
            item.aggStockedQtt = -e?.quantity;
            item.phyAwaitingAllocated = 0;
            item.phyInProgress = 0;
            item.stockedPhySellable = -e?.quantity;
          }
        }
      } else {
        // DVVC khong co API thi chi tru ton o trang thai cuoi co lien quan den ton kho
        let valid = false;

        switch (newStatus) {
          case OrderFFMStatus.AwaitingCollection:
            if (
              oldStatus == OrderFFMStatus.New ||
              oldStatus == OrderFFMStatus.AwaitingStock ||
              oldStatus == OrderFFMStatus.Draft
            ) {
              valid = true;
              item.stockedPhySellable = -e?.quantity;
            }
            break;
          case OrderFFMStatus.Confirmed:
            if (oldStatus == OrderFFMStatus.New || oldStatus == OrderFFMStatus.AwaitingStock) {
              valid = true;
              item.stockedPhySellable = -e?.quantity;
            }
            break;
          case OrderFFMStatus.Reconfirm:
            if (oldStatus == OrderFFMStatus.AwaitingStock) {
              valid = true;
              item.stockedPhySellable = -e?.quantity;
            }
            break;
          case OrderFFMStatus.Canceled:
            if (oldStatus == OrderFFMStatus.Awaiting3PLPickup) {
              valid = true;
              item.phyAwaitingAllocated = 0;
              item.phyAwaitingReimport = e?.quantity;
            } else if (oldStatus == OrderFFMStatus.Collecting) {
              valid = true;
              item.phyInProgress = 0;
              item.phyAwaitingReimport = e?.quantity;
            } else if (
              oldStatus == OrderFFMStatus.AwaitingCollection ||
              oldStatus == OrderFFMStatus.Confirmed ||
              oldStatus == OrderFFMStatus.Reconfirm
            ) {
              valid = true;
              item.stockedPhySellable = 0;
              reConfirmOrderAwaitingStock = true;
              totalStock[e?.productId] = stock.stockedPhySellable + e?.quantity;
            }
            break;
          case OrderFFMStatus.Collecting:
            if (oldStatus == OrderFFMStatus.AwaitingCollection) {
              valid = true;
              item.phyInProgress = e?.quantity;
            }
            break;
          case OrderFFMStatus.Awaiting3PLPickup:
            if (oldStatus == OrderFFMStatus.Collecting) {
              valid = true;
              item.phyInProgress = 0;
              item.phyAwaitingAllocated = e?.quantity;
            }
            if (oldStatus == OrderFFMStatus.PickedUp3PL) {
              valid = true;
              item.phyAwaitingAllocated = e?.quantity;
              item.aggActualGood = 0;
              item.aggStockedQtt = 0;
            }

            break;
          case OrderFFMStatus.PickedUp3PL:
            if (oldStatus == OrderFFMStatus.Awaiting3PLPickup) {
              valid = true;
              item.phyAwaitingAllocated = 0;
              item.aggActualGood = -e?.quantity;
              item.aggStockedQtt = -e?.quantity;
            }

            break;
          // case OrderFFMStatus.Lost:
          //   if (oldStatus == OrderFFMStatus.Awaiting3PLPickup) {
          //     valid = true;
          //     item.phyAwaitingAllocated = 0;
          //     item.aggActualGood = -e?.quantity;
          //     item.aggStockedQtt = -e?.quantity;
          //     item.aggLost = e?.quantity;
          //   }
          //   break;
          case OrderFFMStatus.LostByWH:
            if (oldStatus == OrderFFMStatus.Awaiting3PLPickup) {
              valid = true;
              item.phyAwaitingAllocated = 0;
              item.aggActualGood = -e?.quantity;
              item.aggStockedQtt = -e?.quantity;
              item.aggLost = e?.quantity;
            }
            if (oldStatus == OrderFFMStatus.Returned) {
              valid = true;
              item.phyAwaitingReimport = 0;
              item.aggActualGood = -e?.quantity;
              item.aggStockedQtt = -e?.quantity;
              item.aggLost = e?.quantity;
            }
            break;
          // case OrderFFMStatus.Damaged:
          //   if (oldStatus == OrderFFMStatus.Awaiting3PLPickup) {
          //     valid = true;
          //     item.phyAwaitingAllocated = 0;
          //     item.aggActualGood = -e?.quantity;
          //     item.aggDamaged = e?.quantity;
          //   }
          //   break;
          case OrderFFMStatus.DamagedByWH:
            if (oldStatus == OrderFFMStatus.Awaiting3PLPickup) {
              valid = true;
              item.phyAwaitingAllocated = 0;
              item.aggActualGood = -e?.quantity;
              item.aggDamaged = e?.quantity;
            }
            if (oldStatus == OrderFFMStatus.Returned) {
              valid = true;
              item.phyAwaitingReimport = 0;
              item.aggActualGood = -e?.quantity;
              item.aggDamaged = e?.quantity;
            }
            break;
          case OrderFFMStatus.LostBy3PL:
            if (
              [
                OrderFFMStatus.PickedUp3PL,
                OrderFFMStatus.InTransit,
                OrderFFMStatus.Stocked3PL,
                OrderFFMStatus.InDelivery,
                OrderFFMStatus.FailedDelivery,
                OrderFFMStatus.AwaitingReturn,
                OrderFFMStatus.Returned,
              ].includes(oldStatus)
            ) {
              valid = true;
              item.aggLost = e?.quantity;
              if (oldStatus == OrderFFMStatus.Returned) {
                item.phyAwaitingReimport = 0;
                item.aggActualGood = -e?.quantity;
                item.aggStockedQtt = -e?.quantity;
              }
            }
            break;
          case OrderFFMStatus.Returned:
            if ([OrderFFMStatus.AwaitingReturn, OrderFFMStatus.InReturn].includes(oldStatus)) {
              valid = true;
              item.phyAwaitingReimport = e?.quantity;
              item.aggActualGood = 0;
              item.aggStockedQtt = 0;
            }

            break;
          // case OrderFFMStatus.ReturnedLost:
          //   if (oldStatus == OrderFFMStatus.Returned) {
          //     valid = true;
          //     item.phyAwaitingReimport = 0;
          //     item.aggActualGood = -e?.quantity;
          //     item.aggStockedQtt = -e?.quantity;
          //     item.aggLost = e?.quantity;
          //   }
          //   break;
          case OrderFFMStatus.ReturnedStocked:
            if (oldStatus == OrderFFMStatus.Returned) {
              valid = true;
              item.phyAwaitingReimport = 0;
              item.stockedPhySellable = 0;
              reConfirmOrderAwaitingStock = true;
              totalStock[e?.productId] = stock.stockedPhySellable + e?.quantity;
            }
            break;
          // case OrderFFMStatus.ReturnedDamaged:
          //   if (oldStatus == OrderFFMStatus.Returned) {
          //     valid = true;
          //     item.phyAwaitingReimport = 0;
          //     item.aggActualGood = -e?.quantity;
          //     item.aggDamaged = e?.quantity;
          //   }
          //   break;
          case OrderFFMStatus.ReturnedDamagedBy3PL:
            if (oldStatus == OrderFFMStatus.Returned) {
              valid = true;
              item.phyAwaitingReimport = 0;
              item.aggActualGood = -e?.quantity;
              item.aggDamaged = e?.quantity;
            }
            break;
        }

        const ORDER_STATUS_3PL = [
          OrderFFMStatus.PickedUp3PL,
          OrderFFMStatus.InTransit,
          OrderFFMStatus.Stocked3PL,
          OrderFFMStatus.InDelivery,
          OrderFFMStatus.FailedDelivery,
          OrderFFMStatus.Delivered,
          OrderFFMStatus.AwaitingReturn,
          OrderFFMStatus.InReturn,
          OrderFFMStatus.Returned,
        ];
        const ALLOW_UPDATE_STATUS_OUT_DELIVERY = [
          OrderFFMStatus.InTransit,
          OrderFFMStatus.Stocked3PL,
          OrderFFMStatus.InDelivery,
          OrderFFMStatus.FailedDelivery,
          OrderFFMStatus.AwaitingReturn,
          OrderFFMStatus.InReturn,
        ];
        // Nhay coc trang thai do 3PL thi tinh lai ton kho
        if (
          !valid &&
          ORDER_STATUS_3PL.includes(newStatus) &&
          [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting].includes(oldStatus)
        ) {
          if (oldStatus == OrderFFMStatus.Awaiting3PLPickup) {
            item.phyAwaitingAllocated = 0;
            item.aggActualGood = -e?.quantity;
            item.aggStockedQtt = -e?.quantity;
          } else if (oldStatus == OrderFFMStatus.Collecting) {
            item.phyInProgress = 0;
            item.aggActualGood = -e?.quantity;
            item.aggStockedQtt = -e?.quantity;
          }
          if (oldStatus != newStatus && newStatus == OrderFFMStatus.Returned) {
            item.phyAwaitingReimport = e?.quantity;
            item.aggActualGood = 0;
            item.aggStockedQtt = 0;
          }
        }
        // Nhay coc trang thai do 3PL -> Returned
        else if (
          oldStatus != newStatus &&
          !valid &&
          newStatus == OrderFFMStatus.Returned &&
          [
            OrderFFMStatus.AwaitingReturn,
            OrderFFMStatus.InDelivery,
            OrderFFMStatus.InTransit,
            OrderFFMStatus.PickedUp3PL,
          ].includes(oldStatus)
        ) {
          item.phyAwaitingReimport = e?.quantity;
          item.stockedPhySellable = -e?.quantity;
          item.aggActualGood = 0;
          item.aggStockedQtt = 0;
          item.phyAwaitingAllocated = 0;
          item.phyInProgress = 0;
        }

        // update trạng thái out for delivery
        if (
          !valid &&
          [OrderFFMStatus.LostBy3PL, OrderFFMStatus.Returned].includes(newStatus) &&
          ALLOW_UPDATE_STATUS_OUT_DELIVERY.includes(oldStatus) &&
          oldStatus != newStatus
        ) {
          if (newStatus == OrderFFMStatus.Returned) {
            item.phyAwaitingReimport += e?.quantity;
            item.aggActualGood += e?.quantity;
            item.aggStockedQtt += e?.quantity;
          } else if (newStatus == OrderFFMStatus.LostBy3PL) {
            item.aggLost += e?.quantity;
          }
        }
      }

      item.variantId = e?.productId;
      item.stockInId = stock?.id;
      item.originStatus = newStatus;

      if (
        item?.lastUpdatedAt > moment(lastUpdateStatus)?.valueOf() &&
        rmqErrorAttempts &&
        isUpdateStatus3PL
      ) {
        return new Nack();
      }

      const conditionUpdate = item?.lastUpdatedAt; //last update item
      if (!!lastUpdateStatus) item.lastUpdatedAt = moment(lastUpdateStatus)?.valueOf(); // new time changes status
      params.push({
        ...item,
        conditionUpdate,
      });
    }

    if (
      [
        OrderFFMStatus.Awaiting3PLPickup,
        OrderFFMStatus.Collecting,
        OrderFFMStatus.AwaitingCollection,
        OrderFFMStatus.PickedUp3PL,
      ]?.includes(newStatus) &&
      [
        OrderFFMStatus.InTransit,
        OrderFFMStatus.Stocked3PL,
        OrderFFMStatus.InDelivery,
        OrderFFMStatus.FailedDelivery,
        OrderFFMStatus.Delivered,
        OrderFFMStatus.AwaitingReturn,
        OrderFFMStatus.InReturn,
        OrderFFMStatus.Returned,
      ]?.includes(oldStatus)
    ) {
      console.log(payload?.id, 'revert 3pl');
      return new Nack();
    }
    // console.log(payload?.id, params);

    if (params?.length > 0) {
      let affected = true;
      const connection = getConnection(catalogConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        for (const item of params) {
          if (!!item?.id) {
            const responseUpdate = await queryRunner.manager.update(
              StockInventoryItem,
              { id: item?.id, lastUpdatedAt: item?.conditionUpdate },
              omit(item, ['created_at', 'id', 'updated_at', 'conditionUpdate']),
            );
            if (responseUpdate?.affected == 0 || !responseUpdate?.affected) affected = false;
          } else {
            await queryRunner.manager.insert(
              StockInventoryItem,
              plainToInstance(StockInventoryItem, item),
            );
          }
        }
        if (!affected) {
          await queryRunner.rollbackTransaction();
          console.log(payload?.id, 'failed affected = 0');
          if (isUpdateStatus3PL) throw new BadRequestException(payload?.id, 'failed affected = 0');
          return new Nack();
        } else {
          await queryRunner.commitTransaction();
        }
      } catch (e) {
        await queryRunner.rollbackTransaction();
        console.log('error - KHONG CAP NHAT DUOC TON KHO', payload?.id);
        console.log('error - KHONG CAP NHAT DUOC TON KHO', e);

        console.log('stock-update-status-order', typeUpdateStatus, callBackStatus);
        if (!typeUpdateStatus && !!callBackStatus) {
          await this.amqpConnection.publish('ffm-order-external', 'stock-update-status-order-2-1', {
            id,
            status: oldStatus,
            user: {
              ...user,
              id: SystemIdEnum.system,
            },
            lastUpdateStatus,
          });
        }

        for (const itCheckConstraint of inventoryItemCheckConstraints) {
          if (e?.message?.includes(itCheckConstraint)) return new Nack();
        }

        for (const stockCheckConstraint of stockCheckConstraints) {
          if (e?.message?.includes(stockCheckConstraint)) return new Nack();
        }

        throw new BadRequestException(e);
      } finally {
        await queryRunner.release();
      }
    }

    console.log('success', payload?.id);
    const _param = !!typeUpdateStatus
      ? {
          id,
          status: newStatus,
          user,
          typeUpdateStatus,
          lastUpdateStatus:
            rmqErrorAttempts > 0 && ORDER_STATUS_BEFORE_EXPORT?.includes(newStatus)
              ? new Date()
              : lastUpdateStatus,
          reason,
        }
      : {
          id,
          status: newStatus,
          user,
          lastUpdateStatus:
            rmqErrorAttempts > 0 && ORDER_STATUS_BEFORE_EXPORT?.includes(newStatus)
              ? new Date()
              : lastUpdateStatus,
        };
    await this.amqpConnection.publish(
      'ffm-order-external',
      'stock-update-status-order-2-1',
      _param,
    );
    const updatedAt = moment()?.valueOf();
    if (
      reConfirmOrderAwaitingStock &&
      [
        OrderFFMStatus.Confirmed,
        OrderFFMStatus.AwaitingCollection,
        OrderFFMStatus.Reconfirm,
        OrderFFMStatus.Returned,
      ]?.includes(oldStatus)
    ) {
      for (const prod of products) {
        if (!!totalStock?.[prod?.productId] && totalStock?.[prod?.productId] > 0)
          await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
            id: prod?.productId,
            user: {
              companyId,
            },
            warehouseId: data?.warehouseId,
            quantity: totalStock?.[prod?.productId] ?? 0,
            updatedAt,
          });
      }
    }

    return new Nack();
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'order-changed-warehouse',
    queue: 'ffm-queue-order-changed-warehouse',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async orderChangedWarehouse({ order, variantIds, user, newWarehouseId, products }) {
    const { companyId, id } = user;
    let result: Record<string, any> = { code: 200 };
    const orderId = order?.id;
    const oldWarehouseId = order?.warehouseId;
    const status = order?.status;

    const sql = this.stiRepository.createQueryBuilder('sti');
    sql.andWhere('sti.variantId in (:...variantIds)', { variantIds });
    sql.andWhere('sti.type = :type', { type: StockInventoryType.order });
    sql.andWhere('sti.parentId = :orderId', { orderId });
    sql.leftJoinAndSelect('sti.stock', 'stock');
    sql.andWhere('stock.warehouseId = :warehouseId', { warehouseId: oldWarehouseId });

    const [items, inventories] = await Promise.all([
      sql.getMany(),
      this.stRepository.find({
        where: qb => {
          qb.where({
            variantId: In(variantIds),
            warehouseId: newWarehouseId,
            bizId: companyId,
          });
        },
      }),
    ]);

    if (
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status) &&
      (inventories?.length == 0 || inventories?.length != variantIds?.length)
    )
      return { code: 'SO_0013', message: 'Sản phẩm không đủ tồn kho' };

    const lookupInv = [];
    inventories.forEach((inv: StockInventory) => {
      lookupInv[inv?.variantId] = inv;
    });

    const params: StockInventoryItem[] = [];
    if (status == OrderFFMStatus.AwaitingStock) {
      for (const prod of products) {
        const product: IOrderProduct = prod;

        let stock = new StockInventory();
        if (!lookupInv[product?.productId]) {
          stock.variantId = product?.productId;
          stock.warehouseId = newWarehouseId;
          stock.bizId = companyId;

          stock = await this.stRepository.save(stock).catch(err => {
            if (err?.driverError)
              console.log('error-order-stock-inventory', err?.driverError?.detail);
            return err;
          });
        } else {
          stock = { ...lookupInv[product?.productId] };
        }

        const item = new StockInventoryItem();
        item.parentId = orderId;
        item.itemId = product?.id;
        item.lastEditorId = id;
        item.type = StockInventoryType.order;
        item.stockedPhySellable = -product?.quantity;

        item.variantId = product?.productId;
        item.stockInId = stock?.id;
        item.originStatus = OrderFFMStatus.Reconfirm.toString();
        item.beforeOrderStatus = status;
        params.push(item);
      }
    } else {
      items.forEach((invt: StockInventoryItem) => {
        if (!!lookupInv[invt?.variantId]) {
          invt.stockInId = lookupInv[invt?.variantId]?.id;
          params.push(invt);
        }
      });
    }
    if (
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status) &&
      items?.length != params?.length
    )
      return { code: 'SO_0013', message: 'Sản phẩm không đủ tồn kho' };

    // console.log(params);
    if (params?.length > 0) {
      const connection = getConnection(catalogConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        for (const item of params) {
          if (item?.id) {
            await queryRunner.manager.update(
              StockInventoryItem,
              { id: item?.id },
              omit(item, ['created_at', 'id', 'updated_at', 'stock']),
            );
          } else {
            await queryRunner.manager.insert(
              StockInventoryItem,
              plainToInstance(StockInventoryItem, item),
            );
          }
        }
        await queryRunner.commitTransaction();
      } catch (e) {
        await queryRunner.rollbackTransaction();
        console.log('error - Khong du ton kho', e);
        result = { code: 'SO_0013', message: 'Sản phẩm không đủ tồn kho' };
      } finally {
        await queryRunner.release();
      }
    }
    // console.log('order-change-warehouse', result);
    return result;
  }

  // @RabbitRPC({
  //   exchange: 'stock-inventory-service',
  //   routingKey: 'order-changed-warehouse-v2',
  //   queue: 'ffm-queue-order-changed-warehouse-v2',
  //   allowNonJsonMessages: true,
  //   errorHandler: defaultNackErrorHandler,
  // })
  // async orderChangedWarehouseV2(payload: IChangeWarehouse) {
  //   const { order, variantIds, newWarehouseId, products, user } = payload;
  //   const { companyId, id } = user;
  //   let result: Record<string, any> = { code: 200 };
  //   const status = order?.status;

  //   const [stockSenders, stockRecipients] = await Promise.all([
  //     [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)
  //       ? this.imRepository.find({
  //           where: qb => {
  //             qb.where({
  //               variantId: In(variantIds),
  //               warehouseId: order.warehouseId,
  //               companyId,
  //             });
  //           },
  //         })
  //       : [],
  //     this.imRepository.find({
  //       where: qb => {
  //         qb.where({
  //           variantId: In(variantIds),
  //           warehouseId: newWarehouseId,
  //           companyId,
  //         });
  //       },
  //     }),
  //   ]);

  //   const lookupSender = [],
  //     lookupRecipient = [];

  //   stockSenders?.forEach((e: InventoryManagement) => {
  //     lookupSender[e.variantId] = e;
  //   });

  //   stockRecipients?.forEach((e: InventoryManagement) => {
  //     lookupRecipient[e.variantId] = e;
  //   });

  //   const params: InventoryLineItem[] = [];
  //   const paramReverts: InventoryLineItem[] = [];
  //   let stateRevert = true;
  //   let stateSaveRecipient = true;

  //   for (const prod of products) {
  //     if ([OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)) {
  //       const stockInventoryOld = lookupSender?.[prod?.productId];
  //       if (!stockInventoryOld?.id) {
  //         stateRevert = false;
  //       } else {
  //         console.log('order-stock', stockInventoryOld);
  //         const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
  //           stockInventoryOld,
  //         );

  //         const stockInventoryItemOld = new InventoryLineItem();
  //         stockInventoryItemOld.originName = `Sales Order ${order?.displayId}`;

  //         stockInventoryItemOld.stockedPhySellable = Number(prod.quantity);

  //         stockInventoryItemOld.inventory = stockInventoryOld;

  //         stockInventoryItemOld.originId = order?.id;
  //         stockInventoryItemOld.originItemId = prod?.id;
  //         stockInventoryItemOld.type = InventoryType.order;

  //         stockInventoryItemOld.currentInventory =
  //           Number(stockInventoryOld.stockedPhySellable) ?? 0;
  //         stockInventoryItemOld.currentSellable = Number(currentSellableOld) ?? 0;
  //         stockInventoryItemOld.currentDamaged = Number(currentDamagedOld) ?? 0;

  //         stockInventoryItemOld.variantId = prod?.productId;
  //         stockInventoryItemOld.warehouseId = order.warehouseId;
  //         stockInventoryItemOld.companyId = companyId?.toString();
  //         stockInventoryItemOld.originUpdatedAt = moment().valueOf();
  //         stockInventoryItemOld.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
  //         console.log('change-warehouse-order-stock', stockInventoryItemOld);
  //         paramReverts.push(stockInventoryItemOld);
  //       }
  //     }
  //     if (stateRevert) {
  //       const stockInventory = lookupRecipient?.[prod?.productId];
  //       if (!stockInventory?.id) {
  //         stateSaveRecipient = false;
  //       } else {
  //         console.log('order-stock', stockInventory);
  //         const [currentSellable, currentDamaged] = await this.parseCurrentInventory(
  //           stockInventory,
  //         );

  //         const stockInventoryItem = new InventoryLineItem();
  //         stockInventoryItem.originName = `Sales Order ${order?.displayId}`;

  //         stockInventoryItem.stockedPhySellable = -Number(prod.quantity);

  //         stockInventoryItem.inventory = stockInventory;

  //         stockInventoryItem.originId = order?.id;
  //         stockInventoryItem.originItemId = prod?.id;
  //         stockInventoryItem.type = InventoryType.order;

  //         stockInventoryItem.currentInventory = Number(stockInventory.stockedPhySellable) ?? 0;
  //         stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
  //         stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;

  //         stockInventoryItem.variantId = prod?.productId;
  //         stockInventoryItem.warehouseId = newWarehouseId;
  //         stockInventoryItem.companyId = companyId?.toString();
  //         stockInventoryItem.originUpdatedAt = moment().valueOf();
  //         stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
  //         console.log('purchase-order-stock', stockInventoryItem);
  //         params.push(stockInventoryItem);
  //       }
  //     }
  //   }
  //   if (!stateRevert || !stateSaveRecipient) {
  //     result = {
  //       code: 500,
  //       message: 'Not found sender | recipient',
  //     };
  //   } else {
  //     // if (!stateSaveRecipient) params = [];

  //     const dataUpdates = concat(params, paramReverts);

  //     if (dataUpdates?.length > 0) {
  //       const res: any = await this.iltRepository.save(dataUpdates).catch(err => {
  //         if (err?.driverError) console.log('change-warehouse-order-stock-error', err?.driverError);
  //         return {
  //           code: 500,
  //           message: err?.driverError?.detail ?? err?.driverError?.error,
  //         };
  //       });
  //       if (res?.code == 500)
  //         result = {
  //           code: 500,
  //           message: res?.message ?? 'Save inventory error',
  //         };
  //       else
  //         result = {
  //           code: 200,
  //         };

  //       // result = {
  //       //   code: 200,
  //       //   status: OrderFFMStatus.AwaitingStock,
  //       // };

  //       // if (paramReverts?.length > 0) {
  //       //   const res: any = await this.iltRepository.save(paramReverts).catch(err => {
  //       //     if (err?.driverError)
  //       //       console.log('change-warehouse-order-stock-error', err?.driverError);
  //       //     return {
  //       //       code: 500,
  //       //       message: err?.driverError?.detail ?? err?.driverError?.error,
  //       //     };
  //       //   });
  //       //   if (res?.code == 500)
  //       //     result = {
  //       //       code: 500,
  //       //       message: "Can't Revert sellable warehouse sender",
  //       //     };
  //       // }

  //       // if (result?.code == 200 && params?.length > 0) {
  //       //   const res: any = await this.iltRepository.save(params).catch(err => {
  //       //     if (err?.driverError)
  //       //       console.log('change-warehouse-order-stock-error', err?.driverError);
  //       //     return {
  //       //       code: 500,
  //       //       message: err?.driverError?.detail ?? err?.driverError?.error,
  //       //     };
  //       //   });

  //       //   if (res?.code == 500)
  //       //     result = {
  //       //       code: 200,
  //       //       message: res?.message,
  //       //       status: OrderFFMStatus.AwaitingStock,
  //       //     };
  //       //   else
  //       //     result = {
  //       //       code: 200,
  //       //     };
  //       // }
  //     } else {
  //       result = {
  //         code: 500,
  //         message: "Can't Revert sellable warehouse sender",
  //       };
  //     }
  //   }
  //   console.log('order-change-warehouse-v2', result);
  //   return result;
  // }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'order-changed-warehouse-v2',
    queue: 'ffm-queue-order-changed-warehouse-v2',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async orderChangedWarehouseV2(payload: IChangeWarehouse) {
    const {
      order,
      variantIds,
      newWarehouseId,
      products,
      user,
      stillChangeWarehouse,
      updatedAt,
      checkUniq,
    } = payload;
    const { companyId, id } = user;
    let result: Record<string, any> = { code: 200 };
    const status = order?.status;

    const [stockSenders, stockRecipients] = await Promise.all([
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)
        ? this.imRepository.find({
            where: qb => {
              qb.where({
                variantId: In(variantIds),
                warehouseId: order.warehouseId,
                companyId,
              });
            },
          })
        : [],
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(variantIds),
            warehouseId: newWarehouseId,
            companyId,
          });
        },
      }),
    ]);

    const lookupSender = [],
      lookupRecipient = [];

    stockSenders?.forEach((e: InventoryManagement) => {
      lookupSender[e.variantId] = e;
    });

    stockRecipients?.forEach((e: InventoryManagement) => {
      lookupRecipient[e.variantId] = e;
    });

    if (
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status) &&
      (stockRecipients?.length == 0 || stockRecipients?.length != uniq(variantIds)?.length)
    ) {
      if (!stillChangeWarehouse) return { code: 'SO_0013', message: 'Sản phẩm không đủ tồn kho' };
    }

    const params: InventoryLineItem[] = [];

    for (const prod of products) {
      const stockInventory = lookupRecipient?.[prod?.productId];

      if (!stockInventory?.id) {
        if (!stillChangeWarehouse) {
          return {
            status: 500,
            message: 'Not found inventory',
          };
        } else {
          if ([OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)) {
            const stockInventoryOld = lookupSender?.[prod?.productId];

            if (!stockInventoryOld?.id) {
              return {
                status: 500,
                message: 'Not found inventory',
              };
            }

            // console.log('order-stock stockInventoryOld', stockInventoryOld);
            const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
              stockInventoryOld,
            );

            const stockInventoryItemOld = new InventoryLineItem();
            stockInventoryItemOld.originName = `Sales Order ${order?.displayId}`;

            stockInventoryItemOld.stockedPhySellable = Number(prod.quantity);

            stockInventoryItemOld.inventory = stockInventoryOld;

            stockInventoryItemOld.originId = order?.id;
            stockInventoryItemOld.originItemId = prod?.id;
            stockInventoryItemOld.type = InventoryType.order;

            stockInventoryItemOld.currentInventory =
              Number(stockInventoryOld.stockedPhySellable) ?? 0;
            stockInventoryItemOld.currentSellable = Number(currentSellableOld) ?? 0;
            stockInventoryItemOld.currentDamaged = Number(currentDamagedOld) ?? 0;

            stockInventoryItemOld.variantId = prod?.productId;
            stockInventoryItemOld.warehouseId = order.warehouseId;
            stockInventoryItemOld.companyId = companyId?.toString();
            stockInventoryItemOld.originUpdatedAt = checkUniq
              ? moment(updatedAt).valueOf() + 10
              : moment(updatedAt).valueOf();
            stockInventoryItemOld.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
            // console.log('change-warehouse-order-stock', stockInventoryItemOld);
            params.push(stockInventoryItemOld);
          }
        }
      } else {
        if (!stillChangeWarehouse) {
          // console.log('order-stock stockInventory 2', stockInventory);
          const [currentSellable, currentDamaged] = await this.parseCurrentInventory(
            stockInventory,
          );

          const stockInventoryItem = new InventoryLineItem();
          stockInventoryItem.originName = `Sales Order ${order?.displayId}`;

          stockInventoryItem.stockedPhySellable = -Number(prod.quantity);

          stockInventoryItem.inventory = stockInventory;

          stockInventoryItem.originId = order?.id;
          stockInventoryItem.originItemId = prod?.id;
          stockInventoryItem.type = InventoryType.order;

          stockInventoryItem.currentInventory = Number(stockInventory.stockedPhySellable) ?? 0;
          stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
          stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;

          stockInventoryItem.variantId = prod?.productId;
          stockInventoryItem.warehouseId = newWarehouseId;
          stockInventoryItem.companyId = companyId?.toString();
          stockInventoryItem.originUpdatedAt = moment(updatedAt).valueOf();
          stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
          // console.log('purchase-order-stock 2', stockInventoryItem);
          params.push(stockInventoryItem);

          if ([OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)) {
            const stockInventoryOld = lookupSender?.[prod?.productId];

            if (!stockInventoryOld?.id) {
              return {
                status: 500,
                message: 'Not found inventory',
              };
            }

            // console.log('order-stock stockInventoryOld 2', stockInventoryOld);
            const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
              stockInventoryOld,
            );

            const stockInventoryItemOld = new InventoryLineItem();
            stockInventoryItemOld.originName = `Sales Order ${order?.displayId}`;

            stockInventoryItemOld.stockedPhySellable = Number(prod.quantity);

            stockInventoryItemOld.inventory = stockInventoryOld;

            stockInventoryItemOld.originId = order?.id;
            stockInventoryItemOld.originItemId = prod?.id;
            stockInventoryItemOld.type = InventoryType.order;

            stockInventoryItemOld.currentInventory =
              Number(stockInventoryOld.stockedPhySellable) ?? 0;
            stockInventoryItemOld.currentSellable = Number(currentSellableOld) ?? 0;
            stockInventoryItemOld.currentDamaged = Number(currentDamagedOld) ?? 0;

            stockInventoryItemOld.variantId = prod?.productId;
            stockInventoryItemOld.warehouseId = order.warehouseId;
            stockInventoryItemOld.companyId = companyId?.toString();
            stockInventoryItemOld.originUpdatedAt = checkUniq
              ? moment(updatedAt).valueOf() + 10
              : moment(updatedAt).valueOf();
            stockInventoryItemOld.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
            // console.log('change-warehouse-order-stock 2', stockInventoryItemOld);
            params.push(stockInventoryItemOld);
          }
        } else {
          if ([OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)) {
            const stockInventoryOld = lookupSender?.[prod?.productId];

            if (!stockInventoryOld?.id) {
              return {
                status: 500,
                message: 'Not found inventory',
              };
            }

            // console.log('order-stock stockInventoryOld 3', stockInventoryOld);
            const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
              stockInventoryOld,
            );

            const stockInventoryItemOld = new InventoryLineItem();
            stockInventoryItemOld.originName = `Sales Order ${order?.displayId}`;

            stockInventoryItemOld.stockedPhySellable = Number(prod.quantity);

            stockInventoryItemOld.inventory = stockInventoryOld;

            stockInventoryItemOld.originId = order?.id;
            stockInventoryItemOld.originItemId = prod?.id;
            stockInventoryItemOld.type = InventoryType.order;

            stockInventoryItemOld.currentInventory =
              Number(stockInventoryOld.stockedPhySellable) ?? 0;
            stockInventoryItemOld.currentSellable = Number(currentSellableOld) ?? 0;
            stockInventoryItemOld.currentDamaged = Number(currentDamagedOld) ?? 0;

            stockInventoryItemOld.variantId = prod?.productId;
            stockInventoryItemOld.warehouseId = order.warehouseId;
            stockInventoryItemOld.companyId = companyId?.toString();
            stockInventoryItemOld.originUpdatedAt = checkUniq
              ? moment(updatedAt).valueOf() + 10
              : moment(updatedAt).valueOf();
            stockInventoryItemOld.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
            // console.log('change-warehouse-order-stock 3', stockInventoryItemOld);
            params.push(stockInventoryItemOld);
          }
        }
      }
    }

    // console.log(params);
    if (params?.length > 0) {
      const res: any = await this.iltRepository.save(params).catch(err => {
        if (err?.driverError) console.log('change-warehouse-order-stock-error', err?.driverError);
        return {
          status: 500,
          message: err?.driverError?.detail ?? err?.driverError?.error,
        };
      });

      if (res?.status == 500) result = res;
    }
    // console.log('order-change-warehouse-v2', result);
    return result;
  }

  async updateInventory(request, data: StockInventoryDto): Promise<any> {
    // let { warehouses, type, companyId } = request?.user;

    const sql = this.stRepository.createQueryBuilder('st');
    sql.where('st.bizId = :companyId', { companyId: data?.bizId });
    sql.andWhere('st.variantId = :variantId', { variantId: data?.variantId });
    sql.andWhere('st.warehouseId = :warehouseId', { warehouseId: data?.warehouseId });

    const inventory = await sql.getOne();
    if (!inventory) throw new BadRequestException('');

    await this.stRepository
      .update(inventory?.id, {
        aggNewImported: data?.aggNewImported ?? 0,
        stockedPhySellable: data?.stockedPhySellable ?? 0,
        phyInProgress: data?.phyInProgress ?? 0,
        phyAwaitingAllocated: data?.phyAwaitingAllocated ?? 0,
        phyAwaitingReimport: data?.phyAwaitingReimport ?? 0,
        aggActualGood: data?.aggActualGood ?? 0,
        aggDamaged: data?.aggDamaged ?? 0,
        aggLost: data?.aggLost ?? 0,
        aggStockedQtt: data?.aggStockedQtt ?? 0,
        itemId: '',
        lastEditorId: SystemIdEnum.system?.toString(),
      })
      .catch(err => {
        if (err?.driverError) console.log('error-fix-inventory', err?.driverError?.detail);
        throw err;
      });

    return inventory;
  }

  async detail(id: number, request, header): Promise<StockInventory[]> {
    const { companyId } = request?.user;

    const sql = this.stRepository.createQueryBuilder('st');
    sql.where('st.bizId = :companyId', { companyId });
    sql.andWhere('st.variantId = :variantId', { variantId: id });
    if (!!header['country-ids']) {
      sql.andWhere('warehouse.countryCode = :countryCode', { countryCode: header['country-ids'] });
      sql.andWhere('variant.countryId = :countryCode', { countryCode: header['country-ids'] });
    }

    sql.leftJoinAndSelect('st.warehouse', 'warehouse');
    sql.leftJoinAndSelect('st.variant', 'variant');
    sql.leftJoinAndSelect('variant.properties', 'properties');
    sql.leftJoinAndSelect('variant.product', 'product');
    // sql.leftJoinAndSelect('st.detail','logs');

    return sql.getMany();
  }

  async histories(
    id: number,
    request: any,
    header: any,
    filter: FilterStock,
    pagination: PaginationOptions,
  ): Promise<any> {
    const _INVENTORY: string[] = [
      'agg_new_imported',
      'stocked_phy_sellable',
      'phy_in_progress',
      'phy_awaiting_allocated',
      'phy_awaiting_reimport',
      'agg_actual_good',
      'agg_damaged',
      'agg_lost',
      'agg_stocked_qtt',
    ];

    const { type } = request?.user;
    const countryCode = header['country-ids'];

    let { from, to } = filter;

    if (!from)
      from = new Date(
        moment()
          .add(-1, 'day')
          .startOf('day')
          ?.valueOf(),
      );
    if (!to)
      to = new Date(
        moment()
          .endOf('day')
          ?.valueOf(),
      );

    const queryParent = await this.stRepository.createQueryBuilder('si');

    queryParent.leftJoin(SlotWarehouses, 'wh', 'wh.id = si.warehouse_id');

    queryParent.leftJoin(
      Logs,
      'logs',
      '(logs.record_id = si.id::TEXT AND logs."table_name" = \'stock_inventory\')',
    );

    queryParent
      .where('si.variant_id = :variantId', { variantId: id })
      .andWhere('wh.country_code = :countryCode', { countryCode })
      .andWhere("logs.action != 'INSERT'");

    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      queryParent.andWhere('wh.type = :typeWarehouse', { typeWarehouse: WarehouseType.main });
    }

    if (!!filter?.warehouseId)
      queryParent.andWhere('wh.id = :warehouseId', { warehouseId: filter?.warehouseId });

    if (from) queryParent.andWhere('logs.created_at >= :from', { from });
    if (to) queryParent.andWhere('logs.created_at <= :to', { to });
    const queryParentCount = queryParent.clone();

    queryParent
      .select('si.id', 'id')
      .addSelect('logs.id', 'lid')
      .addSelect('si.variant_id', 'variant')
      .addSelect('wh.name', 'warehouse')
      .addSelect('logs.creator_id', 'uid')
      .addSelect('logs.after_changes', 'after')
      .addSelect('logs.changes', 'changes')
      .addSelect('logs.before_changes', 'before')
      .addSelect('logs.children_id', 'child')
      .addSelect('logs.created_at', 'time')
      .andWhere('logs.changes && :values::text[]', { values: _INVENTORY })
      .orderBy('logs.created_at', 'DESC');

    queryParentCount.andWhere('logs.changes && :values::text[]', { values: _INVENTORY });

    if (pagination) queryParent.offset(pagination?.skip).limit(pagination?.limit);

    queryParentCount.select('count(*)', 'total');

    const [resultParent, countData] = await Promise.all([
      queryParent.getRawMany(),
      queryParentCount.getRawOne(),
    ]);
    const total = countData?.total ?? 0;

    const itemIds = compact(resultParent?.map((item: any) => item?.child));
    let result = [];
    if (!isEmpty(itemIds)) {
      const query = await this.stRepository
        .createQueryBuilder('si')
        .select('si.id', 'id')
        .addSelect('si.variant_id', 'variant')
        .addSelect('wh.name', 'warehouse')
        .addSelect('sti.type', 'type')
        .addSelect('sti.id', 'sti')
        .addSelect('sti.parent_id', 'origin')
        .addSelect('po.code', 'pocode')
        .addSelect("concat(sta.id, ' - ', sta.name)", 'stname')
        .addSelect('logs.changes', 'after')
        .addSelect('logs.creator_id', 'uid')
        .addSelect('logs.before_changes', 'before')
        .addSelect('logs.created_at', 'time');

      query.leftJoin(StockInventoryItem, 'sti', 'sti.stock_id = si.id');

      query.leftJoin(SlotWarehouses, 'wh', 'wh.id = si.warehouse_id');

      query.leftJoin(
        Logs,
        'logs',
        "(logs.record_id = sti.id::TEXT AND logs.table_name = 'stock_inventory_item')",
      );

      query.leftJoin(PurchaseOrder, 'po', 'sti.parent_id = po.id AND sti."type" = 1');

      query.leftJoin(StockTaking, 'sta', 'sti.parent_id = sta.id AND sti."type" = 3');

      query
        .where('si.variant_id = :variantId', { variantId: id })
        .andWhere('wh.country_code = :countryCode', { countryCode })
        .andWhere("(sti.type != 1 OR (logs.action != 'INSERT' AND sti.type = 1))")
        // .orderBy('si.warehouse_id')
        .orderBy('logs.created_at', 'DESC');

      if (!!filter?.warehouseId)
        query.andWhere('wh.id = :warehouseId', { warehouseId: filter?.warehouseId });

      // if (from) query.andWhere('logs.created_at >= :from', { from });
      // if (to) query.andWhere('logs.created_at <= :to', { to });
      if (!!resultParent) {
        query.andWhere('sti.id in (:...itemIds)', { itemIds: uniq(itemIds) });
      }
      // if (pagination) query.offset(pagination?.skip)
      // .limit(pagination?.limit+20);

      result = itemIds?.length > 0 ? await query.getRawMany() : [];
    }

    // console.log(result);

    const reImportIds = [];
    const oIds = result?.map((item: any) => {
      if (
        [StockInventoryType.returned, StockInventoryType.reImportOrderReturned]?.includes(
          item?.type,
        )
      )
        reImportIds.push(item?.origin);
      if (item?.type == StockInventoryType.order) return item?.origin;
    });

    const ods = isEmpty(oIds)
      ? []
      : await this.odRepository.find({
          where: qb => {
            qb.andWhere({
              id: In(uniq(compact(oIds))),
            });
          },
          select: ['displayId', 'id'],
        });

    let reImports: any = {};
    if (reImportIds?.length > 0) {
      try {
        const res = await this.amqpConnection.request({
          exchange: 'ffm-order-external',
          routingKey: 're-import-order-cancel-2-1',
          payload: { ids: reImportIds, user: request?.user },
          timeout: 10000,
        });
        reImports = res?.data;
        reImports.forEach((item: any) => {
          ods.push(
            plainToInstance(Order, {
              id: `re-import-${item?.id}`,
              displayId: item?.orderDisplayId,
            }),
          );
        });
      } catch (error) {
        console.log(error);
      }
    }

    const parse = await HistoryLogUtils.inventory(
      result,
      concat(_INVENTORY, ['origin_status']),
      StockInventoryName.inventoryItem,
      ods,
      _INVENTORY,
    );
    const parseParent = await HistoryLogUtils.inventory(
      resultParent,
      _INVENTORY,
      StockInventoryName.inventory,
      [],
      _INVENTORY,
    );

    let res = compact(concat(parse, parseParent));
    // res = sortBy(res, ['time','sort','agg_new_imported','stocked_phy_sellable','phy_in_progress','phy_awaiting_allocated','phy_awaiting_reimport','agg_actual_good','agg_damaged']);
    res = sortBy(res, ['time', 'sort']);

    // console.log('parseParent', parseParent);

    let data = [];
    res?.forEach((item: any) => {
      if (item?.sort == StockInventoryName.inventory) {
        // item.timeSort = moment(moment(item?.createAt)?.format('YYYY-MM-DD HH:mm'))?.valueOf();
        data.push(item);
      } else if (item?.sort == StockInventoryName.inventoryItem) {
        const index = findIndex(data, function(o) {
          return (
            Number(o.child) == Number(item?.sti) &&
            o.sort == StockInventoryName.inventory &&
            o.time >= item?.time - 1000 &&
            o.time <= item?.time + 1000
          );
        });

        if (index > -1) {
          if (!data[index]?.children) data[index].children = [];
          data[index].children?.push(item);
          data[index].reason = item?.reason;
          data[index].uid = item?.uid;
          data[index].type = item?.type;
          data[index].status = item?.status;
        }
      }
    });
    data = sortBy(data, ['lid']);
    reverse(data);
    return {
      result: data,
      count: total,
    };
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'revert-order',
    queue: 'revert-order',
    errorHandler: rmqErrorsHandler,
  })
  async revertOrder(payload) {
    console.log('payload', 'revert-order', payload);

    if (!payload?.parentId || !payload?.productId || !payload?.quantity) {
      return new Nack();
    }

    const { parentId, productId, quantity } = payload;
    const stis = await this.stiRepository
      .createQueryBuilder('sti')
      .where('sti.parent_id = :parentId', { parentId })
      .andWhere('sti.item_id = :productId', { productId })
      .getOne();

    const result = await this.stiRepository
      .save({
        ...stis,
        stockedPhySellable: Number(-quantity),
        phyInProgress: 0,
        phyAwaitingAllocated: Number(quantity),
        phyAwaitingReimport: 0,
        aggActualGood: 0,
        aggDamaged: 0,
        aggLost: 0,
        aggStockedQtt: 0,
      })
      .catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });

    return result;
  }

  async importStockInventory(
    data: PurchaseOrder,
    user: Record<string, any>,
    time: number,
  ): Promise<IStockImport> {
    console.log('payload', 'purchase-order-stock-v2', { data, user, time });

    if (!data?.id || !data) {
      return {
        status: 500,
        message: 'Not found PO',
      };
    }
    const { warehouses, companyId, isAdmin } = user;

    if (!warehouses || !companyId) {
      return {
        status: 500,
        message: 'Not found warehouse or company',
      };
    }

    if (!warehouses.includes(data?.warehouseId) && !isAdmin) {
      return {
        status: 500,
        message: 'Not found warehouse',
      };
    }

    const varIds = [];

    data?.items.forEach(item => {
      varIds.push(item?.variantId);
    });
    const [inventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(varIds),
            warehouseId: data?.warehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: uniq(varIds) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);

    console.log('purchase-order-stock', inventories, data?.items);

    const params = [],
      comboLookups = [],
      totalStock = [];

    productCombos?.forEach((item: any) => {
      if (!comboLookups[Number(item?.variant_id)]) comboLookups[item?.variant_id] = [];
      comboLookups[Number(item?.variant_id)].push(item?.id);
    });

    for (const e of data?.items) {
      let stockInventory = find(inventories, function(o) {
        return o?.variantId == e?.variantId;
      });

      console.log('purchase-order-stock', stockInventory);
      let currentSellable = 0,
        currentDamaged = 0;
      if (!stockInventory?.id) {
        stockInventory = new InventoryManagement();
        stockInventory.companyId = companyId?.toString();
        stockInventory.stockedPhySellable = 0;
        stockInventory.lastEditorId = user?.id;
        stockInventory.warehouseId = data?.warehouseId;
        stockInventory.variantId = e?.variantId;
      } else {
        const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
          stockInventory,
        );
        currentSellable = currentSellableOld;
        currentDamaged = currentDamagedOld;
      }

      const stockInventoryItem = new InventoryLineItem();
      stockInventoryItem.originName = `Import Receipt: [${data?.code}]`;
      stockInventoryItem.stockedPhySellable = e?.newestGood ?? 0;
      stockInventoryItem.importSellableSupplier = e?.newestGood ?? 0;
      stockInventoryItem.importDamagedSupplier = e?.newestDamaged ?? 0;
      stockInventoryItem.inventory = stockInventory;
      stockInventoryItem.originId = data?.id;
      stockInventoryItem.originItemId = e?.id;
      stockInventoryItem.type = InventoryType.import;
      stockInventoryItem.currentInventory = Number(stockInventory.stockedPhySellable) ?? 0;
      stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
      stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;
      stockInventoryItem.variantId = e?.variantId;
      stockInventoryItem.warehouseId = data?.warehouseId;
      stockInventoryItem.companyId = companyId?.toString();
      stockInventoryItem.originUpdatedAt = time;
      stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
      const logs = [];
      if (stockInventoryItem.importSellableSupplier > 0) {
        logs.push({
          variantId: stockInventoryItem.variantId,
          warehouseId: stockInventoryItem.warehouseId,
          companyId: stockInventoryItem.companyId,
          quantity: stockInventoryItem.importSellableSupplier,
          inventory: TypeInventory.importSellable,
          purpose: TypePurpose.supplier,
          reference: stockInventoryItem.originName,
          actor: stockInventoryItem.createdBy,
        } as InventoryLogs);
      }
      if (stockInventoryItem.importDamagedSupplier > 0) {
        logs.push({
          variantId: stockInventoryItem.variantId,
          warehouseId: stockInventoryItem.warehouseId,
          companyId: stockInventoryItem.companyId,
          quantity: stockInventoryItem.importDamagedSupplier,
          inventory: TypeInventory.importDamaged,
          purpose: TypePurpose.supplier,
          reference: stockInventoryItem.originName,
          actor: stockInventoryItem.createdBy,
        } as InventoryLogs);
      }
      if (logs?.length > 0) stockInventoryItem.logs = logs;

      console.log('purchase-order-stock', stockInventoryItem);
      params.push(stockInventoryItem);
      totalStock[`${e?.variantId}.${data?.warehouseId}`] =
        Number(stockInventory.stockedPhySellable) + Number(e?.newestGood);
    }
    console.log('purchase-order-stock', params, 'totalStock', totalStock);

    if (params?.length > 0) {
      const res: any = await this.iltRepository.save(params).catch(err => {
        if (err?.driverError)
          console.log('purchase-order-stock update-stock-inventory-error', err?.driverError);
        return {
          status: 500,
          message: err?.driverError?.detail ?? err?.driverError?.error,
        };
      });

      if (res?.status == 500) return res;
    }
    const updatedAt = moment()?.valueOf();
    for (const item of params) {
      if (
        !!totalStock?.[`${item?.variantId}.${item?.warehouseId}`] &&
        totalStock?.[`${item?.variantId}.${item?.warehouseId}`] > 0
      )
        await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
          id: item?.variantId,
          user: {
            companyId,
          },
          warehouseId: data?.warehouseId,
          quantity: totalStock?.[`${item?.variantId}.${item?.warehouseId}`] ?? 0,
          comboIds: uniq(comboLookups[Number(item?.variantId)]),
          updatedAt,
        });
    }

    return {
      status: 200,
      message: 'success',
    };
  }

  // inventory v2 -- order
  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'order-changed-stock-v2',
    queue: 'queue-order-changed-stock-v2',
    errorHandler: rmqErrorsHandler,
  })
  async changeStockInventoryByOrder(payload: IOrderInventory) {
    console.log('payload', 'order-changed-stock', payload);

    const {
      id,
      user,
      oldStatus,
      newStatus,
      typeUpdateStatus,
      lastUpdateStatus,
      reason,
      revert,
      revertType,
      rmqErrorAttempts,
      updatedAt,
      isRequestQueue,
      rollBackStatus,
      isSyncToPartner,
      saveNewStatus,
      isReimport,
      isPriorityStockInventory,
    } = payload;

    if (!oldStatus || !newStatus || !payload?.id) {
      console.log('Not found payload');
      return new Nack();
    }
    const { companyId } = user;

    if (
      rmqErrorAttempts &&
      rmqErrorAttempts > 0 &&
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm, OrderFFMStatus.PickedUp3PL]?.includes(
        newStatus,
      ) &&
      isRequestQueue
    ) {
      return new Nack();
    }

    let data: any = {};
    try {
      const res = await this.amqpConnection.request({
        exchange: 'ffm-order-external',
        routingKey: 'get-one-order-2-1',
        payload: { user, id },
        timeout: 20000,
      });
      data = res?.data;
    } catch (error) {
      console.log(payload?.id, 'amqpConnection failed', error);
      // return new Nack();
      throw new Error('');
    }

    if (
      isPriorityStockInventory &&
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm]?.includes(data?.status)
    ) {
      return {
        status: 200,
        message: 'success',
      };
    }

    if (revert) {
      let dataReImport: any = {};
      try {
        const res = await this.amqpConnection.request({
          exchange: 'ffm-order-external',
          routingKey: 'get-return-order-2-1',
          payload: { user, id },
          timeout: 20000,
        });
        dataReImport = res?.data;
        // console.log('re-import-data', data);
      } catch (error) {
        console.log(payload?.id, 'amqpConnection failed', error);
        // return new Nack();
        throw new Error('');
      }
      if (dataReImport) {
        console.log(payload?.id, 'Already exits reimport order');
        if (isRequestQueue)
          return {
            status: 500,
            message: 'Already exits reimport order',
          };
        else return new Nack();
      }
    }

    if (!data) {
      console.log(payload?.id, 'Not found order');
      if (
        [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
        oldStatus != newStatus
      )
        await this.amqpConnection.publish('ffm-order-external', 'stock-update-status-order-2-2', {
          id,
          status: oldStatus,
          user: {
            ...user,
            id: SystemIdEnum.system,
          },
          lastUpdateStatus,
        });
      return isRequestQueue
        ? {
            status: 500,
            message: 'Not found order',
          }
        : new Nack();
    }

    const ids = [],
      varIds = [],
      prodInOd = [];

    data?.products.forEach((item: OrderProduct) => {
      ids.push(item?.id);
      prodInOd[item?.productId] = item;
    });

    const products: IOrderProduct[] = [],
      comboLookups = [];

    // get product in order

    data?.products?.forEach((item: OrderProduct) => {
      // export combo => single product
      if (item?.productDetail.product?.isCombo) {
        item?.productDetail?.product?.combo?.forEach((el: ProductComboVariant) => {
          if (
            el?.status?.toString() ==
            $enum(ProductStatus)
              .getKeyOrDefault(ProductStatus.active, null)
              ?.toString()
          )
            products.push({
              id: item?.id,
              quantity: item?.quantity * el?.qty,
              productId: el?.variantId,
            });
          varIds.push(el?.variantId);
        });
      } else {
        products.push({
          id: item?.id,
          quantity: item?.quantity,
          productId: item?.productId,
        });
        varIds.push(item?.productId);
      }
    });

    const [inventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(uniq(varIds)),
            warehouseId: isReimport ? data?.returnWarehouseId : data?.warehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: uniq(varIds) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);

    productCombos?.forEach((item: any) => {
      if (!comboLookups[Number(item?.variant_id)]) comboLookups[item?.variant_id] = [];
      comboLookups[Number(item?.variant_id)].push(item?.id);
    });

    if (uniq(varIds)?.length != inventories?.length) {
      if (
        [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
        oldStatus != newStatus
      ) {
        await this.amqpConnection.publish('ffm-order-external', 'stock-update-status-order-2-1', {
          id,
          status: oldStatus,
          user: {
            ...user,
            id: SystemIdEnum.system,
          },
          lastUpdateStatus,
        });
      }
      // console.log(payload?.id, 'Not found inventory', inventories, varIds);
      return isRequestQueue
        ? {
            status: 500,
            message: 'Not found inventory',
          }
        : new Nack();
    }

    const params = [],
      totalStock = [];
    let reConfirmOrderAwaitingStock = false;

    for (const it of products) {
      const prod: IOrderProduct = it;
      const stock = find(inventories, function(o: InventoryManagement) {
        return o?.variantId == prod?.productId;
      });
      if (stock?.id) {
        const [currentSellable, currentDamaged] = await this.parseCurrentInventory(stock);

        const stockInventoryItem = new InventoryLineItem();
        stockInventoryItem.originName = `Sales Order ${data?.displayId}`;
        stockInventoryItem.inventoryId = stock?.id;
        stockInventoryItem.originId = data?.id;
        stockInventoryItem.originStatus = newStatus;
        stockInventoryItem.originItemId = prod?.id;
        stockInventoryItem.type = InventoryType.order;
        stockInventoryItem.currentInventory = Number(stock.stockedPhySellable) ?? 0;
        stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
        stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;
        stockInventoryItem.variantId = prod?.productId;
        stockInventoryItem.warehouseId = data?.warehouseId;
        stockInventoryItem.companyId = companyId?.toString();
        stockInventoryItem.originUpdatedAt = updatedAt;
        stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
        const logs = [];

        //revert đơn
        if (revert) {
          if (revertType == TypeRevertStatus.api) {
            if ([OrderFFMStatus.New, OrderFFMStatus.AwaitingStock]?.includes(newStatus)) {
              stockInventoryItem.originName = `Revert Order ${data?.displayId}`;
              stockInventoryItem.stockedPhySellable = prod?.quantity;
              console.log(
                'INVENTORY',
                `Revert to status ${$enum(OrderFFMStatus).getKeyOrDefault(
                  oldStatus,
                  null,
                )}: SO code [${data?.displayId}]`,
              );
            } else if (
              [OrderFFMStatus.Returned, OrderFFMStatus.Delivered]?.includes(Number(newStatus)) &&
              [OrderFFMStatus.ReturnedStocked]?.includes(Number(oldStatus))
            ) {
              stockInventoryItem.originName = `Revert Order ${data?.displayId}`;
              stockInventoryItem.stockedPhySellable = -prod?.quantity;
              stockInventoryItem.importSellableReimport = -prod?.quantity;
              stockInventoryItem.warehouseId = data?.returnWarehouseId;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: -prod?.quantity,
                inventory: TypeInventory.importSellable,
                purpose: TypePurpose.reimport,
                reference: `Revert to status Returned Stocked: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
          } else if (revertType == TypeRevertStatus.bulkRevertStatus) {
            if (
              newStatus == OrderFFMStatus.Awaiting3PLPickup &&
              oldStatus == OrderFFMStatus.PickedUp3PL
            ) {
              // stockInventoryItem.stockedPhySellable = -prod?.quantity;
              stockInventoryItem.importSellableOthers = prod?.quantity;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.importSellableOthers,
                inventory: TypeInventory.importSellable,
                purpose: TypePurpose.others,
                reference: `Revert to status Awaiting Carrier Pickup: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            } else if (
              [OrderFFMStatus.InDelivery, OrderFFMStatus.InReturn]?.includes(newStatus) &&
              [OrderFFMStatus.LostBy3PL, OrderFFMStatus.LostByWH]?.includes(oldStatus)
            ) {
              stockInventoryItem.exportLost = prod?.quantity;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.exportLost,
                inventory: TypeInventory.exportLost,
                purpose: TypePurpose.others,
                reference: `Revert to status ${
                  newStatus == OrderFFMStatus.InDelivery ? `InDelivery` : `InReturn`
                }: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            } else if (
              [OrderFFMStatus.InDelivery, OrderFFMStatus.InReturn]?.includes(newStatus) &&
              [OrderFFMStatus.DamagedByWH, OrderFFMStatus.ReturnedDamagedBy3PL]?.includes(oldStatus)
            ) {
              stockInventoryItem.exportDamagedOthers = prod?.quantity;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.exportDamagedOthers,
                inventory: TypeInventory.exportDamaged,
                purpose: TypePurpose.others,
                reference: `Revert to status ${
                  newStatus == OrderFFMStatus.InDelivery ? `InDelivery` : `InReturn`
                }: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
          }
        } else {
          if (
            [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
            oldStatus != newStatus
          ) {
            stockInventoryItem.stockedPhySellable = -prod?.quantity;
            console.log(
              'INVENTORY',
              `${$enum(OrderFFMStatus).getKeyOrDefault(newStatus, null)}: SO code [${
                data?.displayId
              }]`,
            );
          } else if (newStatus == OrderFFMStatus.PickedUp3PL) {
            stockInventoryItem.exportSellableSales = prod?.quantity;
            // stockInventoryItem.originUpdatedAt = 0;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.exportSellableSales,
              inventory: TypeInventory.exportSellable,
              purpose: TypePurpose.sale,
              reference: `Update to status 3PL Pickedup: SO code [${data?.displayId}]`,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          } else if (
            newStatus == OrderFFMStatus.Canceled &&
            [
              OrderFFMStatus.Reconfirm,
              OrderFFMStatus.Confirmed,
              OrderFFMStatus.AwaitingCollection,
            ]?.includes(oldStatus)
          ) {
            stockInventoryItem.stockedPhySellable = prod?.quantity;
            if (!totalStock[stockInventoryItem.variantId])
              totalStock[stockInventoryItem.variantId] = 0;
            totalStock[stockInventoryItem.variantId] += Number(prod?.quantity);
            reConfirmOrderAwaitingStock = true;
          }
          // else if (newStatus == OrderFFMStatus.Returned) {
          //   // stockInventoryItem.stockedPhySellable = prod?.quantity;
          //   stockInventoryItem.importSellableReimport = prod?.quantity;
          //   logs.push({
          //     variantId: stockInventoryItem.variantId,
          //     warehouseId: stockInventoryItem.warehouseId,
          //     companyId: stockInventoryItem.companyId,
          //     quantity: stockInventoryItem.importSellableReimport,
          //     inventory: TypeInventory.importSellable,
          //     purpose: TypePurpose.reimport,
          //     reference: stockInventoryItem.originName,
          //     actor: stockInventoryItem.createdBy,
          //   } as InventoryLogs);
          // }
        }

        if (logs?.length > 0) stockInventoryItem.logs = logs;
        // console.log('sale-order-stock-item', stockInventoryItem);
        params.push(stockInventoryItem);
      }
    }

    // console.log('sale-order-stock', params);

    if (params?.length > 0) {
      if (
        [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
        oldStatus != newStatus
      ) {
        const res: any = await this.iltRepository.save(params).catch(err => {
          if (err?.driverError)
            console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
          return {
            status: 500,
            message: err?.driverError?.constraint,
          };
        });

        if (res?.status == 500) {
          if (rollBackStatus)
            await this.amqpConnection.publish(
              'ffm-order-external',
              'stock-update-status-order-2-2',
              {
                id,
                status: oldStatus,
                user: {
                  ...user,
                  id: SystemIdEnum.system,
                },
                lastUpdateStatus,
              },
            );
          return isRequestQueue ? res : new Nack();
        } else if (saveNewStatus) {
          await this.amqpConnection.publish('ffm-order-external', 'stock-update-status-order-2-2', {
            id,
            status: newStatus,
            user: {
              ...user,
              id: SystemIdEnum.system,
            },
            lastUpdateStatus,
          });
        }
      } else {
        const res: any = await this.iltRepository
          .createQueryBuilder()
          .insert()
          .values(params)
          .execute()
          .catch(err => {
            if (err?.driverError)
              console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
            return {
              status: 500,
              message: err?.driverError?.detail ?? err?.driverError?.error,
              constraint: err?.driverError?.constraint,
            };
          });

        if (res.identifiers?.length > 0) {
          const logData = [];
          res.identifiers.map((it, idx) => {
            params[idx]?.logs?.map((log: InventoryLogs) => {
              logData.push({
                ...log,
                inventoryLineId: it?.id,
              });
            });
          });

          // console.log(222, logData);
          await this.amqpConnection.publish('stock-inventory-service', 'save-log-inventory', {
            data: logData,
          });
        } else if (res?.status == 500) return isRequestQueue ? res : new Nack();
      }
    }

    if (reConfirmOrderAwaitingStock) {
      for (const prod of products) {
        if (!!totalStock?.[prod?.productId] && totalStock?.[prod?.productId] > 0)
          await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
            id: prod?.productId,
            user: {
              companyId,
            },
            warehouseId: data?.warehouseId,
            quantity: totalStock?.[prod?.productId] ?? 0,
            comboIds: uniq(comboLookups[Number(prod?.productId)]),
            updatedAt,
          });
      }
    }

    if (isSyncToPartner) {
      if (!!data?.externalId)
        await this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
          id: data?.id,
          user,
          updatedAt: lastUpdateStatus,
        });
    }
    if (isRequestQueue)
      return {
        status: 200,
        message: 'success',
      };
    else {
      return new Nack();
    }
  }

  // stock-taking-inventory
  async stockTakingInventory(
    data: StockTaking,
    user: Record<string, any>,
    time: number,
    lookupSkus: Record<string, any>,
  ): Promise<IStockImport> {
    console.log('payload', 'stock-taking-inventory-v2', { data, user, time });

    if (!data?.id || !data) {
      return {
        status: 500,
        message: 'Not found StockTaking',
      };
    }
    const { warehouses, companyId, isAdmin } = user;

    if (!warehouses || !companyId) {
      return {
        status: 500,
        message: 'Not found warehouse or company',
      };
    }

    if (!warehouses.includes(data?.warehouseId) && !isAdmin) {
      return {
        status: 500,
        message: 'Not found warehouse',
      };
    }

    const varIds = [];

    data?.items.forEach(item => {
      varIds.push(item?.variantId);
    });
    const [inventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(varIds),
            warehouseId: data?.warehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: uniq(varIds) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);

    const params = [],
      comboLookups = [],
      totalStock = [];

    productCombos?.forEach((item: any) => {
      if (!comboLookups[Number(item?.variant_id)]) comboLookups[item?.variant_id] = [];
      comboLookups[Number(item?.variant_id)].push(item?.id);
    });

    for (const e of data?.items) {
      const stockInventory = find(inventories, function(o) {
        return o?.variantId == e?.variantId;
      });

      console.log('stock-taking-stockInventory', stockInventory);

      if (!stockInventory?.id) {
        return {
          status: 500,
          message: 'Not found inventory',
        };
      }

      const [currentSellable, currentDamaged] = await this.parseCurrentInventory(stockInventory);

      const stockInventoryItem = new InventoryLineItem();
      stockInventoryItem.originName = `Stocktaking Receipt: [${data?.code}]`;
      stockInventoryItem.inventoryId = stockInventory?.id;
      stockInventoryItem.originId = data?.id;
      stockInventoryItem.originItemId = e?.id;
      stockInventoryItem.type = InventoryType.stockTaking;
      stockInventoryItem.currentInventory = Number(stockInventory.stockedPhySellable) ?? 0;
      stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
      stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;
      stockInventoryItem.variantId = e?.variantId;
      stockInventoryItem.warehouseId = data?.warehouseId;
      stockInventoryItem.companyId = companyId?.toString();
      stockInventoryItem.originUpdatedAt = time;
      stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
      const logs = [];
      e.good = Number(e?.good ?? 0) - Number(e?.inProgress ?? 0);

      if (e?.inventoryGood != e?.good) {
        if (e?.inventoryGood > e?.good) {
          stockInventoryItem.exportSellableStocktaking = Number(e?.inventoryGood) - Number(e?.good);
          stockInventoryItem.stockedPhySellable = Number(e?.good) - Number(e?.inventoryGood);
          logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: stockInventoryItem.exportSellableStocktaking,
            inventory: TypeInventory.exportSellable,
            purpose: TypePurpose.stocktaking,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          } as InventoryLogs);
        } else {
          stockInventoryItem.importSellableStocktaking = Number(e?.good) - Number(e?.inventoryGood);
          stockInventoryItem.stockedPhySellable = Number(e?.good) - Number(e?.inventoryGood);
          logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: stockInventoryItem.importSellableStocktaking,
            inventory: TypeInventory.importSellable,
            purpose: TypePurpose.stocktaking,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          } as InventoryLogs);
        }
      }
      if (e?.inventoryDamaged != e?.damaged) {
        if (e?.inventoryDamaged > e?.damaged) {
          stockInventoryItem.exportDamagedStocktaking =
            Number(e?.inventoryDamaged) - Number(e?.damaged);
          logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: stockInventoryItem.exportDamagedStocktaking,
            inventory: TypeInventory.exportDamaged,
            purpose: TypePurpose.stocktaking,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          } as InventoryLogs);
        } else {
          stockInventoryItem.importDamagedStocktaking =
            Number(e?.damaged) - Number(e?.inventoryDamaged);
          logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: stockInventoryItem.importDamagedStocktaking,
            inventory: TypeInventory.importDamaged,
            purpose: TypePurpose.stocktaking,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          } as InventoryLogs);
        }
      }

      if (logs?.length > 0) stockInventoryItem.logs = logs;

      console.log('stock-taking-stockInventoryItem', stockInventoryItem);
      params.push(stockInventoryItem);
      if (e?.variantId)
        totalStock[e?.variantId] =
          Number(stockInventoryItem.currentInventory) +
          Number(stockInventoryItem.stockedPhySellable);
    }
    console.log('stock-taking-params', params);

    if (params?.length > 0) {
      const res: any = await this.iltRepository.save(params).catch(err => {
        if (err?.driverError)
          console.log(
            'stock-taking update-stock-inventory-error',
            err?.driverError?.detail,
            err?.driverError?.detail?.split(','),
          );
        const varId = trim(err?.driverError?.detail?.split(',')?.[3]);
        return {
          status: 500,
          message: err?.driverError?.detail ?? err?.driverError?.error,
          variantId: err?.driverError?.detail ? lookupSkus?.[varId] : varId,
          code: 'STK_0001',
        };
      });

      if (res?.status == 500) return res;
    }
    const updatedAt = moment()?.valueOf();
    for (const item of params) {
      if (!!totalStock?.[item?.variantId] && totalStock?.[item?.variantId] > 0)
        await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
          id: item?.variantId,
          user: {
            companyId,
          },
          warehouseId: data?.warehouseId,
          quantity: totalStock?.[item?.variantId] ?? 0,
          comboIds: uniq(comboLookups[Number(item?.variantId)]),
          updatedAt,
        });
    }

    return {
      status: 200,
      message: 'success',
    };
  }

  // stock-transfer-inventory
  async stockTransferInventory(
    data: StockTransfer,
    user: Record<string, any>,
    time: number,
  ): Promise<IStockImport> {
    console.log('stock-transfer-inventory-v2', { data, user, time });

    if (!data?.id || !data) {
      return {
        status: 500,
        message: 'Not found StockTransfer',
      };
    }
    const { warehouses, companyId, isAdmin } = user;

    if (!warehouses || !companyId) {
      return {
        status: 500,
        message: 'Not found warehouse or company',
      };
    }

    const senderVariantIds = [],
      recipientVariantIds = [];

    data?.items.forEach(item => {
      senderVariantIds.push(item?.senderVariantId);
      recipientVariantIds.push(item?.recipientVariantId);
    });
    const varIds = [];

    data?.items.forEach(item => {
      varIds.push(item?.recipientVariantId);
      varIds.push(item?.senderVariantId);
    });
    const [senderInventories, recipientInventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(uniq(senderVariantIds)),
            warehouseId: data?.senderWarehouseId,
            companyId,
          });
        },
      }),
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(uniq(recipientVariantIds)),
            warehouseId: data?.recipientWarehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: uniq(varIds) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);

    const lookupSender = [],
      lookupRecipient = [];
    senderInventories?.forEach((e: InventoryManagement) => {
      lookupSender[e.variantId] = e;
    });
    recipientInventories?.forEach((e: InventoryManagement) => {
      lookupRecipient[e.variantId] = e;
    });

    const params = [];
    const totalStock = [];
    const comboLookups = [];
    const skus = [];

    productCombos?.forEach((item: any) => {
      if (!comboLookups[Number(item?.variant_id)]) comboLookups[item?.variant_id] = [];
      comboLookups[Number(item?.variant_id)].push(item?.id);
    });

    for (const e of data?.items) {
      skus[e?.senderVariantId] = e?.senderSku;
      // stock transfer damaged -> sellable
      if (
        data.purpose == StockTransferPurpose.damagedToSellable &&
        lookupSender[e.senderVariantId]
      ) {
        const stockInventoryItem = await this.parseInventoryItemLine(
          data?.id,
          e?.id,
          e.senderVariantId,
          e.senderWarehouseId,
          time,
          user,
          lookupSender[e.senderVariantId],
          `Transfer Damaged → Sellable: [${data?.code}]`,
        );

        stockInventoryItem.stockedPhySellable = Number(e?.transferredDamaged);
        stockInventoryItem.exportDamagedTransfer = Number(e?.transferredDamaged);
        stockInventoryItem.importSellableTransfer = Number(e?.transferredDamaged);
        stockInventoryItem.logs = [
          {
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredDamaged),
            inventory: TypeInventory.importSellable,
            purpose: TypePurpose.transfer,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          },
          {
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredDamaged),
            inventory: TypeInventory.exportDamaged,
            purpose: TypePurpose.transfer,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          },
        ];
        if (!totalStock?.[e?.senderVariantId]) totalStock[e.senderVariantId] = 0;
        totalStock[e.senderVariantId] += Number(e?.transferredDamaged);
        console.log('stock-transfer-stockInventoryItem', stockInventoryItem);
        params.push(stockInventoryItem);
      }

      // check already exits inventory
      if (
        data.purpose == StockTransferPurpose.stockTransferred &&
        !lookupRecipient[e.recipientVariantId]
      ) {
        const stockInventory = new InventoryManagement();
        stockInventory.companyId = companyId?.toString();
        stockInventory.stockedPhySellable = 0;
        stockInventory.lastEditorId = user?.id;
        stockInventory.warehouseId = e.recipientWarehouseId;
        stockInventory.variantId = e.recipientVariantId;
        lookupRecipient[e.recipientVariantId] = await this.imRepository.save(stockInventory);
      }
      // stock transfer sender
      if (
        data.purpose == StockTransferPurpose.stockTransferred &&
        lookupSender[e.senderVariantId] &&
        lookupRecipient[e.recipientVariantId]
      ) {
        console.log(222);
        const stockInventoryItem = await this.parseInventoryItemLine(
          data?.id,
          e?.id,
          e.senderVariantId,
          e.senderWarehouseId,
          time,
          user,
          lookupSender[e.senderVariantId],
          `Transfer Stock: [${data.code}]`,
        );
        stockInventoryItem.logs = [];
        if (e?.transferredSellable) {
          stockInventoryItem.stockedPhySellable = -Number(e?.transferredSellable);
          stockInventoryItem.exportSellableTransfer = Number(e?.transferredSellable);
          stockInventoryItem.logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredSellable),
            inventory: TypeInventory.exportSellable,
            purpose: TypePurpose.transfer,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          });
        }
        if (e?.transferredDamaged) {
          stockInventoryItem.exportDamagedTransfer = Number(e?.transferredDamaged);
          stockInventoryItem.logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredDamaged),
            inventory: TypeInventory.exportDamaged,
            purpose: TypePurpose.transfer,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          });
        }

        console.log('stock-transfer-stockInventoryItem', stockInventoryItem);
        params.push(stockInventoryItem);
      }

      // stock transfer recipient
      if (
        data.purpose == StockTransferPurpose.stockTransferred &&
        lookupSender[e.senderVariantId] &&
        lookupRecipient[e.recipientVariantId]
      ) {
        console.log(111);

        const stockInventoryItem = await this.parseInventoryItemLine(
          data?.id,
          e?.id,
          e.recipientVariantId,
          e.recipientWarehouseId,
          time,
          user,
          lookupRecipient[e.recipientVariantId],
          `Transfer Stock: [${data.code}]`,
        );
        stockInventoryItem.logs = [];
        if (e?.transferredSellable) {
          stockInventoryItem.stockedPhySellable = Number(e?.transferredSellable);
          stockInventoryItem.importSellableTransfer = Number(e?.transferredSellable);
          stockInventoryItem.logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredSellable),
            inventory: TypeInventory.importSellable,
            purpose: TypePurpose.transfer,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          });
          if (!totalStock?.[e?.recipientVariantId]) totalStock[e.recipientVariantId] = 0;
          totalStock[e.recipientVariantId] += Number(e?.transferredSellable);
        }
        if (e?.transferredDamaged) {
          stockInventoryItem.importDamagedTransfer = Number(e?.transferredDamaged);
          stockInventoryItem.logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredDamaged),
            inventory: TypeInventory.importDamaged,
            purpose: TypePurpose.transfer,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          });
        }

        console.log('stock-transfer-stockInventoryItem', stockInventoryItem);
        params.push(stockInventoryItem);
      }

      // stock Withdrawal
      if (data.type == StockTransferType.withdrawal && lookupSender[e.senderVariantId]) {
        const stockInventoryItem = await this.parseInventoryItemLine(
          data?.id,
          e?.id,
          e.senderVariantId,
          e.senderWarehouseId,
          time,
          user,
          lookupSender[e.senderVariantId],
          `${
            data.purpose == StockTransferPurpose.returnToSupplier
              ? `Return to Supplier`
              : data.purpose == StockTransferPurpose.clearance
              ? `Withdraw to Clearance`
              : `Withdraw to Others`
          }: [${data.code}]`,
        );
        stockInventoryItem.logs = [];
        if (e?.transferredSellable) {
          stockInventoryItem.stockedPhySellable = -Number(e?.transferredSellable);
          if (data.purpose == StockTransferPurpose.returnToSupplier)
            stockInventoryItem.exportSellableSupplier = Number(e?.transferredSellable);
          else stockInventoryItem.exportSellableOthers = Number(e?.transferredSellable);

          stockInventoryItem.logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredSellable),
            inventory: TypeInventory.exportSellable,
            purpose:
              data.purpose == StockTransferPurpose.returnToSupplier
                ? TypePurpose.supplier
                : data.purpose == StockTransferPurpose.clearance
                ? TypePurpose.clearance
                : TypePurpose.others,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          });
        }
        if (e?.transferredDamaged) {
          if (data.purpose == StockTransferPurpose.returnToSupplier)
            stockInventoryItem.exportDamagedSupplier = Number(e?.transferredDamaged);
          else if (data.purpose == StockTransferPurpose.clearance)
            stockInventoryItem.exportDamagedClearance = Number(e?.transferredDamaged);
          else stockInventoryItem.exportDamagedOthers = Number(e?.transferredDamaged);

          stockInventoryItem.logs.push({
            variantId: stockInventoryItem.variantId,
            warehouseId: stockInventoryItem.warehouseId,
            companyId: stockInventoryItem.companyId,
            quantity: Number(e?.transferredDamaged),
            inventory: TypeInventory.exportDamaged,
            purpose:
              data.purpose == StockTransferPurpose.returnToSupplier
                ? TypePurpose.supplier
                : data.purpose == StockTransferPurpose.clearance
                ? TypePurpose.clearance
                : TypePurpose.others,
            reference: stockInventoryItem.originName,
            actor: stockInventoryItem.createdBy,
          });
        }
        params.push(stockInventoryItem);
      }
    }
    console.log('stock-transfer-params', params, data);

    if (params?.length > 0) {
      const res: any = await this.iltRepository.save(params).catch(err => {
        if (err?.driverError)
          console.log('stock-transfer update-stock-inventory-error', err?.driverError?.detail);
        const varId = trim(err?.driverError?.detail?.split(',')?.[3]);
        return {
          status: 500,
          message: err?.driverError?.detail ?? err?.driverError?.error,
          variantId: skus[varId] ?? varId,
          code: data?.type == StockTransferType.transfer ? 'STR_0009' : 'SWD_0006',
        };
      });

      if (res?.status == 500) return res;
    } else {
      return {
        status: 500,
        message: 'Not found parameters',
      };
    }
    const updatedAt = moment()?.valueOf();
    if (
      totalStock?.length > 0 &&
      [StockTransferPurpose.damagedToSellable, StockTransferPurpose.stockTransferred]?.includes(
        data.purpose,
      )
    )
      for (const prod of data?.items) {
        const totalProduct =
          data.purpose == StockTransferPurpose.damagedToSellable
            ? totalStock?.[prod?.senderVariantId]
            : totalStock?.[prod?.recipientVariantId];
        const warehouseId =
          data.purpose == StockTransferPurpose.damagedToSellable
            ? prod?.senderWarehouseId
            : prod?.recipientWarehouseId;
        const variantId =
          data.purpose == StockTransferPurpose.damagedToSellable
            ? prod?.senderVariantId
            : prod?.recipientVariantId;

        const comboIds =
          data.purpose == StockTransferPurpose.damagedToSellable
            ? comboLookups[Number(prod?.senderVariantId)]
            : comboLookups[Number(prod?.recipientVariantId)];

        if (totalProduct && totalProduct > 0)
          await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
            id: variantId,
            user: {
              companyId,
            },
            warehouseId,
            quantity: totalProduct ?? 0,
            comboIds,
            updatedAt,
          });
      }

    return {
      status: 200,
      message: 'success',
    };
  }

  // reimport queue
  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'reimport-change-stock',
    queue: 'queue-reimport-change-stock',
    errorHandler: rmqErrorsHandler,
  })
  async reimportChangeStockInventory(payload: IReImportOrderPayload) {
    console.log('payload', 'reimport-change-stock', payload);
    if (!payload?.reImport?.orderId) {
      return new Nack();
    }
    const { reImport, user, nextStatus, currentStatus, productReimports, updatedAt } = payload;
    const { warehouses, companyId } = user;
    if (!warehouses || !companyId) {
      return new Nack();
    }

    if (isEmpty(reImport?.products)) {
      console.log(payload?.reImport?.orderId, 'not found order');
      return new Nack();
    }

    const varIds = [],
      totalStock = [],
      prodCombo = [],
      variantPhyIds = [];

    reImport?.products.forEach((item: any) => {
      varIds.push(item?.productId);
    });

    const variants: any[] = [];

    // get product in order
    if (varIds?.length > 0) {
      reImport?.products?.forEach((item: IReImportOrderItem) => {
        variantPhyIds.push(item?.productId);
        variants.push({
          quantity: item?.needed,
          good: item?.good,
          damaged: item?.damaged + item?.damagedBy3pl,
          lost: item?.lost + item?.lostBy3pl,
          needed: item?.needed,
          productId: item?.productId,
          id: item?.productId,
          itemId: item?.itemId,
        });
      });
    }

    if (variants?.length <= 0) {
      console.log(payload?.reImport?.orderId, 'not found product');
      return new Nack();
    }

    // const inventories = await this.imRepository.find({
    //   where: qb => {
    //     qb.where({
    //       variantId: In(uniq(variantPhyIds)),
    //       warehouseId: reImport?.warehouseId,
    //       companyId,
    //     });
    //   },
    // });

    const [inventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(uniq(variantPhyIds)),
            warehouseId: reImport?.warehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: uniq(varIds) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);
    productCombos?.forEach((item: any) => {
      prodCombo.push(item?.id);
    });

    if (uniq(variantPhyIds)?.length != inventories?.length) {
      console.log(payload?.reImport?.orderId, 'Not found inventory');
      return new Nack();
    }

    const params = [];
    for (const it of variants) {
      const prod: IReImportOrderItem = it;
      const stock = find(inventories, function(o: InventoryManagement) {
        return o?.variantId == prod?.productId;
      });

      if (stock?.id) {
        const [currentSellable, currentDamaged] = await this.parseCurrentInventory(stock);

        const stockInventoryItem = new InventoryLineItem();
        stockInventoryItem.originName = `Re-import: SO code [${reImport?.orderDisplayId}]`;
        stockInventoryItem.inventoryId = stock?.id;
        stockInventoryItem.originId = reImport?.orderId;
        stockInventoryItem.originStatus = nextStatus;
        stockInventoryItem.originItemId = prod?.itemId;
        stockInventoryItem.type = InventoryType.reImport;

        stockInventoryItem.currentInventory = Number(stock.stockedPhySellable) ?? 0;
        stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
        stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;

        stockInventoryItem.variantId = prod?.productId;
        stockInventoryItem.warehouseId = reImport?.warehouseId;
        stockInventoryItem.companyId = companyId?.toString();
        stockInventoryItem.originUpdatedAt = updatedAt;
        stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
        const logs = [];

        if (currentStatus == OrderFFMStatus.Canceled || currentStatus == OrderFFMStatus.Returned) {
          let exportSellable = 0;
          if (prod.damaged) {
            stockInventoryItem.importDamagedReimport = prod?.damaged;
            exportSellable += Number(prod?.damaged);
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importDamagedReimport,
              inventory: TypeInventory.importDamaged,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (prod.lost) {
            stockInventoryItem.importLost = prod?.lost;
            exportSellable += Number(prod?.lost);
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importLost,
              inventory: TypeInventory.importLost,
              purpose: TypePurpose.others,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (currentStatus == OrderFFMStatus.Returned) {
            stockInventoryItem.importSellableReimport = prod?.good;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importSellableReimport,
              inventory: TypeInventory.importSellable,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }

          if (currentStatus == OrderFFMStatus.Canceled && exportSellable > 0) {
            stockInventoryItem.exportSellableOthers = exportSellable;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.exportSellableOthers,
              inventory: TypeInventory.exportSellable,
              purpose: TypePurpose.others,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          stockInventoryItem.stockedPhySellable = prod?.good;
        } else if (currentStatus == OrderFFMStatus.InReturn) {
          if (prod?.good) {
            stockInventoryItem.importSellableReimport = prod?.good;
            stockInventoryItem.stockedPhySellable = prod?.good;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importSellableReimport,
              inventory: TypeInventory.importSellable,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (prod.good < prod.needed) {
            if (prod.damaged) {
              stockInventoryItem.importDamagedReimport = prod?.damaged;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.importDamagedReimport,
                inventory: TypeInventory.importDamaged,
                purpose: TypePurpose.reimport,
                reference: stockInventoryItem.originName,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
            if (prod.lost) {
              stockInventoryItem.importLost = prod?.lost;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.importLost,
                inventory: TypeInventory.importLost,
                purpose: TypePurpose.others,
                reference: stockInventoryItem.originName,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
          }
        }

        if (logs?.length > 0) stockInventoryItem.logs = logs;
        // console.log('reimport-order', stockInventoryItem);
        params.push(stockInventoryItem);
        if (prod?.productId)
          totalStock[prod?.productId] =
            Number(stockInventoryItem.currentInventory) +
            Number(stockInventoryItem.stockedPhySellable);
      }
    }

    if (params?.length > 0) {
      const res: any = await this.iltRepository
        .createQueryBuilder()
        .insert()
        .values(params)
        .execute()
        .catch(err => {
          if (err?.driverError)
            console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
          return {
            status: 500,
            message: err?.driverError?.detail ?? err?.driverError?.error,
          };
        });

      if (res.identifiers?.length > 0) {
        const logData = [];
        res.identifiers.map((it, idx) => {
          params[idx]?.logs?.map((log: InventoryLogs) => {
            logData.push({
              ...log,
              inventoryLineId: it?.id,
            });
          });
        });

        // console.log(222, logData);
        await this.amqpConnection.publish('stock-inventory-service', 'save-log-inventory', {
          data: logData,
        });
        const updatedAt = moment()?.valueOf();
        for (const prod of variants) {
          if (!!totalStock?.[prod?.productId] && totalStock?.[prod?.productId] > 0)
            await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
              id: prod?.productId,
              user: {
                companyId,
              },
              warehouseId: reImport?.warehouseId,
              quantity: totalStock?.[prod?.productId] ?? 0,
              comboIds: uniq(prodCombo),
              updatedAt,
            });
        }
      } else if (res?.status == 500) return res;
    }
    return {
      status: 200,
      message: 'success',
    };
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'save-log-inventory',
    queue: 'queue-save-log-inventory',
    errorHandler: rmqErrorsHandler,
  })
  async saveLogInventory(payload: { data: InventoryLogs[] }) {
    const { data } = payload;
    await this.logsRepository
      .createQueryBuilder()
      .insert()
      .values(data)
      .execute()
      .catch(err => {
        if (err?.driverError)
          console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
        throw new Error(err?.driverError);
      });
    return new Nack();
  }

  async parseInventoryItemLine(
    originId: number,
    originItemId: number,
    variantId: number,
    warehouseId: number,
    time: number,
    user: Record<string, any>,
    stockInventory: InventoryManagement,
    originName: string,
  ): Promise<InventoryLineItem> {
    const [currentSellable, currentDamaged] = await this.parseCurrentInventory(stockInventory);
    const stockInventoryItem = new InventoryLineItem();

    stockInventoryItem.originName = originName;
    stockInventoryItem.inventoryId = stockInventory?.id;
    stockInventoryItem.originId = originId;
    stockInventoryItem.originItemId = originItemId;
    stockInventoryItem.type = InventoryType.transfer;
    stockInventoryItem.currentInventory = Number(stockInventory.stockedPhySellable) ?? 0;
    stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
    stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;
    stockInventoryItem.variantId = variantId;
    stockInventoryItem.warehouseId = warehouseId;
    stockInventoryItem.companyId = user?.companyId?.toString();
    stockInventoryItem.originUpdatedAt = time;
    stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;

    return stockInventoryItem;
  }

  async parseCurrentInventory(stockInventory: InventoryManagement): Promise<any[]> {
    const currentSellable =
      Number(stockInventory.importSellableOthers) +
      Number(stockInventory.importSellableReimport) +
      Number(stockInventory.importSellableStocktaking) +
      Number(stockInventory.importSellableSupplier) +
      Number(stockInventory.importSellableTransfer) -
      Number(stockInventory.exportSellableOthers) -
      Number(stockInventory.exportSellableSales) -
      Number(stockInventory.exportSellableStocktaking) -
      Number(stockInventory.exportSellableSupplier) -
      Number(stockInventory.exportSellableTransfer);

    const currentDamaged =
      Number(stockInventory.importDamagedOthers) +
      Number(stockInventory.importDamagedReimport) +
      Number(stockInventory.importDamagedStocktaking) +
      Number(stockInventory.importDamagedSupplier) +
      Number(stockInventory.importDamagedTransfer) -
      Number(stockInventory.exportDamagedClearance) -
      Number(stockInventory.exportDamagedOthers) -
      Number(stockInventory.exportDamagedStocktaking) -
      Number(stockInventory.exportDamagedSupplier) -
      Number(stockInventory.exportDamagedTransfer);
    return [currentSellable, currentDamaged];
  }

  @RabbitRPC({
    exchange: 'stock-inventory-service',
    routingKey: 'create-stock-inventory',
    queue: 'queue-create-stock-inventory',
    errorHandler: rmqErrorsHandler,
  })
  async createInventoryManagement(payload) {
    const { variantIds, warehouseId, companyId } = payload;
    const uniqVariantIds = uniq(variantIds);
    const inventories = await this.imRepository.find({
      where: qb => {
        qb.where({
          variantId: In(uniq(variantIds)),
          warehouseId: warehouseId,
          companyId,
        });
      },
    });

    const stocks = [];
    for (const item of uniqVariantIds) {
      const checkExistInventory = inventories.find(x => x?.variantId == item);
      if (!checkExistInventory) {
        const stock = new InventoryManagement();
        stock.companyId = companyId?.toString();
        stock.stockedPhySellable = 0;
        stock.lastEditorId = '-99';
        stock.warehouseId = warehouseId;
        stock.variantId = Number(item);
        stocks.push(stock);
      }
    }
    await this.imRepository.insert(stocks).catch(err => {
      if (err?.driverError) console.log('error-order-stock-inventory', err?.driverError?.detail);
      throw err;
    });
    return new Nack();
  }
}
