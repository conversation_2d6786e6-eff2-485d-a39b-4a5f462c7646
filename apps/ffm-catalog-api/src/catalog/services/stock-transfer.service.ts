import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { catalogConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { UserType } from 'core/enums/user-type.enum';
import { isEmpty, isNil, isNull, omit, padStart, pick, reduce, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import { $enum } from 'ts-enum-util';
import { Brackets, Repository } from 'typeorm';
import { ExportDetailDto, StockTransferDto } from '../../dtos/stock-transfer.dto';
import { Logs } from '../../entities/logs.entity';
import { ProductVariation } from '../../entities/product-variation.entity';
import { StockTakingItem } from '../../entities/stock-taking-item.entity';
import { StockTaking } from '../../entities/stock-taking.entity';
import { StockTransferItems } from '../../entities/stock-transfer-items.entity';
import { StockTransfer } from '../../entities/stock-transfer.entity';
import { SlotWarehouses } from '../../entities/warehouses';
import { NEXT_STOCK_TRANSFER_STATUS } from '../constants/stock-taking-statuses.constant';
import {
  StockTransferPurpose,
  StockTransferStatus,
  StockTransferType,
} from '../enum/stock-transfer.enum';
import { FilterStockTransfer } from '../filters/filterStockTransfer.filter';
import { StockInventoryService } from './stock-inventory.service';
import xlsx, { WorkSheet } from 'node-xlsx';
import { Users } from '../../read-entities/identity-entities/Users';
// import excel from 'node-excel-export';
import * as ExcelJS from 'exceljs';
import { saveImageToExcel } from 'core/utils/ImageUtils';

@Injectable()
export class StockTransferService {
  constructor(
    @InjectRepository(StockTransfer, catalogConnection)
    private stockTransferRepository: Repository<StockTransfer>,
    @InjectRepository(StockTransferItems, catalogConnection)
    private stockTransferItemRepository: Repository<StockTransferItems>,
    @InjectRepository(ProductVariation, catalogConnection)
    private variantRepository: Repository<ProductVariation>,
    @InjectRepository(SlotWarehouses, catalogConnection)
    private whRepository: Repository<SlotWarehouses>,
    @InjectRepository(StockTaking, catalogConnection)
    private stRepository: Repository<StockTaking>,
    @InjectRepository(StockTakingItem, catalogConnection)
    private stiRepository: Repository<StockTakingItem>,
    @InjectRepository(Logs, catalogConnection)
    private logRepository: Repository<Logs>,
    private readonly amqpConnection: AmqpConnection,
    private inventoryService: StockInventoryService,
  ) {}

  async findAll(
    pagination: PaginationOptions,
    query: FilterStockTransfer,
    request,
    header,
  ): Promise<[StockTransfer[], number]> {
    const { warehouses, type, companyId, isAdmin } = request?.user;
    const { recipientIds, warehouseIds, from, to, name, clientIds, status, variantIds } = query;
    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null))
      throw new ForbiddenException('');

    const sql = this.stockTransferRepository.createQueryBuilder('st');
    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');
      const countryIds = header['country-ids']?.split(',');

      if (!isEmpty(whIds)) sql.andWhere('st.senderWarehouseId IN (:...whIds)', { whIds });
    }

    if (clientIds?.length > 0) sql.andWhere('st.senderId IN (:...clientIds)', { clientIds });
    if (recipientIds?.length > 0)
      sql.andWhere('st.recipientId IN (:...recipientIds)', { recipientIds });
    if (warehouseIds?.length > 0)
      sql.andWhere('st.senderWarehouseId IN (:...warehouseIds)', { warehouseIds });
    if (status?.length > 0) sql.andWhere('st.status IN (:...status)', { status });
    if (companyId) sql.andWhere('st.companyId = :companyId', { companyId });

    if (!isEmpty(variantIds)) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where(
            'items.sender_variant_id IN (:...variantIds)',
          ).orWhere('items.recipient_variant_id IN (:...variantIds)', { variantIds });
        }),
      );
    }

    if (name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('st.note ILIKE :name')
            .orWhere('st.name ILIKE :name')
            .orWhere('st.code ILIKE :name', { name: `%${name}%` });
        }),
      );
    }
    if (from) sql.andWhere('st.createdAt >= :from', { from });
    if (to) sql.andWhere('st.createdAt <= :to', { to });
    sql.leftJoin('st.senderWarehouse', 'senderWarehouse');
    sql.leftJoin('st.recipientWarehouse', 'recipientWarehouse');
    sql.leftJoin('st.items', 'items');
    sql.leftJoin('items.senderVariant', 'senderVariant');
    sql.leftJoin('items.recipientVariant', 'recipientVariant');
    sql.addSelect('st.*');
    sql.addSelect('senderWarehouse.name');
    sql.addSelect('senderWarehouse.displayId');
    sql.addSelect('recipientWarehouse.name');
    sql.addSelect('recipientWarehouse.displayId');

    // sql.addSelect('variants.senderVariant.id');
    sql.addSelect('items.id');
    sql.addSelect('senderVariant.id');
    sql.addSelect('senderVariant.sku');
    sql.addSelect('senderVariant.prefix');
    sql.addSelect('senderVariant.productId');
    sql.addSelect('recipientVariant.id');
    sql.addSelect('recipientVariant.sku');
    sql.addSelect('recipientVariant.prefix');
    sql.addSelect('recipientVariant.productId');
    sql.orderBy('st.updatedAt', 'DESC');
    if (pagination) sql.take(pagination.limit).skip(pagination.skip);

    return await sql.getManyAndCount();
  }

  async createStockTransfer(request, data: StockTransferDto): Promise<StockTransfer> {
    const { warehouses, type, companyId, id, isAdmin } = request?.user;
    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null))
      throw new ForbiddenException('');

    const result = {
      code: 200,
      statusCode: 200,
      ids: [],
      index: [],
      message: [],
    };
    let st = new StockTransfer();
    st = {
      ...data,
      companyId,
      senderId: data?.senderId?.toString(),
      recipientId: data?.recipientId?.toString(),
      dueDate: !!data?.dueDate ? data?.dueDate : null,
      status: StockTransferStatus.new,
      creatorId: id,
      lastEditorId: id,
    };
    st.items = [];
    data?.items.forEach((e, index) => {
      let sti = new StockTransferItems();
      sti = {
        ...omit(e, 'id'),
        lastEditorId: id,
        companyId,
        senderId: st?.senderId,
        senderWarehouseId: st?.senderWarehouseId,
        recipientId: st?.recipientId,
        recipientWarehouseId: st?.recipientWarehouseId,
      };
      st.items.push(sti);
    });
    if (result.code == 500) throw new BadRequestException(result);
    await this.stockTransferRepository.query(`CREATE SEQUENCE IF NOT EXISTS stock_transfer_seq;`);
    const increment = await this.stockTransferRepository.query(
      `select nextval('stock_transfer_seq') as id`,
    );
    return this.stockTransferRepository.save({
      ...st,
      code: `${data.type == StockTransferType.transfer ? `ST` : `SW`}${padStart(
        increment[0]?.id ?? 1,
        6,
        '0',
      )}`,
    });
  }

  async updateStockTransfer(request, data: StockTransferDto, stId: number): Promise<StockTransfer> {
    const { warehouses, type, companyId, id, isAdmin } = request?.user;

    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null))
      throw new ForbiddenException('');

    let st = await this.stockTransferRepository.findOne({
      where: qb => {
        qb.andWhere({
          id: stId,
          companyId,
        });
      },
      relations: ['items'],
    });

    const currentStatus = st.status;
    console.log(
      st,
      currentStatus,
      NEXT_STOCK_TRANSFER_STATUS[data.type][currentStatus],
      data.status,
    );
    // const oldItems = st.items;

    if (currentStatus == StockTransferStatus.done) throw new BadRequestException('Status invalid');
    const syncInventory = data?.status != currentStatus && StockTransferStatus.done == data?.status;

    const lookupItems = [];

    st.items.forEach(el => {
      lookupItems[`${el.senderVariantId}.${el.recipientVariantId}`] = el;
    });

    if (
      !isNull(data.status) &&
      currentStatus != data.status &&
      (!NEXT_STOCK_TRANSFER_STATUS[data.purpose][currentStatus] ||
        (NEXT_STOCK_TRANSFER_STATUS[data.purpose][currentStatus] &&
          !NEXT_STOCK_TRANSFER_STATUS[data.purpose][currentStatus].includes(data.status)))
    )
      throw new ForbiddenException('');

    const result = {
      code: 200,
      statusCode: 200,
      message: [],
    };
    st = {
      ...st,
      companyId,
      creatorId: id,
      lastEditorId: id,
      senderId: currentStatus == StockTransferStatus.new ? `${data.senderId}` : st.senderId,
      senderWarehouseId:
        currentStatus == StockTransferStatus.new ? data.senderWarehouseId : st.senderWarehouseId,
      recipientId:
        currentStatus == StockTransferStatus.new ? `${data.recipientId}` : st.recipientId,
      recipientWarehouseId:
        currentStatus == StockTransferStatus.new
          ? data.recipientWarehouseId
          : st.recipientWarehouseId,
      ...pick(
        data,
        currentStatus == StockTransferStatus.new
          ? ['name', 'dueDate', 'note', 'status']
          : ['note', 'status'],
      ),
    };

    st.items = [StockTransferStatus.new, StockTransferStatus.processing]?.includes(currentStatus)
      ? []
      : st.items;
    data?.items.forEach(e => {
      let sti = new StockTransferItems();
      sti = {
        ...omit(e, ['id']),
        lastEditorId: id,
        companyId,
        senderId: st?.senderId,
        senderWarehouseId: st?.senderWarehouseId,
        recipientId: st?.recipientId,
        recipientWarehouseId: st?.recipientWarehouseId,
      };

      if (lookupItems[`${e.senderVariantId}.${e.recipientVariantId}`]?.id) {
        sti.id = lookupItems[`${e.senderVariantId}.${e.recipientVariantId}`]?.id;
      } else if (currentStatus != StockTransferStatus.new) {
        result.code = 500;
        result.statusCode = 500;
        result.message.push('Variant must be valid value');
      }

      if ([StockTransferStatus.new, StockTransferStatus.processing]?.includes(currentStatus))
        st.items.push(sti);
    });

    if (result.code == 500) throw new BadRequestException(result);

    if (syncInventory) {
      const res = await this.inventoryService.stockTransferInventory(
        st,
        request?.user,
        moment()?.valueOf(),
      );
      console.log('stock-transfer-success', res);

      if (res.status == 500) throw new BadRequestException(res);
    }
    return await this.stockTransferRepository.save(st);
  }

  async detailTransfer(request: Record<string, any>, id: number): Promise<StockTransfer> {
    const { companyId } = request?.user;
    const sql = this.stockTransferRepository.createQueryBuilder('st');
    sql.leftJoin('st.senderWarehouse', 'senderWarehouse');
    sql.leftJoin('st.recipientWarehouse', 'recipientWarehouse');
    sql.leftJoin('st.items', 'items');
    sql.leftJoin('items.senderVariant', 'senderVariant');
    sql.leftJoin('senderVariant.product', 'senderProduct');
    sql.leftJoin('items.recipientVariant', 'recipientVariant');
    sql.leftJoin('recipientVariant.product', 'recipientProduct');
    sql.addSelect('st.*');
    sql.addSelect('senderWarehouse.name');
    sql.addSelect('senderWarehouse.displayId');
    sql.addSelect('recipientWarehouse.name');
    sql.addSelect('recipientWarehouse.displayId');
    sql.addSelect('items.id');
    sql.addSelect('items.senderId');
    sql.addSelect('items.senderWarehouseId');
    sql.addSelect('items.recipientId');
    sql.addSelect('items.recipientWarehouseId');
    sql.addSelect('items.stockTransferId');
    sql.addSelect('items.senderVariantId');
    sql.addSelect('items.recipientVariantId');
    sql.addSelect('items.senderSellable');
    sql.addSelect('items.senderDamaged');
    sql.addSelect('items.recipientSellable');
    sql.addSelect('items.recipientDamaged');
    sql.addSelect('items.transferredSellable');
    sql.addSelect('items.transferredDamaged');
    sql.addSelect('items.companyId');
    sql.addSelect('senderVariant.id');
    sql.addSelect('senderVariant.sku');
    sql.addSelect('senderVariant.prefix');
    sql.addSelect('senderVariant.productId');
    sql.addSelect('senderVariant.images');
    sql.addSelect('recipientVariant.id');
    sql.addSelect('recipientVariant.sku');
    sql.addSelect('recipientVariant.prefix');
    sql.addSelect('recipientVariant.productId');
    sql.addSelect('recipientVariant.images');
    sql.addSelect('senderProduct.name');
    sql.addSelect('recipientProduct.name');
    sql.where('st.id = :id', { id });
    sql.andWhere('st.companyId = :companyId', { companyId });
    const data = await sql.getOneOrFail();
    data.items = data.items?.map((item: StockTransferItems) => {
      return {
        ...item,
        senderSku: `${item.senderVariant.prefix}-${item.senderVariant.originSku}`,
        senderProduct: item.senderVariant.product.name,
        recipientSku: `${item.recipientVariant.prefix}-${item.recipientVariant.originSku}`,
        recipientProduct: item.recipientVariant.product.name,
      };
    });
    return data;
  }

  async exportDataDetail(id: number, dto: ExportDetailDto, request, header) {
    const { companyId } = request?.user;
    const sql = this.stockTransferRepository
      .createQueryBuilder('st')
      .andWhere('st.id = :id', { id })
      .leftJoinAndSelect('st.recipientWarehouse', 'recipientWarehouse')
      .leftJoinAndSelect('st.senderWarehouse', 'senderWarehouse')
      .leftJoinAndSelect('st.items', 'items')
      .leftJoinAndSelect('items.senderVariant', 'senderVariant')
      .leftJoinAndSelect('items.recipientVariant', 'recipientVariant')
      .leftJoinAndSelect('recipientVariant.product', 'recipientProduct')
      .leftJoinAndSelect('senderVariant.product', 'senderProduct');

    if (!isNil(header)) {
      const countryIds = header['country-ids']?.split(',');

      if (!isEmpty(countryIds))
        sql.andWhere('recipientWarehouse.countryCode IN (:...countryIds)', { countryIds });
    }

    if (companyId) {
      sql.andWhere('st.companyId = :companyId', { companyId });
    }

    const data = await sql.getOne();
    if (isEmpty(data)) throw new BadRequestException('Data not found!');
    const userIds = [data?.creatorId, data?.senderId, data?.recipientId];

    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: uniq(userIds) },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    let index = 1;

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(
      data?.type == StockTransferType.transfer
        ? `${dto['StockTransferDetail']}`
        : `${dto['StockWithdrawalDetail']}`,
    );
    worksheet.addRow([
      data?.type == StockTransferType.transfer
        ? `${dto['TransferCode']}`
        : `${dto['WithdrawalCode']}`,
      `${data?.code}`,
      `${dto['Type']}`,
      `${dto[StockTransferType[data?.type]]}`,
      `${dto['RecieptName']}`,
      `${data?.name}`,
      `${dto['Purpose']}`,
      `${dto[StockTransferPurpose[data?.purpose]]}`,
    ]);
    worksheet.addRow([
      `${dto['SenderWarehouse']}`,
      `${data?.senderWarehouse?.name}`,
      `${dto['ClientName']}`,
      `${userLookup[data?.senderId]?.name}`,
    ]);
    if (data?.type == StockTransferType.transfer) {
      worksheet.addRow([
        `${dto['RecipientWarehouse']}`,
        `${data?.recipientWarehouse?.name}`,
        `${dto['ClientName']}`,
        `${userLookup[data?.recipientId]?.name}`,
      ]);
    }
    worksheet.addRow([
      `${dto['Status']}`,
      `${dto[StockTransferStatus[data?.status]]}`,
      `${dto['Created']}`,
      `${moment(data?.createdAt).format('DD/MM/YY')}`,
      `${dto['Creator']}`,
      `${userLookup[data?.creatorId]?.name}`,
    ]);
    worksheet.addRow(['Note', `${data?.note ?? ''}`]);
    worksheet.addRow([]);
    if (data?.type == StockTransferType.transfer) {
      const row = worksheet.addRow([
        `${dto['No']}`,
        `${dto['TransferSKUImage']}`,
        `${dto['TransferSKU']}`,
        `${dto['ProductName']}`,
        `${dto['RecipientSKUImage']}`,
        `${dto['RecipientSKU']}`,
        `${dto['ProductName']}`,
        `${dto['SenderSellable']}`,
        `${dto['TransferedSellable']}`,
        `${dto['Recipientaftertransferred']}`,
        `${dto['SenderDamaged']}`,
        `${dto['TransferedDamaged']}`,
        `${dto['Recipientaftertransferred']}`,
      ]);
      row.eachCell((cell, colIndex) => {
        // Áp dụng border cho từng ô
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    } else {
      const row = worksheet.addRow([
        `${dto['No']}`,
        `${dto['Image']}`,
        `${dto['SKU']}`,
        `${dto['ProductName']}`,
        `${dto['OriginSellable']}`,
        `${dto['WithdrewSellable']}`,
        `${dto['SellableAfterWithdrew']}`,
        `${dto['OriginDamaged']}`,
        `${dto['WithdrewDamaged']}`,
        `${dto['DamagedAfterWithdrew']}`,
      ]);
      row.eachCell((cell, colIndex) => {
        // Áp dụng border cho từng ô
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    }

    worksheet.getCell('B1').font = { bold: true };
    worksheet.getCell('D1').font = { bold: true };
    worksheet.getCell('F1').font = { bold: true };
    worksheet.getCell('H1').font = { bold: true };
    worksheet.getCell('B2').font = { bold: true };
    worksheet.getCell('D2').font = { bold: true };
    worksheet.getCell('B3').font = { bold: true };
    worksheet.getCell('D3').font = { bold: true };
    worksheet.getCell('D3').font = { bold: true };
    worksheet.getCell('F3').font = { bold: true };
    worksheet.getCell('B4').font = { bold: true };
    if (data?.type == StockTransferType.transfer) {
      worksheet.getCell('D4').font = { bold: true };
      worksheet.getCell('F4').font = { bold: true };
    }

    if (!isEmpty(data?.items)) {
      const senderArrayImage = [];
      const recipientArrayImage = [];
      if (data?.type == StockTransferType.transfer) {
        for (const next of data?.items) {
          const damagedAfter =
            data?.purpose == StockTransferPurpose.damagedToSellable
              ? next?.senderDamaged - next?.transferredDamaged
              : data?.purpose == StockTransferPurpose.stockTransferred
              ? next?.recipientDamaged + next?.transferredDamaged
              : next?.senderDamaged - next?.transferredDamaged;

          const row = worksheet.addRow([
            index++,
            '',
            next?.senderVariant?.sku,
            next?.senderVariant?.product?.name,
            '',
            next?.recipientVariant?.sku,
            next?.recipientVariant?.product?.name,
            next?.senderSellable,
            next?.transferredSellable,
            next?.recipientSellable + next?.transferredSellable,
            next?.senderDamaged,
            next?.transferredDamaged,
            damagedAfter,
          ]);
          row.eachCell((cell, colIndex) => {
            // Áp dụng border cho từng ô
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          });
          if (!!next?.senderVariant?.images) {
            senderArrayImage.push({
              image: next?.senderVariant?.images[0],
              rowIndex: index + 6,
            });
          }
          if (!!next?.recipientVariant?.images) {
            recipientArrayImage.push({
              image: next?.recipientVariant?.images[0],
              rowIndex: index + 6,
            });
          }
        }
        // Save images to Excel
        const imageColumnSender = 2;
        const imageColumnRecipient = 5;
        if (senderArrayImage?.length > 0) {
          await saveImageToExcel(senderArrayImage, workbook, worksheet, imageColumnSender);
        }
        if (recipientArrayImage?.length > 0) {
          await saveImageToExcel(recipientArrayImage, workbook, worksheet, imageColumnRecipient);
        }
      } else {
        for (const next of data?.items) {
          const row = worksheet.addRow([
            index++,
            '',
            next?.senderVariant?.sku,
            next?.senderVariant?.product?.name,
            next?.senderSellable,
            next?.transferredSellable,
            next?.senderSellable - next?.transferredSellable,
            next?.senderDamaged,
            next?.transferredDamaged,
            next?.senderDamaged - next?.transferredDamaged,
          ]);
          row.eachCell((cell, colIndex) => {
            // Áp dụng border cho từng ô
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          });
          if (!!next?.senderVariant?.images) {
            senderArrayImage.push({
              image: next?.senderVariant?.images[0],
              rowIndex: index + 5,
            });
          }
        }
        // Save images to Excel
        const imageColumn = 2;
        if (senderArrayImage?.length > 0) {
          await saveImageToExcel(senderArrayImage, workbook, worksheet, imageColumn);
        }
      }
    }
    // Set width for image columns
    if (data?.type == StockTransferType.transfer) {
      // Columns B and E contain images (2 and 5)
      worksheet.getColumn(2).width = 24;
      worksheet.getColumn(5).width = 24;
    } else {
      // Column B contains image (2)
      worksheet.getColumn(2).width = 24;
    }

    return await workbook.xlsx.writeBuffer();
  }

  // async exportDataDetail(id: number, request, header) {
  //   const { companyId } = request?.user;
  //   const sql = this.stockTransferRepository
  //     .createQueryBuilder('st')
  //     .andWhere('st.id = :id', { id })
  //     .leftJoinAndSelect('st.recipientWarehouse', 'recipientWarehouse')
  //     .leftJoinAndSelect('st.senderWarehouse', 'senderWarehouse')
  //     .leftJoinAndSelect('st.items', 'items')
  //     .leftJoinAndSelect('items.senderVariant', 'senderVariant')
  //     .leftJoinAndSelect('items.recipientVariant', 'recipientVariant')
  //     .leftJoinAndSelect('recipientVariant.product', 'recipientProduct')
  //     .leftJoinAndSelect('senderVariant.product', 'senderProduct');

  //   if (!isNil(header)) {
  //     const countryIds = header['country-ids']?.split(',');

  //     if (!isEmpty(countryIds))
  //       sql.andWhere('recipientWarehouse.countryCode IN (:...countryIds)', { countryIds });
  //   }

  //   if (companyId) {
  //     sql.andWhere('st.companyId = :companyId', { companyId });
  //   }

  //   const data = await sql.getOne();
  //   if (isEmpty(data)) throw new BadRequestException('Data not found!');
  //   const userIds = [data?.creatorId, data?.senderId, data?.recipientId];

  //   const { data: uData } = await this.amqpConnection.request({
  //     exchange: 'identity-service-roles',
  //     routingKey: 'get-users-by-ids',
  //     payload: { ids: uniq(userIds) },
  //     timeout: 10000,
  //   });
  //   const users = uData as Users[];
  //   const userLookup: Record<string, Users> = reduce(
  //     users,
  //     (prev, item) => {
  //       prev[item.id] = item;
  //       return prev;
  //     },
  //     {},
  //   );
  //   let param = [];
  //   let index = 1;
  //   const xlsxSheets: WorkSheet[] = [];

  //   if (data?.type == StockTransferType.transfer) {
  //     param = reduce(
  //       data?.items,
  //       (prev: (string | number)[][], next) => {
  //         if (!isNil(next)) {
  //           prev.push([
  //             index++,
  //             `${next?.senderVariant?.prefix}-${next?.senderVariant?.sku}`,
  //             next?.senderVariant?.product?.name,
  //             `${next?.recipientVariant?.prefix}-${next?.recipientVariant?.sku}`,
  //             next?.recipientVariant?.product?.name,
  //             next?.senderSellable,
  //             next?.transferredSellable,
  //             next?.recipientSellable + next?.transferredSellable,
  //             next?.senderDamaged,
  //             next?.transferredDamaged,
  //             next?.recipientDamaged + next?.transferredDamaged,
  //           ]);
  //         }
  //         return prev;
  //       },
  //       [
  //         [
  //           `TransferCode: ${data?.code} - Type: ${StockTransferType[data?.type]} - RecieptName:  ${
  //             data?.name
  //           } - Purpose: ${StockTransferPurpose[data?.purpose]}`,
  //         ],
  //         [
  //           `SenderWarehouse: ${data?.senderWarehouse?.name} - ClientName: ${
  //             userLookup[data?.senderId]?.name
  //           }`,
  //         ],
  //         [
  //           `RecipientWarehouse: ${data?.recipientWarehouse?.name} - ClientName: ${
  //             userLookup[data?.recipientId]?.name
  //           }`,
  //         ],
  //         [
  //           `Status: ${StockTransferStatus[data?.status]} - Created: ${moment(
  //             data?.createdAt,
  //           ).format('DD/MM/YY')} - Creator: ${userLookup[data?.creatorId]?.name}`,
  //         ],
  //         [`Note: ${data?.note} `],
  //         [],
  //         [
  //           'No',
  //           'TransferSKU',
  //           'ProductName',
  //           'RecipientSKU',
  //           'ProductName',
  //           'SenderSellable',
  //           'TransferedSellable',
  //           'Recipientaftertransferred',
  //           'SenderDamaged',
  //           'TransferedDamaged',
  //           'Recipientaftertransferred',
  //         ],
  //       ],
  //     );

  //     xlsxSheets.push({
  //       name: 'Stock Transfer Detail',
  //       data: param,
  //       options: {},
  //     });
  //   } else {
  //     param = reduce(
  //       data?.items,
  //       (prev: (string | number)[][], next) => {
  //         if (!isNil(next)) {
  //           prev.push([
  //             index++,
  //             `${next?.senderVariant?.prefix}-${next?.senderVariant?.sku}`,
  //             next?.senderVariant?.product?.name,
  //             next?.senderSellable,
  //             next?.transferredSellable,
  //             next?.senderSellable - next?.transferredSellable,
  //             next?.senderDamaged,
  //             next?.transferredDamaged,
  //             next?.senderDamaged - next?.transferredDamaged,
  //           ]);
  //         }
  //         return prev;
  //       },
  //       [
  //         [
  //           `WithdrawalCode: ${data?.code}  - Type: ${
  //             StockTransferType[data?.type]
  //           } - RecieptName: ${data?.name} - Purpose: ${StockTransferPurpose[data?.purpose]}`,
  //         ],
  //         [
  //           `Warehouse: ${data?.senderWarehouse?.name} - ClientName: ${
  //             userLookup[data?.senderId]?.name
  //           }`,
  //         ],
  //         [
  //           `Status: ${StockTransferStatus[data?.status]} - Created: ${moment(
  //             data?.createdAt,
  //           ).format('DD/MM/YY')} - Creator: ${userLookup[data?.creatorId]?.name}`,
  //         ],
  //         [`Note: ${data?.note ?? ''} `],
  //         [],
  //         [
  //           'No',
  //           'SKU',
  //           'ProductName',
  //           'OriginSellable',
  //           'WithdrewSellable',
  //           'SellableAfterWithdrew',
  //           'OriginDamaged',
  //           'WithdrewDamaged',
  //           'DamagedAfterWithdrew',
  //         ],
  //       ],
  //     );

  //     xlsxSheets.push({
  //       name: 'Stock Withdrawal Detail',
  //       data: param,
  //       options: {},
  //     });
  //   }
  //   const buffer = xlsx.build(xlsxSheets);
  //   return buffer;
  // }

  async exportData(query: FilterStockTransfer, request, header) {
    const { warehouses, type, companyId, isAdmin } = request?.user;
    const {
      recipientIds,
      warehouseIds,
      from,
      to,
      name,
      clientIds,
      status,
      variantIds,
      ids,
    } = query;
    const sql = this.stockTransferRepository.createQueryBuilder('st');
    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');

      if (!isEmpty(whIds)) sql.andWhere('st.senderWarehouseId IN (:...whIds)', { whIds });
    }

    if (clientIds?.length > 0) sql.andWhere('st.senderId IN (:...clientIds)', { clientIds });
    if (recipientIds?.length > 0)
      sql.andWhere('st.recipientId IN (:...recipientIds)', { recipientIds });
    if (warehouseIds?.length > 0)
      sql.andWhere('st.senderWarehouseId IN (:...warehouseIds)', { warehouseIds });
    if (status?.length > 0) sql.andWhere('st.status IN (:...status)', { status });
    if (companyId) sql.andWhere('st.companyId = :companyId', { companyId });

    if (!isEmpty(variantIds)) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where(
            'items.sender_variant_id IN (:...variantIds)',
          ).orWhere('items.recipient_variant_id IN (:...variantIds)', { variantIds });
        }),
      );
    }

    if (name) {
      sql.andWhere(
        new Brackets(qb => {
          qb.where('st.note ILIKE :name')
            .orWhere('st.name ILIKE :name')
            .orWhere('st.code ILIKE :name', { name: `%${name}%` });
        }),
      );
    }
    if (from) sql.andWhere('st.createdAt >= :from', { from });
    if (to) sql.andWhere('st.createdAt <= :to', { to });
    if (!isEmpty(ids)) sql.andWhere('st.id IN (:...ids)', { ids });
    sql.leftJoin('st.senderWarehouse', 'senderWarehouse');
    sql.leftJoin('st.recipientWarehouse', 'recipientWarehouse');
    sql.leftJoinAndSelect('st.items', 'items');
    sql.leftJoin('items.senderVariant', 'senderVariant');
    sql.leftJoin('senderVariant.product', 'senderProduct');
    sql.leftJoin('items.recipientVariant', 'recipientVariant');
    sql.leftJoin('recipientVariant.product', 'recipientProduct');
    sql.addSelect('st.*');
    sql.addSelect('senderWarehouse.name');
    sql.addSelect('senderWarehouse.displayId');
    sql.addSelect('recipientWarehouse.name');
    sql.addSelect('recipientWarehouse.displayId');

    // sql.addSelect('variants.senderVariant.id');
    // sql.addSelect('items.id');
    sql.addSelect('senderVariant.id');
    sql.addSelect('senderVariant.sku');
    sql.addSelect('senderVariant.prefix');
    sql.addSelect('senderVariant.productId');
    sql.addSelect('recipientVariant.id');
    sql.addSelect('recipientVariant.sku');
    sql.addSelect('recipientVariant.prefix');
    sql.addSelect('recipientVariant.productId');
    sql.addSelect('senderProduct.name');
    sql.addSelect('recipientProduct.name');
    sql.orderBy('st.updatedAt', 'DESC');

    const data = await sql.getMany();
    if (isEmpty(data)) throw new BadRequestException('Data not found!');
    let result = [];
    for (const ele of data) {
      const variant = ele?.items.map(x => ({
        ...x,
        id: ele.id,
        createdAt: ele.createdAt,
        note: ele.note,
        name: ele.name,
        status: ele.status,
        type: ele.type,
        purpose: ele.purpose,
        senderWarehouse: ele?.senderWarehouse,
        recipientWarehouse: ele?.recipientWarehouse,
        senderId: ele?.senderId,
        recipientId: ele?.recipientId,
        creatorId: ele.creatorId,
        code: ele.code,
        dueDate: ele.dueDate,
        senderSellable: x?.senderSellable,
        transferredSellable: x?.transferredSellable,
        recipientSellable: x?.recipientSellable,
        senderDamaged: x?.senderDamaged,
        transferredDamaged: x?.transferredDamaged,
        recipientDamaged: x?.recipientDamaged,
      }));
      result = result.concat(variant);
    }
    const userIds = [];
    for (const item of result) {
      userIds.push(item?.creatorId, item?.senderId, item?.recipientId);
    }

    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: uniq(userIds) },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    let param = [];
    let index = 1;
    param = reduce(
      result,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          const damagedAfter =
            next?.purpose == StockTransferPurpose.damagedToSellable
              ? next?.senderDamaged - next?.transferredDamaged
              : next?.purpose == StockTransferPurpose.stockTransferred
              ? next?.recipientDamaged + next?.transferredDamaged
              : next?.senderDamaged - next?.transferredDamaged;

          prev.push([
            index++,
            next?.code,
            next?.name,
            StockTransferType[next?.type],
            StockTransferPurpose[next?.purpose],
            next?.senderWarehouse?.name,
            userLookup[next?.senderId]?.name,
            next?.type == StockTransferType.transfer ? next?.recipientWarehouse?.name : '',
            next?.type == StockTransferType.transfer ? userLookup[next?.recipientId]?.name : '',
            next?.dueDate ? moment(next?.dueDate).format('DD/MM/YY') : '',
            StockTransferStatus[next?.status],
            next?.note,
            moment(next?.createdAt).format('DD/MM/YY'),
            userLookup[next?.creatorId]?.name,
            next?.senderVariant?.sku,
            next?.senderVariant?.product?.name,
            next?.type == StockTransferType.transfer
              ? next?.recipientVariant?.sku
              : '',
            next?.type == StockTransferType.transfer ? next?.recipientVariant?.product?.name : '',
            next?.senderSellable,
            next?.transferredSellable,
            next?.type == StockTransferType.transfer
              ? next?.recipientSellable + next?.transferredSellable
              : next?.senderSellable - next?.transferredSellable,
            next?.senderDamaged,
            next?.transferredDamaged,
            damagedAfter,
          ]);
        }
        return prev;
      },
      [
        [
          'No',
          'RecieptCode',
          'ReceiptName',
          'Type',
          'Purpose',
          'SenderWarehouse',
          'SenderClient',
          'RecipientWarehouse',
          'RecipientClient',
          'DueDate',
          'Status',
          'Note',
          'Created',
          'Creator',
          'Transfer/WithdrawalSKU',
          'Productname',
          'RecipientSKU',
          'Productname',
          'OriginSellable',
          'Withdrew/Transfer',
          'SellableAfter',
          'OriginDamaged',
          'Withdrew/Transfer',
          'DamagedAfter',
        ],
      ],
    );
    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'Stock Transfer Withdrawal',
      data: param,
      options: {},
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }
}
