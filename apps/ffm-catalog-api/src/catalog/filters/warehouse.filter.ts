import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { WarehouseStatus, WarehouseType } from '../enum/warehouse-type.enum';
export class FilterWarehouse {

    @IsOptional()
    @ApiProperty({ required: false, type: 'string' })
    name?: string;

    @IsOptional()
    @ArrayTransform()
    ids?: number[];

    @IsOptional()
    @ArrayTransform()
    displayIds?: string[];

    @IsOptional()
    getAll?: boolean;

    @IsOptional()
    getOriginWh?: boolean;

    @IsOptional()
    countryId?: string;

    @IsOptional()
    @ArrayTransform()
    countryIds?: string[];

    @ApiProperty({ required: false, enum: WarehouseType })
    @IsOptional()
    @EnumTransform(WarehouseType)
    type?: WarehouseType;

    @ApiProperty({ required: false, enum: WarehouseStatus })
    @IsOptional()
    @EnumTransform(WarehouseStatus)
    status?: WarehouseStatus;
}
