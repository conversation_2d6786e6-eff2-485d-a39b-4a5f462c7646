import { ApiProperty } from '@nestjs/swagger';
import { IsEmpty, IsEnum, IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { ProductStatus } from '../enum/product-status.enum';
import {
  ExportType,
  SortByType,
  SortType,
  StockInventoryFilterColumns,
  TypeInventory,
  TypePurpose,
} from '../enum/stock-inventory-type.enum';
export class FilterFluctuation {
  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  categoryIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  clientIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  variantIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  warehouseIds: number[];

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ApiProperty({ required: false, enum: ExportType })
  @IsOptional()
  @EnumTransform(ExportType)
  export?: ExportType;

  @ApiProperty({ required: false, enum: SortByType })
  @IsOptional()
  @EnumTransform(SortByType)
  sortBy?: SortByType;

  @ApiProperty({ required: false, enum: SortType })
  @IsOptional()
  @EnumTransform(SortType)
  sortType?: SortType;

  @ApiProperty({ required: false })
  @IsOptional()
  image: boolean;
}

export class FilterFluctuationDetail {
  @ApiProperty({ required: false, enum: TypeInventory, isArray: true })
  @IsEnum(TypeInventory, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(TypeInventory)
  inventory?: TypeInventory[];

  @ApiProperty({ required: false, enum: TypePurpose, isArray: true })
  @IsEnum(TypePurpose, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(TypePurpose)
  purpose?: TypePurpose[];

  @ApiProperty({ required: false })
  @IsOptional()
  clientId: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  variantId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  warehouseIds: number[];

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;
}
