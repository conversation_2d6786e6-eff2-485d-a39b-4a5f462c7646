import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsIn, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { ProductStatus } from '../enum/product-status.enum';
import { SortType, StockInventoryFilterColumns } from '../enum/stock-inventory-type.enum';
import { StockTransferPurpose } from '../enum/stock-transfer.enum';
export class FilterStock {
  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  clientId?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  warehouseId?: number;

  @IsOptional()
  @ApiProperty({ required: false })
  cateId?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  name?: string;

  @ApiProperty({ required: false, enum: ProductStatus })
  @IsOptional()
  @EnumTransform(ProductStatus)
  status?: ProductStatus;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @IsOptional()
  @ApiProperty({ required: false, enum: StockInventoryFilterColumns, type: 'string' })
  @IsEnum(StockInventoryFilterColumns)
  // @IsIn(Object.keys(StockInventoryFilterColumns))
  filterBy?: StockInventoryFilterColumns;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  filterRangeFrom?: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  filterRangeTo?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  clientIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  categoryIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  variantIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  skus: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  warehouseIds: number[];

  @IsOptional()
  @ApiProperty({ required: false, enum: SortType, type: 'string' })
  @IsEnum(SortType)
  sort?: SortType;

  @IsOptional()
  @ApiProperty({ required: false, enum: StockInventoryFilterColumns, type: 'string' })
  @IsEnum(StockInventoryFilterColumns)
  typeSort?: StockInventoryFilterColumns;

  @IsOptional()
  @ApiProperty({ required: false, enum: StockTransferPurpose })
  @IsEnum(StockTransferPurpose)
  typePurpose?: StockTransferPurpose;

  @IsOptional()
  getAllCountry?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  image: boolean;
}
