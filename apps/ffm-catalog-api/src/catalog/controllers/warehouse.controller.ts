import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  Headers,
  Delete,
} from '@nestjs/common';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { UpdateWarehouseUser, WarehouseDto } from '../../dtos/warehouse.dto';
import { SlotWarehouses } from '../../entities/warehouses';
import { FilterWarehouse } from '../filters/warehouse.filter';
import { WarehouseService } from '../services/warehouse.service';
import { Permission } from 'core/enums/permission-ffm.enum';
import { WarehouseClientAllocation } from '../../entities/warehouse-client-allocation.entity';
import { WarehouseClientAllocationDto } from '../../dtos/warehouse-client-allowcation.dto';
import { WarehouseSession } from '../../entities/warehouse-session.entity';
import { WarehouseSessionDto } from '../../dtos/warehouse-session.dto';

@Controller('warehouse')
export class WarehouseController {
  constructor(private whService: WarehouseService) {}

  @Get('allocation')
  @Auth(Permission.orderAllocationRule)
  async allocation(
    @Request() request,
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterWarehouse,
    @Headers() header,
  ): Promise<[SlotWarehouses[], number]> {
    return this.whService.findAll(pagination, { ...query, getAll: true }, request, header);
  }

  @Get('allocation/:id')
  @Auth(Permission.orderAllocationRule)
  async detailAllocation(
    @Request() request,
    @Headers() header,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WarehouseClientAllocation> {
    return this.whService.detailAllocation(request, header, id);
  }

  @Get('')
  @Auth()
  async list(
    @Request() request,
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterWarehouse,
    @Headers() header,
  ): Promise<[SlotWarehouses[], number]> {
    return this.whService.findAll(pagination, query, request, header);
  }

  @Get('all-country')
  @Auth()
  async getAllCountry(
    @Request() request,
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterWarehouse,
    @Headers() header,
  ): Promise<SlotWarehouses[]> {
    return this.whService.getAllCountry(pagination, query, request, header);
  }

  @Post('allocation')
  @Auth(Permission.orderAllocationRule)
  async createWarehouseAllocation(
    @Request() request,
    @Body() data: WarehouseClientAllocationDto,
  ): Promise<WarehouseClientAllocation> {
    return this.whService.createWarehouseAllocation(request, data);
  }

  @Put('allocation')
  @Auth(Permission.orderAllocationRule)
  async updateWarehouseAllocation(
    @Request() request,
    @Body() data: WarehouseClientAllocationDto,
  ): Promise<any> {
    return this.whService.removeWarehouseAllocation(request, data);
  }

  @Post('')
  @Auth(Permission.createWarehouse)
  async create(@Request() request, @Body() data: WarehouseDto): Promise<SlotWarehouses> {
    return this.whService.createWh(request, data);
  }

  @Post('update-user')
  @Auth(Permission.createWarehouse)
  async updateUser(@Request() request, @Body() data: UpdateWarehouseUser): Promise<SlotWarehouses> {
    return this.whService.updateUserWh(request, data);
  }

  @Put(':id')
  @Auth(Permission.createWarehouse)
  async updateWh(
    @Request() request,
    @Body() data: WarehouseDto,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SlotWarehouses> {
    return this.whService.updateWh(id, data, request);
  }

  @Get(':id')
  // @Auth(Role.warehouseEdit)
  @Auth(Permission.warehouse)
  async detail(@Headers() header, @Param('id', ParseIntPipe) id: number): Promise<SlotWarehouses> {
    return this.whService.detail(id, header);
  }

  @Post(':id/create-session')
  @Auth(Permission.createWarehouse)
  async createSession(
    @Request() request,
    @Body() data: WarehouseSessionDto,
    @Param('id', ParseIntPipe) id: number,
    @Headers() header,
  ): Promise<WarehouseSession> {
    return this.whService.createWhSession(id, data, request, header);
  }

  @Put(':id/session/:sessionId')
  @Auth(Permission.createWarehouse)
  async updateWhSession(
    @Request() request,
    @Body() data: WarehouseSessionDto,
    @Param('id', ParseIntPipe) id: number,
    @Param('sessionId', ParseIntPipe) sessionId: number,
  ): Promise<WarehouseSession> {
    return this.whService.updateWhSession(id, sessionId, data, request);
  }

  @Delete(':id/session/:sessionId')
  @Auth(Permission.createWarehouse)
  async deleteWhSession(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
    @Param('sessionId', ParseIntPipe) sessionId: number,
  ): Promise<WarehouseSession> {
    return this.whService.deleteWhSession(id, sessionId, request);
  }

  @Get(':id/sessions')
  @Auth(Permission.warehouse)
  async getAllWhSession(@Param('id', ParseIntPipe) id: number): Promise<WarehouseSession[]> {
    return this.whService.getAllWhSession(id);
  }

  @Get(':id/session/:sessionId')
  @Auth(Permission.warehouse)
  async getWhSession(
    @Param('id', ParseIntPipe) id: number,
    @Param('sessionId', ParseIntPipe) sessionId: number,
  ): Promise<WarehouseSession> {
    return this.whService.getWhSession(id, sessionId);
  }
}
