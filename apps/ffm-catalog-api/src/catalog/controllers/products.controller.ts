import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  Response,
} from '@nestjs/common';
import { ApiBody } from '@nestjs/swagger';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { Permission } from 'core/enums/permission-ffm.enum';
import { DelProductComboDto, DelVariantDto, ProductComboDto } from '../../dtos/product-combo.dto';
import { ProductsDto, ProductUpdateStatusDto } from '../../dtos/products.dto';
import { Attributes } from '../../entities/attributes.entity';
import { ProductVariation } from '../../entities/product-variation.entity';
import { Product } from '../../entities/product.entity';
import { TypeRemove } from '../enum/product-status.enum';
import { FilterProduct, FilterVariant } from '../filters/product.filter';
import { ProductsService } from '../services/products.service';

@Controller('product')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get('')
  @Auth(Permission.product)
  async fetch(
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterProduct,
    @Request() request,
    @Headers() header,
  ): Promise<[Product[], number]> {
    const data = await this.productsService.find(request, pagination, query, header);
    return data;
  }

  @Get('/export')
  @Auth(Permission.exportProduct)
  async exportData(
    @Response() response,
    @Query() query: FilterProduct,
    @Request() request,
    @Headers() header,
  ) {
    const buffer = await this.productsService.exportExcel(query, request, header);
    response.attachment(`Product information.xlsx`);
    return response.send(buffer);
  }

  @Get('/all-products')
  @Auth(Permission.product)
  async fetchAllProduct(
    @Query() query: FilterVariant,
    @Request() request,
    @Headers() header,
  ): Promise<Product[]> {
    return await this.productsService.findAllProducts(query, request, header);
  }

  @Get('/all-variants')
  @Auth(Permission.product)
  async fetchAllVariants(
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterVariant,
    @Request() request,
    @Headers() header,
  ): Promise<ProductVariation[]> {
    return await this.productsService.findAllVariants(query, pagination, request, header);
  }

  @Get('/variants')
  @Auth()
  async fetchVariants(
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterProduct,
    @Request() request,
    @Headers() header,
  ): Promise<[ProductVariation[], number]> {
    const data = !query?.search
      ? await this.productsService.findVariant(request, pagination, query, header)
      : await this.productsService.searchVariant(request, pagination, query, header);

    return data;
  }

  @Post('')
  @Auth(Permission.createProduct)
  async create(@Request() request, @Body() data: ProductsDto, @Headers() header): Promise<Product> {
    return this.productsService.create(data, request?.user?.id, request, header);
  }

  @Post('/combo')
  @Auth(Permission.createProduct)
  async createCombo(
    @Request() request,
    @Body() data: ProductComboDto,
    @Headers() header,
  ): Promise<Product> {
    return this.productsService.createCombo(data, request?.user?.id, request, header);
  }

  @Get(':id')
  @Auth(Permission.product)
  async view(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
    @Headers() header,
  ): Promise<Product> {
    return this.productsService.findOne(id, header, request);
  }

  @Get('combo/:id')
  @Auth(Permission.product)
  async viewCombo(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
    @Headers() header,
  ): Promise<Product> {
    return this.productsService.findCombo(id, header, request);
  }

  @Get('attributes/:id')
  @Auth()
  async attributes(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Attributes[]> {
    return this.productsService.findAttributes(id);
  }

  @Get('histories/:id')
  @Auth()
  async detailHistories(@Request() request, @Param('id', ParseIntPipe) id: number): Promise<any> {
    return this.productsService.detailHistories(id, request);
  }

  @Put(':id')
  @Auth(Permission.createProduct)
  async update(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
    @Body() data: ProductsDto,
    @Headers() header,
  ): Promise<Product> {
    return this.productsService.update(data, id, request, header);
  }

  @Put('status/:id')
  @Auth(Permission.createProduct)
  async updateStatus(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
    @Body() data: ProductUpdateStatusDto,
  ): Promise<Product> {
    return this.productsService.updateStatus(data, id, request);
  }

  @Put('combo/:id')
  @Auth(Permission.createProduct)
  async updateCombo(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
    @Body() data: ProductComboDto,
  ): Promise<Product> {
    return this.productsService.updateCombo(data, id, request);
  }

  @Put('update/att/:id')
  @Auth()
  async updateAtt(@Request() request, @Param('id', ParseIntPipe) id: number) {
    return this.productsService.updateAttribute(id, request);
  }

  // @Put('variant/remove/:id')
  // @Auth(Permission.createProduct)
  // async removeVariant(
  //   @Request() request,
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body() data: DelVariantDto,
  // ) {
  //   if (data?.type == TypeRemove.delete) return this.productsService.removeVariant(id, request);
  //   return this.productsService.hideVariant(id, request);
  // }

  @Delete(':id')
  @Auth(Permission.createProduct)
  async delete(@Request() request, @Param('id', ParseIntPipe) id: number): Promise<Product> {
    return this.productsService.delete(id, request);
  }

  // @Put('remove/combo')
  // @Auth(Permission.createProduct)
  // async deleteProductCombo(@Request() request, @Body() data: DelProductComboDto) {
  //   if (data?.type == TypeRemove.delete)
  //     return this.productsService.deleteProductCombo(data, request);
  //   return this.productsService.hideProductCombo(data, request);
  // }
}
