import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  Response,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation } from '@nestjs/swagger';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { Permission } from 'core/enums/permission-ffm.enum';
import * as moment from 'moment-timezone';
import { $enum } from 'ts-enum-util';
import { PoDto, PoImportDto } from '../../dtos/po.dto';
import { PurchaseOrderItem } from '../../entities/purchase-order-item.entity';
import { PurchaseOrder } from '../../entities/purchase-order.entity';
import {
  NEXT_PO_STATUS,
  PO_STATUS_EDIT,
  PO_STATUS_EDIT_PRODUCT,
} from '../constants/po-statuses.constant';
import { PoStatus } from '../enum/po-status.enum';
import { FilterPo } from '../filters/filterPo.filter';
import { PoService } from '../services/po.service';

@Controller('po')
export class PoController {
  constructor(private poService: PoService) {}

  @Get('config')
  @Auth()
  async config() {
    const poStatus = {};
    for (const [key, value] of Object.entries(NEXT_PO_STATUS)) {
      poStatus[$enum(PoStatus).getKeyOrDefault(Number(key), '')] = value.map((item: any) =>
        $enum(PoStatus).getKeyOrDefault(Number(item), ''),
      );
    }

    return {
      poStatus,
      isEdit: PO_STATUS_EDIT.map(item => {
        return $enum(PoStatus).getKeyOrDefault(Number(item), '');
      }),
      isEditProduct: PO_STATUS_EDIT_PRODUCT.map(item => {
        return $enum(PoStatus).getKeyOrDefault(Number(item), '');
      }),
    };
  }

  @Get('search')
  @Auth(Permission.import)
  async search(
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterPo,
    @Request() request,
    @Headers() header,
  ): Promise<[PurchaseOrder[], number]> {
    return this.poService.searchPo(pagination, query, request, header);
  }

  @Get('export')
  @Auth()
  async exportData(
    @Response() response,
    @Query() query: FilterPo,
    @Request() request,
    @Headers() header,
  ) {
    const { buffer, arrWareHouse } = await this.poService.exportExcel(query, request, header);
    const listWareHouseToString = arrWareHouse.join('-');
    response.attachment(
      `Import Receipt-${listWareHouseToString}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );

    return response.send(buffer);
  }

  @Get('')
  @Auth(Permission.import)
  async list(
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterPo,
    @Request() request,
    @Headers() header,
  ): Promise<[PurchaseOrder[], number]> {
    return this.poService.findAll(pagination, query, request, header);
  }

  @Get('count')
  @Auth(Permission.import)
  async countPo(
    @Pagination() pagination: PaginationOptions,
    @Query() query: FilterPo,
    @Request() request,
    @Headers() header,
  ): Promise<any> {
    return this.poService.countPo(pagination, query, request, header);
  }

  @Post('')
  @Auth(Permission.import)
  async create(@Request() request, @Body() data: PoDto): Promise<PurchaseOrder> {
    return this.poService.createPo(request, data);
  }

  @Post('/import')
  @Auth()
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: '',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importOrders(
    @Request() request: Record<string, any>,
    @Query() filters,
    @Body() data: PoImportDto,
    @UploadedFile('file') file,
  ) {
    const { buffer } = file;
    return this.poService.importExcel(request, buffer, data);
  }

  @Put(':id')
  @Auth(Permission.import)
  async updatePo(
    @Request() request,
    @Body() data: PoDto,
    @Param('id', ParseIntPipe) id: number,
    @Headers() header,
  ): Promise<PurchaseOrder> {
    const countryId = Number(header['country-ids']);
    return this.poService.updatePo(data, id, request);
  }

  @Get('histories/:id')
  @Auth(Permission.import)
  async detailHistories(@Param('id', ParseIntPipe) id: number): Promise<any> {
    return this.poService.detailHistories(id);
  }

  @Get(':id')
  @Auth(Permission.import)
  async detail(
    @Request() request,
    @Headers() header,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<PurchaseOrder> {
    return this.poService.detail(id, request, header);
  }

  @Get('product/:id')
  @Auth(Permission.import)
  async findProductPo(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: FilterPo,
  ): Promise<PurchaseOrderItem[]> {
    return this.poService.findProductPo(id, query);
  }

  @Post('/importxx')
  @Auth()
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: '',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importOrdersx(
    @Request() request: Record<string, any>,
    @Query() filters,
    @Body() data: PoImportDto,
    @UploadedFile('file') file,
    @Response() response,
    ) {
    const { buffer } = file;
    const bufferx = await this.poService.importExcelx(request, buffer, data);
    // const bufferx = await this.poService.importExcelx(data, req, headers);
    response.attachment(
      `file 200k.xlsx`,
    );
    return response.send(bufferx);
  }
}
