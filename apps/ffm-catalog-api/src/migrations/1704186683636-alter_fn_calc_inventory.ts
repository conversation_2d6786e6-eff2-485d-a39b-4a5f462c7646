import { MigrationInterface, QueryRunner } from 'typeorm';

export class alterFnCalcInventory1704186683636 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE 
	OR REPLACE FUNCTION "public"."fn_calc_inventory" ( ) RETURNS "pg_catalog"."trigger" AS $BODY$ DECLARE
	stocked_phy_sellable DOUBLE PRECISION;
phy_in_progress DOUBLE PRECISION;
phy_awaiting_allocated DOUBLE PRECISION;
phy_awaiting_reimport DOUBLE PRECISION;
agg_actual_good DOUBLE PRECISION;
agg_damaged DOUBLE PRECISION;
agg_lost DOUBLE PRECISION;
agg_stocked_qtt DOUBLE PRECISION;
agg_new_imported DOUBLE PRECISION;
BEGIN 
	IF ( TG_OP = 'UPDATE' AND exist(hstore(NEW) - hstore(OLD), 'stock_id' ) ) THEN 
		EXECUTE 'UPDATE stock_inventory SET stocked_phy_sellable = stocked_phy_sellable + ($1) WHERE id = ' || NEW.stock_id USING NEW.stocked_phy_sellable;
		EXECUTE 'UPDATE stock_inventory SET stocked_phy_sellable = stocked_phy_sellable - ($1) WHERE id = ' || OLD.stock_id USING NEW.stocked_phy_sellable;
	ELSE
	
		IF ( TG_OP <> 'INSERT' ) THEN
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_new_imported' ) THEN
					agg_new_imported := ( NEW.agg_new_imported - OLD.agg_new_imported );
				ELSE agg_new_imported := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'stocked_phy_sellable' ) THEN
					stocked_phy_sellable := ( NEW.stocked_phy_sellable - OLD.stocked_phy_sellable );
				ELSE stocked_phy_sellable := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'phy_in_progress' ) THEN
					phy_in_progress := ( NEW.phy_in_progress - OLD.phy_in_progress );
				ELSE phy_in_progress := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'phy_awaiting_allocated' ) THEN
					phy_awaiting_allocated := ( NEW.phy_awaiting_allocated - OLD.phy_awaiting_allocated );
				ELSE phy_awaiting_allocated := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'phy_awaiting_reimport' ) THEN
					phy_awaiting_reimport := ( NEW.phy_awaiting_reimport - OLD.phy_awaiting_reimport );
				ELSE phy_awaiting_reimport := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_actual_good' ) THEN
					agg_actual_good := ( NEW.agg_actual_good - OLD.agg_actual_good );
				ELSE agg_actual_good := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_damaged' ) THEN
					agg_damaged := ( NEW.agg_damaged - OLD.agg_damaged );
				ELSE agg_damaged := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_lost' ) THEN
					agg_lost := ( NEW.agg_lost - OLD.agg_lost );
				ELSE agg_lost := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_stocked_qtt' ) THEN
					agg_stocked_qtt := ( NEW.agg_stocked_qtt - OLD.agg_stocked_qtt );
				ELSE agg_stocked_qtt := 0;
				
			END IF;
			
		ELSE 
			stocked_phy_sellable := NEW.stocked_phy_sellable;
			phy_in_progress := NEW.phy_in_progress;
			phy_awaiting_allocated := NEW.phy_awaiting_allocated;
			phy_awaiting_reimport := NEW.phy_awaiting_reimport;
			agg_actual_good := NEW.agg_actual_good;
			agg_damaged := NEW.agg_damaged;
			agg_lost := NEW.agg_lost;
			agg_stocked_qtt := NEW.agg_stocked_qtt;
			agg_new_imported := NEW.agg_new_imported;
			
		END IF;
		
		EXECUTE 'UPDATE stock_inventory SET updated_at = now(), stocked_phy_sellable = stocked_phy_sellable + ($1), phy_in_progress = phy_in_progress + ($2), phy_awaiting_allocated = phy_awaiting_allocated + ($3), phy_awaiting_reimport = phy_awaiting_reimport + ($4), agg_actual_good = agg_actual_good + ($5), agg_damaged = agg_damaged + ($6), agg_lost = agg_lost + ($7), agg_stocked_qtt = agg_stocked_qtt + ($8), agg_new_imported = agg_new_imported + ($9), item_id = ($10)
		WHERE id = ' || NEW.stock_id USING stocked_phy_sellable,
		phy_in_progress,
		phy_awaiting_allocated,
		phy_awaiting_reimport,
		agg_actual_good,
		agg_damaged,
		agg_lost,
		agg_stocked_qtt,
		agg_new_imported,
		NEW.ID;
	END IF;
	
	RETURN NEW;
	
END;
$BODY$ LANGUAGE plpgsql VOLATILE COST 100
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE 
	OR REPLACE FUNCTION "public"."fn_calc_inventory" ( ) RETURNS "pg_catalog"."trigger" AS $BODY$ DECLARE
	stocked_phy_sellable DOUBLE PRECISION;
phy_in_progress DOUBLE PRECISION;
phy_awaiting_allocated DOUBLE PRECISION;
phy_awaiting_reimport DOUBLE PRECISION;
agg_actual_good DOUBLE PRECISION;
agg_damaged DOUBLE PRECISION;
agg_lost DOUBLE PRECISION;
agg_stocked_qtt DOUBLE PRECISION;
agg_new_imported DOUBLE PRECISION;
BEGIN 
	IF ( TG_OP = 'UPDATE' AND exist(hstore(NEW) - hstore(OLD), 'stock_id' ) ) THEN 
		EXECUTE 'UPDATE stock_inventory SET stocked_phy_sellable = stocked_phy_sellable + ($1) WHERE id = ' || NEW.stock_id USING NEW.stocked_phy_sellable;
		EXECUTE 'UPDATE stock_inventory SET stocked_phy_sellable = stocked_phy_sellable - ($1) WHERE id = ' || OLD.stock_id USING NEW.stocked_phy_sellable;
	ELSE
	
		IF ( TG_OP <> 'INSERT' ) THEN
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_new_imported' ) THEN
					agg_new_imported := ( NEW.agg_new_imported - OLD.agg_new_imported );
				ELSE agg_new_imported := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'stocked_phy_sellable' ) THEN
					stocked_phy_sellable := ( NEW.stocked_phy_sellable - OLD.stocked_phy_sellable );
				ELSE stocked_phy_sellable := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'phy_in_progress' ) THEN
					phy_in_progress := ( NEW.phy_in_progress - OLD.phy_in_progress );
				ELSE phy_in_progress := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'phy_awaiting_allocated' ) THEN
					phy_awaiting_allocated := ( NEW.phy_awaiting_allocated - OLD.phy_awaiting_allocated );
				ELSE phy_awaiting_allocated := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'phy_awaiting_reimport' ) THEN
					phy_awaiting_reimport := ( NEW.phy_awaiting_reimport - OLD.phy_awaiting_reimport );
				ELSE phy_awaiting_reimport := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_actual_good' ) THEN
					agg_actual_good := ( NEW.agg_actual_good - OLD.agg_actual_good );
				ELSE agg_actual_good := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_damaged' ) THEN
					agg_damaged := ( NEW.agg_damaged - OLD.agg_damaged );
				ELSE agg_damaged := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_lost' ) THEN
					agg_lost := ( NEW.agg_lost - OLD.agg_lost );
				ELSE agg_lost := 0;
				
			END IF;
			IF
				exist ( hstore ( NEW ) - hstore ( OLD ), 'agg_stocked_qtt' ) THEN
					agg_stocked_qtt := ( NEW.agg_stocked_qtt - OLD.agg_stocked_qtt );
				ELSE agg_stocked_qtt := 0;
				
			END IF;
			
		ELSE 
			stocked_phy_sellable := NEW.stocked_phy_sellable;
			phy_in_progress := NEW.phy_in_progress;
			phy_awaiting_allocated := NEW.phy_awaiting_allocated;
			phy_awaiting_reimport := NEW.phy_awaiting_reimport;
			agg_actual_good := NEW.agg_actual_good;
			agg_damaged := NEW.agg_damaged;
			agg_lost := NEW.agg_lost;
			agg_stocked_qtt := NEW.agg_stocked_qtt;
			agg_new_imported := NEW.agg_new_imported;
			
		END IF;
		
		EXECUTE 'UPDATE stock_inventory SET stocked_phy_sellable = stocked_phy_sellable + ($1), phy_in_progress = phy_in_progress + ($2), phy_awaiting_allocated = phy_awaiting_allocated + ($3), phy_awaiting_reimport = phy_awaiting_reimport + ($4), agg_actual_good = agg_actual_good + ($5), agg_damaged = agg_damaged + ($6), agg_lost = agg_lost + ($7), agg_stocked_qtt = agg_stocked_qtt + ($8), agg_new_imported = agg_new_imported + ($9), item_id = ($10)
		WHERE id = ' || NEW.stock_id USING stocked_phy_sellable,
		phy_in_progress,
		phy_awaiting_allocated,
		phy_awaiting_reimport,
		agg_actual_good,
		agg_damaged,
		agg_lost,
		agg_stocked_qtt,
		agg_new_imported,
		NEW.ID;
	END IF;
	
	RETURN NEW;
	
END;
$BODY$ LANGUAGE plpgsql VOLATILE COST 100
        `);
  }
}
