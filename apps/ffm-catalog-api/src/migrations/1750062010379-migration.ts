import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1750062010379 implements MigrationInterface {
    name = 'migration1750062010379'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "slot_warehouses" ADD "is_return_default" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "slot_warehouses" DROP COLUMN "is_return_default"`);
    }

}
