import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1681963923362 implements MigrationInterface {
    name = 'migration1681963923362'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "client_type" smallint`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "client_type"`);
    }

}
