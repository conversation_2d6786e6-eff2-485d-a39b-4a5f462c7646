import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1698743496417 implements MigrationInterface {
    name = 'migration1698743496417'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "languages" ("code" character varying(4) NOT NULL, "namespace" character varying(50) NOT NULL, "data" json NOT NULL DEFAULT '{}', "status" smallint NOT NULL DEFAULT '1', CONSTRAINT "PK_7ea66f096cfcd43a334dbe7e2bd" PRIMARY KEY ("code", "namespace"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "languages"`);
    }

}
