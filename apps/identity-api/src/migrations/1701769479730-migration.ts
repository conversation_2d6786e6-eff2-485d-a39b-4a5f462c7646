import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1701769479730 implements MigrationInterface {
    name = 'migration1701769479730'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_filter" ADD "user_id" integer`);
        await queryRunner.query(`CREATE INDEX "IDX_4b8372503bd39ccd852ce14678" ON "request_filter" ("user_id") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_4b8372503bd39ccd852ce14678"`);
        await queryRunner.query(`ALTER TABLE "request_filter" DROP COLUMN "user_id"`);
    }

}
