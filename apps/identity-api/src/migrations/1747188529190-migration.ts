import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1747188529190 implements MigrationInterface {
  name = 'migration1747188529190';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "markets" ADD "is_activate" boolean DEFAULT true`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "markets" DROP COLUMN "is_activate"`);
  }
}
