import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { ProjectStatus } from '../enums/project-status.enum';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';

export class ProjectsFilter {
  @ApiProperty({ required: false })
  @IsOptional()
  query?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  companyIds?: number[];

  @ApiProperty({ required: false, enum: ProjectStatus, isArray: true })
  @IsOptional()
  @EnumTransform(ProjectStatus)
  @NonEmptyTransform()
  @ArrayTransform()
  status?: ProjectStatus[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  userIds?: number[];

  _getAll?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isFilterActivateMarket?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isGetDeleted?: boolean;
}

export class ProjectFromFfmFilter {
  // @ApiProperty({ required: false })
  // @IsNotEmpty()
  // companyId?: number

  // @ApiProperty({ required: false })
  // @IsNotEmpty()
  // countryId?: number

  @ApiProperty({ required: false })
  @IsOptional()
  query?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  clientIds?: number[];
}

export class CountUsersOfProjectDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  projectIds: number[];
}
