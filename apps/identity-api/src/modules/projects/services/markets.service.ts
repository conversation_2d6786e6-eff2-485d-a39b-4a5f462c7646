import { defaultNack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateMarketDto, UpdateMarketDto } from 'apps/identity-api/src/dtos/market.dto';
import { FulfillmentPartner } from 'apps/identity-api/src/entities/fulfillment-partner.entity';
import { Market } from 'apps/identity-api/src/entities/market.entity';
import { MarketsFilter } from 'apps/identity-api/src/filters/markets.filter';
import { plainToInstance } from 'class-transformer';
import { identityConnection } from 'core/constants/database-connection.constant';
import { isEmpty, omit } from 'lodash';
import { FindCondition, getConnection, Repository } from 'typeorm';
import { ProjectService } from './project.service';

@Injectable()
export class MarketsService {
  constructor(
    @InjectRepository(Market, identityConnection)
    private marketRepo: Repository<Market>,
    @InjectRepository(FulfillmentPartner, identityConnection)
    private ffmPartnerRepo: Repository<FulfillmentPartner>,
    private projectService: ProjectService,
  ) {}

  async findById(id: number): Promise<Market> {
    return await this.marketRepo.findOne(id);
  }

  async findByIds(ids: number[]): Promise<Market[]> {
    const markets = await this.marketRepo.findByIds(ids);
    return markets;
  }

  async getMarkets(
    filter: MarketsFilter = {},
    header?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Market[]> {
    const companyId = request?.user?.companyId;
    const { query, projectIds, countryIds, ffmPartnerIds, ffmPartnerClientIds } = filter;
    const companyIds = filter.companyIds || [companyId] || [];
    const sql = this.marketRepo.createQueryBuilder('m').orderBy('id', 'DESC');
    if (projectIds) sql.andWhere('m.projectId IN (:...projectIds)', { projectIds });
    if (countryIds) sql.andWhere('m.countryId IN (:...countryIds)', { countryIds });
    if (!isEmpty(companyIds)) sql.andWhere('m.companyId IN (:...companyIds)', { companyIds });
    if (!isEmpty(ffmPartnerIds))
      sql.andWhere('m.fulfillmentPartnerId IN (:...ffmPartnerIds)', { ffmPartnerIds });
    if (!isEmpty(ffmPartnerClientIds))
      sql.andWhere('m.fulfillmentPartnerClientId IN (:...ffmPartnerClientIds)', {
        ffmPartnerClientIds,
      });
    return await sql.getMany();
  }

  async createMarket(
    data: CreateMarketDto,
    header?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Market> {
    const project = await this.projectService.findById(data.projectId, request.user);
    if (!project) throw new NotFoundException('Không tìm thấy dự án');
    const oldRecord = await this.marketRepo.find({
      companyId: project.companyId,
      projectId: data.projectId,
      countryId: data.countryId,
    });
    if (oldRecord) {
      throw new BadRequestException('Thị trường đã tồn tại');
    }
    const market = plainToInstance(Market, {
      companyId: project.companyId,
      projectId: data.projectId,
      countryId: data.countryId,
    });
    market.fulfillmentPartner = await this.ffmPartnerRepo.findOne(data.fulfillmentPartnerId);
    return await this.marketRepo.save(market);
  }

  async updateMarket(
    id: number,
    data: UpdateMarketDto,
    header?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Market> {
    const oldRecord = await this.marketRepo.findOne(id);
    if (!oldRecord) {
      throw new NotFoundException();
    }
    try {
      const updateData: Partial<Market> = omit(data, ['businessId']);
      if (data.fulfillmentPartnerId)
        updateData.fulfillmentPartner = await this.ffmPartnerRepo.findOne(
          data.fulfillmentPartnerId,
        );
      if (data.isActivate) {
        updateData.isActivate = data.isActivate;
      }
      return await this.marketRepo.save(
        plainToInstance(Market, {
          ...oldRecord,
          ...updateData,
        }),
      );
    } catch (error) {
      console.log(`error when update old market:`, error);
    }
    return oldRecord;
  }

  async migrateOldIntegratedFfmPartner() {
    const integratedMarkets = await this.marketRepo
      .createQueryBuilder('m')
      .where('m.fulfillment_partner_id IS NOT NULL')
      .andWhere('m.fulfillment_partner_client_id IS NULL')
      .leftJoinAndSelect('m.fulfillmentPartner', 'ffmPartner')
      .leftJoinAndSelect('ffmPartner.clients', 'clients')
      .getMany();

    if (isEmpty(integratedMarkets)) return integratedMarkets;

    const markets = integratedMarkets.reduce((prev, item) => {
      const idx = item.fulfillmentPartner.clients.findIndex(
        client =>
          client.clientId === item.fulfillmentPartner.clientId &&
          client.apiKey === item.fulfillmentPartner.apiKey,
      );
      if (idx !== -1)
        prev.push({
          id: item.id,
          fulfillmentPartnerClientId: item.fulfillmentPartner.clients[idx].id,
        });
      return prev;
    }, []);

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const results = await Promise.all(
        markets.map(it =>
          queryRunner.manager.update(
            Market,
            { id: it.id },
            { fulfillmentPartnerClientId: it.fulfillmentPartnerClientId },
          ),
        ),
      );
      await queryRunner.commitTransaction();
      console.log(`migration old records results`, results);
      return markets;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  @RabbitRPC({
    exchange: 'identity-service-projects',
    routingKey: 'find-market',
    queue: 'identity-find-market',
    errorHandler: defaultNackErrorHandler,
  })
  async findMarket(findOptions: FindCondition<Market>) {
    try {
      const data = await this.marketRepo.findOne({
        where: findOptions,
        relations: ['fulfillmentPartner', 'fulfillmentPartnerClient'],
      });
      if (!data) return new Nack(false);

      return data;
    } catch (error) {
      console.error(`error occurred when find market where with options`, findOptions, error);
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'identity-service-projects',
    routingKey: 'find-markets-list',
    queue: 'identity-find-markets-list',
    errorHandler: defaultNackErrorHandler,
  })
  async findMarketsList(findOptions: FindCondition<Market>) {
    try {
      const data = await this.marketRepo.find({
        where: findOptions,
        relations: ['fulfillmentPartner', 'fulfillmentPartnerClient'],
      });
      if (!data) return new Nack(false);

      return data;
    } catch (error) {
      console.error(`error occurred when find markets list where with options`, findOptions, error);
    }
    return new Nack(false);
  }
}
