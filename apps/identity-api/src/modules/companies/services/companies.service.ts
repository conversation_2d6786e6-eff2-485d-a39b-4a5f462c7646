import {
  AmqpConnection,
  defaultN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  RabbitRP<PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UpdateCompanyDto } from 'apps/identity-api/src/dtos/company.dto';
import { Company } from 'apps/identity-api/src/entities/company.entity';
import { Roles } from 'apps/identity-api/src/entities/roles.entity';
import { CompaniesFilter } from 'apps/identity-api/src/filters/companies.filter';
import { plainToInstance } from 'class-transformer';
import { COMPANY, COMPANY_NAME, USER_ROLES } from 'core/cache/constants/prefix.constant';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { identityConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { ENUM_PERMISSION_MAPPING } from 'core/enums/sale-permissions';
import { IRmqMessage } from 'core/interfaces';
import { isEmpty, isNil, omit } from 'lodash';
import { $enum } from 'ts-enum-util';
import { Brackets, getConnection, In, Repository } from 'typeorm';
import { BusinessesService } from '../../business/services/businesses.service';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { Business } from 'apps/identity-api/src/entities/business.entity';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company, identityConnection)
    private companyRepo: Repository<Company>,
    @InjectRepository(Roles, identityConnection)
    private rolesRepo: Repository<Roles>,
    private businessesService: BusinessesService,
    private amqpConnection: AmqpConnection,
    private redisCache: RedisCacheService,
    @InjectRedis()
    private redis: Redis,
  ) {}

  async getMyCompany(user: AuthUser): Promise<Company> {
    const companyId = user.companyId;
    return this.companyRepo
      .createQueryBuilder('c')
      .where(`c.id = ${companyId}`)
      .select(['c.id', 'c.name', 'c.icon', 'c.logo'])
      .getOne();
  }

  async findById(id: number, relations?: string[]): Promise<Company> {
    const company = await this.companyRepo.findOne(id, {
      relations: relations || ['business', 'roles', 'adminRole', 'fulfillmentPartners'],
    });
    return company;
  }

  async getAllName(filter?: CompaniesFilter): Promise<Company[]> {
    const { ids } = filter || {};
    const mQuery = this.companyRepo.createQueryBuilder('c').select(['c.id', 'c.name']);
    if (ids) mQuery.andWhere('c.id IN (:...ids)', { ids });

    return await mQuery.getMany();
  }

  async getCompanies(
    pagination?: PaginationOptions,
    filter?: CompaniesFilter,
  ): Promise<[Company[], number]> {
    const qb = this.companyRepo
      .createQueryBuilder('c')
      .leftJoinAndSelect('c.business', 'business')
      .leftJoinAndSelect('c.owner', 'owner')
      .orderBy('c.createdAt', 'DESC');
    const { query, ownerId, status, isOwned, businessType } = filter || {};

    if (pagination) {
      qb.take(pagination.limit).skip(pagination.skip);
    }
    if (query) {
      qb.andWhere(
        new Brackets(qb => {
          qb.where('cast(c.id as text) ILIKE :query', {
            query: `%${query}%`,
          }).orWhere('c.name ILIKE :query');
        }),
      );
    }
    if (ownerId) qb.andWhere('c.ownerId = :ownerId', { ownerId });
    if (status) qb.andWhere('c.status IN (:...status)', { status });
    if (!isNil(isOwned)) {
      if (isOwned) qb.andWhere('c.ownerId IS NOT NULL');
      else qb.andWhere('c.ownerId IS NULL');
    }
    if (businessType) qb.andWhere('business.type = :businessType', { businessType });
    const companies = await qb.getManyAndCount();
    return companies;
  }

  async getRoleAdminIds(companyIds: number[]) {
    const mQuery = this.companyRepo
      .createQueryBuilder('c')
      .where('c.id IN (:...companyIds)', { companyIds })
      .select('c.adminRoleId');
    const result = await mQuery.getMany();
    return result.map(it => it.adminRoleId);
  }

  async updateCompany(id: number, data: UpdateCompanyDto): Promise<Company> {
    const company = await this.companyRepo.findOne(id);
    if (!company) {
      throw new NotFoundException('Công ty không tồn tại');
    }

    const adminRoleUpdateData: Partial<Roles> = {};
    const updateData: Partial<Company> = omit(data, ['updatedAt']);

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (data.businessId) {
        const business = await queryRunner.manager.findOne(Business, data.businessId);
        if (!business) throw new NotFoundException(`Business id ${data.businessId} not found`);
        adminRoleUpdateData.permission = business.permission;
        adminRoleUpdateData.newPermission = business.newPermission?.reduce((prev, it) => {
          prev[it] = $enum(ENUM_PERMISSION_MAPPING[it])
            .getValues()
            ?.reduce((sum: number, e: number) => BigInt(sum) | BigInt(e), [BigInt(0)]);
          return prev;
        }, Array(business.newPermission[business.newPermission.length - 1] + 1).fill(BigInt(0)));
      }

      const result = await queryRunner.manager.update(Company, { id: company.id }, updateData);
      if (result.affected) {
        if (!isEmpty(adminRoleUpdateData)) {
          await queryRunner.manager.update(Roles, { id: company.adminRoleId }, adminRoleUpdateData);
          if (adminRoleUpdateData.newPermission) {
            const qb = queryRunner.manager
              .createQueryBuilder(Roles, 'r')
              .innerJoin('roles_closure', 'closure', 'closure.id_ancestor = r.id')
              .where('r.id = :id')
              .select('closure.id_descendant', 'role_id');
            // console.log(`qb`, qb.getQuery());
            await queryRunner.manager
              .createQueryBuilder(Roles, 'r')
              .update()
              .set({
                newPermission: () => `(SELECT ARRAY (
                                    SELECT (COALESCE(arr1[idx], 0) & COALESCE(arr2[idx], 0))
                                    FROM generate_series(1, GREATEST(array_length(arr1, 1), array_length(arr2, 1))) as idx
                                  ) FROM (
                                    SELECT new_permission::BIGINT[] as arr1, :permission::BIGINT[] as arr2
                                  ) sub)`,
              })
              .where(`id IN (${qb.getQuery()})`)
              .setParameters({
                id: company.adminRoleId,
                permission: adminRoleUpdateData.newPermission,
              })
              .execute();
          }
        }

        await queryRunner.commitTransaction();
        await this.redisCache.delWithPrefix(COMPANY);
        return plainToInstance(Company, {
          ...company,
          ...updateData,
        });
      }

      await queryRunner.commitTransaction();
      return company;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async getCompanyByOwnerId(ownerId: number): Promise<Company> {
    const qb = this.companyRepo
      .createQueryBuilder('c')
      .select('c.id')
      .where('c.owner_id = :ownerId', { ownerId });
    return qb.getOne();
  }

  @RabbitRPC({
    exchange: 'identity-service-companies',
    routingKey: 'update-company-admin-roles',
    queue: 'identity-update-company-admin-roles-permissions',
    errorHandler: rmqErrorsHandler,
  })
  async updateCompanyAdminRole(payload: IRmqMessage) {
    try {
      const businessId: number = payload.message.businessId;
      if (!businessId) return new Nack(false);

      const business = await this.businessesService.findById(businessId);
      if (!business) return new Nack(false);
      const permission = business.permission;
      const companies = await this.companyRepo
        .createQueryBuilder('c')
        .andWhere('c.businessId = :businessId', { businessId })
        .getMany();

      const adminRoleIds = companies.map(it => it.adminRoleId);

      const newPermission = business.newPermission?.reduce((prev, it) => {
        prev[it] = $enum(ENUM_PERMISSION_MAPPING[it])
          .getValues()
          ?.reduce((sum: number, e: number) => BigInt(sum) | BigInt(e), [BigInt(0)]);
        return prev;
      }, Array(business.newPermission[business.newPermission.length - 1] + 1).fill(BigInt(0)));

      // console.log(`newPermission`, newPermission)

      const result = await this.rolesRepo.update(
        { id: In(adminRoleIds) },
        { permission, newPermission },
      );
      console.log(`new admin role permission`, permission);
      console.log(`admin role update result`, result);

      await this.redisCache.delWithPrefix(USER_ROLES);

      for (const company of companies) {
        await this.amqpConnection.sendMessage(
          'identity-service-companies',
          null,
          {
            companyId: company.id,
            adminRoleId: company.adminRoleId,
            adminPermission: permission,
            newAdminPermission: newPermission,
          },
          { routingKey: 'update-company-sub-roles' },
        );
      }
    } catch (error) {
      console.log(`update company admin role error`, error);
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'identity-service-companies',
    routingKey: 'update-company-sub-roles',
    queue: 'identity-update-company-sub-roles-permissions',
  })
  async updateCompanySubRoles(payload: IRmqMessage) {
    const { companyId, adminRoleId, newAdminPermission } = payload.message;

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const qb = this.rolesRepo
        .createQueryBuilder('r')
        .innerJoin('roles_closure', 'closure', 'closure.id_ancestor = r.id')
        .where(`r.id = ${adminRoleId}`)
        .andWhere(`r.company_id = ${companyId}`)
        .select('closure.id_descendant', 'role_id');

      const result = await queryRunner.manager
        .createQueryBuilder(Roles, 'r')
        .update()
        .set({
          newPermission: () => `(SELECT ARRAY (
                                      SELECT (COALESCE(arr1[idx], 0) & COALESCE(arr2[idx], 0))
                                      FROM generate_series(1, GREATEST(array_length(arr1, 1), array_length(arr2, 1))) as idx
                                    ) FROM (
                                      SELECT new_permission::BIGINT[] as arr1, :permission::BIGINT[] as arr2
                                    ) sub)`,
        })
        .where(`id IN (${qb.getQuery()})`)
        .setParameters({ permission: newAdminPermission })
        .execute();
      await queryRunner.commitTransaction();
      console.log(`update company sub role result`, result);
      return new Nack(false);
    } catch (e) {
      console.log(`error when updating role`, e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  @RabbitRPC({
    exchange: 'identity-service-companies',
    routingKey: 'get-company-name-by-id',
    queue: 'identity-get-company-name-by-id',
  })
  async getCompanyNameById({ id }) {
    if (!id) return new Nack(false);
    const key = `${COMPANY}.${id}.${COMPANY_NAME}`;
    const data = await this.redisCache.get(key);
    if (data) return data;

    const company = await this.companyRepo
      .createQueryBuilder('c')
      .select(['c.id', 'c.name'])
      .where('c.id = :id', { id })
      .getOne();

    await this.redisCache.set(key, company);
    return company;
  }

  @RabbitRPC({
    exchange: 'identity-service-companies',
    routingKey: 'get-company-by-id',
    queue: 'identity-get-company-by-id',
    errorHandler: defaultNackErrorHandler,
  })
  async getCompany({ id }) {
    if (!id) {
      return null;
    }
    const key = `${COMPANY}.${id}.`;
    const data = await this.redisCache.get(key);
    if (data) return data;
    const company = await this.companyRepo
      .createQueryBuilder('c')
      .where('c.id = :id', { id })
      .innerJoin('c.business', 'business')
      .addSelect('business.id')
      .addSelect('business.name')
      .addSelect('business.type')
      .addSelect('business.status')
      .getOne();
    await this.redisCache.set(key, company);
    return company;
  }

  randomStr(length: number) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
      counter += 1;
    }
    return result;
  }
}
