import { DashboardPermission } from 'core/enums/sale-permissions/dashboard-permission.enum';
import { MenuNavigation } from '../model/navigation.model';
import { SalePermission } from 'core/enums/sale-permissions';
import { OrderPermission } from 'core/enums/sale-permissions/order-permission.enum';
import { CustomerPermission } from 'core/enums/sale-permissions/customer-permission.enum';
import { ProductPermission } from 'core/enums/sale-permissions/product-permission.enum';
import { SupplierPermission } from 'core/enums/sale-permissions/supplier-permission.enum';
import { cloneDeep, isEmpty } from 'lodash';
import { plainToInstance } from 'class-transformer';
import { InventoryPermission } from 'core/enums/sale-permissions/inventory-permission.enum';
import { InboundPermission } from 'core/enums/sale-permissions/inbound-permission.enum';
import { OutboundPermission } from 'core/enums/sale-permissions/outbound-permission.enum';
import { StocktakingPermission } from 'core/enums/sale-permissions/stocktaking-permission.enum';
import { ReturnHandlingPermission } from 'core/enums/sale-permissions/return-handling-permission.enum';
import { TelesalesPermission } from 'core/enums/sale-permissions/telesales-permission.enum';
import { CarePagePermission } from 'core/enums/sale-permissions/care-page-permission.enum';
import { BotManagementPermission } from 'core/enums/sale-permissions/bot-management-permission.enum';
import { FanPagePermission } from 'core/enums/sale-permissions/fanpage-permission.enum';
import { CampaignPermission } from 'core/enums/sale-permissions/campaign-permission.enum';
import { ProjectPermission } from 'core/enums/sale-permissions/project-permission.enum';
import { CountryPermission } from 'core/enums/sale-permissions/country-permission.enum';
import { AccountPermission } from 'core/enums/sale-permissions/account-permission.enum';
import { ShiftPermission } from 'core/enums/sale-permissions/shift-permission.enum';
import { RolePermission } from 'core/enums/sale-permissions/role-permission.enum';
import { TagPermission } from 'core/enums/sale-permissions/tag-permission.enum';
import { OrderSourcePermission } from 'core/enums/sale-permissions/order-source-permission.enum';
import { CancelReasonPermission } from 'core/enums/sale-permissions/cancel-reason-permission.enum';
import { PrintNotePermission } from 'core/enums/sale-permissions/print-note-permission.enum';
import { TranslationPermission } from 'core/enums/sale-permissions/translation-permission.enum';
import { MarketplacePermission } from 'core/enums/sale-permissions/marketplace-permission.enum';
import { FfmSettingPermission } from 'core/enums/sale-permissions/ffm-setting-permission.enum';
import { OrgPermission } from 'core/enums/sale-permissions/org-permission.enum';
import { CurrencyPermission } from 'core/enums/sale-permissions/currency-permission.enum';
import { CallCenterPermission } from 'core/enums/sale-permissions/callcenter-permission.enum';
import { AfterSalesLeadPermission } from 'core/enums/sale-permissions/after-sales-lead.permission.enum';
import { AnalyticsAccountPermission } from 'core/enums/sale-permissions/analytics-account-permission.enum';
import { IntegrationPermission } from 'core/enums/sale-permissions/integration-permission.enum';

export const getMenuV2 = (userPermissions: bigint[], data: MenuNavigation[]): MenuNavigation[] => {
  const menu = cloneDeep(data);

  if (isEmpty(menu)) return undefined;

  const value = plainToInstance(
    MenuNavigation,
    menu.reduce((prev: MenuNavigation[], i) => {
      i.children = getMenuV2(userPermissions, i?.children || []);

      if (i.hideIfEmptyChildren && isEmpty(i.children)) {
        return prev;
      }

      for (const perm of i.authority) {
        const idx = perm[0];
        if (userPermissions[idx] && userPermissions[idx] & BigInt(perm[1])) {
          prev.push(i);
          return prev;
        }
      }
      return prev;
    }, []),
  );
  return value;
};

export const navigationMenuV2: MenuNavigation[] = [
  {
    path: '/dashboard',
    name: 'Báo cáo',
    translateKey: 'dashboard',
    icon: 'dashboard',
    authority: [
      [SalePermission.dashboard, DashboardPermission.orderShipmentOverview],
      [SalePermission.dashboard, DashboardPermission.orderShipmentSaleReps],
      [SalePermission.dashboard, DashboardPermission.orderShipmentCarrier],
      [SalePermission.dashboard, DashboardPermission.telesalesOverview],
      [SalePermission.dashboard, DashboardPermission.telesalesPerformance],
      [SalePermission.dashboard, DashboardPermission.telesalesReasons],
      [SalePermission.dashboard, DashboardPermission.carePageOverview],
      [SalePermission.dashboard, DashboardPermission.carePagePerformance],
      [SalePermission.dashboard, DashboardPermission.carePageReasons],
      [SalePermission.dashboard, DashboardPermission.marketingCarePage],
      [SalePermission.dashboard, DashboardPermission.marketingTelesales],
    ],
    children: [
      {
        path: '/dashboard/order-shipment',
        name: 'Tổng quan',
        translateKey: 'order_shipment_overview',
        exact: true,
        authority: [
          [SalePermission.dashboard, DashboardPermission.orderShipmentOverview],
          [SalePermission.dashboard, DashboardPermission.orderShipmentSaleReps],
          [SalePermission.dashboard, DashboardPermission.orderShipmentCarrier],
        ],
      },
      {
        path: '/dashboard/care-page-manager',
        name: 'CarePage',
        translateKey: 'care_page_manager',
        exact: true,
        authority: [
          [SalePermission.dashboard, DashboardPermission.carePageOverview],
          [SalePermission.dashboard, DashboardPermission.carePagePerformance],
          [SalePermission.dashboard, DashboardPermission.carePageReasons],
        ],
      },
      {
        path: '/dashboard/telesales-manager',
        name: 'Telesales',
        translateKey: 'telesales_manager',
        exact: true,
        authority: [
          [SalePermission.dashboard, DashboardPermission.telesalesOverview],
          [SalePermission.dashboard, DashboardPermission.telesalesPerformance],
          [SalePermission.dashboard, DashboardPermission.telesalesReasons],
        ],
      },
      {
        path: '/dashboard/marketing/carepage',
        name: 'marketing carepage',
        translateKey: 'marketing_carepage',
        exact: true,
        authority: [[SalePermission.dashboard, DashboardPermission.marketingCarePage]],
      },
      {
        path: '/dashboard/marketing/telesales',
        name: 'marketing telesales',
        translateKey: 'marketing_telesales',
        exact: true,
        authority: [[SalePermission.dashboard, DashboardPermission.marketingTelesales]],
      },
    ],
    group: 'system',
  },
  {
    path: '/orders',
    name: 'Đơn hàng',
    translateKey: 'order',
    icon: 'order',
    group: 'system',
    authority: [
      [SalePermission.order, OrderPermission.fetchMany],
      [SalePermission.order, OrderPermission.create],
      [SalePermission.order, OrderPermission.bulkUpdateSaleReps],
      [SalePermission.order, OrderPermission.bulkUpdateSource],
      [SalePermission.order, OrderPermission.bulkUpdateStatus],
      [SalePermission.order, OrderPermission.bulkUpdateTags],
    ],
    children: [
      {
        path: '/orders/manager',
        name: 'Quản lý đơn hàng',
        translateKey: 'order_manager',
        exact: true,
        authority: [[SalePermission.order, OrderPermission.fetchMany]],
      },
      {
        path: '/orders/create',
        name: 'Tạo đơn',
        translateKey: 'order_create',
        exact: true,
        authority: [[SalePermission.order, OrderPermission.create]],
      },
      {
        path: '/orders/update/bulk',
        name: 'Cập nhật đơn',
        translateKey: 'bulk_update',
        exact: true,
        authority: [
          [SalePermission.order, OrderPermission.bulkUpdateSaleReps],
          [SalePermission.order, OrderPermission.bulkUpdateSource],
          [SalePermission.order, OrderPermission.bulkUpdateStatus],
          [SalePermission.order, OrderPermission.bulkUpdateTags],
        ],
      },
      // {
      //   path: '/orders/draft',
      //   name: 'Đơn nháp',
      //   translateKey: 'order_draft',
      //   exact: true,
      //   authority: [[SalePermission.order, OrderPermission.fetchMany]],
      // },
    ],
  },
  {
    path: '/customers/manager',
    name: 'Quản lý khách hàng',
    translateKey: 'custormer_manager',
    icon: 'customer',
    authority: [[SalePermission.customer, CustomerPermission.fetchMany]],
    group: 'system',
    // children: [
    //   {
    //     path: '/customers/manager',
    //     name: 'Quản lý khách hàng',
    //     translateKey: 'custormer_manager',
    //     authority: [Permission.customer],
    //     exact: true,
    //   },
    // ],
  },

  {
    path: '/products',
    name: 'Sản phẩm',
    translateKey: 'product',
    icon: 'product',
    authority: [
      [SalePermission.product, ProductPermission.fetchMany],
      [SalePermission.supplier, SupplierPermission.fetchMany],
    ],
    group: 'system',
    children: [
      {
        path: '/products/manager',
        name: 'Quản lý sản phẩm',
        translateKey: 'product_manager',
        exact: true,
        authority: [[SalePermission.product, ProductPermission.fetchMany]],
      },
      {
        path: '/products/suppliers',
        name: 'Quản lý nhà cung cấp',
        translateKey: 'product_supplier',
        exact: true,
        authority: [[SalePermission.supplier, SupplierPermission.fetchMany]],
      },
    ],
  },
  {
    path: '/warehouses',
    name: 'Tồn kho',
    translateKey: 'warehouse',
    icon: 'warehouse',
    group: 'system',
    authority: [
      [SalePermission.inventory, InventoryPermission.fetchMany],
      [SalePermission.inbound, InboundPermission.fetchMany],
      [SalePermission.outbound, OutboundPermission.fetchMany],
      [SalePermission.stocktaking, StocktakingPermission.fetchMany],
      [SalePermission.returnHandling, ReturnHandlingPermission.fetchMany],
    ],
    children: [
      {
        path: '/products/warehouses',
        name: 'Quản lý kho',
        translateKey: 'warehouse_manager',
        exact: true,
        authority: [[SalePermission.inventory, InventoryPermission.fetchMany]],
      },
      {
        path: '/stock-move/import',
        name: 'Nhập hàng',
        translateKey: 'stock_move_import',
        exact: true,
        authority: [[SalePermission.inbound, InboundPermission.fetchMany]],
      },
      {
        path: '/stock-move/export',
        name: 'Xuất hàng',
        translateKey: 'stock_move_export',
        exact: true,
        authority: [[SalePermission.outbound, OutboundPermission.fetchMany]],
      },
      {
        path: '/stock-move/inventory',
        name: 'Kiểm kê hàng hóa',
        translateKey: 'stock_move_manager',
        exact: true,
        authority: [[SalePermission.stocktaking, StocktakingPermission.fetchMany]],
      },
      {
        path: '/orders/check-goods',
        name: 'Kiểm hàng hoàn',
        translateKey: 'check_goods',
        exact: true,
        authority: [[SalePermission.returnHandling, ReturnHandlingPermission.fetchMany]],
      },
    ],
  },
  {
    path: '/telesales',
    name: 'Telesales',
    translateKey: 'telesales',
    icon: 'telesales',
    group: 'sale',
    authority: [
      [SalePermission.telesales, TelesalesPermission.fetchAssignedLeads],
      [SalePermission.telesales, TelesalesPermission.fetchLeads],
      [SalePermission.telesales, TelesalesPermission.distributeConfig],
      [SalePermission.telesales, TelesalesPermission.processingProcedureConfig],
    ],
    children: [
      {
        path: '/telesales/leads-management',
        name: 'Quản lý leads',
        translateKey: 'leads_management',
        exact: true,
        authority: [[SalePermission.telesales, TelesalesPermission.fetchAssignedLeads]],
      },
      {
        path: '/telesales/data-manual-distribution',
        name: 'Chia thủ công',
        translateKey: 'data_manual_distribution',
        exact: true,
        authority: [[SalePermission.telesales, TelesalesPermission.fetchLeads]],
      },
      //   {
      //     path: '/telesales/data-auto-distribution',
      //     name: 'Chia tự động',
      //     translateKey: 'data_auto_distribution',
      //     exact: true,
      //     authority: [Permission.telesalesAutoDistribution],
      //   },
      {
        path: '/telesales/configurations',
        name: 'Cấu hình định mức',
        translateKey: 'telesales_configurations',
        exact: true,
        authority: [
          [SalePermission.telesales, TelesalesPermission.distributeConfig],
          [SalePermission.telesales, TelesalesPermission.processingProcedureConfig],
        ],
      },
    ],
  },
  {
    path: '/telesales-as',
    name: 'Telesales AfterSale',
    translateKey: 'telesales-as',
    icon: 'aftersales',
    group: 'sale',
    authority: [
      [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchAssignedLeads],
      [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads],
      [SalePermission.afterSalesLead, AfterSalesLeadPermission.distributeConfig],
      [SalePermission.afterSalesLead, AfterSalesLeadPermission.processingProcedureConfig],
    ],
    children: [
      {
        path: '/aftersales/leads-management',
        name: 'Quản lý leads',
        translateKey: 'leads_management',
        exact: true,
        authority: [[SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchAssignedLeads]],
      },
      {
        path: '/aftersales/data-manual-distribution',
        name: 'Chia thủ công',
        translateKey: 'data_manual_distribution',
        exact: true,
        authority: [
          [SalePermission.afterSalesLead, AfterSalesLeadPermission.fetchLeads],
        ],
      },
      //   {
      //     path: '/telesales/data-auto-distribution',
      //     name: 'Chia tự động',
      //     translateKey: 'data_auto_distribution',
      //     exact: true,
      //     authority: [Permission.telesalesAutoDistribution],
      //   },
      {
        path: '/aftersales/configurations',
        name: 'Cấu hình định mức',
        translateKey: 'telesales_configurations',
        exact: true,
        authority: [
          [SalePermission.afterSalesLead, AfterSalesLeadPermission.distributeConfig],
          [SalePermission.afterSalesLead, AfterSalesLeadPermission.processingProcedureConfig],
        ],
      },
    ],
  },
  {
    path: '/sale-care-page',
    name: 'Care Page',
    translateKey: 'sale_care_page',
    icon: 'sale_care_page',
    group: 'sale',
    authority: [
      [SalePermission.carePage, CarePagePermission.fetchPageGroups],
      [SalePermission.carePage, CarePagePermission.fetchConfigGroups],
      [SalePermission.carePage, CarePagePermission.limitationSettings],
      [SalePermission.carePage, CarePagePermission.aiConfigurations],
      [SalePermission.carePage, CarePagePermission.aiProductConfigurations],
    ],
    children: [
      {
        path: '/sale-care-page',
        name: 'Care Page',
        translateKey: 'sale_care_page_group',
        icon: 'sale_care_page',
        group: 'sale',
        exact: true,
        authority: [[SalePermission.carePage, CarePagePermission.fetchPageGroups]],
      },
      {
        path: '/marketing-automation/automation-configs',
        name: 'Automation',
        translateKey: 'automation_config',
        icon: 'marketing_automation_configs',
        group: 'sale',
        exact: true,
        authority: [[SalePermission.carePage, CarePagePermission.fetchConfigGroups]],
      },
      {
        path: '/sale-care-page/limitation-settings',
        name: 'Cấu hình định mức',
        translateKey: 'care_page_limitation_settings',
        exact: true,
        authority: [[SalePermission.carePage, CarePagePermission.limitationSettings]],
      },
      {
        path: '/llm/chatbot',
        name: 'AI - Xử lý hội thoại',
        translateKey: 'llm_configuration',
        exact: true,
        authority: [[SalePermission.carePage, CarePagePermission.aiConfigurations]],
      },
      /* {
        path: '/llm/product-configurations',
        name: 'AI - Cấu hình hội thoại',
        translateKey: 'llm_product_configuration',
        exact: true,
        authority: [[SalePermission.carePage, CarePagePermission.aiProductConfigurations]],
      }, */
    ],
  },
  // {
  //   path: '/marketing-automation/manage-bots',
  //   name: 'Chat Bot',
  //   translateKey: 'manage_bots',
  //   icon: 'marketing_automation_manage_bots',
  //   exact: true,
  //   group: 'marketing',
  //   authority: [
  //     [SalePermission.botManagement, BotManagementPermission.fetchMany],
  //     [SalePermission.botManagement, BotManagementPermission.crawlBotList],
  //     [SalePermission.botManagement, BotManagementPermission.botAdsManager],
  //   ],
  //   children: [
  //     {
  //       path: '/marketing-automation/manage-bots',
  //       name: 'Bot management',
  //       translateKey: 'manage_bots',
  //       group: 'marketing',
  //       exact: true,
  //       authority: [[SalePermission.botManagement, BotManagementPermission.fetchMany]],
  //     },
  //     // {
  //     //   path: '/marketing-automation/analytics',
  //     //   name: 'Bot analytics',
  //     //   translateKey: 'manage_analytic_bots',
  //     //   group: 'marketing',
  //     //   exact: true,
  //     //   authority: [
  //     //     [SalePermission.botManagement, BotManagementPermission.crawlBotList],
  //     //     [SalePermission.botManagement, BotManagementPermission.botAdsManager],
  //     //   ],
  //     // },
  //   ],
  // },
  {
    path: '/marketing-automation/manage-pages',
    name: 'Fanpage',
    translateKey: 'manage_pages',
    icon: 'marketing_automation_manage_pages',
    exact: true,
    group: 'marketing',
    authority: [
      [SalePermission.fanPages, FanPagePermission.fetchMany], 
      // [SalePermission.fanPages, FanPagePermission.link],
      [SalePermission.campaigns, CampaignPermission.fetchMany]
    ],
    children: [
      {
        path: '/marketing/connect-fanpage',
        name: 'Connect Fanpage',
        translateKey: 'connect_fanpage',
        group: 'marketing',
        exact: true,
        authority: [[SalePermission.fanPages, FanPagePermission.fetchMany]],
      },
      {
        path: '/marketing-automation/manage-campaigns',
        name: 'Chiến dịch',
        translateKey: 'manage_campaigns',
        icon: 'marketing_automation_manage_campains',
        exact: true,
        group: 'marketing',
        authority: [[SalePermission.campaigns, CampaignPermission.fetchMany]],
      },
    ],
  },
  {
    path: '/marketing/analytics',
    name: 'FB Ads Tracking',
    translateKey: 'fb_ads_tracking',
    icon: 'fb_ads_tracking',
    group: 'marketing',
    exact: true,
    authority: [
      [SalePermission.analyticsAccount, AnalyticsAccountPermission.fetch],
      [SalePermission.analyticsAccount, AnalyticsAccountPermission.update],
      [SalePermission.analyticsAccount, AnalyticsAccountPermission.delete],
    ],
  },
  // {
  //   path: '/marketing-automation/manage-campaigns',
  //   name: 'Chiến dịch',
  //   translateKey: 'manage_campaigns',
  //   icon: 'marketing_automation_manage_campains',
  //   exact: true,
  //   group: 'marketing',
  //   authority: [[SalePermission.campaigns, CampaignPermission.fetchMany]],
  // },
  // {
  //   path: '/transactions',
  //   name: 'Giao dịch',
  //   translateKey: 'transactions',
  //   icon: 'transactions',
  //   authority: [Permission.manageTransactions],
  //   children: [
  //     {
  //       path: '/transactions/manager',
  //       name: 'Quản lý giao dịch',
  //       translateKey: 'transactions_manager',
  //       authority: [Permission.manageTransactions],
  //       exact: true,
  //     },
  //   ],
  // },

  {
    path: '/setting',
    name: 'Cấu hình',
    translateKey: 'setting',
    icon: 'setting',
    includesGroup: true,
    group: 'setting',
    authority: [
      [SalePermission.projects, ProjectPermission.fetchMany],
      [SalePermission.countries, CountryPermission.fetchMany],
      [SalePermission.accounts, AccountPermission.fetchMany],
      [SalePermission.shift, ShiftPermission.fetchMany],
      [SalePermission.shift, ShiftPermission.schedules],
      [SalePermission.roles, RolePermission.fetchMany],
      [SalePermission.roles, RolePermission.fetchMany],
      [SalePermission.org, OrgPermission.fetch],

      //cấu hình
      [SalePermission.tags, TagPermission.fetchMany],
      [SalePermission.orderSource, OrderSourcePermission.fetchMany],
      [SalePermission.reportReason, RolePermission.fetchMany],
      [SalePermission.cancelReason, CancelReasonPermission.fetchMany],
      [SalePermission.printNotes, PrintNotePermission.fetchMany],
      [SalePermission.translation, TranslationPermission.fetchMany],
      [SalePermission.currency, CurrencyPermission.fetchMany],
      [SalePermission.integration, IntegrationPermission.fetchMany],

      //cài đặt
      [SalePermission.marketplace, MarketplacePermission.fetchMany],
      [SalePermission.fulfillment, FfmSettingPermission.fetchMany],
      // [SalePermission.callCenterManagement, CallCenterPermission.fetchMany],
    ],
    children: [
      {
        path: '/setting/projects',
        name: 'Dự án',
        translateKey: 'projects',
        icon: 'project',
        exact: true,
        authority: [[SalePermission.projects, ProjectPermission.fetchMany]],
        group: 'configurations',
      },
      {
        path: '/setting/countries',
        name: 'Thị trường',
        icon: 'country',
        translateKey: 'countries',
        exact: true,
        authority: [[SalePermission.countries, CountryPermission.fetchMany]],
        group: 'configurations',
      },
      {
        path: '/setting/employees',
        name: 'Nhân viên',
        icon: 'employee',
        translateKey: 'employees',
        exact: true,
        authority: [[SalePermission.accounts, AccountPermission.fetchMany]],
        group: 'configurations',
        children: [
          {
            path: '/setting/employees',
            name: 'Nhân viên',
            icon: 'employee',
            translateKey: 'employees',
            exact: true,
            authority: [[SalePermission.accounts, AccountPermission.fetchMany]],
            group: 'configurations',
          },
          {
            path: '/setting/roles-permissions',
            name: 'Vai trò/ Nhóm quyền',
            icon: 'role',
            translateKey: 'roles',
            exact: true,
            authority: [[SalePermission.roles, RolePermission.fetchMany]],
            group: 'configurations',
          },
        ],
      },
      {
        path: '/setting/org-chart',
        name: 'Nhóm/ Phòng ban',
        icon: 'org_chart',
        translateKey: 'org_chart',
        exact: true,
        authority: [[SalePermission.org, OrgPermission.fetch]],
        group: 'configurations',
      },
      {
        path: '/setting/shifts',
        name: 'Ca làm việc',
        icon: 'shift',
        translateKey: 'shift',
        exact: true,
        authority: [
          [SalePermission.shift, ShiftPermission.fetchMany],
          [SalePermission.shift, ShiftPermission.schedules],
        ],
        group: 'configurations',
        children: [
          {
            path: '/setting/shifts',
            name: 'Ca làm việc',
            translateKey: 'shift',
            exact: true,
            authority: [[SalePermission.shift, ShiftPermission.fetchMany]],
            group: 'config_shifts',
          },
          {
            path: '/setting/employee-shifts',
            name: 'Phân công ca làm việc',
            translateKey: 'employee_shifts',
            exact: true,
            authority: [[SalePermission.shift, ShiftPermission.schedules]],
            group: 'config_shifts',
          },
        ],
      },
      {
        path: '/setting/tags',
        name: 'Nhóm thẻ/ thẻ đơn hàng',
        translateKey: 'tags',
        exact: true,
        icon: 'tag',
        authority: [[SalePermission.tags, TagPermission.fetchMany]],
        group: 'configurations',
      },
      {
        path: 'setting/order',
        name: 'Đơn hàng',
        translateKey: 'order',
        icon: 'setting_order',
        exact: true,
        authority: [
          [SalePermission.orderSource, OrderSourcePermission.fetchMany],
          [SalePermission.reportReason, RolePermission.fetchMany],
          [SalePermission.cancelReason, CancelReasonPermission.fetchMany],
          [SalePermission.printNotes, PrintNotePermission.fetchMany],
        ],
        group: 'configurations',
        children: [
          {
            path: '/setting/order-sources',
            name: 'Nguồn hàng',
            translateKey: 'order_sources',
            exact: true,
            authority: [[SalePermission.orderSource, OrderSourcePermission.fetchMany]],
            group: 'config_order',
          },
          {
            path: '/setting/report-reasons',
            name: 'Lý do báo xấu',
            translateKey: 'report_reasons',
            authority: [[SalePermission.reportReason, RolePermission.fetchMany]],
            exact: true,
            group: 'config_order',
          },
          {
            path: '/setting/cancel-reasons',
            name: 'Lý do hoàn/ hủy',
            translateKey: 'cancel_reasons',
            exact: true,
            authority: [[SalePermission.cancelReason, CancelReasonPermission.fetchMany]],
            group: 'config_order',
          },
          //   {
          //     path: '/setting/care-fail-reasons',
          //     name: 'Lý do từ chối chăm sóc',
          //     translateKey: 'care_fail_reasons',
          //     exact: true,
          //     authority: [Permission.configCareFailReasons],
          //     group: 'config_order',
          //   },
          {
            path: '/setting/print-notes',
            name: 'Ghi chú in đơn',
            translateKey: 'print_notes',
            exact: true,
            authority: [[SalePermission.printNotes, PrintNotePermission.fetchMany]],
            group: 'config_order',
          },
        ],
      },
      {
        path: '/setting/translations',
        name: 'Language Translation',
        translateKey: 'setting_translation',
        authority: [[SalePermission.translation, TranslationPermission.fetchMany]],
        group: 'configurations',
        icon: 'translation',
        exact: true,
      },
      {
        path: '/setting/currency-configurations',
        name: 'Currency Configuration',
        translateKey: 'currency',
        authority: [[SalePermission.currency, CurrencyPermission.fetchMany]],
        group: 'configurations',
        icon: 'currency',
        exact: true,
      },
      // {
      //   path: '/callcenter',
      //   name: 'CallCenter management',
      //   translateKey: 'callcenter_management',
      //   authority: [[SalePermission.callCenterManagement, CallCenterPermission.fetchMany]],
      //   group: 'configurations',
      //   icon: 'callcenter',
      //   exact: true,
      // },
      {
        path: '/setting/landing-page',
        name: 'Cấu hình ứng dụng',
        translateKey: 'landing-page',
        icon: 'landing_source',
        authority: [[SalePermission.marketplace, MarketplacePermission.fetchMany]],
        group: 'integrate',
      },
      {
        path: '/integrate-services',
        name: 'Tích hợp',
        translateKey: 'integrate_services',
        authority: [[SalePermission.integration, IntegrationPermission.fetchMany]],
        group: 'integrate',
        icon: 'integrate',
        exact: true,
      },
      {
        path: '/integrate-services/ffm-services',
        name: 'Tích hợp fulfillment',
        translateKey: 'integrate_ffm_services',
        authority: [[SalePermission.fulfillment, FfmSettingPermission.fetchMany]],
        group: 'integrate',
        icon: 'fulfillment',
        exact: true,
      },

      ////
      // {
      //   path: '/setting/shipping',
      //   name: 'Vận chuyển',
      //   translateKey: 'shipping',
      //   exact: true,
      //   authority: [Permission.configShipping],
      //   group: 'preferences',
      // },
      // {
      //   path: '/setting/partners',
      //   name: 'Nhà cung cấp',
      //   translateKey: 'partners',
      //   exact: true,
      //   authority: [Permission.configPartners],
      //   group: 'preferences',
      // },
      // {
      //   path: '/setting/billing',
      //   name: 'In bill',
      //   translateKey: 'billing',
      //   exact: true,
      //   authority: [Permission.configBilling],
      //   group: 'preferences',
      // },

      // {
      //   path: '/setting/facebook-pixel',
      //   name: 'Facebook Pixel',
      //   translateKey: 'facebook_pixel',
      //   exact: true,
      //   authority: [Permission.marketing],
      //   group: 'setting',
      // },
    ],
  },
];
