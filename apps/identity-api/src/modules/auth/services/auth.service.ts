import {
  Amqp<PERSON>onnection,
  default<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CompanyRootUserDto,
  UpdateRootUserDto,
} from 'apps/identity-api/src/dtos/company-root-user.dto';
import { CreateCompanyDto, UpdateCompanyDto } from 'apps/identity-api/src/dtos/company.dto';
import {
  CreateUserDto,
  LoginUserDto,
  UpdateUserDto,
  UpdateWarehouseUser,
} from 'apps/identity-api/src/dtos/user.dto';
import { Company } from 'apps/identity-api/src/entities/company.entity';
import { Roles } from 'apps/identity-api/src/entities/roles.entity';
import { Team } from 'apps/identity-api/src/entities/team.entity';
import { UserScope } from 'apps/identity-api/src/entities/user-scope.entity';
import { BusinessType } from 'apps/identity-api/src/enums/business-type.enum';
import { UsersFilter } from 'apps/identity-api/src/filters/users.filter';
import { plainToInstance } from 'class-transformer';
import {
  USER,
  USER_ACTIVE,
  USER_ROLES,
  USER_SCOPES,
  USER_SESSION,
  USER_WAREHOUSE,
} from 'core/cache/constants/prefix.constant';
import { CommonStatus } from 'core/enums/common-status.enum';
import { UserStatus } from 'core/enums/user-status.enum';
import { UserType } from 'core/enums/user-type.enum';
import StringUtils from 'core/utils/StringUtils';
import * as crypto from 'crypto';
import * as _ from 'lodash';
import {
  concat,
  difference,
  findIndex,
  isEmpty,
  isNil,
  omit,
  pickBy,
  remove,
  startsWith,
  sum,
  uniq,
  intersection,
} from 'lodash';
import { Brackets, getConnection, In, QueryRunner, Repository, UpdateResult } from 'typeorm';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { identityConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { Role } from 'core/enums/role.enum';
import { User } from '../../../entities/user.entity';
import { BusinessesService } from '../../business/services/businesses.service';
import { CompaniesService } from '../../companies/services/companies.service';
import { Permission } from 'core/enums/permission.enum';
import * as FFM from 'core/enums/permission-ffm.enum';
import { Department } from 'apps/identity-api/src/entities/department.entity';
import { ENUM_PERMISSION_MAPPING } from 'core/enums/sale-permissions';
import { UserProfile } from 'apps/identity-api/src/entities/user-profile.entity';
import { ProfileType } from 'apps/identity-api/src/enums/profile-type.enum';
import { $enum } from 'ts-enum-util';
import { DataSet, DataSetEntity } from 'apps/identity-api/src/entities/data-set.entity';
import { DataSetScope } from 'apps/identity-api/src/entities/data-set-scope.entity';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import { Business } from 'apps/identity-api/src/entities/business.entity';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User, identityConnection)
    private userRepo: Repository<User>,
    @InjectRepository(UserProfile, identityConnection)
    private userProfileRepo: Repository<UserProfile>,
    @InjectRepository(Department, identityConnection)
    private departmentRepo: Repository<Department>,
    @InjectRepository(UserScope, identityConnection)
    private userScopeRepo: Repository<UserScope>,
    private jwtService: JwtService,
    @InjectRepository(Roles, identityConnection)
    private rolesRepo: Repository<Roles>,
    @InjectRepository(Team, identityConnection)
    private teamRepo: Repository<Team>,
    @InjectRepository(Company, identityConnection)
    private companyRepo: Repository<Company>,
    private companiesService: CompaniesService,
    private businessesService: BusinessesService,
    private redisCache: RedisCacheService,
    private amqpConnection: AmqpConnection,
    @InjectRedis()
    private redis: Redis,
  ) {}

  async checkRoles(id: number, roles: Role[]) {
    const user = await this.userRepo.findOne({
      relations: ['role'],
      select: ['id'],
      where: { id },
    });
    const permissions = _.sum(user.permissions);
    for (const role of roles) {
      if (permissions & role) {
        return { permissions: user.permissions, valid: true };
      }
    }
    return { valid: false };
  }

  async getUser(id: number, request?: Record<string, any>, userFfm?: boolean): Promise<User> {
    // const user = this.userRepo.findOne(id, {
    //   relations: [
    //     'ownsCompany',
    //     'ownsCompany.business',
    //     'company',
    //     'company.business',
    //     'company.countries',
    //     'company.projects',
    //     'role',
    //     'teams',
    //     'scopes',
    //   ],
    // });

    const qb = this.userRepo.createQueryBuilder('u').where('u.id = :id', { id });
    if (userFfm) qb.andWhere('u.company_id =:companyId', { companyId: request?.user?.companyId });

    qb
      // .leftJoinAndSelect('u.ownsCompany', 'ownsCompany')
      // .leftJoinAndSelect('ownsCompany.business', 'business')
      .leftJoin('u.company', 'company')
      .addSelect(['company.id', 'company.name', 'company.domainName', 'company.businessId'])
      .leftJoin('company.business', 'business')
      .addSelect(['business.id', 'business.name', 'business.type'])
      // .leftJoinAndSelect('company.countries', 'countries')
      // .leftJoinAndSelect('company.projects', 'projects')
      .leftJoin('u.role', 'role')
      .addSelect(['role.id', 'role.name', 'role.status'])
      .leftJoin(
        'u.profiles',
        'profiles',
        // `profiles.status = ${CommonStatus.activated}`
      )
      .unScope('profiles')
      .addSelect(['profiles.id', 'profiles.status'])
      .leftJoin('profiles.role', 'profileRole')
      .addSelect(['profileRole.id', 'profileRole.name', 'profileRole.status'])
      .leftJoin('profiles.department', 'd', 'profiles.department_id = d.id')
      .addSelect(['d.id', 'd.name'])
      .leftJoin(
        'd.leaderProfile',
        'lp',
        'd.leaderProfileId IS NOT NULL AND d.leaderProfileId = lp.id',
      )
      .unScope('lp')
      .addSelect(['lp.id', 'lp.status'])
      .leftJoin('lp.role', 'lpRole')
      .addSelect(['lpRole.id', 'lpRole.name', 'lpRole.status'])
      .leftJoin('lp.user', 'lp_u')
      .addSelect(['lp_u.id', 'lp_u.name', 'lp_u.email', 'lp_u.avatar', 'lp_u.status']);
    // .leftJoinAndMapOne('profiles.leader', 'users', 'lp_u', 'lp_u.id = lp.user_id')

    const user = await qb.getOne();

    if (!user) {
      throw new NotFoundException('Tài khoản không tồn tại');
    }
    return user;
  }

  async getUsers(userIds: number[]) {
    return this.userRepo.findByIds(userIds);
  }

  randomStr(length: number) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
      counter += 1;
    }
    return result;
  }

  async getJwtToken(user: User): Promise<string> {
    if (isNil(user?.company?.business?.type)) {
      const company = await this.companiesService.findById(user.companyId);
      user.company = plainToInstance(Company, { ...company, ...user.company });
    }
    if (!user.ownsCompany)
      user.ownsCompany = await this.companiesService.getCompanyByOwnerId(user.id);

    // if (!user?.isAdmin && isNil(user?.company?.business?.type)) throw new BadRequestException();

    const payload: Record<string, any> = {
      sub: user.id,
      fullname: user.name,
      companyId: user.companyId,
      type: UserType[user.type],
      warehouses: user.warehouses,
      displayId: user?.displayId ?? user?.id,
      isAdmin: user?.isAdmin || user?.roleCode == 'admin',
      sid: user.sessionId,
      service: BusinessType[user?.company?.business?.type],
      p: user?.role?.newPermission || [],
    };

    if (!user?.isAdmin && (!payload.p || payload.p.length === 0)) {
      const userWithPerms = await this.getMe(user);
      payload.p = userWithPerms?.salePermissions || [];
    }

    // const descendantIds = await this.getDescendantIdsByUserId(user.id) || [];
    // payload.dids = descendantIds
    const { data: descendants } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service',
      routingKey: 'check-user-descendants',
      payload: { id: user.id },
      timeout: 10000,
    });
    payload.marketerDids = descendants[0];
    payload.saleDids = descendants[1];
    payload.carePageDids = descendants[2];
    if (!isNil(user?.company?.business?.type)) {
      payload.service = BusinessType[user?.company?.business?.type];
    }

    return this.jwtService.sign(payload);
  }

  async genJwtTokenMobile(user: User) {
    const payload: Record<string, any> = {
      email: user.email,
      serviceRole: 'mobile',
    };
    return this.jwtService.sign(payload);
  }

  @RabbitRPC({
    exchange: 'identity-service-users',
    routingKey: 'find-user',
    queue: 'queue-find-queue',
    errorHandler: defaultNackErrorHandler,
  })
  async findUser({ filters, pagination, headers, request }) {
    const data = await this.getAllName(filters, pagination, headers, request);
    return data;
  }

  @RabbitRPC({
    exchange: 'identity-service-users',
    routingKey: 'find-user-by-code',
    queue: 'queue-find-user-by-code',
    errorHandler: defaultNackErrorHandler,
  })
  async findUserByCode({ code }) {
    // console.log('code', code);
    return await this.userRepo
      .createQueryBuilder('u')
      .andWhere('u.displayId = :code', { code })
      .getOne();
  }

  async getAllName(
    filters?: UsersFilter,
    pagination?: PaginationOptions,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<User[]> {
    const { teamIds, countryIds, query, type, ids, countries, status } = filters;
    const companyId = request?.user?.companyId;
    filters.companyIds = uniq([...(filters.companyIds || []), companyId]);

    const mQuery = this.userRepo
      .createQueryBuilder('u')
      .select([
        'u.id',
        'u.name',
        'u.email',
        'u.avatar',
        'u.phone',
        'u.status',
        'u.displayId',
        'u.countries',
      ])
      .leftJoinAndSelect('u.ownsCompany', 'ownsCompany')
      .leftJoinAndSelect('u.role', 'role')
      .leftJoinAndSelect('u.company', 'company')
      .leftJoinAndSelect('company.business', 'business')
      .andWhere('u.companyId IN (:...companyIds)', {
        companyIds: filters.companyIds,
      });
    if (status) {
      mQuery.andWhere('u.status IN (:...status)', { status });
    }
    if (type) mQuery.andWhere('u.type = :type', { type });
    if (filters.isBizAccount) {
      mQuery.andWhere('ownsCompany.ownerId IS NOT NULL ');
    }

    if (!!countries) mQuery.andWhere(':countries = ANY (u.countries)', { countries });

    if (!!teamIds) {
      mQuery.leftJoin('u.teams', 'teams');
      mQuery.andWhere('teams.id IN (:...teamIds)', { teamIds });
    }

    if (!isEmpty(countryIds)) {
      mQuery
        .leftJoin('u.scopes', 'scopes')
        .andWhere('(scopes.country_ids && :countryIds OR scopes.id IS NULL)', {
          countryIds,
        });
    }

    if (query) {
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('cast(u.id as text) ILIKE :userId', {
            userId: `%${query}%`,
          })
            .orWhere('u.name ILIKE :name', { name: `%${query}%` })
            .orWhere('u.phone ILIKE :name', { phone: `%${query}%` })
            .orWhere('u.displayId ILIKE :name', { name: `%${query}%` })
            .orWhere('u.email ILIKE :name', { email: `%${query}%` });
        }),
      );
    }
    if (!isEmpty(ids)) mQuery.andWhereInIds(ids);
    mQuery.addOrderBy('u.name', 'ASC');
    return await mQuery.getMany();
  }

  async findV2(
    filters?: UsersFilter,
    pagination?: PaginationOptions,
    request?: Record<string, any>,
  ) {
    const { query, roleIds, status } = filters;
    const companyId = request?.user?.companyId;

    const qb = this.userRepo
      .createQueryBuilder('u')
      .where('u.companyId = :companyId', { companyId })
      .leftJoin('u.profiles', 'profiles')
      .unScope('profiles')
      .orderBy('u.createdAt', 'DESC');

    if (pagination) {
      qb.take(pagination.limit).skip(pagination.skip);
    }
    qb.addSelect('profiles.id')
      .leftJoin('profiles.role', 'profileRole')
      .addSelect([
        'profileRole.id',
        'profileRole.name',
        'profileRole.status',
        'profileRole.moduleInCharge',
        'profileRole.dataAccessLevel',
      ]);

    if (query) {
      qb.andWhere(
        new Brackets(qb => {
          qb.where('cast(u.id as text) ILIKE :userId', {
            userId: `%${query}%`,
          })
            .orWhere('u.name ILIKE :name', { name: `%${query}%` })
            .orWhere('u.displayId ILIKE :name', { name: `%${query}%` })
            .orWhere('u.phone ILIKE :name', { phone: `%${query}%` })
            .orWhere('u.email ILIKE :name', { email: `%${query}%` });
        }),
      );
    }
    if (status) {
      qb.andWhere('u.status IN (:...status)', { status });
    }

    if (roleIds) {
      qb.andWhere('profileRole.id IN (:...roleIds)', { roleIds });
    }

    return await qb.getManyAndCount();
  }

  async find(
    filters?: UsersFilter,
    pagination?: PaginationOptions,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<[User[], number]> {
    const mQuery = this.getUserQuery(filters, pagination, headers, request)
      .leftJoinAndSelect('u.company', 'company')
      .leftJoin('company.business', 'business')
      .addSelect(['business.id', 'business.type'])
      .orderBy('u.createdAt', 'DESC');
    let joinProfiles = false;
    try {
      const alias = mQuery.expressionMap.findAliasByName('profiles');
      if (alias) {
        joinProfiles = true;
      }
    } catch (e) {}
    if (!joinProfiles) {
      if (filters?.query) {
        mQuery.leftJoin('u.profiles', 'profiles');
        mQuery.andWhere('u.name ILIKE :name', { name: `%${filters?.query}%` });
      } else {
        mQuery.leftJoin('u.profiles', 'profiles', `profiles.status = ${CommonStatus.activated}`);
      }
      mQuery.unScope('profiles');
    }
    mQuery
      .addSelect('profiles.id')
      .leftJoin('profiles.role', 'profileRole')
      .leftJoin('profiles.department', 'd1', 'profiles.department_id = d1.id')
      .addSelect(['d1.id', 'd1.name'])
      .addSelect([
        'profileRole.id',
        'profileRole.name',
        'profileRole.status',
        'profileRole.moduleInCharge',
        'profileRole.dataAccessLevel',
      ]);

    return await mQuery.getManyAndCount();
  }

  async countUser(filters: UsersFilter, groupBy: string[] = [], headers, request) {
    const mQuery = this.getUserQuery(filters, undefined, headers, request);
    mQuery.select('COUNT(DISTINCT(u.id))', 'count');
    for (const group of groupBy) {
      mQuery.addGroupBy(`u.${group}`);
      mQuery.addSelect(`u.${group}`, group);
    }

    const data = await mQuery.getRawMany();
    for (const item of data) {
      if (!isNil(item.status)) {
        item.status = UserStatus[item.status];
      }
      item.count = Number(item.count);
    }
    return data;
  }

  getUserQuery(
    filters: UsersFilter,
    pagination?: PaginationOptions,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    let hasJoinedProfiles = false;
    const qb = this.userRepo.createQueryBuilder('u');
    qb
      // .leftJoinAndSelect('u.company', 'company')
      // .leftJoinAndSelect('company.business', 'business')
      .leftJoinAndSelect('u.role', 'role')
      .leftJoinAndSelect('u.ownsCompany', 'ownsCompany');
    // .leftJoinAndSelect('u.scopes', 'scopes')
    // .leftJoinAndSelect('scopes.project', 'project');
    if (pagination) {
      qb.take(pagination.limit).skip(pagination.skip);
    }
    const {
      query,
      ids,
      status,
      role,
      fromPancake,
      ownsCompany,
      companyIds,
      teamIds,
      projectIds,
      roleIds,
      warehouses,
      countries,
      type,
      displayIds,
      countryIds,
      isOnline,
      unassigned,
    } = filters;
    const companyId = request?.user?.companyId;

    if (!isEmpty(ids)) qb.andWhereInIds(ids);
    if (!isEmpty(displayIds)) {
      qb.andWhere('u.displayId IN (:...displayIds)', { displayIds });
    }
    if (status) {
      qb.andWhere('u.status IN (:...status)', { status });
    }
    if (!_.isNil(fromPancake)) {
      qb.andWhere(`u.pancakeId IS ${fromPancake ? 'NOT ' : ''}NULL`);
    }

    if (role) {
      const roles = role?.map(it => {
        let per = Permission?.[it];
        if (!per) per = FFM.Permission?.[it];
        return per;
      });

      qb.andWhere('CAST(role.permission as BIGINT) & :role > 0', {
        role: `${sum(roles.map(i => BigInt(i)))}`,
      });
    }
    if (query) {
      qb.andWhere(
        new Brackets(qb => {
          qb.where('cast(u.id as text) ILIKE :userId', {
            userId: `%${query}%`,
          })
            .orWhere('u.name ILIKE :name', { name: `%${query}%` })
            .orWhere('u.displayId ILIKE :name', { name: `%${query}%` })
            .orWhere('u.phone ILIKE :name', { phone: `%${query}%` })
            .orWhere('u.email ILIKE :name', { email: `%${query}%` });
        }),
      );
    }
    if (!isNil(ownsCompany)) {
      qb.andWhere(`ownsCompany.id IS ${ownsCompany ? 'NOT' : ''} NULL`);
    }
    if (companyIds) {
      qb.andWhere('u.companyId IN (:...companyIds)', { companyIds });
    }
    // if (!!countryIds)
    // if (!!teamIds) {
    //   qb.leftJoin('u.teams', 'teams');
    //   qb.andWhere('teams.id IN (:...teamIds)', { teamIds });
    // }
    if (!isEmpty(projectIds)) {
      const scopePairs = projectIds.map(pid => `('${headers['country-ids']}', '${pid}')`);
      if (!hasJoinedProfiles) {
        qb.leftJoin(
          'u.profiles',
          'profiles',
          `profiles.status = ${CommonStatus.activated}`,
        ).unScope('profiles');
        hasJoinedProfiles = true;
      }

      qb.leftJoin('profiles.department', 'd', 'profiles.dataSetId IS NULL')
        .innerJoin(
          DataSet,
          'dataSet',
          'dataSet.id = profiles.dataSetId OR dataSet.id = d.dataSetId',
        )
        .innerJoin('dataSet.scopes', 'scopes')
        .andWhere(`(scopes.country_id, scopes.entity_id) IN (${scopePairs.join(', ')})`);
    }
    // if (ownsCompany) {
    //   qb.leftJoinAndSelect('ownsCompany.business', 'business');
    // }
    if (!!companyId) {
      qb.andWhere('u.companyId = :companyId', { companyId });
    }

    if (type) qb.andWhere('u.type = :type', { type });

    if (!!roleIds) {
      qb.andWhere('u.roleId IN (:...roleIds)', {
        roleIds,
      });
    }

    if (!!warehouses) qb.andWhere(':warehouses = ANY (u.warehouses)', { warehouses });

    if (!!countries) qb.andWhere(':countries = ANY (u.countries)', { countries });
    if (!isNil(isOnline)) qb.andWhere('u.isOnline = :isOnline', { isOnline });
    if (!isNil(unassigned)) {
      if (!hasJoinedProfiles) {
        qb.leftJoin(
          'u.profiles',
          'profiles',
          `profiles.status = ${CommonStatus.activated}`,
        ).unScope('profiles');
        hasJoinedProfiles = true;
      }
      if (unassigned) qb.andWhere('profiles.id IS NULL');
      else qb.andWhere('profiles.id IS NOT NULL');
    }
    return qb;
  }

  async findById(id: number, exp?: number): Promise<User> {
    const user = await this.userRepo.findOne(id, {
      relations: ['role', 'ownsCompany'],
    });
    if (exp - Date.now() < 1000 * 60 * 60 * 24 * 3) {
      user.token = await this.getJwtToken(user);
    }
    return user;
  }

  findByEmail(email: string): Promise<User | undefined> {
    return this.userRepo.findOne({
      where: {
        email,
      },
      relations: ['role'],
    });
  }

  findByPhone(phone: string): Promise<User | undefined> {
    return this.userRepo.findOne({
      where: {
        phone,
      },
      relations: ['role'],
    });
  }

  encodePassWord(password: string): string {
    return crypto
      .createHmac('sha256', process.env.PASSWORD_SECRET)
      .update(password)
      .digest('base64');
  }

  async registerUser(data: CreateUserDto, request): Promise<User> {
    const domainName = this.getHostName(request);
    return this.createUser({ ...data, domainName }, request);
  }

  async createUser(data: CreateUserDto, request?: Record<string, any>): Promise<User> {
    if (!data.domainName && !data.companyId)
      throw new BadRequestException('domain name or company id is required');

    const qb = this.userRepo
      .createQueryBuilder('user')
      .withDeleted()
      .andWhere('user.email = :email', { email: data.email });
    if (data.companyId)
      qb.andWhere('user.company_id = :companyId', {
        companyId: data.companyId,
      });
    if (data.domainName)
      qb.leftJoin('user.company', 'company').andWhere(
        ":domainName = ANY(string_to_array(company.domain_name, ','))",
        {
          domainName: data.domainName,
        },
      );
    const oldUser = await qb.getOne();

    if (oldUser) {
      throw new BadRequestException('Email đã được sử dụng cho tài khoản khác');
    }
    let newUser = plainToInstance(User, {
      id: oldUser?.id,
      ...data,
      password: this.encodePassWord(data.password),
      updatedBy: request?.user?.id,
    });
    if (data?.teamIds) newUser.teams = await this.teamRepo.findByIds(data.teamIds);
    if (data.roleId) newUser.role = await this.rolesRepo.findOne(data.roleId);
    if (data.scopes) newUser.scopes = plainToInstance(UserScope, data.scopes);
    newUser = await this.userRepo.save(plainToInstance(User, newUser)).catch(err => {
      if (err?.driverError)
        throw new BadRequestException(
          err?.driverError?.detail.search('display_id') > -1 ? `CL-0002` : err?.driverError?.detail,
        );
      return err;
    });
    newUser.token = await this.getJwtToken(newUser);

    return newUser;

    /** let authUser: User;
     if (userId) {
     authUser = await this.findById(userId);
     }
     if (body.providerToken) {
     let decodedIdToken: auth.DecodedIdToken;
     try {
     decodedIdToken = await this.firebaseAuthService.verifyIdToken(
     body.providerToken,
     );
     if (!decodedIdToken) {
     throw new BadRequestException();
     }
     console.log('decoded id token:', JSON.stringify(decodedIdToken));
     } catch (error) {
     console.log('failed to verify id token:', error);
     throw new BadRequestException();
     }

     const now = new Date();
     const userInfo = await this.firebaseAuthService.getUserInfo(
     decodedIdToken.uid,
     );
     const data = {
     email: userInfo.email || userInfo.providerData[0]?.email,
     phone: PhoneUtils.format(
     userInfo.phoneNumber || userInfo.providerData[0]?.phoneNumber,
     ),
     name: decodedIdToken.name,
     avatar: decodedIdToken.picture || userInfo.providerData[0]?.photoURL,
     updatedAt: now,
     fbId: userInfo.providerData[0]?.uid,
     } as User;
     let user = plainToInstance(User, data);
     let currentUser: User;
     if (user.fbId) {
     currentUser = await this.userRepository.findOne({
     fbId: user.fbId,
     });
     }
     if (authUser && currentUser && authUser.id !== currentUser.id) {
     throw new BadRequestException('Tài khoản Facebook đã được liên kết với tài khoản khác');
     }
     if (!currentUser && authUser) {
     currentUser = authUser;
     }
     if (currentUser) {
     // update from current user
     currentUser.phone = user.phone;
     currentUser.email = user.email;
     currentUser.name = user.name;
     currentUser.avatar = user.avatar;
     currentUser.updatedAt = user.updatedAt;
     currentUser.fbId = user.fbId;
     user = currentUser;
     } else {
     // user.roles = [Role.admin];
     user.createdAt = now;
     }
     user = await this.userRepository.save(user);
     user.token = this.getJwtToken(user);
     return user;
     } else {
     const oldUser = await this.userRepository.findOne(
     { email: body.email },
     { select: ['id'] },
     );
     if (oldUser) {
     throw new BadRequestException('Email đã được sử dụng cho tài khoản khác');
     }
     let newUser = plainToInstance(User, {
     ...body,
     password: this.encodePassWord(body.password),
     });
     if (body.roleIds)
     newUser.userRoles = await this.rolesRepository.findByIds(body.roleIds);
     newUser = await this.userRepository.save(plainToInstance(User, newUser));
     newUser.token = this.getJwtToken(newUser);
     return newUser;
     } */
  }

  async createCompany(body: CreateCompanyDto, queryRunner: QueryRunner): Promise<Company> {
    const data = plainToInstance(Company, {
      ...body,
      apiKey: crypto.randomUUID(),
    });
    if (body.businessId) {
      const business = await this.businessesService.findById(body.businessId);
      data.business = business;
    }

    const company = await queryRunner.manager.save(data);

    return company;
  }

  async createCompanyRootRole(company: Company, queryRunner: QueryRunner) {
    const perm = company.business.permission || BigInt(0);
    const newPermission = company.business.newPermission?.reduce((prev, it) => {
      prev[it] = $enum(ENUM_PERMISSION_MAPPING[it])
        .getValues()
        ?.reduce((sum: number, e: number) => BigInt(sum) | BigInt(e), [BigInt(0)]);
      return prev;
    }, Array(company.business.newPermission[company.business.newPermission.length - 1] + 1).fill(BigInt(0)));

    const role = plainToInstance(Roles, {
      name: 'Admin ' + company.name,
      description: 'Admin tổng của công ty ' + company.name,
      permission: perm,
      newPermission,
      companyId: company.id,
    });
    const adminRole = await queryRunner.manager.save(role);

    return adminRole;
  }

  async createCompanyRoot(body: CompanyRootUserDto, uid: number): Promise<User> {
    const oldCompany = await this.companyRepo
      .createQueryBuilder('c')
      .where('c.name = :companyName')
      .orWhere('c.domainName = :domainName')
      .setParameters({ companyName: body.companyName, domainName: body.domainName })
      .getOne();
    if (oldCompany) throw new BadRequestException('Tên công ty hoặc tên miền đã tồn tại');

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const company = await this.createCompany(
        {
          name: body.companyName,
          businessId: body.businessId,
          domainName: StringUtils.extractDomainName(body.domainName),
        },
        queryRunner,
      );

      const adminRole = await this.createCompanyRootRole(company, queryRunner);

      let newUser = plainToInstance(User, {
        ...body,
        company,
        companyId: company.id,
        status: UserStatus.active,
        password: this.encodePassWord(body.password),
        updatedBy: uid,
        ownsCompany: company,
      });

      newUser.role = adminRole;
      newUser = await queryRunner.manager.save(plainToInstance(User, newUser));
      // newUser.token = await this.getJwtToken(newUser);

      company.adminRoleId = adminRole.id;
      await queryRunner.manager.update(
        Company,
        { id: company.id },
        { adminRoleId: adminRole.id, ownerId: newUser.id },
      );

      const department = await queryRunner.manager.save(
        plainToInstance(Department, {
          name: company.name,
          roleId: adminRole?.id,
          level: 0,
          companyId: company.id,
        }),
      );

      const profile = await queryRunner.manager.save(
        plainToInstance(UserProfile, {
          userId: newUser.id,
          departmentId: department.id,
          roleId: adminRole?.id,
          type: ProfileType.leader,
        }),
      );
      department.leaderProfileId = profile.id;
      department.leaderProfile = profile;

      const dataSet = await queryRunner.manager.save(
        plainToInstance(DataSet, {
          entityId: department.id,
          entity: DataSetEntity.department,
          shortName: 'ALL',
          name: 'Tất cả dự án, thị trường',
          companyId: company.id,
        }),
      );
      await queryRunner.manager.insert(
        DataSetScope,
        plainToInstance(DataSetScope, {
          dataSetId: dataSet.id,
          entityId: null,
          countryId: null,
        }),
      );
      department.dataSetId = dataSet.id;
      department.dataSet = dataSet;

      await queryRunner.manager.update(
        Department,
        { id: department.id },
        { leaderProfileId: profile.id, dataSetId: dataSet.id },
      );

      await queryRunner.commitTransaction();

      await this.amqpConnection.publish('facebook-bot', 'init-care-reasons', {
        companyId: company.id,
      });
      await this.amqpConnection.publish('order-service', 'init-care-reasons', {
        companyId: company.id,
      });

      return newUser;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async editCompanyRoot(
    id: number,
    data: UpdateRootUserDto,
    request?: Record<string, any>,
  ): Promise<User> {
    let user = await this.userRepo
      .createQueryBuilder('u')
      .leftJoinAndSelect('u.ownsCompany', 'ownsCompany')
      .where('u.id = :id', { id })
      .andWhere('ownsCompany.id IS NOT NULL')
      .getOne();
    if (!user) throw new NotFoundException('Tài khoản không tồn tại');

    user = plainToInstance(User, {
      ...user,
      ...data,
      updatedBy: request?.user?.id,
    });
    if (data.password) {
      user.password = this.encodePassWord(data?.password);
    }
    const newUser = await this.userRepo.save(user);

    const companyUpdateData: UpdateCompanyDto = {};
    if (data.businessId) companyUpdateData.businessId = data.businessId;
    if (!isNil(data.status)) {
      companyUpdateData.status =
        data.status === UserStatus.active ? CommonStatus.activated : CommonStatus.deactivated;
    }
    if (!isEmpty(companyUpdateData)) {
      newUser.ownsCompany = await this.companiesService.updateCompany(
        user.ownsCompany.id,
        companyUpdateData,
      );
      const users = await this.userRepo.find({
        where: { companyId: user.ownsCompany.id },
        select: ['id'],
      });
      const userIds = users.map(it => it.id);
      await Promise.all(userIds.map(async id => await this.removeUserCache(id)));
    }
    await this.removeUserCache(newUser?.id);
    return newUser;
  }

  getHostName = (request: Record<string, any>) => {
    const hostName =
      request.headers.origin === 'https://localhost:3006'
        ? 'root.agbiz.tech'
        : startsWith(request.headers.origin, 'https://localhost')
        ? request.headers.hostlocal
          ? request.headers.hostlocal
          : 'admin-staging.agbiz.vn'
        : request.headers.origin?.replace('https://', '');
    return hostName;
  };

  async login(data: LoginUserDto, request): Promise<User> {
    const hostName = this.getHostName(request);
    const qb = this.userRepo
      .createQueryBuilder('u')
      .leftJoin(Company, 'c', 'u.companyId = c.id')
      .andWhere('LOWER(u.email) = :email', { email: data.email });

    if (hostName !== process.env.ROOT_ADMIN_HOST)
      qb.andWhere(":hostName = ANY(string_to_array(c.domain_name, ','))", { hostName });
    else qb.andWhere('u.type = :type', { type: UserType.admin });
    const user = await qb.getOne();
    if (!user) {
      throw new BadRequestException('Email không tồn tại');
    }
    const hashPassWord = this.encodePassWord(data.password);
    if (hashPassWord !== user.password) {
      throw new BadRequestException('Mật khẩu không đúng');
    }
    if (!user.sessionId) {
      const sid = this.randomStr(32);
      user.sessionId = sid;
      await this.userRepo.save(user);
    }
    switch (user.status) {
      case UserStatus.pending:
        throw new BadRequestException('Tài khoản đang được chờ duyệt');
      case UserStatus.deactivated:
        throw new BadRequestException('Tài khoản bị khoá');
      default:
        const token = await this.getJwtToken(user);
        user.token = token;
        return user;
    }
  }

  async getMe(reqUser, exp?: number): Promise<User> {
    const { id, companyId, service } = reqUser;
    const [
      user,
      // ownsCompany,
      // company
    ] = await Promise.all([
      this.userRepo
        .createQueryBuilder('u')
        .where('u.id = :id')
        .andWhere('u.status = :status')
        .andWhere('(u.company = :companyId OR u.company IS NULL)')
        .leftJoinAndSelect('u.role', 'role')
        .leftJoinAndSelect('u.teams', 'teams')
        .leftJoinAndSelect('u.scopes', 'scopes')
        .leftJoin('u.profiles', 'profiles', `profiles.status = ${CommonStatus.activated}`)
        .addSelect([
          'profiles.id',
          'profiles.userId',
          'profiles.departmentId',
          'profiles.status',
          'profiles.roleId',
          'profiles.type',
        ])
        .leftJoin(
          'profiles.role',
          'profilesRole',
          `profilesRole.status = ${CommonStatus.activated}`,
        )
        .addSelect([
          'profilesRole.id',
          'profilesRole.name',
          'profilesRole.status',
          'profilesRole.moduleInCharge',
          'profilesRole.parentId',
          'profilesRole.dataAccessLevel',
          'profilesRole.newPermission',
        ])
        .leftJoin('profiles.department', 'department')
        .addSelect(['department.id', 'department.name'])
        .unScope('profiles')
        .setParameters({ id, companyId, status: UserStatus.active })
        .getOne(),
      // this.companiesService.getCompanyByOwnerId(id),
      // this.companiesService.findById(companyId, [
      //   'business',
      //   'countries',
      //   'projects',
      //   'projects.markets',
      // ]),
    ]);
    // user.ownsCompany = ownsCompany;
    // user.company = company;
    // console.log(`type`, service)
    if (user.companyId) {
      user.company = plainToInstance(Company, {
        business: plainToInstance(Business, { type: BusinessType[service] }),
      });
    }
    if (user.role) user.role.company = user.company;

    if (!user) throw new ForbiddenException('Account has been locked or does not exist');

    if (exp && exp - Date.now() < *********) {
      user.token = await this.getJwtToken(user);
    }
    return user;
  }

  async updateWarehouse(data: UpdateWarehouseUser, updatedBy?: number): Promise<User[]> {
    const needRemoveIds = difference(data?.oldIds, data?.newIds);
    const needAddIds = difference(data?.newIds, data?.oldIds);

    const mergeIds = concat(needRemoveIds, needAddIds);
    if (mergeIds.length > 0) {
      const users = await this.userRepo.find({
        where: {
          id: In(mergeIds),
        },
      });
      const newData = users.map(el => {
        el.warehouses = el?.warehouses ?? [];
        if (needRemoveIds.includes(el?.id?.toString())) {
          remove(el?.warehouses, function(n: any) {
            return n == data?.id?.toString();
          });
        } else {
          el.warehouses.push(data?.id?.toString());
        }

        el.warehouses = uniq(el?.warehouses ?? []);
        delete el.createdAt;
        delete el.updatedAt;

        el.updatedBy = updatedBy;

        return el;
      });
      const res = await this.userRepo.save(newData).catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
      if (!!newData) {
        newData.forEach(item => {
          this.removeWarehouseUserCache(item?.id);
        });
      }
      return res;
    }
    throw new Error('');
  }

  async update(
    body: UpdateUserDto,
    id: number,
    updatedBy?: number,
    user?: Record<string, any>,
  ): Promise<User> {
    const oldRecord = await this.userRepo.findOne({
      relations: ['ownsCompany', 'scopes', 'teams'],
      where: { id, companyId: user?.companyId },
    });
    if (!oldRecord) throw new NotFoundException('Tài khoản không tồn tại');

    const isDeactivated =
      (body?.roleId && body?.roleId != oldRecord?.roleId) ||
      body.status === UserStatus.deactivated ||
      (body?.countries &&
        oldRecord?.countries &&
        intersection(
          body?.countries?.map((it: string) => Number(it)),
          oldRecord?.countries?.map((it: string) => Number(it)),
        )?.length != oldRecord?.countries?.length) ||
      (body?.warehouses &&
        oldRecord?.warehouses &&
        intersection(
          body?.warehouses?.map((it: string) => Number(it)),
          oldRecord?.warehouses?.map((it: string) => Number(it)),
        )?.length != oldRecord?.warehouses?.length);

    console.log('isDeactivated', isDeactivated);

    const updateUser = plainToInstance(User, {
      ...omit(body, ['scopes']),
      ...(isDeactivated ? { sessionId: this.randomStr(32) } : {}),
      id: oldRecord.id,
      updatedBy,
    });

    if (isDeactivated) {
      const skey = `${USER_SESSION}.${updateUser.id}.`;
      this.redisCache.set(skey, updateUser.sessionId || '');
    }

    if (body.password) updateUser.password = this.encodePassWord(body?.password);
    if (body.roleId) updateUser.role = await this.rolesRepo.findOne(body.roleId);

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const data = await queryRunner.manager.save(updateUser);

      if (body?.teamIds) {
        const teams = await queryRunner.manager.findByIds(Team, body.teamIds);
        const removeTeamIds = oldRecord.teams.reduce((prev, t) => {
          if (body.teamIds.includes(t.id)) return prev;
          prev.push(t.id);
          return prev;
        }, []);
        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into('users_teams')
          .values(teams.map(t => ({ user_id: id, team_id: t.id })))
          .orIgnore()
          .execute();

        if (!isEmpty(removeTeamIds)) {
          await queryRunner.manager
            .createQueryBuilder()
            .delete()
            .from('users_teams')
            .where({ team_id: In(removeTeamIds) })
            .andWhere({ user_id: id })
            .execute();
        }

        data.teams = teams;
      }

      if (body.scopes) {
        const scopes: UserScope[] = [];
        const projectIds: number[] = [];
        for (const scope of body.scopes) {
          scopes.push(plainToInstance(UserScope, { ...scope, userId: data.id }));
          projectIds.push(scope.projectId);
        }
        const removeScopes = oldRecord.scopes.filter(it => !projectIds.includes(it.projectId));

        const mScopes = [...oldRecord.scopes, ...scopes].reduce((prev: UserScope[], item) => {
          const index = findIndex(prev, it => it.projectId === item.projectId);
          if (index !== -1) {
            prev[index] = {
              ...prev[index],
              ...pickBy(item, it => it !== undefined),
            };
          } else prev.push(item);
          return prev;
        }, []);
        // console.log(`mScopes`, mScopes)
        data.scopes = await queryRunner.manager.save(plainToInstance(UserScope, mScopes));
        const removed = await queryRunner.manager.remove(removeScopes);
        data.scopes = difference(data.scopes, removed);
      }

      await queryRunner.commitTransaction();
      await this.removeUserCache(data?.id);

      // console.log(`newUser is admin`, data.isAdmin)
      // console.log(`user.status`, user.status)
      // console.log(`newUser.status !== user.status`, oldRecord.status !== user.status)

      if (Boolean(data.ownsCompany) && !isNil(body.status) && oldRecord.status !== body.status) {
        await this.companiesService.updateCompany(oldRecord.companyId, {
          status:
            data.status === UserStatus.active ? CommonStatus.activated : CommonStatus.deactivated,
        });
        const users = await this.userRepo.find({
          where: { companyId: oldRecord.companyId },
          select: ['id'],
        });
        const userIds = users.map(it => it.id);
        await Promise.all(userIds.map(id => this.removeUserCache(id)));
      }

      return data;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      if (err?.driverError)
        throw new BadRequestException(
          err?.driverError?.constraint
            ? err?.driverError?.constraint == 'UQ_EMAIL_COMPANY'
              ? `Email ${body?.email} already exists`
              : err?.driverError?.detail.search('display_id') > -1
              ? `DisplayID ${body?.displayId} already exists`
              : err?.driverError?.detail
            : err?.driverError?.detail,
        );
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async removeUserCache(id: number) {
    if (!id) {
      return;
    }
    return await Promise.all([
      this.redisCache.del(`user-*.${id}.*`),
      this.removeWarehouseUserCache(id),
      this.redisCache.del(`${USER_ACTIVE}.${id}.`),
      this.redisCache.delWithPrefix(`${USER_ROLES}.${id}`),
      this.redisCache.del(`${USER_SCOPES}.${id}.`),
    ]);
  }

  async removeWarehouseUserCache(id: number) {
    if (!id) {
      return;
    }
    return await this.redisCache.del(`${USER_WAREHOUSE}.${id}.`);
  }

  async delete(id: number): Promise<UpdateResult> {
    const oldUser = await this.userRepo.findOne({ id });
    if (!oldUser) {
      throw new BadRequestException('Không tìm thấy người dùng');
    }
    return this.userRepo.softDelete(oldUser.id);
  }

  async getAll(): Promise<User[]> {
    return this.userRepo.find();
  }

  async getDescendantIdsByUserId(userId: number): Promise<number[]> {
    const data = await this.userProfileRepo
      .createQueryBuilder('profile')
      .innerJoin('profile.department', 'department')
      .where('profile.userId = :userId', { userId })
      .andWhere(`department.leader_profile_id = profile.id`)
      .select(['profile.id', 'profile.user_id', 'department.id'])
      .getRawOne();
    if (!data) return [];
    const departmentIds = await this.departmentRepo
      .createQueryBuilder('d')
      .innerJoin('departments_closure', 'dc', `dc.id_descendant = d.id`)
      .where('dc.id_ancestor = :departmentId', { departmentId: data.department_id })
      .select(['dc.id_descendant as id_descendant'])
      .getRawMany();
    const departmenIdstDescendant = departmentIds.map(it => it.id_descendant);

    if (!departmenIdstDescendant || departmenIdstDescendant.length == 0) return [];

    const userIdsDescendant = await this.userProfileRepo
      .createQueryBuilder('userProfile')
      .select('userProfile.userId', 'userId')
      .where('userProfile.departmentId IN (:...departmentIds)', {
        departmentIds: departmenIdstDescendant,
      })
      .groupBy('userProfile.userId')
      .getRawMany();
    return userIdsDescendant ? userIdsDescendant.map(it => it.userId) : [];
  }

  async resetPassword(params): Promise<User> {
    const user = await this.userRepo.findOne({
      email: params?.email,
    });

    if (!!!user) throw new BadRequestException(`Không tồn tại tại khoản ${params?.email}`);
    if (isEmpty(params?.password?.trim()))
      throw new BadRequestException('Mật khẩu không được để trống');
    const newUser = plainToInstance(User, {
      ...user,
      password: this.encodePassWord(params?.password?.trim()),
    });
    return await this.userRepo.save(newUser);
  }

  @RabbitRPC({
    exchange: 'identity-service',
    routingKey: 'clear-auth-cache',
    queue: 'identity-clear-auth-cache',
    errorHandler: rmqErrorsHandler,
  })
  async clearAuthCache(payload: { roleId: number; departmentId: number }) {
    const { roleId, departmentId } = payload;
    if (!roleId && !departmentId) return new Nack(false);

    let raw = [];
    const qb = getConnection(identityConnection)
      .createQueryBuilder()
      .from(UserProfile, 'profile')
      .unScope('profile')
      .select('DISTINCT profile.userId', 'user_id')
      .groupBy('profile.userId');

    if (roleId) {
      const descendantRolesQb = getConnection(identityConnection)
        .createQueryBuilder()
        .from('roles_closure', 'r_closure')
        .where(`r_closure.id_ancestor = ${roleId}`)
        .select('r_closure.id_descendant');

      raw = await qb
        .where(`profile.roleId IN (${descendantRolesQb.getQuery()})`)
        .setParameters(descendantRolesQb.getParameters())
        .getRawMany();
    } else if (departmentId) {
      const descendantDepartmentSubQb = getConnection(identityConnection)
        .createQueryBuilder()
        .from('departments_closure', 'd_closure')
        .where(`d_closure.id_ancestor = ${departmentId}`)
        .select('d_closure.id_descendant');
      raw = await qb
        .where(`profile.departmentId IN (${descendantDepartmentSubQb.getQuery()})`)
        .setParameters(descendantDepartmentSubQb.getParameters())
        .getRawMany();
    }

    const redisMulti = this.redis.multi();

    for (const it of raw) {
      const key = `${USER}.${it.user_id}`;
      redisMulti.del(key);
    }

    return redisMulti.exec();
  }
}
