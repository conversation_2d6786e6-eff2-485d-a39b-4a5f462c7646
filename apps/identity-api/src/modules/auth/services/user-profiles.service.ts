import { defaultNack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSet } from 'apps/identity-api/src/entities/data-set.entity';
import { UserProfile } from 'apps/identity-api/src/entities/user-profile.entity';
import { ProfileType } from 'apps/identity-api/src/enums/profile-type.enum';
import { RoleStatus } from 'apps/identity-api/src/enums/role-status.enum';
import { UserProfilesFilter } from 'apps/identity-api/src/filters/user-profiles.filter';
import { USER, USER_DESCENDANTS, USER_PROFILES } from 'core/cache/constants/prefix.constant';
import { identityConnection } from 'core/constants/database-connection.constant';
import { CommonStatus } from 'core/enums/common-status.enum';
import { Redis } from 'ioredis';
import { isEmpty, isNil } from 'lodash';
import { getConnection, Repository } from 'typeorm';

@Injectable()
export class UserProfilesService {
  constructor(
    @InjectRepository(UserProfile, identityConnection)
    private profilesRepo: Repository<UserProfile>,
    @InjectRedis()
    private redis: Redis,
  ) {}

  getProfilesQueryBuilder(filter: UserProfilesFilter) {
    const qb = this.profilesRepo.createQueryBuilder('p').select('p.id');
    const { ids, userIds, status, companyId } = filter;

    qb.innerJoin('p.department', 'd', 'd.companyId = :companyId', { companyId });

    if (!isEmpty(ids)) qb.andWhere('p.id IN (:...ids)', { ids });
    if (!isEmpty(userIds)) qb.andWhere('p.userId IN (:...userIds)', { userIds });
    if (!isNil(status)) qb.andWhere('p.status = :status', { status });
    return getConnection(identityConnection)
      .createQueryBuilder()
      .addCommonTableExpression(() => qb, 'profiles_cte')
      .from(`profiles_cte`, 'p');
  }

  async getProfiles(filter: UserProfilesFilter) {
    const cteQb = this.getProfilesQueryBuilder(filter);
    const qb = this.profilesRepo
      .createQueryBuilder('p')
      .where(`p.id IN (${cteQb.getQuery()})`)
      .setParameters(cteQb.getParameters());

    qb.leftJoin('p.role', 'r', `r.id = p.roleId`)
      .addSelect(['r.id', 'r.name', 'r.status'])
      .leftJoin('p.user', 'u')
      .addSelect(['u.id', 'u.name']);

    return qb.getMany();
  }

  async countProfiles(filter: UserProfilesFilter, groupBy: string[] = ['userId']) {
    const cteQb = this.getProfilesQueryBuilder(filter);
    const qb = this.profilesRepo
      .createQueryBuilder('p')
      .where(`p.id IN (${cteQb.getQuery()})`)
      .setParameters(cteQb.getParameters());
    qb.leftJoinAndSelect('p.user', 'u').select('COUNT(DISTINCT p.id)', 'count');
    for (const group of groupBy) {
      qb.addGroupBy(`p.${group}`);
      qb.addSelect(`p.${group}`, group);
    }

    const data = await qb.getRawMany();
    for (const item of data) {
      if (!isNil(item.status)) {
        item.status = CommonStatus[item.status];
      }
      item.count = Number(item.count);
    }
    return data;
  }

  @RabbitRPC({
    exchange: 'identity-service',
    routingKey: 'check-user-profiles',
    queue: 'identity-check-user-profiles',
    errorHandler: defaultNackErrorHandler,
  })
  async checkUserProfiles({ id }) {
    if (!id) return new Nack(false);

    const key = `${USER}.${id}`;
    let data: string = await this.redis.hget(key, USER_PROFILES);
    if (data) {
      try {
        const minified = JSON.parse(data);
        return minified;
      } catch (error) {
        console.log(`parse cached user profiles data err`, error);
      }
      return data;
    }

    const profiles = await this.profilesRepo
      .createQueryBuilder('profile')
      .leftJoin('profile.department', 'd', 'profile.dataSetId IS NULL')
      .unScope('d')
      .innerJoinAndMapOne(
        'profile.dataSet',
        DataSet,
        'dataSet',
        'dataSet.id = profile.dataSetId OR dataSet.id = d.dataSetId',
      )
      .innerJoin('dataSet.scopes', 'scopes')
      .addSelect(['scopes.id', 'scopes.entityId', 'scopes.countryId'])
      .innerJoin('profile.role', 'role', `role.status = ${RoleStatus.activated}`)
      .addSelect(['role.id', 'role.dataAccessLevel', 'role.moduleInCharge', 'role.newPermission'])
      .where(`profile.status = ${CommonStatus.activated}`)
      .andWhere('profile.userId = :id')
      .setParameters({ id })
      .getMany();
    // return profiles;

    const minified = profiles.map(pr => {
      const minifiedScopes = pr.dataSet.scopes.map(sc => [sc.countryId, sc.entityId]);
      return [
        pr.role.dataAccessLevel,
        pr.role.moduleInCharge,
        minifiedScopes,
        pr.role.newPermission,
        pr.type,
        pr.departmentId,
      ];
    });

    // console.log(`minified`, minified);
    data = JSON.stringify(minified);
    await this.redis
      .multi()
      .hset(key, USER_PROFILES, data)
      .expire(key, 5 * 60)
      .exec();
    return minified;
  }

  @RabbitRPC({
    exchange: 'identity-service',
    routingKey: 'check-user-descendants',
    queue: 'identity-check-user-descendants',
    errorHandler: defaultNackErrorHandler,
  })
  async checkUserDescendants({ id }) {
    if (!id) return new Nack(false);

    const key = `${USER}.${id}`;
    let data: string = await this.redis.hget(key, USER_DESCENDANTS);
    if (data) {
      try {
        const minified = JSON.parse(data);
        return minified;
      } catch (error) {
        console.log(`parse cached user descendants data err`, error);
      }
      return data;
    }

    const cteQb = this.profilesRepo
      .createQueryBuilder('up')
      .select('up.department_id')
      .where('up.user_id = :userId')
      .andWhere('up.status = :status')
      .setParameters({ userId: id, status: CommonStatus.activated });

    const results = await this.profilesRepo
      .createQueryBuilder('profile')
      .addCommonTableExpression(() => cteQb, 'user_department')
      .innerJoin(
        'departments_closure',
        'dept_closure',
        'profile.department_id = dept_closure.id_descendant',
      )
      .innerJoin('departments', 'd', 'd.id = dept_closure.id_descendant')
      .innerJoin(
        'roles',
        'r',
        '((profile.role_id IS NOT NULL AND profile.role_id = r.id) OR (profile.role_id IS NULL AND d.role_id = r.id))',
      )
      .where(`profile.status = ${CommonStatus.activated}`)
      .andWhere(`dept_closure.id_ancestor IN (SELECT department_id FROM user_department)`)
      .select('profile.user_id', 'uid')
      .addSelect(
        `CASE WHEN (r.module_in_charge IS NULL OR r.module_in_charge = '{}' OR 0 = ANY(r.module_in_charge)) THEN TRUE ELSE FALSE END`,
        'is_marketer',
      )
      .addSelect(
        `CASE WHEN (r.module_in_charge IS NULL OR r.module_in_charge = '{}' OR 1 = ANY(r.module_in_charge)) THEN TRUE ELSE FALSE END`,
        'is_telesale',
      )
      .addSelect(
        `CASE WHEN (r.module_in_charge IS NULL OR r.module_in_charge = '{}' OR 2 = ANY(r.module_in_charge)) THEN TRUE ELSE FALSE END`,
        'is_carepage',
      )
      .getRawMany();

    const [marketerIds, saleIds, carePageIds] = results.reduce(
      (prev, result) => {
        if (result.is_marketer && !prev[0].includes(result.uid)) {
          prev[0].push(result.uid);
        }
        if (result.is_telesale && !prev[1].includes(result.uid)) {
          prev[1].push(result.uid);
        }
        if (result.is_carepage && !prev[2].includes(result.uid)) {
          prev[2].push(result.uid);
        }
        return prev;
      },
      [[], [], []],
    );

    data = JSON.stringify([marketerIds, saleIds, carePageIds]);
    await this.redis
      .multi()
      .hset(key, USER_DESCENDANTS, data)
      .expire(key, 5 * 60)
      .exec();
    return [marketerIds, saleIds, carePageIds];
  }

  @RabbitRPC({
    exchange: 'identity-service',
    routingKey: 'check-user-descendants-v2',
    queue: 'identity-check-user-descendants-v2',
    errorHandler: defaultNackErrorHandler,
  })
  @RabbitRPC({
    exchange: 'identity-service',
    routingKey: 'get-user-department-descendants',
    queue: 'identity-get-user-department-descendants',
    errorHandler: defaultNackErrorHandler,
  })
  async getUserDepartmentDescendants({ companyId, countryId }) {
    // Query to get user_id | department_ids | descendants_ids format
    const qb = this.profilesRepo
      .createQueryBuilder('up')
      .select('up.user_id', 'user_id')
      .addSelect(`ARRAY_AGG(DISTINCT up.department_id)`, 'department_ids')
      .addSelect(
        `ARRAY[
          ARRAY_AGG(DISTINCT CASE WHEN (0 = ANY(r.module_in_charge)) THEN descendant_profiles.user_id END) FILTER (WHERE descendant_profiles.user_id IS NOT NULL),
          ARRAY_AGG(DISTINCT CASE WHEN (1 = ANY(r.module_in_charge)) THEN descendant_profiles.user_id END) FILTER (WHERE descendant_profiles.user_id IS NOT NULL),
          ARRAY_AGG(DISTINCT CASE WHEN (2 = ANY(r.module_in_charge)) THEN descendant_profiles.user_id END) FILTER (WHERE descendant_profiles.user_id IS NOT NULL)
        ]`,
        'descendants_ids',
      )
      .innerJoin('departments', 'd', 'd.id = up.department_id')
      .innerJoin(
        'roles',
        'user_role',
        `((up.role_id IS NOT NULL AND up.role_id = user_role.id) OR (up.role_id IS NULL AND d.role_id = user_role.id)) AND user_role.status = ${RoleStatus.activated}`,
      )
      .leftJoin(
        'departments_closure',
        'dept_closure',
        'dept_closure.id_ancestor = up.department_id',
      )
      .leftJoin(
        'user_profiles',
        'descendant_profiles',
        `descendant_profiles.department_id = dept_closure.id_descendant
         AND descendant_profiles.status = ${CommonStatus.activated}
         AND descendant_profiles.user_id != up.user_id`,
      )
      .leftJoin(
        'departments',
        'descendant_dept',
        'descendant_dept.id = descendant_profiles.department_id',
      )
      .leftJoin(
        'roles',
        'r',
        `((descendant_profiles.role_id IS NOT NULL AND descendant_profiles.role_id = r.id) OR (descendant_profiles.role_id IS NULL AND descendant_dept.role_id = r.id)) AND r.status = ${RoleStatus.activated}`,
      )
      .where(`up.status = ${CommonStatus.activated}`)
      .andWhere(`up.type = ${ProfileType.leader}`)
      .andWhere('d.company_id = :companyId', { companyId })
      .groupBy('up.user_id');

    if (countryId) {
      qb.leftJoin(
        'data_set_scopes',
        'scopes',
        '(descendant_profiles.data_set_id IS NOT NULL AND descendant_profiles.data_set_id = scopes.data_set_id) OR (descendant_profiles.data_set_id IS NULL AND descendant_dept.data_set_id = scopes.data_set_id)',
      ).andWhere('scopes.country_id = :countryId', { countryId });
    }

    const results = await qb.getRawMany();

    return results.map(result => ({
      user_id: result.user_id,
      department_ids: result.department_ids || [],
      descendants_ids: [
        result.descendants_ids?.[0]?.filter((id: any) => id !== null) || [], // marketer IDs
        result.descendants_ids?.[1]?.filter((id: any) => id !== null) || [], // telesale IDs
        result.descendants_ids?.[2]?.filter((id: any) => id !== null) || [], // carepage IDs
      ],
    }));
  }

  /**
   * Alternative method using raw SQL for better performance
   * Returns data in format: user_id | department_ids | descendants_ids
   */
  async getUserDepartmentDescendantsSQL({ companyId, countryId }) {
    let sql = `
      WITH leader_departments AS (
        SELECT
          up.user_id,
          ARRAY_AGG(DISTINCT up.department_id) as department_ids
        FROM user_profiles up
        INNER JOIN departments d ON d.id = up.department_id
        WHERE up.status = ${CommonStatus.activated}
          AND up.type = ${ProfileType.leader}
          AND d.company_id = $1
        GROUP BY up.user_id
      ),
      descendant_users AS (
        SELECT
          ld.user_id as leader_user_id,
          ld.department_ids,
          dp.user_id as descendant_user_id,
          CASE WHEN (0 = ANY(r.module_in_charge)) THEN dp.user_id END as marketer_id,
          CASE WHEN (1 = ANY(r.module_in_charge)) THEN dp.user_id END as telesale_id,
          CASE WHEN (2 = ANY(r.module_in_charge)) THEN dp.user_id END as carepage_id
        FROM leader_departments ld
        INNER JOIN departments_closure dc ON dc.id_ancestor = ANY(ld.department_ids)
        INNER JOIN user_profiles dp ON dp.department_id = dc.id_descendant
        INNER JOIN departments dd ON dd.id = dp.department_id
        LEFT JOIN roles r ON (
          (dp.role_id IS NOT NULL AND dp.role_id = r.id) OR
          (dp.role_id IS NULL AND dd.role_id = r.id)
        ) AND r.status = ${RoleStatus.activated}
        WHERE dp.status = ${CommonStatus.activated}
          AND dp.user_id != ld.user_id
    `;

    const params = [companyId];

    if (countryId) {
      sql += `
        LEFT JOIN data_set_scopes dss ON (
          (dp.data_set_id IS NOT NULL AND dp.data_set_id = dss.data_set_id) OR
          (dp.data_set_id IS NULL AND dd.data_set_id = dss.data_set_id)
        )
        WHERE dss.country_id = $2
      `;
      params.push(countryId);
    }

    sql += `
      )
      SELECT
        leader_user_id as user_id,
        department_ids,
        ARRAY[
          ARRAY_AGG(DISTINCT marketer_id) FILTER (WHERE marketer_id IS NOT NULL),
          ARRAY_AGG(DISTINCT telesale_id) FILTER (WHERE telesale_id IS NOT NULL),
          ARRAY_AGG(DISTINCT carepage_id) FILTER (WHERE carepage_id IS NOT NULL)
        ] as descendants_ids
      FROM descendant_users
      GROUP BY leader_user_id, department_ids
      ORDER BY leader_user_id;
    `;

    const results = await this.profilesRepo.query(sql, params);

    return results.map(result => ({
      user_id: result.user_id,
      department_ids: result.department_ids || [],
      descendants_ids: [
        result.descendants_ids?.[0] || [], // marketer IDs
        result.descendants_ids?.[1] || [], // telesale IDs
        result.descendants_ids?.[2] || [], // carepage IDs
      ],
    }));
  }

  async checkUserDescendantsV2({ id, countryId }) {
    if (!id) return new Nack(false);

    const key = `${USER}.${id}`;
    let data: string = await this.redis.hget(key, `${USER_DESCENDANTS}-v2`);
    if (data) {
      try {
        const minified = JSON.parse(data);
        return minified;
      } catch (error) {
        console.log(`parse cached user descendants data err`, error);
      }
      return data;
    }

    const cteQb = this.profilesRepo
      .createQueryBuilder('up')
      .select('up.department_id')
      .where('up.user_id = :userId')
      .andWhere('up.status = :status')
      .andWhere('up.type = :type')
      .setParameters({ userId: id, status: CommonStatus.activated, type: ProfileType.leader });

    const qb = this.profilesRepo
      .createQueryBuilder('profile')
      .addCommonTableExpression(() => cteQb, 'user_department')
      .innerJoin(
        'departments_closure',
        'dept_closure',
        'profile.department_id = dept_closure.id_descendant',
      )
      .innerJoin('departments', 'd', 'd.id = dept_closure.id_descendant')
      .innerJoin(
        'roles',
        'r',
        `((profile.role_id IS NOT NULL AND profile.role_id = r.id) OR (profile.role_id IS NULL AND d.role_id = r.id)) AND r.status = ${RoleStatus.activated}`,
      )
      .leftJoin(
        'data_set_scopes',
        'scopes',
        '(profile.data_set_id IS NOT NULL AND profile.data_set_id = scopes.data_set_id) OR (profile.data_set_id IS NULL AND d.data_set_id = scopes.data_set_id)',
      )
      .where(`profile.status = ${CommonStatus.activated}`)
      .andWhere(`dept_closure.id_ancestor IN (SELECT department_id FROM user_department)`);

    if (countryId) {
      qb.andWhere('scopes.country_id = :countryId', { countryId });
    }

    qb.select('profile.user_id', 'uid')
      .addSelect(`CASE WHEN (0 = ANY(r.module_in_charge)) THEN TRUE ELSE FALSE END`, 'is_marketer')
      .addSelect(`CASE WHEN (1 = ANY(r.module_in_charge)) THEN TRUE ELSE FALSE END`, 'is_telesale')
      .addSelect(`CASE WHEN (2 = ANY(r.module_in_charge)) THEN TRUE ELSE FALSE END`, 'is_carepage');

    const results = await qb.getRawMany();

    const [marketerIds, saleIds, carePageIds] = results.reduce(
      (prev, result) => {
        if (result.is_marketer && !prev[0].includes(result.uid)) {
          prev[0].push(result.uid);
        }
        if (result.is_telesale && !prev[1].includes(result.uid)) {
          prev[1].push(result.uid);
        }
        if (result.is_carepage && !prev[2].includes(result.uid)) {
          prev[2].push(result.uid);
        }
        return prev;
      },
      [[], [], []],
    );

    data = JSON.stringify([marketerIds, saleIds, carePageIds]);
    await this.redis
      .multi()
      .hset(key, `${USER_DESCENDANTS}-v2`, data)
      .expire(key, 5 * 60)
      .exec();
    return [marketerIds, saleIds, carePageIds];
  }
}
