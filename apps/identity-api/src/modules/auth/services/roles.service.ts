import { AmqpConnection, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateRoleDto, UpdateRoleDto } from 'apps/identity-api/src/dtos/role.dto';
import { Logs } from 'apps/identity-api/src/entities/logs.entity';
import { UserProfile } from 'apps/identity-api/src/entities/user-profile.entity';
import { BusinessType } from 'apps/identity-api/src/enums/business-type.enum';
import { RoleStatus } from 'apps/identity-api/src/enums/role-status.enum';
import { RolesFilter } from 'apps/identity-api/src/filters/roles.filter';
import { plainToInstance } from 'class-transformer';
import {
  NEW_USER_ROLES,
  USER,
  USER_ROLES,
  USER_SESSION,
} from 'core/cache/constants/prefix.constant';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { identityConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { CommonStatus } from 'core/enums/common-status.enum';
import { FfmPermission } from 'core/enums/ffm-permissions';
import * as FFM from 'core/enums/permission-ffm.enum';
import { Permission } from 'core/enums/permission.enum';
import { ENUM_PERMISSION_MAPPING, SalePermission } from 'core/enums/sale-permissions';
import { UserStatus } from 'core/enums/user-status.enum';
import { UserType } from 'core/enums/user-type.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import PhoneUtils from 'core/utils/PhoneUtils';
import { Redis } from 'ioredis';
import { compact, concat, difference, isEmpty, isNil, omit, reverse, sortBy, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import { $enum } from 'ts-enum-util';
import { Brackets, getConnection, In, Repository, TreeRepository } from 'typeorm';
import { UpdateUserRoleDto } from '../../../dtos/update-user-roles.dto';
import { Company } from '../../../entities/company.entity';
import { Roles } from '../../../entities/roles.entity';
import { User } from '../../../entities/user.entity';
import { CompaniesService } from '../../companies/services/companies.service';
import { getMenuV2, navigationMenuV2 } from '../constants/menu-v2.constant';
import {
  getMenu,
  navigationMenu,
  navigationMenuClientFFM,
  navigationMenuDefault,
  navigationMenuFFM,
} from '../constants/menu.constant';
import { MenuNavigation } from '../model/navigation.model';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Logs, identityConnection)
    private logsRepo: Repository<Logs>,
    @InjectRepository(Roles, identityConnection)
    private rolesRepository: TreeRepository<Roles>,
    @InjectRepository(User, identityConnection)
    private userRepository: Repository<User>,
    @InjectRepository(Company, identityConnection)
    private companyRepository: Repository<Company>,
    private redisCache: RedisCacheService,
    private companyService: CompaniesService,
    private amqpConnection: AmqpConnection,
    @InjectRedis()
    private redis: Redis,
  ) {}

  async getMenu(user, headers?: Record<string, any>): Promise<MenuNavigation[]> {
    const { id, service, type } = user;
    if (!service) throw new UnauthorizedException();
    const mapRoles = await this.getRoles(id);

    if (!mapRoles) {
      if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
        return navigationMenuDefault;
      }
      return [];
    }

    const data =
      Number(BusinessType[service]) == BusinessType.fulfillment
        ? type == $enum(UserType).getKeyOrDefault(UserType.customer, null)
          ? navigationMenuClientFFM
          : navigationMenuFFM
        : navigationMenu;

    Object.keys(mapRoles).map(key => {
      mapRoles[key] = compact(
        mapRoles[key]?.map(
          it =>
            (Number(BusinessType[service]) == BusinessType.fulfillment
              ? FFM.Permission
              : Permission)?.[it],
        ),
      );
    });

    return getMenu(mapRoles, data);
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-module-permissions',
    queue: 'identity-check-module-permissions',
    errorHandler: rmqErrorsHandler,
  })
  async checkModulePermissions({ userId }: { userId: number }): Promise<number[]> {
    if (!userId) return [];

    const key = `${USER}.${userId}`;
    let data: string = await this.redis.hget(key, NEW_USER_ROLES);
    if (data) {
      try {
        const minified = JSON.parse(data);
        return minified;
      } catch (error) {
        console.log(`parse cached user scopes data err`, error);
      }
      return [];
    }
    const qb = getConnection(identityConnection)
      .createQueryBuilder()
      .from(UserProfile, 'profile')
      .innerJoin(Roles, 'r', `profile.role_id = r.id AND r.status = '${CommonStatus.activated}'`)
      // .leftJoin(`UNNEST(t.new_permission) WITH ORDINALITY`, `a(elem, nr)`, 'TRUE')
      .where('profile.user_id = :userId')
      .andWhere(`profile.status = '${CommonStatus.activated}'`)
      .andWhere(`r.new_permission IS NOT NULL`)
      .select('profile.user_id')
      .addSelect('r.new_permission')
      // .select('BIT_OR(a.elem::BIGINT)', 'permission')
      .setParameters({ userId });
    // .getOne();

    const raw = await qb.getRawMany();
    const result: number[] = raw.reduce((prev: number[], record) => {
      for (let idx = 0; idx < record.new_permission.length; idx++) {
        prev[idx] = (prev[idx] || 0) | record.new_permission[idx];
      }
      return prev;
    }, []);

    data = JSON.stringify(result);
    await this.redis
      .multi()
      .hset(key, NEW_USER_ROLES, data)
      .expire(key, 5 * 60)
      .exec();
    return result;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-roles',
    queue: 'identity-check-roles',
    errorHandler: rmqErrorsHandler,
  })
  async checkRoles({ id, role }) {
    if (!id) {
      return 0;
    }
    const key = `${USER_ROLES}.${id}.${role}`;
    let data = await this.redisCache.get(key);
    if (data) {
      return data;
    }
    data = await this.rolesRepository
      .createQueryBuilder('r')
      .leftJoin('r.users', 'ur')
      .where('ur.id = :id', { id })
      .andWhere('CAST(r.permission as BIGINT) & CAST(:role as BIGINT) > 0', { role })
      .andWhere('r.status = 1')
      .getRawMany();
    await this.redisCache.set(key, data);
    return data;
  }

  async getRoles(id: number): Promise<Record<string, any[]>> {
    const account = await this.userRepository
      .createQueryBuilder('u')
      .leftJoinAndSelect('u.company', 'company')
      .leftJoinAndSelect('company.business', 'business')
      .leftJoinAndSelect('u.role', 'role')
      .where('u.id = :id', { id })
      .andWhere('u.status = :status', { status: UserStatus.active })
      .andWhere('role.status = :status', { status: RoleStatus.activated })
      .getOne();

    if (!account) return undefined;

    const mapRoles: Record<string, any[]> = {
      [account?.companyId]: account?.permissions,
    };
    return mapRoles;
  }

  async getRoleLogs(
    id: number,
    request: Record<string, any>,
    pagination: PaginationOptions,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const company = await this.companyRepository
      .createQueryBuilder('c')
      .leftJoinAndSelect('c.business', 'business')
      .where('c.id = :companyId', { companyId })
      .getOne();

    const permissionData =
      company?.business.type == BusinessType?.sale ? Permission : FFM.Permission;

    const qb = this.logsRepo
      .createQueryBuilder('logs')
      .andWhere(
        new Brackets(subQb => {
          subQb.where(`logs.tableName = 'roles'`);
          subQb.andWhere(`logs.recordId = :recordId `, {
            recordId: id,
          });
        }),
      )
      .orWhere(
        new Brackets(subQb => {
          subQb.where(`logs.parentTableName = 'roles'`);
          subQb.andWhere(`logs.parentId = :parentId `, {
            parentId: id,
          });
        }),
      )
      .orderBy('logs.createdAt', 'ASC');

    // if (pagination) {
    //   qb.take(pagination.limit).skip(pagination.skip);
    // }
    if (companyId) {
      qb.leftJoin(
        Roles,
        'roles',
        '(roles.id::TEXT = logs.recordId OR roles.id::TEXT = logs.parentId)',
      ).andWhere('roles.companyId = :companyId', { companyId });
    }
    const data = await qb.getMany();

    const logUserRoles = [];
    let result = [],
      userIds = [];
    data?.forEach((item: any) => {
      if (item?.action == 'users') {
        userIds = concat(userIds, item?.changes);
        const key = `${item?.creatorId}-${moment(item?.updatedAt).valueOf()}`;

        if (!result[key]) {
          result[key] = {
            ...item,
            time: moment(item?.updatedAt).valueOf(),
            beforeChanges: logUserRoles?.[logUserRoles?.length - 1] ?? '',
          };
        } else {
          result[key] = {
            ...item,
            time: moment(item?.updatedAt).valueOf(),
            beforeChanges: !!result[key]?.beforeChanges
              ? result[key]?.beforeChanges
              : result[key]?.changes,
          };
        }
        logUserRoles.push(item?.changes);
      } else if (item.action === 'status') {
        result.push({
          ...item,
          changes: [$enum(RoleStatus).getKeyOrDefault(Number(item?.changes?.[0]), null)],
          beforeChanges: [
            $enum(RoleStatus).getKeyOrDefault(Number(item?.beforeChanges?.[0]), null),
          ],
          time: moment(item?.updatedAt).valueOf(),
        });
      } else {
        result.push({
          ...item,
          changes:
            item?.action == 'permission'
              ? $enum(permissionData)
                  .getValues()
                  .filter(role => BigInt(role) & BigInt(item?.changes))
                  ?.map(item => $enum(permissionData).getKeyOrDefault(Number(item)))
              : item?.changes,
          beforeChanges:
            item?.action == 'permission'
              ? $enum(permissionData)
                  .getValues()
                  .filter(role => BigInt(role) & BigInt(item?.beforeChanges))
                  ?.map(item => $enum(permissionData).getKeyOrDefault(Number(item)))
              : item?.beforeChanges,
          time: moment(item?.updatedAt).valueOf(),
        });
      }
      userIds.push(item?.creatorId);
    });
    result = sortBy(Object.values(result), ['time']);
    result = reverse(result);

    const users = await this.userRepository.find({
      where: { id: In(uniq(userIds)) },
      select: ['id', 'name', 'displayId'],
    });

    // console.log(users);

    return {
      logs: result,
      users,
    };
  }

  async getPermissionCompany(request) {
    const uId = await this.companyRepository
      .createQueryBuilder('company')
      .where({ id: request?.user?.companyId })
      .getOne();
    if (!uId) throw new NotFoundException(`Công ty không tồn tại`);

    const data = await this.rolesRepository
      .createQueryBuilder('r')
      .leftJoinAndSelect(User, 'u', 'u.role_id = r.id')
      .leftJoinAndSelect('r.company', 'company')
      .leftJoinAndSelect('company.business', 'business')
      .where('u.id = :id', { id: uId?.ownerId })
      .getOne();

    // console.log(data);

    return data;
  }

  async getAllRoles(companyId: number): Promise<Roles[]> {
    const qb = this.rolesRepository
      .createQueryBuilder('r')
      .where('r.company_id = :companyId', { companyId })
      .select(['r.id', 'r.name', 'r.status']);
    return qb.getMany();
  }

  getRolesQueryBuilder(pagination?: PaginationOptions, filter?: RolesFilter) {
    const qb = this.rolesRepository.createQueryBuilder('role');
    const { query, status, companyIds, getRoleEmployee, ancestorId, ids } = filter;

    if (pagination) qb.take(pagination?.limit).skip(pagination?.skip);
    if (query) qb.andWhere('"role"."name"  ILIKE :query', { query: `%${query}%` });
    if (!isEmpty(status)) qb.andWhere('role.status IN (:...status)', { status });
    if (!isEmpty(companyIds)) qb.andWhere('role.companyId IN (:...companyIds)', { companyIds });
    if (getRoleEmployee)
      qb.innerJoin('role.company', 'company').andWhere('role.id <> company.adminRoleId');
    if (!isNil(ancestorId))
      qb.innerJoin(
        'roles_closure',
        'closures',
        'closures.id_descendant = role.id AND closures.id_ancestor = :ancestorId',
        { ancestorId },
      );
    if (!isEmpty(ids)) qb.andWhere('role.id IN (:...ids)', { ids });
    qb.leftJoin(UserProfile, 'p', `p.role_id = role.id AND p.status = ${CommonStatus.activated}`)
      .unScope('p')
      .select('role.id', 'id')
      .addSelect('COUNT (*)', 'profiles_count')
      .groupBy('role.id');
    return qb;
  }

  async find(pagination: PaginationOptions, filter?: RolesFilter): Promise<[Roles[], number]> {
    const cteQb = this.getRolesQueryBuilder(pagination, filter);
    const [roles, count] = await Promise.all([
      this.rolesRepository
        .createQueryBuilder('role')
        .addCommonTableExpression(() => cteQb, 'roles_cte')
        .innerJoin(`roles_cte`, 'roles_cte', 'roles_cte.id = role.id')
        .leftJoinAndSelect('role.parent', 'parent')
        .leftJoinAndSelect('role.users', 'users')
        .leftJoinAndSelect('role.company', 'company')
        .leftJoinAndSelect('company.business', 'business')
        .leftJoinAndMapOne(
          'role.adminRoleOfCompany',
          Company,
          'adminCompany',
          'adminCompany.adminRoleId = role.id',
        )
        .addSelect('roles_cte.profiles_count', 'profiles_count')
        .setParameters(cteQb.getParameters())
        .getMany(),
      cteQb.getCount(),
    ]);

    return [roles, count];
  }

  async findByIds(ids: number[]): Promise<Roles[]> {
    return await this.rolesRepository.findByIds(ids, {
      relations: ['users', 'company', 'company.business'],
    });
  }

  async findById(id: number): Promise<Roles> {
    return await this.rolesRepository.findOne(id, {
      relations: ['users', 'company', 'company.business'],
    });
  }

  async setRole(userId: number, data: UpdateUserRoleDto): Promise<User> {
    const user = await this.userRepository.findOne(userId);
    if (!user) {
      throw new NotFoundException('Tài khoản không tồn tại');
    }
    user.role = await this.findById(data.roleId);
    user.updatedAt = new Date();
    if (data.phone) user.phone = PhoneUtils.format(data.phone);
    await this.userRepository.save(user);
    return user;
  }

  async createRole(body: CreateRoleDto, companyId: number, uid: number): Promise<Roles> {
    const company = await this.companyRepository.findOne(companyId, {
      relations: ['business'],
    });

    const now = new Date();

    const role = plainToInstance(Roles, {
      name: body.name,
      description: body.description,
      status: body?.status,
      dataAccessLevel: body?.dataAccessLevel,
      moduleInCharge: body?.moduleInCharge,
      companyId,
      company,
      createdAt: now,
      updatedAt: now,
      creatorId: uid,
      updatedBy: uid,
    });
    if (company?.business.type == BusinessType?.fulfillment) throw new Error('');
    role.permission = (body.permissions || []).reduce((prev, it) => {
      const checkPer = (company?.business.type == BusinessType?.sale
        ? Permission
        : FFM.Permission)?.[it?.toString()];
      if (!checkPer) return BigInt(prev);
      return BigInt(prev) | BigInt(checkPer);
    }, BigInt(0));

    if (body.parentId) {
      const parent = await this.findById(body.parentId);
      if (!parent) throw new BadRequestException('Parent not found');
      role.parentId = body.parentId;
      role.parent = parent;
    }
    if (isEmpty(body.newPermissions)) {
      // if (!role.parent) throw new BadRequestException(`Parent cannot be empty`);
      role.newPermission = role.parent?.newPermission;
    } else {
      const businessType = company.business.type;
      const enumPerm = businessType === BusinessType.sale ? SalePermission : FfmPermission;
      const businessPerms = company.business.newPermission;

      role.newPermission = Object.keys(body.newPermissions).reduce((prev, key) => {
        const parentPerm = role.parent.newPermission[enumPerm[key]];
        prev[enumPerm[key]] = body.newPermissions[key].reduce((pre, i) => {
          const perm = ENUM_PERMISSION_MAPPING[enumPerm[key]][i];
          if (!perm)
            throw new BadRequestException(
              `newPermission.${key}.${i} is not a valid enum permission`,
            );

          if ((BigInt(parentPerm) & BigInt(perm)) !== BigInt(perm))
            throw new BadRequestException(
              `newPermission.${key}.${i} is outside of parent permissions scope`,
            );

          return BigInt(pre) | BigInt(perm);
        }, BigInt(0));
        return prev;
      }, Array(businessPerms[businessPerms.length - 1] + 1).fill(BigInt(0)));
    }

    // console.log(`role`, role)

    // return role;

    await this.rolesRepository.save(role);
    return role;
  }

  async createRoleV2(body: CreateRoleDto, companyId: number, uid: number): Promise<Roles> {
    const company = await this.companyRepository.findOne(companyId, {
      relations: ['business'],
    });

    const now = new Date();

    const role = plainToInstance(Roles, {
      name: body.name,
      description: body.description,
      status: body?.status,
      dataAccessLevel: body?.dataAccessLevel,
      moduleInCharge: body?.moduleInCharge,
      companyId,
      company,
      createdAt: now,
      updatedAt: now,
      creatorId: uid,
      updatedBy: uid,
    });
    if (company?.business.type == BusinessType?.sale) throw new Error('');
    role.permission = (body.permissions || []).reduce((prev, it) => {
      const checkPer = (company?.business.type == BusinessType?.sale
        ? Permission
        : FFM.Permission)?.[it?.toString()];
      if (!checkPer) return BigInt(prev);
      return BigInt(prev) | BigInt(checkPer);
    }, BigInt(0));

    if (body.parentId) {
      const parent = await this.findById(body.parentId);
      if (!parent) throw new BadRequestException('Parent not found');
      role.parentId = body.parentId;
      role.parent = parent;
    }
    if (isEmpty(body.newPermissions)) {
      // if (!role.parent) throw new BadRequestException(`Parent cannot be empty`);
      role.newPermission = role.parent?.newPermission;
    } else {
      const enumPerm = FfmPermission;
      const businessPerms = company.business.newPermission;

      role.newPermission = Object.keys(body.newPermissions).reduce((prev, key) => {
        const parentPerm = role.parent.newPermission[enumPerm[key]];
        prev[enumPerm[key]] = body.newPermissions[key].reduce((pre, i) => {
          const perm = ENUM_PERMISSION_MAPPING[enumPerm[key]][i];
          if (!perm)
            throw new BadRequestException(
              `newPermission.${key}.${i} is not a valid enum permission`,
            );

          if ((BigInt(parentPerm) & BigInt(perm)) !== BigInt(perm))
            throw new BadRequestException(
              `newPermission.${key}.${i} is outside of parent permissions scope`,
            );

          return BigInt(pre) | BigInt(perm);
        }, BigInt(0));
        return prev;
      }, Array(businessPerms[businessPerms.length - 1] + 1).fill(BigInt(0)));
    }

    // console.log(`role`, role)

    // return role;

    await this.rolesRepository.save(role);
    return role;
  }

  async deleteRole(id: number, request: Record<string, any>) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const result = await queryRunner.manager.softDelete(Roles, { id, companyId });
      await queryRunner.commitTransaction();
      await this.removeRoleCache();
      return result;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async setPermissions(roleId: number, data: UpdateRoleDto): Promise<Roles> {
    const role = await this.rolesRepository.findOne(roleId);
    if (!role) {
      throw new NotFoundException('Nhóm quyền không tồn tại');
    }
    role.name = data.name;
    role.description = data.description;
    role.updatedAt = new Date();
    role.permission = data.permissions.reduce((prev, it) => BigInt(prev) + BigInt(it), BigInt(0));
    await this.rolesRepository.save(role);
    return role;
  }

  async updateRole(
    roleId: number,
    body: UpdateRoleDto,
    companyId: number,
    uid: number,
  ): Promise<Roles> {
    const role = await this.rolesRepository.findOne(roleId, {
      where: { companyId },
      relations: ['company', 'company.business', 'parent'],
    });
    if (!role) {
      throw new NotFoundException('Nhóm quyền không tồn tại');
    }
    if (role?.company?.business.type == BusinessType?.fulfillment) throw new Error('');
    const updateData: Omit<Partial<Roles>, 'permissions' | 'newPermissions'> = omit(body, [
      'userIds',
      'permissions',
      'updatedAt',
    ]);
    if (body.permissions)
      updateData.permission = body.permissions.reduce((prev, it) => {
        const checkPer = (role?.company?.business.type == BusinessType?.sale
          ? Permission
          : FFM.Permission)?.[it?.toString()];
        if (!checkPer) return BigInt(prev);
        return BigInt(prev) + BigInt(checkPer);
      }, BigInt(0));

    if (body.userIds) {
      const users = await this.userRepository
        .createQueryBuilder('u')
        .leftJoin('u.ownsCompany', 'ownsCompany')
        .andWhereInIds(body.userIds)
        .andWhere('ownsCompany.id IS NULL')
        .getMany();

      updateData.users = users?.map((user: any) => ({
        ...user,
        updatedBy: uid,
      }));
    }

    const oldUserIds = body?.users?.map(x => x.id);
    if (isEmpty(body.newPermissions)) {
      // if (!role.parent) throw new BadRequestException(`Parent cannot be empty`);
      updateData.newPermission = role.parent?.newPermission;
    } else {
      const businessType = role.company.business.type;
      const enumPerm = businessType === BusinessType.sale ? SalePermission : FfmPermission;

      updateData.newPermission = Object.keys(body.newPermissions).reduce((prev, key) => {
        const parentPerm = role.parent.newPermission[enumPerm[key]];
        prev[enumPerm[key]] = body.newPermissions[key].reduce((pre, i) => {
          const perm = ENUM_PERMISSION_MAPPING[enumPerm[key]][i];
          if (!perm)
            throw new BadRequestException(
              `newPermission.${key}.${i} is not a valid enum permission`,
            );

          if ((BigInt(parentPerm) & BigInt(perm)) !== BigInt(perm))
            throw new BadRequestException(
              `newPermission.${key}.${i} is outside of parent permissions scope`,
            );

          return BigInt(pre) + BigInt(perm);
        }, BigInt(0));
        return prev;
      }, []);
    }

    updateData.updatedBy = uid;
    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const result = await queryRunner.manager.save(
        plainToInstance(Roles, {
          id: roleId,
          ...updateData,
        }),
      );
      // if(body.userIds.length != oldUserIds.length && result){
      //   await queryRunner.manager.update(User,
      //     {id: In(oldUserIds)}, {updatedBy: uid}
      //   );
      // }
      if (updateData.newPermission) {
        const qb = queryRunner.manager
          .createQueryBuilder(Roles, 'r')
          .innerJoin('roles_closure', 'closure', 'closure.id_ancestor = r.id')
          .where('r.id = :id')
          .select('closure.id_descendant', 'role_id');
        // console.log(`qb`, qb.getQuery());
        await queryRunner.manager
          .createQueryBuilder(Roles, 'r')
          .update()
          .set({
            newPermission: () => `(SELECT ARRAY (
                                    SELECT (COALESCE(arr1[idx], 0) & COALESCE(arr2[idx], 0))
                                    FROM generate_series(1, GREATEST(array_length(arr1, 1), array_length(arr2, 1))) as idx
                                  ) FROM (
                                    SELECT new_permission::BIGINT[] as arr1, :permission::BIGINT[] as arr2
                                  ) sub)`,
          })
          .where(`id IN (${qb.getQuery()})`)
          .setParameters({ id: roleId, permission: updateData.newPermission })
          .execute();
      }
      if (
        !isNil(updateData.status) &&
        updateData.status === RoleStatus.deactivated &&
        updateData.status !== role.status
      ) {
        const subQb = queryRunner.manager
          .createQueryBuilder()
          .from('roles_closure', 'r_closure')
          .where(`r_closure.id_ancestor = ${role.id}`)
          .select('r_closure.id_descendant');
        await queryRunner.manager
          .createQueryBuilder(Roles, 'r')
          .update()
          .set({ status: RoleStatus.deactivated })
          .where(`id IN (${subQb.getQuery()})`)
          .execute();
      }
      await queryRunner.commitTransaction();
      await this.removeRoleCache();
      await this.amqpConnection.publish('identity-service', 'clear-auth-cache', { roleId });

      return plainToInstance(Roles, {
        ...role,
        ...result,
      });
    } catch (e) {
      console.log(`error when updating role`, e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  randomStr(length: number) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
      counter += 1;
    }
    return result;
  }

  async updateRoleV2(
    roleId: number,
    body: UpdateRoleDto,
    companyId: number,
    uid: number,
  ): Promise<Roles> {
    const role = await this.rolesRepository.findOne(roleId, {
      where: { companyId },
      relations: ['company', 'company.business', 'parent', 'users'],
    });
    if (!role) {
      throw new NotFoundException('Nhóm quyền không tồn tại');
    }

    const updateData: Omit<Partial<Roles>, 'permissions' | 'newPermissions'> = omit(body, [
      'userIds',
      'permissions',
      'updatedAt',
    ]);
    if (role?.company?.business.type == BusinessType?.sale) throw new Error('');

    if (body.permissions)
      updateData.permission = body.permissions.reduce((prev, it) => {
        const checkPer = FFM.Permission?.[it?.toString()];
        if (!checkPer) return BigInt(prev);
        return BigInt(prev) + BigInt(checkPer);
      }, BigInt(0));

    if (isEmpty(body.newPermissions)) {
      // if (!role.parent) throw new BadRequestException(`Parent cannot be empty`);
      updateData.newPermission = role.parent?.newPermission;
    } else {
      const enumPerm = FfmPermission;

      updateData.newPermission = Object.keys(body.newPermissions).reduce((prev, key) => {
        const parentPerm = role.parent.newPermission[enumPerm[key]];
        prev[enumPerm[key]] = body.newPermissions[key].reduce((pre, i) => {
          const perm = ENUM_PERMISSION_MAPPING[enumPerm[key]][i];
          if (!perm)
            throw new BadRequestException(
              `newPermission.${key}.${i} is not a valid enum permission`,
            );

          if ((BigInt(parentPerm) & BigInt(perm)) !== BigInt(perm))
            throw new BadRequestException(
              `newPermission.${key}.${i} is outside of parent permissions scope`,
            );

          return BigInt(pre) + BigInt(perm);
        }, BigInt(0));
        return prev;
      }, []);
    }
    const currentIds = role?.users?.map((it: any) => it?.id);

    const clearSession =
      (!isNil(updateData.status) && updateData.status !== role.status) ||
      updateData.permission?.toString() !== role?.permission?.toString() ||
      body.userIds?.length != currentIds?.length;

    if (body.userIds) {
      const users = await this.userRepository
        .createQueryBuilder('u')
        .leftJoin('u.ownsCompany', 'ownsCompany')
        .andWhereInIds(body.userIds)
        .andWhere('ownsCompany.id IS NULL')
        .getMany();

      updateData.users = users?.map((user: any) => ({
        ...user,
        updatedBy: uid,
      }));
    }

    const lookupUids =
      updateData.permission?.toString() !== role?.permission?.toString()
        ? uniq(concat(body.userIds ?? [], currentIds ?? []))
        : uniq(
            concat(
              difference(body.userIds, currentIds), // new user
              difference(currentIds, body.userIds), // remove user
            ),
          );

    updateData.updatedBy = uid;
    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const result = await queryRunner.manager.save(
        plainToInstance(Roles, {
          id: roleId,
          ...updateData,
        }),
      );
      if (updateData.newPermission) {
        const qb = queryRunner.manager
          .createQueryBuilder(Roles, 'r')
          .innerJoin('roles_closure', 'closure', 'closure.id_ancestor = r.id')
          .where('r.id = :id')
          .select('closure.id_descendant', 'role_id');
        // console.log(`qb`, qb.getQuery());
        await queryRunner.manager
          .createQueryBuilder(Roles, 'r')
          .update()
          .set({
            newPermission: () => `(SELECT ARRAY (
                                    SELECT (COALESCE(arr1[idx], 0) & COALESCE(arr2[idx], 0))
                                    FROM generate_series(1, GREATEST(array_length(arr1, 1), array_length(arr2, 1))) as idx
                                  ) FROM (
                                    SELECT new_permission::BIGINT[] as arr1, :permission::BIGINT[] as arr2
                                  ) sub)`,
          })
          .where(`id IN (${qb.getQuery()})`)
          .setParameters({ id: roleId, permission: updateData.newPermission })
          .execute();
      }
      if (
        !isNil(updateData.status) &&
        updateData.status === RoleStatus.deactivated &&
        updateData.status !== role.status
      ) {
        const subQb = queryRunner.manager
          .createQueryBuilder()
          .from('roles_closure', 'r_closure')
          .where(`r_closure.id_ancestor = ${role.id}`)
          .select('r_closure.id_descendant');
        await queryRunner.manager
          .createQueryBuilder(Roles, 'r')
          .update()
          .set({ status: RoleStatus.deactivated })
          .where(`id IN (${subQb.getQuery()})`)
          .execute();
      }
      if (clearSession && lookupUids?.length > 0)
        await queryRunner.manager
          .createQueryBuilder(User, 'u')
          .update()
          .set({ sessionId: this.randomStr(32) })
          .where(`id IN (${lookupUids?.join(',')})`)
          .execute();
      await queryRunner.commitTransaction();
      await this.removeRoleCache();
      await this.amqpConnection.publish('identity-service', 'clear-auth-cache', { roleId });
      if (clearSession && lookupUids?.length > 0)
        for (const userId of lookupUids) {
          const skey = `${USER_SESSION}.${userId}.`;
          await this.redisCache.set(skey, '');
        }
      return plainToInstance(Roles, {
        ...role,
        ...result,
      });
    } catch (e) {
      console.log(`error when updating role`, e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  removeRoleCache() {
    return this.redisCache.delWithPrefix(USER_ROLES);
  }

  async onRoleUpdated({ id, beforeChanges, changes, updatedBy }) {
    // TO DO: Handle role changes
  }

  async getMenuV2({ id, service, type, profiles }: AuthUser): Promise<MenuNavigation[]> {
    if (!service) throw new UnauthorizedException();

    const userPermissions = profiles.reduce((prev: bigint[], record) => {
      for (let idx = 0; idx < record[3].length; idx++) {
        prev[idx] = BigInt(prev[idx] || 0) | BigInt(record[3][idx] || 0);
      }
      return prev;
    }, []);

    // const mapRoles = await this.getRoles(id);

    // return mapRoles

    // if (!mapRoles) {
    //   if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
    //     return navigationMenuDefault;
    //   }
    //   return [];
    // }

    const data =
      Number(BusinessType[service]) == BusinessType.fulfillment
        ? type == $enum(UserType).getKeyOrDefault(UserType.customer, null)
          ? navigationMenuClientFFM
          : navigationMenuFFM
        : navigationMenuV2;

    // Object.keys(mapRoles).map(key => {
    //   mapRoles[key] = compact(
    //     mapRoles[key]?.map(
    //       it =>
    //         (Number(BusinessType[service]) == BusinessType.fulfillment
    //           ? FFM.Permission
    //           : Permission)?.[it],
    //     ),
    //   );
    // });

    return getMenuV2(userPermissions, data);
  }
}
