import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Param,
  ParseArrayPipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UnauthorizedException,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ClientReportDto } from 'apps/identity-api/src/dtos/client-report.dto';
import {
  CompanyRootUserDto,
  UpdateRootUserDto,
} from 'apps/identity-api/src/dtos/company-root-user.dto';
import {
  ChangeActiveStatusDto,
  CreateUserBankDto,
  CreateUserDto,
  LoginUserAppDto,
  LoginUserDto,
  ResetPasswordDto,
  UpdatePasswordAppDto,
  UpdatePasswordDto,
  UpdateUserDto,
  UpdateWarehouseUser,
  VerifyCodeUpdatePasswordAppDto,
  VerifyCodeUserAppDto,
} from 'apps/identity-api/src/dtos/user.dto';
import { FulfillmentApiKey } from 'apps/identity-api/src/entities/fulfillment-api-key.entity';
import { UserBank } from 'apps/identity-api/src/entities/user-bank.entity';
import { UsersLogsFilter } from 'apps/identity-api/src/filters/users-logs.filter';
import { UsersFilter } from 'apps/identity-api/src/filters/users.filter';
import { Auth, MobileAuth, RootAuth } from 'core/auth/decorators/auth/auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { omit } from 'lodash';
import { UpdateResult } from 'typeorm';
import { Logs } from '../../../entities/logs.entity';
import { User } from '../../../entities/user.entity';
import { AuthService } from '../services/auth.service';
import { UsersService } from '../services/users.service';
import { CreateApiKeyDto } from 'apps/identity-api/src/dtos/create-api-key.dto';
import { CreateDeviceDto } from '../../../dtos/device.dto';
import { UpdateMultipleUserDto } from '../../../dtos/update-mutilple-user.dto';
import { SalePermission } from '../../../../../../core/enums/sale-permissions';
import { AccountPermission } from 'core/enums/ffm-permissions/account-permission.enum';
import { Auth as AuthAGSale } from 'core/auth/decorators/auth/new-auth.decorator';
import { BulkCreateAccountsDto } from 'apps/identity-api/src/dtos/bulk-create-accounts.dto';
import { Permission } from 'core/enums/permission-ffm.enum';
import { Roles } from 'core/auth/decorators/auth/roles.decorator';
import { GetDescendantsDto } from 'apps/identity-api/src/dtos/get-descendants.dto';

@Controller('users')
@ApiTags('users')
export class UsersController {
  constructor(private authService: AuthService, private usersService: UsersService) {}

  @Post('/import')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: '',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        name: {
          type: 'string',
          format: 'string',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importEmployee(
    @Request() request,
    @Query() filters,
    @Body() body: [],
    @UploadedFile('file') file,
  ) {
    const { buffer, originalname } = file;
    const data = (buffer as Buffer).toString('utf-8');
    const rows = data.split('\n').map(i => {
      const cells = i.split(':');
      // let customer = new Custo
      return {
        phone: cells[0],
        id: cells[1],
        firstname: cells[2],
        lastname: cells[3],
        gender: cells[4]?.toUpperCase(),
        from: cells[6],
        living: cells[5],
      };
    });
    return rows[0];
    // return ExcelUtils.read(buffer, 0, 'Order ID');
  }

  @Post('bulk-create-accounts/verify-data')
  @AuthAGSale('sale', [SalePermission.accounts, AccountPermission.create])
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async verifyBulkCreateAccountsFileData(
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @UploadedFile('file') file,
  ) {
    if (!file) throw new BadRequestException('file is required');
    const { buffer } = file;

    return await this.usersService.verifyBulkCreateAccountsFileData(buffer, req.user, headers);
  }

  @Post('bulk-create-accounts')
  @AuthAGSale('sale', [SalePermission.accounts, AccountPermission.create])
  async bulkCreateAccounts(@Req() req: Record<string, any>, @Body() body: BulkCreateAccountsDto) {
    return await this.usersService.bulkCreateAccounts(req.user, body);
  }

  @Get('me')
  @Auth()
  async getMe(@Req() request): Promise<User> {
    // console.log(request.user);
    return this.authService.getMe(request.user, request.user.exp);
  }

  @Get('me/generate-qr')
  @Auth()
  async generateQrCode(@Req() request): Promise<{ code: string; secret: string; image: string }> {
    const userId = request.user?.id;
    const companyId = request.user?.companyId;
    return this.usersService.getQrCode(userId, companyId);
  }

  @Post('waiting-approval')
  async waitingApproval(@Body() data: CreateDeviceDto) {
    return this.usersService.waitApproval(data);
  }

  @Post('waiting-qr')
  @Auth()
  async waitingQr(@Req() request, @Body('code') code: string) {
    const userId = request.user?.id;
    return this.usersService.waitForQr(code, userId);
  }

  @Post('approve-device')
  @Auth()
  async approveDevice(@Req() request, @Body('code') code: string) {
    const userId = request.user?.id;
    return this.usersService.approvalDevice(code, userId);
  }

  @Get('api-key')
  @Auth()
  async getApiKey(@Req() request?: Record<string, any>): Promise<FulfillmentApiKey> {
    return this.usersService.getApiKey(request);
  }

  @Get('account-banks')
  @Auth()
  async getAccountBanks(@Request() request): Promise<UserBank[]> {
    return this.usersService.getAccountBanks(request.user.id);
  }

  @Get('otp')
  @Auth()
  async genOtpRequest(@Request() request, @Query() filters): Promise<UserBank[]> {
    return this.usersService.genOtpRequest(request.user.id);
  }

  @Post('account-banks')
  @Auth()
  async createAccountBank(@Request() request, @Body() data: CreateUserBankDto): Promise<UserBank> {
    return this.usersService.createAccountBank(request.user.id, data);
  }

  @Put('account-banks/:id')
  @Auth()
  async updateAccountBank(
    @Request() request,
    @Body() data: CreateUserBankDto,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<UserBank> {
    // console.log(data);
    return this.usersService.updateAccountBank(id, request.user.id, data);
  }

  @Delete('account-banks/:id')
  @Auth()
  async deleteAccountBank(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
    @Query() filters,
  ): Promise<any> {
    return this.usersService.deleteAccountBank(id, filters);
  }

  @Post('forget-password')
  async forgetPassword(@Body() data, @Req() request?: Record<string, any>): Promise<User> {
    return this.usersService.forgetPassword(data, request);
  }

  @Auth()
  @Post('client-report')
  async clientReport(@Body() data: ClientReportDto): Promise<any> {
    return this.usersService.clientReport(data);
  }

  @Post('reset-password')
  async resetPasswordUser(@Body() data: ResetPasswordDto) {
    return this.usersService.resetPassword(data);
  }

  @Post('change-password')
  @Auth()
  async updatePassword(@Request() request, @Body() data: UpdatePasswordDto) {
    return this.usersService.updatePassword(request.user.id, data);
  }

  @Get('count')
  @Auth()
  async countUser(
    @Req() request,
    @Query() filters: UsersFilter,
    @Headers() headers,
    @Query('groupBy', new ParseArrayPipe({ separator: ',', optional: true, items: String }))
    groupBy: string[],
  ): Promise<any[]> {
    return await this.authService.countUser(filters, groupBy, headers, request);
  }

  @Get('logs')
  @Auth()
  async getUsersLogs(
    @Query() filter: UsersLogsFilter,
    @Pagination() pagination: PaginationOptions,
    @Headers() headers?: Record<string, any>,
    @Req() request?: Record<string, any>,
  ): Promise<[Logs[], number]> {
    return this.usersService.getUsersLogs(filter, pagination, headers, request);
  }

  @Get(':id/logs')
  @Auth()
  async getUserLogs(
    @Param('id', ParseIntPipe) userId,
    @Pagination() pagination: PaginationOptions,
    @Headers() headers?: Record<string, any>,
    @Req() request?: Record<string, any>,
  ): Promise<[Logs[], number]> {
    return this.usersService.getUsersLogs({ userIds: [userId] }, pagination, headers, request);
  }

  @Get('sale')
  @Auth()
  async getUsersSale(
    @Pagination() pagination: PaginationOptions,
    @Query() filters?: UsersFilter,
    @Headers() headers?: Record<string, any>,
    @Req() request?: Record<string, any>,
  ) {
    return await this.authService.findV2(filters, pagination, request);
  }

  @Get()
  @Auth()
  async getUsers(
    @Pagination() pagination: PaginationOptions,
    @Query() filters?: UsersFilter,
    @Headers() headers?: Record<string, any>,
    @Req() request?: Record<string, any>,
  ) {
    if (filters.getAllName)
      return await this.authService.getAllName(
        filters,
        pagination,
        headers,
        request,
      );
    return await this.authService.find(filters, pagination, headers, request);
  }

  @Get('/internal')
  async getUsersFromOrderApi(
    @Pagination() pagination: PaginationOptions,
    @Query() filters?: UsersFilter,
    @Headers() headers?: Record<string, any>,
    @Req() request?: Record<string, any>,
  ) {
    if (!headers['identity-api-key']) {
      throw new BadRequestException('identity api key is required');
    }
    if (headers['identity-api-key'] !== process.env.IDENTITY_API_KEY) {
      throw new UnauthorizedException('identity api key is not right');
    }
    return await this.authService.find(filters, pagination, headers, request);
  }

  @Post('')
  async addUser(@Body() body: CreateUserDto, @Req() request?: Record<string, any>): Promise<User> {
    body = omit(body, ['status']);
    return this.authService.registerUser(body, request);
  }

  @Post('create')
  @Auth()
  async createUser(
    @Body() body: CreateUserDto,
    @Req() request?: Record<string, any>,
  ): Promise<User> {
    return this.authService.createUser(body, request);
  }

  @Post('api-key')
  @Auth()
  async createApiKey(@Req() request?: Record<string, any>): Promise<FulfillmentApiKey> {
    return this.usersService.createApiKey(request);
  }

  @Put('api-key/update')
  @Auth()
  async updateApiKey(
    @Body() data: CreateApiKeyDto,
    @Req() request?: Record<string, any>,
  ): Promise<FulfillmentApiKey> {
    return this.usersService.updateApiKey(data, request);
  }

  @Post('company-root')
  @RootAuth()
  async createCompanyRoot(
    @Body() data: CompanyRootUserDto,
    @Req() request?: Record<string, any>,
  ): Promise<User> {
    return this.authService.createCompanyRoot(data, request.user.id);
  }

  @Put('company-root/:id')
  @RootAuth()
  async updateCompanyRoot(
    @Param('id', ParseIntPipe) userId,
    @Body() data: UpdateRootUserDto,
    @Req() request?: Record<string, any>,
  ): Promise<User> {
    return this.authService.editCompanyRoot(userId, data, request);
  }

  @Post('login')
  async login(@Body() body: LoginUserDto, @Req() request): Promise<User> {
    return this.authService.login(body, request);
  }

  @Put('me')
  @Auth()
  async updateUser(@Body() body: UpdateUserDto, @Req() request): Promise<User> {
    const data = omit(body, ['status']);
    return this.authService.update(data, request?.user?.id, request?.user?.id, request?.user);
  }

  @Put(':id')
  // @Auth(Role.admin, Role.user)
  @Auth()
  async updateUserById(
    @Param('id', ParseIntPipe) userId,
    @Body() data: UpdateUserDto,
    @Req() request,
  ): Promise<User> {
    return this.authService.update(data, userId, request?.user?.id, request?.user);
  }

  @Put('update/multiples')
  @Auth()
  async updateMultipleUsers(@Body() data: UpdateMultipleUserDto, @Req() request) {
    return this.usersService.updateMultipleUsers(data, request?.user?.id);
  }

  @Put('/update/:id')
  @Auth(Permission.manageUsers)
  async updateUserFFM(
    @Param('id', ParseIntPipe) userId,
    @Body() data: UpdateUserDto,
    @Req() request,
  ): Promise<User> {
    return this.authService.update(data, userId, request?.user?.id, request?.user);
  }

  @Post('update-warehouse')
  @Auth()
  async updateWarehouseUser(@Body() data: UpdateWarehouseUser, @Req() request): Promise<User[]> {
    return this.authService.updateWarehouse(data, request?.user?.id);
  }

  @Delete(':id')
  @Auth()
  async deleteUser(@Param() param): Promise<UpdateResult> {
    return this.authService.delete(param.id);
  }

  @Get('ffm/:id')
  @Auth()
  async getUserFfm(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Record<string, any>,
  ): Promise<User> {
    return await this.authService.getUser(id, req, true);
  }

  @Get('descendants')
  @Auth()
  async getDescendants(@Req() request, @Query() query: GetDescendantsDto) {
    return this.usersService.getDescendants(request?.user?.id, query);
  }

  @Get(':id')
  @Auth()
  async getUser(@Param('id', ParseIntPipe) id: number): Promise<User> {
    return await this.authService.getUser(id);
  }

  @Get('/reset-password/:email/:password')
  async resetPassword(@Param() params) {
    return await this.authService.resetPassword(params);
  }

  @Put('change-active/:id')
  @Auth()
  async changeActiveStatus(
    @Param('id', ParseIntPipe) userId,
    @Body() data: ChangeActiveStatusDto,
    @Req() request,
  ): Promise<User> {
    return this.usersService.changeActiveStatus(data, userId, request?.user?.id);
  }

  @Put('fetch/data-category-client')
  @Auth()
  async fetchCategory(): // @Param('id', ParseIntPipe) userId,
  // @Body() data: ChangeActiveStatusDto,
  // @Req() request,
  Promise<any> {
    return this.usersService.fetchCategory();
  }

  // API authen for mobile
  @Get('check/descendant/:id')
  @Auth()
  async checkDescendant(@Param('id', ParseIntPipe) userId, @Req() request) {
    return this.usersService.checkDescendant(userId, request?.user?.id);
  }

  // API authen for mobile
  @Post('login/mobile')
  async loginMobile(@Body() body: LoginUserAppDto) {
    return this.usersService.loginMobile(body);
  }

  @Post('login/mobile/resend-code')
  async resendCodeMobile(@Body() body: LoginUserAppDto) {
    return this.usersService.resendCodeMobile(body);
  }

  @Post('login/mobile/code')
  async veriryMobileCode(@Body() body: VerifyCodeUserAppDto) {
    return this.usersService.verifyMobileCode(body);
  }

  @MobileAuth()
  @Roles('mobile')
  @Get('business/list')
  async getListBusiness(@Req() request) {
    return this.usersService.getListBusiness(request?.user);
  }

  @MobileAuth()
  @Roles('mobile')
  @Post('businesses/selected/:id')
  async selectBusiness(@Param('id', ParseIntPipe) id: number, @Req() request) {
    return this.usersService.selectBusiness(id, request?.user);
  }

  @Put('mobile/change-active-status')
  @Auth()
  async changeActiveStatusMobile(
    @Body() data: ChangeActiveStatusDto,
    @Req() request,
  ): Promise<User> {
    return this.usersService.changeActiveStatusMobile(data, request?.user?.id);
  }

  @Post('mobile/change-password/get-link')
  @Auth()
  async getLinkChangePasswordMobile(@Req() request) {
    return this.usersService.getLinkChangePasswordMobile(request?.user?.id);
  }

  @Post('mobile/change-password/verify-code')
  @Auth()
  async verifyCodeChangePassword(@Body() data: VerifyCodeUpdatePasswordAppDto, @Req() request) {
    return this.usersService.verifyCodeChangePassword(data, request?.user?.id);
  }

  @Post('mobile/change-password')
  @Auth()
  async changePasswordMobile(@Body() data: UpdatePasswordAppDto, @Req() request) {
    return this.usersService.changePasswordMobile(data, request?.user?.id);
  }

  @Post('mobile/deactivate-account')
  @Auth()
  async deactivateAccount(@Req() request) {
    return this.usersService.deactivateAccount(request?.user?.id);
  }

  @Get('mobile/delete/user/:email')
  @Auth()
  async deleteAccount(@Param('email') email: string) {
    return this.usersService.deactivateAccountByEmail(email);
  }
}
