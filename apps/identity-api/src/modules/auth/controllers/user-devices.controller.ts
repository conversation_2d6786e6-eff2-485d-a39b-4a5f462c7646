import {
  Get,
  Param,
  Put,
  Body,
  Post,
  Query,
  Request,
  Controller,
  Delete,
  Req,
} from '@nestjs/common';
import { MakeCallDto, CreateDeviceDto } from 'apps/identity-api/src/dtos/device.dto';
import { UpdateUserDeviceDto } from 'apps/identity-api/src/dtos/update-user-device.dto';
import { UserDevicesFilter } from 'apps/identity-api/src/filters/user-devices.filter';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { UsersService } from '../services/users.service';
import { SalePermission } from 'core/enums/sale-permissions';
import { CallCenterPermission } from 'core/enums/sale-permissions/callcenter-permission.enum';
import { ApiTags } from '@nestjs/swagger';

@Controller('users')
@ApiTags('user-devices')
export class UserDevicesController {
  constructor(private usersService: UsersService) {}
  @Put('devices/:id/ping')
  @Auth()
  async pingDevice(@Param('id') id: string, @Request() req, @Body() body: UpdateUserDeviceDto) {
    return this.usersService.pingDevice(req.user.id, id, body);
  }

  @Put('devices/:id/call')
  @Auth()
  async callDevice(@Param('id') id: string, @Body() body: MakeCallDto, @Request() req) {
    return this.usersService.callDevice(req.user.id, id, body);
  }

  @Post('devices')
  async addDevices(@Body() data: CreateDeviceDto) {
    return this.usersService.addDevice(data);
  }
}
