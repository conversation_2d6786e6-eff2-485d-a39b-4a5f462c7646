import { Expose } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { Permission } from 'core/enums/permission.enum';
import * as FFM from 'core/enums/permission-ffm.enum';
export class MenuNavigation {
  // @Expose()
  // @EnumTransform(Permission | FFM?.Permission)
  authority?: any[];

  @Expose()
  children?: MenuNavigation[];

  @Expose()
  hideChildrenInMenu?: boolean;

  @Expose()
  hideInMenu?: boolean;

  @Expose()
  icon?: string;

  @Expose()
  locale?: string;

  @Expose()
  includesGroup?: boolean;

  @Expose()
  group?: string;

  @Expose()
  name?: string;

  @Expose()
  translateKey?: string;

  @Expose()
  path?: string;

  @Expose()
  exact?: boolean;
  
  @Expose()
  title?: string;

  hideIfEmptyChildren?: boolean;
  
  // @Expose()
  authorized?: string[];

  [key: string]: any;
}
