import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { identityConnection } from 'core/constants/database-connection.constant';
import { Country } from '../../entities/country.entity';
import { CountriesController } from './controllers/countries.controller';
import { CountryService } from './services/country.service';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { CountrySubscriber } from './subscribers/country.subscriber';
import { Language } from '../../entities/language.entity';
import { LanguagesController } from './controllers/languages.controller';

@Module({
  imports: [
    RabbitMQModule.externallyConfigured(RabbitMQModule, 0),
    TypeOrmModule.forFeature([Country, Language], identityConnection),
  ],
  controllers: [CountriesController, LanguagesController],
  providers: [CountryService, CountrySubscriber],
})
export class CountriesModule {}
