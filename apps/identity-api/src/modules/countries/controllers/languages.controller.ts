import {
  BadRequestException,
  Body,
  CacheTTL,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { identityConnection } from '../../../../../../core/constants/database-connection.constant';
import { FindCondition, Repository } from 'typeorm';
import { Language } from '../../../entities/language.entity';
import { CommonStatus } from '../../../../../../core/enums/common-status.enum';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { reduce } from 'lodash';
import { GlobalCache } from '../../../../../../core/decorators/global-cache/global-cache.decorator';
import { RedisCacheService } from '../../../../../../core/cache/services/redisCache.service';
import { DisableCache } from '../../../../../../core/decorators/disable-cache/disable-cache.decorator';

const handleKey = (oldItem, newItem, overrideType = false) => {
  if (!oldItem) {
    return newItem;
  }
  for (const key of Object.keys(newItem)) {
    const el = newItem[key];
    const oldEl = oldItem[key];
    if (typeof el !== 'object') {
      if (!oldEl) {
        oldItem[key] = el;
      }
    } else {
      if (oldEl && typeof oldEl !== 'object') {
        if (!overrideType) {
          oldItem[key] = oldEl;
        } else {
          oldItem[key] = handleKey({}, el);
        }
      } else
        oldItem[key] = handleKey(oldEl, el);
    }
  }
  return oldItem;
};

@Controller('languages')
@ApiTags('languages')
export class LanguagesController {
  constructor(
    @InjectRepository(Language, identityConnection)
    private languageRepository: Repository<Language>,
    private redisService: RedisCacheService,
  ) {
  }

  @Get('')
  @Auth()
  @DisableCache()
  async getLanguages(
    @Query('locale') locale: string,
    @Query('namespace') namespace: string,
  ): Promise<Record<string, any>> {
    const condition: FindCondition<Language> = {
      status: CommonStatus.activated,
    };
    if (locale) {
      condition.code = locale;
    }
    if (namespace) {
      condition.namespace = namespace;
    }
    return await this.languageRepository.find(condition);
  }

  @Get(':locale/:namespace')
  @CacheTTL(3600)
  @GlobalCache()
  async getCountries(
    @Param('locale') locale: string,
    @Param('namespace') namespace: string,
  ): Promise<Record<string, any>> {
    return (await this.languageRepository.findOne({
      code: locale,
      namespace,
      status: CommonStatus.activated,
    }))?.data;
  }

  @Post(':language/import')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileFieldsInterceptor([
    { name: 'files', maxCount: 100 },
  ]))
  @Auth()
  async importLanguage(
    @UploadedFiles() { files },
    @Param('language') language: string,
  ) {
    const saveData: Language[] = [];
    for (const file of files) {
      const { buffer, originalname } = file;
      const namespace = originalname.replace('.json', '');
      console.log('namespace', namespace, language);
      saveData.push({
        data: JSON.parse(buffer.toString()),
        namespace,
        code: language,
      } as Language);
    }
    const oldData = await this.languageRepository.find({
      where: saveData.map(i => ({
        namespace: i.namespace,
        code: i.code,
      })),
    });
    const oldDataGroup = reduce(oldData, (prev, item) => {
      prev[`${item.code}_${item.namespace}`] = item.data;
      return prev;
    }, {} as Record<string, Language>);
    const items = [];
    for (const item of saveData) {
      const old = oldDataGroup[`${item.code}_${item.namespace}`];
      const update = handleKey(old, item.data);
      items.push({ ...item, data: update });
    }
    const res = await this.languageRepository.save(items);
    await this.redisService.delWithPrefix(`/languages/`);
    return res;
  }

  @Post(':locale/:namespace')
  @Auth()
  async updateLanguages(
    @Param('locale') locale: string,
    @Param('namespace') namespace: string,
    @Body() data: Record<string, any>,
  ): Promise<Record<string, any>> {
    if (typeof data !== 'object') {
      throw new BadRequestException('Dữ liệu ngôn ngữ không hợp lệ');
    }
    const oldData = await this.languageRepository.findOne({
      where: {
        namespace,
        code: locale,
      },
    });
    if (oldData) {
      data = handleKey(data, oldData.data, true);
    }
    const updated = (await this.languageRepository.save({
      code: locale,
      namespace,
      data,
    }));
    await this.redisService.delWithPrefix(`/languages/${locale}/${namespace}`);
    return updated;
  }

}
