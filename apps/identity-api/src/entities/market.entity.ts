import { Expose } from 'class-transformer';
import { IntegerIdEntity } from 'core/entities/base/integer-id-entity.entity';
import {
  Column,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  Unique,
} from 'typeorm';
import { Country } from './country.entity';
import { FulfillmentPartnerClient } from './fulfillment-partner-client.entity';
import { FulfillmentPartner } from './fulfillment-partner.entity';
import { Project } from './projects.entity';
import { Roles } from './roles.entity';
import { Scope } from 'core/decorators/typeorm-scope.decorator';
import { isEmpty, uniq } from 'lodash';

@Scope<Market>([
  (qb, alias, user) => {
    if (!user?.profiles) return qb;

    const scopeCountryIds = uniq(user.profiles.flatMap(p => p[4].map(it => it[0])));

    const leftJoin = qb.expressionMap.joinAttributes.find(
      j => j.direction === 'LEFT' && j.alias.name === alias,
    );
    if (leftJoin) {
      if (!isEmpty(scopeCountryIds) && !scopeCountryIds.includes(null))
        leftJoin.condition = String(leftJoin.condition ? leftJoin.condition + ' AND ' : '').concat(
          `${alias}.countryId IN (${scopeCountryIds.join(',')})`,
        );
      return qb;
    }

    if (scopeCountryIds && !scopeCountryIds.includes(null)) {
      if (isEmpty(scopeCountryIds)) qb.andWhere('FALSE');
      else qb.andWhere(`${alias}.countryId IN (:...scopeCountryIds)`, { scopeCountryIds });
    }

    return qb.andWhere(`${alias}.company_id = :companyId`, { companyId: user.companyId });
  },
])
@Entity({
  name: 'markets',
  database: process.env.DATABASE_IDENTITY,
})
@Unique('UQ_COMPANY_PROJECT_COUNTRY', ['companyId', 'projectId', 'countryId'])
export class Market extends IntegerIdEntity {
  @Column({
    name: 'project_id',
    type: 'int',
  })
  @Expose()
  projectId: number;

  @ManyToOne(
    () => Project,
    p => p.markets,
    { nullable: true },
  )
  @JoinColumn({ name: 'project_id' })
  project: Project;

  @Column({
    name: 'country_id',
    type: 'int',
  })
  @Expose()
  countryId: number;

  @Column({
    name: 'company_id',
    type: 'int',
  })
  @Expose()
  companyId: number;

  @DeleteDateColumn({
    name: 'deleted_at',
  })
  deletedAt?: Date;

  @ManyToOne(
    () => Country,
    p => p.markets,
    { nullable: true },
  )
  @JoinColumn([
    { name: 'country_id', referencedColumnName: 'id' },
    { name: 'company_id', referencedColumnName: 'companyId' },
  ])
  @Expose()
  country: Country;

  @Column({
    name: 'fulfillment_partner_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  fulfillmentPartnerId: number;

  @ManyToOne(() => FulfillmentPartner)
  @JoinColumn([
    { name: 'company_id', referencedColumnName: 'companyId' },
    {
      name: 'fulfillment_partner_id',
      referencedColumnName: 'fulfillmentPartnerId',
    },
  ])
  @Expose()
  fulfillmentPartner?: FulfillmentPartner;

  @OneToMany(
    () => Roles,
    role => role.market,
    { nullable: true },
  )
  roles?: Roles[];

  @Column({
    name: 'fulfillment_partner_client_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  fulfillmentPartnerClientId?: number;

  @ManyToOne(() => FulfillmentPartnerClient)
  @JoinColumn({ name: 'fulfillment_partner_client_id' })
  @Expose()
  fulfillmentPartnerClient?: FulfillmentPartnerClient;

  @Column({
    name: 'is_activate',
    type: 'boolean',
  })
  @Expose()
  isActivate: boolean;
}
