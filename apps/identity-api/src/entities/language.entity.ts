import { Expose } from 'class-transformer';
import { Column, Entity, PrimaryColumn } from 'typeorm';
import { NonEmptyTransform } from '../../../../core/decorators/non-empty-transform/non-empty-transform.decorator';
import { CommonStatus } from '../../../../core/enums/common-status.enum';

@Entity({
  name: 'languages'
})
export class Language {
  @PrimaryColumn({
    name: 'code',
    type: 'varchar',
    length: 4,
  })
  @Expose()
  @NonEmptyTransform()
  code?: string;

  @PrimaryColumn({
    name: 'namespace',
    type: 'varchar',
    length: 50,
  })
  @Expose()
  @NonEmptyTransform()
  namespace?: string;

  @Column({
    name: 'data',
    type: 'json',
    default: {},
  })
  @Expose()
  @NonEmptyTransform()
  data?: Record<string, any>;

  @Column({
    name: 'status',
    type: 'smallint',
    default: CommonStatus.activated,
  })
  @NonEmptyTransform()
  status?: CommonStatus;
}
