import { UserClientType } from 'apps/identity-api/src/enums/user-client-type.enum';
import { Expose } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { NewPermissionsTransform } from 'core/decorators/new-permissions-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { IntegerIdEntity } from 'core/entities/base/integer-id-entity.entity';
import { FFM_ENUM_PERMISSION_MAPPING } from 'core/enums/ffm-permissions';
import * as FFM from 'core/enums/permission-ffm.enum';
import { Permission } from 'core/enums/permission.enum';
import { ENUM_PERMISSION_MAPPING } from 'core/enums/sale-permissions';
import { UserStatus } from 'core/enums/user-status.enum';
import { UserType } from 'core/enums/user-type.enum';
import { compact, reduce, union } from 'lodash';
import { $enum } from 'ts-enum-util';
import {
  Column,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  Unique,
} from 'typeorm';
import { BusinessType } from '../enums/business-type.enum';
import { Company } from './company.entity';
import { Project } from './projects.entity';
import { Roles } from './roles.entity';
import { Team } from './team.entity';
import { UserBank } from './user-bank.entity';
import { UserProfile } from './user-profile.entity';
import { UserScope } from './user-scope.entity';
import { DateTransform } from '../../../../core/decorators/date-transform/date-transform.decorator';

@Entity({
  name: 'users',
  database: process.env.DATABASE_IDENTITY,
})
@Unique('UQ_EMAIL_COMPANY', ['email', 'companyId'])
export class User extends IntegerIdEntity {
  @Column({
    name: 'name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  name?: string;

  @Column({
    name: 'shop_name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  shopName?: string;

  @Column({
    name: 'email',
    type: 'varchar',
    nullable: true,
  })
  // @Index({ unique: true, where: 'email IS NOT NULL' })
  @Expose()
  @NonEmptyTransform()
  email?: string;

  @Column({
    name: 'password',
    type: 'varchar',
    nullable: true,
  })
  @NonEmptyTransform()
  password?: string;

  @Column({
    name: 'role_code',
    type: 'varchar',
    nullable: true,
  })
  @NonEmptyTransform()
  roleCode?: string;

  @Column({
    name: 'phone',
    type: 'varchar',
    nullable: true,
    length: 25,
  })
  @Expose()
  @NonEmptyTransform()
  phone?: string;

  @Column({
    name: 'avatar',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  avatar?: string;

  @Column({
    name: 'role_id',
    type: 'int',
    nullable: true,
  })
  roleId?: number;

  @ManyToOne(
    () => Roles,
    role => role.users,
  )
  @JoinColumn({ name: 'role_id' })
  @Expose()
  @NonEmptyTransform()
  role?: Roles;

  @Expose()
  // @EnumTransform({ ...Permission, ...FFM.Permission })
  get permissions(): any[] {
    if (!this.role?.permission) {
      return undefined;
    }
    const _enum =
      this.company?.business?.type == BusinessType.fulfillment ? FFM.Permission : Permission;

    const data = $enum(_enum)
      .getValues()
      ?.map(it => {
        if (BigInt(it) & BigInt(this.role?.permission))
          return $enum(_enum).getKeyOrDefault(Number(it), null);
      });
    return union(compact(data));
  }

  get salePermissions(): string[] {
    if (!this.company || !this.profiles) return undefined;

    const reduced = reduce(
      this.profiles,
      (prev, profile) => {
        if (!profile.role?.newPermission) return prev;
        for (const [index, perm] of Object.entries(profile.role?.newPermission)) {
          if (!prev[index]) prev[index] = BigInt(0);
          prev[index] = prev[index] | BigInt(perm || 0);
        }
        return prev;
      },
      [],
    );
    return reduced;
  }

  @Expose()
  @NonEmptyTransform()
  @NewPermissionsTransform()
  get newPermissions(): string[][] {
    if (!this.company || !this.profiles) return undefined;

    const reduced = reduce(
      this.profiles,
      (prev, profile) => {
        if (!profile.role?.newPermission) return prev;
        for (const [index, perm] of Object.entries(profile.role?.newPermission)) {
          if (!prev[index]) prev[index] = BigInt(0);
          prev[index] = prev[index] | BigInt(perm || 0);
        }
        return prev;
      },
      [],
    );

    const data = reduced.map((perm, index) => {
      const _enum =
        this.company?.business?.type == BusinessType.sale
          ? ENUM_PERMISSION_MAPPING[index]
          : FFM_ENUM_PERMISSION_MAPPING[index];

      // console.log(`_enum`, _enum)

      return $enum(_enum)
        .getValues()
        ?.reduce((prev, it) => {
          if (!perm) return prev;
          if (BigInt(it) & BigInt(perm)) prev.push(_enum[it]);
          return prev;
        }, []);
    });
    return data;
  }

  @Expose()
  token?: string;

  @DeleteDateColumn({
    name: 'deleted_at',
  })
  deletedAt?: Date;

  @Column({
    name: 'status',
    type: 'smallint',
    default: UserStatus.pending,
  })
  @Expose()
  @EnumTransform(UserStatus)
  status: UserStatus;

  @Column({
    name: 'pancake_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  @Expose()
  @Index({ unique: true, where: 'pancake_id IS NOT NULL' })
  pancakeId?: string;

  @Column({
    name: 'type',
    type: 'smallint',
    nullable: true,
    default: UserType.employee,
  })
  @Expose()
  @EnumTransform(UserType)
  type?: UserType;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  companyId?: number;

  @ManyToOne(
    () => Company,
    c => c.users,
    { nullable: true },
  )
  @JoinColumn({ name: 'company_id' })
  @Expose()
  @NonEmptyTransform()
  company?: Company;

  @ManyToMany(
    () => Team,
    team => team.users,
  )
  @JoinTable({
    name: 'users_teams',
    joinColumn: {
      name: 'user_id',
    },
    inverseJoinColumn: {
      name: 'team_id',
    },
  })
  @Expose()
  @NonEmptyTransform()
  teams: Team[];

  @ManyToMany(
    () => Project,
    project => project.scopes,
  )
  @JoinTable({
    name: 'users_projects',
    joinColumn: {
      name: 'user_id',
    },
    inverseJoinColumn: {
      name: 'project_id',
    },
  })
  @Expose()
  @NonEmptyTransform()
  projects: Project[];

  @OneToOne(
    () => Company,
    it => it.owner,
    { nullable: true },
  )
  @Expose()
  @NonEmptyTransform()
  ownsCompany?: Company;

  @Expose()
  get isLinked(): boolean {
    return this.pancakeId ? !!this.password : true;
  }

  @Expose()
  @NonEmptyTransform()
  get isAdmin(): boolean | undefined {
    return this.ownsCompany ? Boolean(this.ownsCompany) : undefined;
  }

  @OneToMany(
    () => UserScope,
    s => s.user,
    { nullable: true, cascade: true },
  )
  @Expose()
  scopes?: UserScope[];

  @Column('varchar', {
    name: 'countries',
    nullable: true,
    array: true,
  })
  @Expose()
  @NonEmptyTransform()
  countries: string[] | null;

  @Column('varchar', {
    name: 'warehouses',
    nullable: true,
    array: true,
  })
  @Expose()
  @NonEmptyTransform()
  warehouses: string[] | null;

  @Column('character varying', {
    name: 'display_id',
    nullable: true,
    length: 255,
    unique: true,
  })
  @Expose()
  @NonEmptyTransform()
  displayId: string | null;

  @Column('varchar', {
    name: 'industry',
    nullable: true,
    array: true,
  })
  @Expose()
  @NonEmptyTransform()
  industry: string[] | null;

  @Column('varchar', {
    name: 'saleChannel',
    nullable: true,
    array: true,
  })
  @Expose()
  @NonEmptyTransform()
  saleChannel: string[] | null;

  @Column({
    name: 'updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  updatedBy?: number;

  @OneToMany(
    () => UserBank,
    ud => ud.user,
    { nullable: true, cascade: true },
  )
  @Expose()
  @NonEmptyTransform()
  banks?: UserBank[];

  @Column({
    name: 'reset',
    type: 'jsonb',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  reset?: object | null;

  @Column({
    name: 'reg_position',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  regPosition?: string;

  @Column({
    name: 'address',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  address?: string;

  @Column({
    name: 'client_type',
    type: 'smallint',
    nullable: true,
  })
  @Expose()
  @EnumTransform(UserClientType)
  clientType?: UserClientType;

  @Column({
    name: 'is_online',
    type: 'boolean',
    nullable: true,
    default: false,
  })
  @Expose()
  isOnline?: boolean;

  @Column('integer', {
    name: 'category_ids',
    nullable: true,
    array: true,
  })
  @Expose()
  categoryIds?: number[] | null;

  @OneToMany(
    () => UserProfile,
    it => it.user,
  )
  @Expose()
  @NonEmptyTransform()
  profiles?: UserProfile[];

  @Column({
    name: 'last_updated_password',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  lastUpdatedPassword?: Date;

  @Column({
    name: 'note',
    type: 'text',
    nullable: true,
  })
  @Expose()
  note?: string;

  @Column({
    name: 'session_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  sessionId?: string;

  @Column({
    name: 'code',
    type: 'varchar',
    length: 6,
    nullable: true,
  })
  @Expose()
  code?: string; // for login app

  @Column({
    name: 'last_time_generate_code',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  lastTimeGenerateCode?: Date; // for login app

  @Column({
    name: 'last_time_generate_change_password_code',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  lastTimeGenerateChangePasswordCode?: Date; // for app

  @Expose()
  projectIds?: number[];
}
