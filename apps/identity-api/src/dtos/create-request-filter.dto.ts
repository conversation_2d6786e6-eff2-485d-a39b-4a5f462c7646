import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export class CreateRequestFilterDto {
  @ApiProperty({
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    required: true,
  })
  @IsNotEmpty()
  module: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  filter: Record<string, any>;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  userId?: number;
}

export class UpdateRequestFilterDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  name?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  filter?: Record<string, any>;
}
