import { ApiProperty } from '@nestjs/swagger';
import {
  IsNumber,
  IsOptional,
  ValidateIf
} from 'class-validator';
import { isNil } from 'lodash';

export class CreateMarketDto {
  @ApiProperty()
  @IsNumber()
  projectId: number;

  @ApiProperty()
  @IsNumber()
  countryId: number;

  @ApiProperty({ type: () => Number, required: false })
  @IsOptional()
  @IsNumber()
  fulfillmentPartnerId?: number
}

export class UpdateMarketDto {
  @ApiProperty({ type: () => Number, required: false })
  @IsOptional()
  @IsNumber()
  fulfillmentPartnerId: number;

  @ApiProperty({ type: () => Boolean, required: false })
  @IsOptional()
  isActivate?: boolean;
}

export class MarketDto {
  @ApiProperty()
  @IsNumber()
  countryId: number;

  @ApiProperty({ type: () => Number, required: false })
  @ValidateIf(obj => !isNil(obj.fulfillmentPartnerClientId))
  @IsNumber()
  fulfillmentPartnerId?: number;

  @ApiProperty({ type: () => Number, required: false })
  @ValidateIf(obj => !isNil(obj.fulfillmentPartnerId))
  @IsNumber()
  fulfillmentPartnerClientId?: number;

  @ApiProperty({ type: () => Boolean, required: false })
  @IsOptional()
  isActivate?: boolean;
}