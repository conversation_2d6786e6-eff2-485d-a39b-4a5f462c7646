import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  MaxLength,
  MinLength,
  Validate,
} from 'class-validator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { Permission } from 'core/enums/permission.enum';
import { RoleStatus } from '../enums/role-status.enum';
import * as FFM from 'core/enums/permission-ffm.enum';
import { Transform } from 'class-transformer';
import { BusinessType } from '../enums/business-type.enum';
import { CommonStatus } from 'core/enums/common-status.enum';
import { DataAccessLevel } from '../enums/data-access-level.enum';
import { ModuleInCharge } from '../enums/module-in-charge.enum';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { ContextAwareDto } from 'core/dtos/body-context.dto';
import { DtoAuth } from 'core/decorators/validate-decorators/dto-auth.decorator';
import { SalePermission } from 'core/enums/sale-permissions';
import { AccountPermission } from 'core/enums/ffm-permissions/account-permission.enum';
import { RoleType } from 'core/enums/role-type.enum';
import { User } from '../entities/user.entity';

export class CreateRoleDto extends ContextAwareDto {
  @ApiProperty()
  @IsNotEmpty()
  @MinLength(2, {
    message: 'Tên nhóm quyền không được ít hơn 2 ký tự',
  })
  @MaxLength(100, {
    message: 'Tên nhóm quyền không được nhiều hơn 100 ký tự',
  })
  // @DtoAuth('sale', [[SalePermission.roles, RoleType.create]])
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  description?: string;

  @ApiProperty({
    required: false,
    enum: RoleStatus,
  })
  @IsOptional()
  @IsEnum(RoleStatus)
  @EnumTransform(RoleStatus)
  status?: RoleStatus;

  @ApiProperty({
    enum: { ...Permission, ...FFM.Permission },
    isArray: true,
    required: false,
  })
  @IsOptional()
  // @EnumTransform({ ...Permission, ...FFM.Permission })
  // @IsEnum({ ...Permission, ...FFM.Permission }, {
  //   each: true,
  // })
  permissions?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  parentId?: number;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  newPermissions;

  @ApiProperty({
    required: false,
    enum: DataAccessLevel,
  })
  @IsOptional()
  @IsEnum(DataAccessLevel)
  @EnumTransform(DataAccessLevel)
  dataAccessLevel?: DataAccessLevel;

  @ApiProperty({
    required: false,
    enum: ModuleInCharge,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ModuleInCharge, { each: true })
  @EnumTransform(ModuleInCharge)
  moduleInCharge?: ModuleInCharge[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  users?: User[];
}

export class UpdateRoleDto extends PartialType(CreateRoleDto) {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber(undefined, { each: true })
  userIds?: number[];
}
