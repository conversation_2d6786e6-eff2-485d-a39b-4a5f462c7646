import {
  BadRequestException,
  Controller,
  Get,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  DiskHealthIndicator,
  HealthCheck,
  HealthCheckService,
  MemoryHealthIndicator,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';
import { InjectConnection } from '@nestjs/typeorm';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { identityConnection } from 'core/constants/database-connection.constant';
import { SalePermission } from 'core/enums/sale-permissions';
import { AccountPermission } from 'core/enums/sale-permissions/account-permission.enum';
import { Connection } from 'typeorm';
import { IdentityService } from './identity.service';
import { OrderPermission } from 'core/enums/sale-permissions/order-permission.enum';
import { ApiBody, ApiConsumes } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { AwsUtils } from 'core/utils/AwsUtils';

@Controller()
export class IdentityController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private memory: MemoryHealthIndicator,
    @InjectConnection(identityConnection)
    private iConnection: Connection,
    private service: IdentityService,
  ) {}

  @Get('health')
  @HealthCheck()
  check() {
    return 'I am fine';
    // return this.health.check([
    //   () => this.db.pingCheck('postgres', {
    //     connection: this.iConnection,
    //     timeout: 5000,
    //   }),
    //   () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.8 }),
    //   () => this.memory.checkHeap('memory_heap', 1024 * 1024 * 1024),
    // ]);
  }

  // @Get('')
  // @Auth('sale', [SalePermission.order, OrderPermission.fetchMany])
  // hello() {
  //   return this.service.getHello();
  // }
  @Get('')
  @Auth('sale', [SalePermission.order, OrderPermission.fetchMany])
  hello() {
    return this.service.getHello();
  }

  @Post('/pre-signed-url')
  @ApiConsumes('multipart/form-data')
  @Auth()
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async getPreSignedUrl(@UploadedFile('file') file) {
    if (!file) throw new BadRequestException('file is required');
    try {
      return await AwsUtils.getPreSignUrl(file);
    } catch (e) {
      throw new BadRequestException(`getPreSignedUrl ERROR: ${e.message}`);
    }
  }
}
