import { WarehouseStatus, WarehouseType } from '../../enums/catalog/warehouse-type.enum';
import { Expose, Type } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { Column, Entity, Index, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { SenderInformation } from './sender-information.entity';
import { WarehouseClientAllocation } from './warehouse_client_allocation.entity';

@Entity({
  name: 'slot_warehouses',
  database: process.env.DATABASE_FFM_CATALOG,
  synchronize: false,
})
export class SlotWarehouses {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  @Expose()
  id: string;

  @Column('character varying', { name: 'name', nullable: true, length: 255 })
  @Expose()
  name: string | null;

  @Column('character varying', {
    name: 'display_id',
    nullable: true,
    length: 255,
  })
  @Index({ unique: true, where: 'display_id IS NOT NULL' })
  @Expose()
  displayId: string | null;

  @Column('integer', { name: 'country_code', nullable: true })
  @Expose()
  countryCode: number | null;

  @Column({
    name: 'biz_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  bizId?: string;

  @Column('varchar', { name: 'type', nullable: true, length: 255 })
  @Expose()
  @EnumTransform(WarehouseType)
  type: WarehouseType;

  @Column('character varying', {
    name: 'full_address',
    nullable: true,
    length: 255,
  })
  @Expose()
  fullAddress: string | null;

  @Column('character varying', {
    name: 'phone_number',
    nullable: true,
    length: 255,
  })
  @Expose()
  phoneNumber: string | null;

  @Column({
    name: 'province_id',
    nullable: true,
    length: 255,
  })
  @Expose()
  provinceId: string | null;

  @Column({
    name: 'is_on_sender',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isOnSender?: boolean;

  @OneToMany(
    () => SenderInformation,
    si => si.warehouse,
    { nullable: true, cascade: true },
  )
  @Type(() => SenderInformation)
  @Expose()
  senderInformations?: SenderInformation[];

  @Column({
    name: 'status',
    type: 'integer',
    default: WarehouseStatus.active,
  })
  // @EnumTransform(WarehouseStatus)
  @Expose()
  status?: number;

  @OneToMany(
    () => WarehouseClientAllocation,
    wcl => wcl.warehouse,
    { nullable: true },
  )
  @Type(() => WarehouseClientAllocation)
  @Expose()
  clients?: WarehouseClientAllocation[];

  @Column({ type: 'integer', name: 'return_warehouse_id', nullable: true })
  @Expose()
  returnWarehouseId?: number;
}
