import { WarehouseStatus, WarehouseType } from '../../enums/catalog/warehouse-type.enum';
import { Expose, Type } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { SenderInformation } from './sender-information.entity';
import { SlotWarehouses } from './warehouses.entity';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';

@Entity({
  name: 'warehouse_client_allocation',
  database: process.env.DATABASE_FFM_CATALOG,
  synchronize: false,
})
export class WarehouseClientAllocation {
  @Column({
      name: 'id_country',
      type: 'integer',
      primary: true,
    })
    @Index()
    @Expose()
    countryId?: number;
  
    @Column({
      name: 'id_client',
      type: 'integer',
      primary: true,
    })
    @Index()
    @Expose()
    clientId?: number;
  
    @Column({
      name: 'id_warehouse',
      type: 'integer'
    })
    @Index()
    @Expose()
    warehouseId?: number;
  
    @Column({
      name: 'biz_id',
      type: 'varchar',
      nullable: true
    })
    @Expose() 
    bizId?: string;

    @JoinColumn({name: "id_warehouse"})
    @ManyToOne(() => SlotWarehouses, r => r.clients)
    @Expose()
    @NonEmptyTransform()
    warehouse?: SlotWarehouses
}
