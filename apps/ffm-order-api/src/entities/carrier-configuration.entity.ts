import { Expose } from 'class-transformer';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { Column, DeleteDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { CarrierCode } from '../../../../core/enums/carrier-code.enum';
import { EnumTransform } from '../../../../core/decorators/enum-transform.decorator';
import { CommonStatus } from '../../../../core/enums/common-status.enum';
import { InsuranceType } from '../enums/carrier-configuration.enum';
@Entity({
  name: 'carrier_configurations',
})
export class CarrierConfiguration extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id?: number;

  @PrimaryColumn({
    name: 'carrier_code',
    type: 'varchar',
    length: 100,
  })
  @Expose()
  @EnumTransform(CarrierCode)
  carrierCode: CarrierCode;

  @PrimaryColumn({
    name: 'carrier_id',
    type: 'varchar',
    length: 100,
  })
  @Expose()
  carrierId: string;

  @PrimaryColumn({
    name: 'channel_code',
    type: 'varchar',
    length: 100,
  })
  @Expose()
  channelCode: string;

  @PrimaryColumn({
    name: 'company_id',
    type: 'int',
  })
  @Expose()
  companyId: number;

  @PrimaryColumn({
    name: 'country_id',
    type: 'int',
  })
  @Expose()
  countryId: number;

  @Column({
    name: 'creator_id',
    type: 'int',
  })
  @Expose()
  creatorId: number;

  @Column({
    name: 'last_updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastUpdatedBy?: number;

  @Column({
    name: 'extra_data',
    type: 'json',
  })
  @Expose()
  extraData: Record<string, any>;

  @Column('varchar', {
    name: 'note',
    nullable: true,
  })
  @Expose()
  note: string;

  @Column({
    name: 'display_name',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  @Expose()
  displayName: string;

  @Column('boolean', {
    name: 'is_default',
    default: false,
  })
  @Expose()
  isDefault: boolean;

  @Column({
    name: 'status',
    type: 'smallint',
    default: CommonStatus.activated,
    comment: '0: deactivated, 1: activated',
  })
  @Expose()
  @EnumTransform(CommonStatus)
  status?: CommonStatus;

  @DeleteDateColumn({
    name: 'deleted_at',
  })
  deletedAt: Date;

  @Column('boolean', {
    name: 'is_insurance',
    default: false,
  })
  @Expose()
  isInsurance: boolean;

  @Column({
    name: 'insurance_type',
    type: 'int',
    nullable: true,
  })
  @EnumTransform(InsuranceType)
  @Expose()
  insuranceType: InsuranceType;

  @Column({
    name: 'order_value',
    type: 'int',
    nullable: true,
  })
  @Expose()
  orderValue: number;
}
