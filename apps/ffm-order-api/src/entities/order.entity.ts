import { Expose, Transform, Type } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { isEmpty, orderBy, reduce } from 'lodash';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';
import {
  ExternalOrderType,
  TypeCreateOrder,
  TypeOrder,
  WarningType,
} from '../enums/order-status.enum';
import { TypeUpdateOrderStatus } from '../enums/type-update-order-status.enum';
import { Customer } from './customer.entity';
import { HandOver } from './hand-over.entity';

import { VirtualColumn } from 'core/decorators/virtual-column.decorator';
import { Note } from './note.entity';
import { OrderCarrier } from './order-carrier.entity';
import { OrderProduct } from './order-product.entity';
import { RaiseTicket } from './raise-ticket.entity';
import { Reason } from './reason.entity';
import { Tag } from './tags.entity';
import { OrderTag } from './order-tag.entity';
import { Remittance } from './remittance.entity';
import { Optional } from '@nestjs/common';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { OrderWarning } from './order_warning.entity';
import { PostCodeTransform } from 'core/decorators/post-code-transform.decorator';
import { OrderProductComboVariant } from './order-product-combo-variant.entity';
import { WarehouseTag } from './warehouse-tag.entity';
import { CollectingPackage } from './collecting-package.entity';

@Entity({
  name: 'orders',
  database: process.env.DATABASE_ORDER_FFM,
})
@Unique('UQ_DISPLAY_ORDER', ['displayId'])
@Index('UQ_EXTERNAL_ORDER', ['externalId', 'clientId', 'companyId'], {
  unique: true,
  where: `(external_code = '${ExternalOrderType?.ag}' AND status IN (
    ${OrderFFMStatus.Draft},
    ${OrderFFMStatus.New},
    ${OrderFFMStatus.AwaitingStock},
    ${OrderFFMStatus.Confirmed},
    ${OrderFFMStatus.Reconfirm},
    ${OrderFFMStatus.AwaitingCollection},
    ${OrderFFMStatus.Collecting},
    ${OrderFFMStatus.Awaiting3PLPickup},
    ${OrderFFMStatus.PickedUp3PL},
    ${OrderFFMStatus.InTransit},
    ${OrderFFMStatus.Stocked3PL},
    ${OrderFFMStatus.InDelivery},
    ${OrderFFMStatus.FailedDelivery}
  ))`,
})
@Index('UQ_EXTERNAL_ORDER_TYPE_PANCAKE', ['externalId', 'clientId', 'companyId', 'countryId'], {
  unique: true,
  where: `(external_code IN ('${ExternalOrderType?.hvnet}', '${ExternalOrderType?.pancake}'))`,
})
@Index('INDEX_ORDER_WAREHOUSE_COMPANY_COUNTRY', ['warehouseId', 'companyId', 'countryId'])
export class Order {
  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamp with time zone',
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Index()
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    type: 'timestamp with time zone',
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  @Index()
  updatedAt: Date;

  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id: number;

  @Column({
    name: 'order_carrier_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  orderCarrierId?: number;

  @Column({
    name: 'discount',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  discount?: number;

  @Column({
    name: 'discount_percentage',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  discountPercentage?: number;

  @Column({
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  surcharge?: number;

  @Column({
    name: 'shipping_fee',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  shippingFee?: number;

  @Column({
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  paid?: number;

  @Column({
    name: 'total_price',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  totalPrice?: number;

  @Column({
    name: 'service_TLS',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  serviceTLS?: number;

  @Column({
    name: 'service_CS',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  serviceCS?: number;

  @Column({
    name: 'service_FFM',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  serviceFFM?: number;

  @Column({
    name: 'service_insurance',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  serviceInsurance?: number;

  @Column({
    name: 'sub_total',
    type: 'double precision',
    nullable: true,
    default: 0,
  })
  @Expose()
  subTotal?: number;

  @Column({
    type: 'smallint',
    default: OrderFFMStatus.Draft,
    nullable: false,
  })
  @EnumTransform(OrderFFMStatus)
  @Index()
  @Expose()
  status: OrderFFMStatus;

  @Column({
    name: 'creator_id',
    type: 'int',
  })
  @Expose()
  creatorId?: number;

  @Column({
    name: 'external_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  externalId?: string;

  @Column({
    name: 'client_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  clientId?: number;

  @Column({
    name: 'warehouse_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  warehouseId: number;

  @Column({
    name: 'return_warehouse_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  returnWarehouseId: number;

  @Column({
    name: 'internal_note',
    type: 'text',
    nullable: true,
  })
  @Expose()
  internalNote: string;

  @Column({
    name: 'country_code',
    type: 'varchar',
  })
  @Expose()
  countryCode: string;

  @Column({
    name: 'country_id',
    type: 'varchar',
    nullable: true,
  })
  @Index()
  @Expose()
  countryId: string;

  @Column({
    name: 'display_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @Index({ unique: true, where: 'display_id IS NOT NULL' })
  displayId?: string;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  companyId?: number;

  @Column({
    name: 'company_partner_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyPartnerId?: number;

  @OneToMany(
    () => OrderProduct,
    product => product.order,
    { cascade: true },
  )
  @Type(() => OrderProduct)
  @Expose()
  products: OrderProduct[];

  @OneToMany(
    () => OrderProductComboVariant,
    orderProductCombo => orderProductCombo.order,
    { cascade: true },
  )
  @Type(() => OrderProductComboVariant)
  @Expose()
  orderProductCombo: OrderProductComboVariant[];

  @OneToMany(
    () => Note,
    item => item.order,
    { nullable: true, cascade: true },
  )
  @Expose()
  notes: Note[];

  @OneToMany(
    () => OrderTag,
    item => item.order,
    { cascade: true },
  )
  @Expose()
  orderTags: OrderTag[];

  @OneToMany(
    () => WarehouseTag,
    item => item.order,
    { cascade: true },
  )
  @Expose()
  whTags: WarehouseTag[];

  @OneToMany(
    () => RaiseTicket,
    item => item.order,
    { nullable: true },
  )
  @Expose()
  tickets: RaiseTicket[];

  @Column({
    name: 'last_updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  lastUpdatedBy?: number;

  @Column({
    name: 'last_updated_hand_over_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  lastUpdatedHandOverBy?: number;

  @Column({
    name: 'last_update_status',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  lastUpdateStatus?: Date;

  @Column({
    name: 'pack_date',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  packDate?: Date;

  @Column({
    name: 'recipient_name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientName: string;

  @Column({
    name: 'recipient_phone',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientPhone: string;

  @Column({
    name: 'recipient_email',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientEmail?: string;

  @Column({
    name: 'recipient_address',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientAddress: string;

  @Column({
    name: 'recipient_address_note',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientAddressNote?: string;

  @Column({
    name: 'recipient_ward',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientWard?: string;

  @Column({
    name: 'recipient_ward_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientWardId?: string;

  @Column({
    name: 'recipient_district',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientDistrict: string;

  @Column({
    name: 'recipient_district_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientDistrictId: string;

  @Column({
    name: 'recipient_province',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientProvince: string;

  @Column({
    name: 'recipient_province_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientProvinceId: string;

  // @Column({
  //   name: 'recipient_post_code_old',
  //   type: 'int',
  //   nullable: true,
  // })
  // @Expose()
  // recipientPostCodeOld?: string | number;

  @Column({
    name: 'recipient_post_code',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @PostCodeTransform()
  recipientPostCode?: string;

  @Column({
    name: 'recipient_country',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientCountry: string;

  @Column({
    name: 'recipient_country_id ',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  recipientCountryId: string;

  @Column({
    name: 'customer_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  customerId?: number;

  @Column({
    name: 'hand_over_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  handOverId?: number;

  @Column({
    name: 'collecting_pack_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  collectingPackageId?: number;

  @Column({
    name: 'collecting_pack_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  collectingPackageBy?: number;

  @Column({
    name: 'type_update_status',
    type: 'int',
    default: TypeUpdateOrderStatus.Manual,
    nullable: true,
  })
  @EnumTransform(TypeUpdateOrderStatus)
  typeUpdateStatus: TypeUpdateOrderStatus;

  @OneToMany(
    () => OrderCarrier,
    oc => oc.order,
    { nullable: true, cascade: true },
  )
  @Expose()
  @Transform(({ value }) => {
    return orderBy(value, 'id', 'desc');
  })
  carriers?: OrderCarrier[];

  // @ManyToOne(() => OrderCarrier)
  // @JoinColumn({
  //   name: 'current_carrier_id',
  //   referencedColumnName: 'id',
  // })
  carrier?: OrderCarrier;

  @ManyToOne(
    () => Customer,
    c => c.orders,
    { nullable: true, cascade: true },
  )
  @JoinColumn({ name: 'customer_id' })
  customer: Customer;
  @DeleteDateColumn({
    name: 'deleted_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  deletedAt?: Date;
  @Column({
    name: 'type_create',
    type: 'int',
    default: TypeCreateOrder.Manual,
    nullable: true,
  })
  @EnumTransform(TypeCreateOrder)
  typeCreate: TypeCreateOrder;
  @Column({
    name: 'external_code',
    type: 'varchar',
    default: null,
    nullable: true,
  })
  @Expose()
  @EnumTransform(ExternalOrderType)
  externalCode: ExternalOrderType;

  @ManyToOne(
    () => HandOver,
    c => c.orders,
    { nullable: true },
  )
  @JoinColumn({ name: 'hand_over_id' })
  @Expose()
  handOver: HandOver;

  @ManyToOne(
    () => CollectingPackage,
    c => c.orders,
    { nullable: true },
  )
  @JoinColumn({ name: 'collecting_pack_id' })
  @Expose()
  collectingPackage: CollectingPackage;

  @Column({
    name: 'update_hand_over_at',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  updateHandOverAt?: Date;
  @Column({
    name: 'customer_edd',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  customerEDD?: Date;

  @ManyToMany(
    () => Tag,
    tg => tg.order,
    { cascade: ['insert', 'remove', 'soft-remove', 'recover'] },
  )
  @JoinTable({
    name: 'order_tag',
    joinColumn: {
      name: 'id_order',
    },
    inverseJoinColumn: {
      name: 'id_tag',
    },
  })
  @Expose()
  tags?: Tag[];

  @ManyToMany(
    () => Tag,
    wt => wt.order,
    { cascade: ['insert', 'remove', 'soft-remove', 'recover'] },
  )
  @JoinTable({
    name: 'warehouse_tag',
    joinColumn: {
      name: 'id_order',
    },
    inverseJoinColumn: {
      name: 'id_warehouse_tag',
    },
  })
  @Expose()
  warehouseTags?: Tag[];

  @Expose()
  @NonEmptyTransform()
  get lastCarrier(): OrderCarrier | undefined {
    return isEmpty(this.carriers) ? undefined : orderBy(this.carriers, 'id', 'desc')[0];
  }

  @Expose()
  @NonEmptyTransform()
  get lastNote(): Note | null {
    return !!this.notes
      ? reduce(
          this.notes,
          function(val, n) {
            return n?.id > Number(val?.id ?? 0) ? n : val;
          },
          null,
        )
      : null;
  }

  @Column({
    name: 'reason_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  reasonId: number;

  @ManyToOne(() => Reason)
  @Expose()
  @JoinColumn({ name: 'reason_id' })
  reason: Reason;

  @Column({
    name: 'reason_note',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  reasonNote: string;

  @Column({
    name: 'reason_code',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  reasonCode: string;

  @Column({
    name: 'last_update_reason',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  lastUpdateReason?: Date;

  @VirtualColumn('before_status')
  @Expose()
  before_status?: string;

  @VirtualColumn('lastConfirm')
  @Expose()
  lastConfirm?: string;

  @Column({
    name: 'seller',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  seller: string;

  @Column({
    name: 'is_dangerous',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isDangerous?: boolean;

  @Column({
    name: 'current_carrier_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  currentCarrierId?: number;

  @Column({
    name: 'proof_of_change',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  proofOfChange: string;

  @Column({
    name: 'is_mapping_3pl',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isMapping3pl?: boolean;

  @Column({
    name: 'no_of_deliveries',
    type: 'int',
    nullable: true,
    default: 0,
  })
  @Expose()
  noOfDeliveries?: number;

  @OneToMany(
    () => Remittance,
    remittance => remittance.order,
    { cascade: true },
  )
  @Type(() => Remittance)
  @Expose()
  remittances?: Remittance[];

  @OneToMany(
    () => OrderWarning,
    orderWarning => orderWarning.order,
    { cascade: true },
  )
  @Type(() => OrderWarning)
  @Expose()
  orderWarnings?: OrderWarning[];

  @Column({
    name: 'unmark_odz',
    type: 'boolean',
    default: false,
  })
  @Expose()
  unmarkODZ?: boolean;

  @Column({
    name: 'waybill_note',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  waybillNote?: string;

  @Column({
    name: 'is_ignore_duplicate',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isIgnoreDuplicate?: boolean;

  //gram
  @Column({
    name: 'total_weight',
    type: 'int',
    nullable: true,
  })
  @Expose()
  totalWeight?: number;

  @VirtualColumn('virtual_total_weight')
  @Expose()
  virtual_total_weight?: string;

  @Column({
    name: 'is_reconciled_cod',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isReconciledCod?: boolean;

  @Column({
    name: 'external_project_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  externalProjectId?: number;

  @Column({
    name: 'external_project_name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  externalProjectName?: string;

  @VirtualColumn('totalSku')
  @Expose()
  totalSku?: number;

  @VirtualColumn('totalQty')
  @Expose()
  totalQty?: number;

  @Column({
    name: 'is_ignore_need_retry_ticket',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isIgnoreNeedRetryTicket?: boolean;

  @Column({
    name: 'awaiting_collection_date',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  awaitingCollectionDate?: Date;

  @Column({
    name: 'type',
    type: 'int',
    default: 0,
  })
  @Expose()
  @EnumTransform(TypeOrder)
  type: number;

  @Column({
    name: 'return_country_id',
    type: 'varchar',
    nullable: true,
  })
  @Index()
  @Expose()
  returnCountryId: string;

  @VirtualColumn('warehouse_name')
  @Expose()
  warehouseName?: string;

  @VirtualColumn('return_warehouse_name')
  @Expose()
  returnWarehouseName?: string;
}
