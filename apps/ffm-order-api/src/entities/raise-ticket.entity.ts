import { Expose, Transform, Type } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';
import { Order } from './order.entity';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import {
  RaiseTicketStatusEnum,
  RaiseTicketTypeEnum,
  RaiseTicketTypeUpdateEnum,
} from '../enums/raise-ticket.enum';
import { RaiseTicketNote } from './raise-ticket-note.entity';
import { VirtualColumn } from 'core/decorators/virtual-column.decorator';
import { reduce } from 'lodash';
import { Tag } from './tags.entity';
import { TicketTag } from './ticket-tag.entity';
import { TicketAppointment } from './ticket-appointment.entity';
import { OneToOne } from 'typeorm';

@Unique('UQ_DISPLAY_TICKET', ['displayId', 'companyId'])
// @Unique('UQ_DISPLAY_TYPE_ORDER', ['orderId','type'])
@Index('UQ_DISPLAY_TYPE_ORDER', ['orderId', 'type'], {
  unique: true,
  where: `(deleted_at IS NULL)`,
})
@Entity({
  name: 'raise_ticket',
  database: process.env.DATABASE_ORDER_FFM,
})
export class RaiseTicket extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id?: number;

  @Column({
    name: 'creator_id',
    type: 'int',
  })
  @Expose()
  creatorId: number;

  @Column({
    name: 'deleted_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  deletedAt?: Date;

  @Column({
    name: 'order_id',
    type: 'int',
  })
  @Index()
  @Expose()
  orderId: number;

  @ManyToOne(() => Order)
  @JoinColumn({ name: 'order_id' })
  @Expose()
  order: Order;

  @Column({
    name: 'display_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  // @Index({unique: true, where: 'display_id IS NOT NULL'})
  displayId?: string;

  @Column({
    name: 'status',
    type: 'int',
  })
  @EnumTransform(RaiseTicketStatusEnum)
  @Expose()
  status: RaiseTicketStatusEnum;

  @Column({
    name: 'type',
    type: 'int',
  })
  @EnumTransform(RaiseTicketTypeEnum)
  @Expose()
  type: RaiseTicketTypeEnum;

  @Column({
    name: 'type_update',
    type: 'int',
    default: RaiseTicketTypeUpdateEnum.Manual,
  })
  @EnumTransform(RaiseTicketTypeUpdateEnum)
  @Expose()
  typeUpdate: RaiseTicketTypeUpdateEnum;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  companyId?: number;

  @Column({
    name: 'assignee',
    type: 'int',
    nullable: true,
  })
  @Expose()
  assignee?: number;

  @Column({
    name: 'assignee_ids',
    type: 'int4',
    nullable: true,
    array: true,
  })
  @Expose()
  assigneeIds?: number[] | null;

  @Column({
    name: 'last_updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  lastUpdatedBy?: number;

  @OneToMany(
    () => RaiseTicketNote,
    item => item.ticket,
    { nullable: true, cascade: true },
  )
  @Expose()
  notes: RaiseTicketNote[];

  @OneToMany(
    () => TicketAppointment,
    item => item.ticket,
    { nullable: true, cascade: true },
  )
  @Expose()
  ticketAppointments: TicketAppointment[];

  @Expose()
  @NonEmptyTransform()
  get lastNote(): RaiseTicketNote | null {
    return !!this.notes
      ? reduce(
          this.notes,
          function(val, n) {
            return n?.id > Number(val?.id ?? 0) ? n : val;
          },
          null,
        )
      : null;
  }

  @Column({
    name: 'last_update_status',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  lastUpdateStatus?: Date;

  @Column({
    name: 'is_spolight',
    type: 'boolean',
    default: false,
  })
  @Expose()
  isSpotlight: boolean;

  @VirtualColumn('number_of_failed_delivery')
  @Expose()
  numberOfFailedDelivery?: string;

  @ManyToMany(
    () => Tag,
    tg => tg.ticket,
    { cascade: ['insert', 'remove', 'soft-remove', 'recover'] },
  )
  @JoinTable({
    name: 'ticket_tag',
    joinColumn: {
      name: 'id_ticket',
    },
    inverseJoinColumn: {
      name: 'id_tag',
    },
  })
  @Expose()
  tags?: Tag[];

  @OneToMany(
    () => TicketTag,
    item => item.tag,
    { cascade: true },
  )
  @Expose()
  ticketTags: TicketTag[];

  @Column({
    name: 'cancel_reason',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  cancelReason: string;

  @Column({
    name: 'last_update_assignee',
    type: 'timestamp',
    nullable: true,
  })
  @DateTransform()
  @Expose()
  lastUpdateAssignee?: Date;

  @Column({
    name: 'last_result_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastResultId: number;

  @OneToOne(
    () => RaiseTicketNote,
    rtn => rtn.id,
  )
  @JoinColumn({
    name: 'last_result_id',
  })
  @Expose()
  lastResult?: RaiseTicketNote;

  @VirtualColumn('no_of_care_results')
  @Expose()
  noOfCareResults?: number;

  @VirtualColumn('last_update_results')
  @DateTransform()
  @Expose()
  lastUpdateResults?: Date;
}
