import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1700541362268 implements MigrationInterface {
    name = 'migration1700541362268'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "order_tag" DROP CONSTRAINT "FK_dc2e09debb588ab4510e9d7be0c"`);
        await queryRunner.query(`ALTER TABLE "order_tag" DROP CONSTRAINT "FK_9a32a2a5520ae122b31fe489f96"`);
        await queryRunner.query(`ALTER TABLE "order_tag" ADD "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_1daeb23be7fb7678f1e8d2a2859" FOREIGN KEY ("hand_over_id") REFERENCES "hand_over"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_tag" ADD CONSTRAINT "FK_dc2e09debb588ab4510e9d7be0c" FOREIGN KEY ("id_order") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "order_tag" ADD CONSTRAINT "FK_9a32a2a5520ae122b31fe489f96" FOREIGN KEY ("id_tag") REFERENCES "tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "order_tag" DROP CONSTRAINT "FK_9a32a2a5520ae122b31fe489f96"`);
        await queryRunner.query(`ALTER TABLE "order_tag" DROP CONSTRAINT "FK_dc2e09debb588ab4510e9d7be0c"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_1daeb23be7fb7678f1e8d2a2859"`);
        await queryRunner.query(`ALTER TABLE "order_tag" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "order_tag" ADD CONSTRAINT "FK_9a32a2a5520ae122b31fe489f96" FOREIGN KEY ("id_tag") REFERENCES "tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_tag" ADD CONSTRAINT "FK_dc2e09debb588ab4510e9d7be0c" FOREIGN KEY ("id_order") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

}
