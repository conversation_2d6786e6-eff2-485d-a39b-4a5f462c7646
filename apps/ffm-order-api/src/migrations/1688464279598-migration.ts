import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1688464279598 implements MigrationInterface {
    name = 'migration1688464279598'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD "last_updated_hand_over_by" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "last_updated_hand_over_by"`);
    }

}
