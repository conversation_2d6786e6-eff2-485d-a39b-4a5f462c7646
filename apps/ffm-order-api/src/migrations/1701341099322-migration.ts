import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1701341099322 implements MigrationInterface {
    name = 'migration1701341099322'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "re_imports" DROP CONSTRAINT "UQ_RE_IMPORT_ORDER"`);
        await queryRunner.query(`ALTER TABLE "re_imports" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`CREATE UNIQUE INDEX "UQ_RE_IMPORT_ORDER" ON "re_imports" ("order_id", "company_id", "client_id") WHERE (deleted_at IS NULL)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."UQ_RE_IMPORT_ORDER"`);
        await queryRunner.query(`ALTER TABLE "re_imports" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "re_imports" ADD CONSTRAINT "UQ_RE_IMPORT_ORDER" UNIQUE ("order_id", "company_id", "client_id")`);
    }

}
