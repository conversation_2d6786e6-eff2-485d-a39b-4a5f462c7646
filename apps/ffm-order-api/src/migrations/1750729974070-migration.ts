import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1750729974070 implements MigrationInterface {
    name = 'migration1750729974070'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ALTER COLUMN "client_id" DROP NOT NULL`);
        await queryRunner.query(`DROP INDEX IF EXISTS "UQ_SHOP_CLIENT"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "UQ_SHOP_CLIENT" ON "marketplace_integration" ("company_id", "type", "shop_id") `);
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ALTER COLUMN "client_id" SET NOT NULL`);
        await queryRunner.query(`DROP INDEX IF EXISTS "UQ_SHOP_CLIENT"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "UQ_SHOP_CLIENT" ON "marketplace_integration" ("client_id", "company_id", "country_id", "shop_id", "type") `);
    }

}
