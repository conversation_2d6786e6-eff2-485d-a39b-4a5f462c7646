import { OrderFFMStatus } from "core/enums/order-ffm-status.enum";
import { MigrationInterface, QueryRunner } from "typeorm";
import { HandOverStatus } from "../enums/hand-over-status.enum";

export class migration1688636007814 implements MigrationInterface {
    name = 'migration1688636007814'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
        CREATE 
            OR REPLACE FUNCTION "public"."fn_update_last_status_order" ( ) RETURNS "pg_catalog"."trigger" AS $BODY$ DECLARE
            code VARCHAR;
        status_cancel INT;
        status_awaiting_pickup INT;
        status_collecting INT;
        status_handover_new INT;
        status_handovered INT;
        total_order INT;
        BEGIN
                status_cancel := TG_ARGV [ 0 ];
            status_awaiting_pickup := TG_ARGV [ 1 ];
            status_collecting := TG_ARGV [ 2 ];
            status_handover_new := TG_ARGV [ 3 ];
            status_handovered := TG_ARGV [ 4 ];
            IF
                ( exist ( hstore ( NEW ) - hstore ( OLD ), 'status' ) ) THEN
                    UPDATE orders o 
                    SET last_update_status = CURRENT_TIMESTAMP 
                WHERE
                    o.ID = NEW.ID;
                
            END IF;
            RETURN NEW;
            
        END;
        $BODY$ LANGUAGE plpgsql VOLATILE COST 100`);

        await queryRunner.query(`DROP TRIGGER trigger_log_hand_over ON hand_over`);
        await queryRunner.query(`DROP TRIGGER trigger_log_order_hand_over ON orders`);
        // await queryRunner.query(`DROP FUNCTION fn_log_hand_over`);

        await queryRunner.query(`CREATE 
        OR REPLACE FUNCTION "public"."fn_log_hand_over" ( ) RETURNS "pg_catalog"."trigger" AS $BODY$ DECLARE
        status_awaiting_pickup INT;
    status_collecting INT;
    status_handed_over INT;
    total_order INT;
    array_fields_hand_over_information TEXT [];
    array_fields_deliveryman TEXT [];
    ACTION TEXT;
    BEGIN
            ACTION := TG_OP;
        status_awaiting_pickup := TG_ARGV [ 0 ];
        status_collecting := TG_ARGV [ 1 ];
        status_handed_over := TG_ARGV [ 2 ];
        array_fields_hand_over_information := TG_ARGV [ 3 ]:: TEXT [];
        array_fields_deliveryman := TG_ARGV [ 4 ]:: TEXT [];
        IF
            array_fields_hand_over_information IS NULL THEN
                array_fields_hand_over_information := ARRAY []:: TEXT [];
            
        END IF;
        IF
            array_fields_deliveryman IS NULL THEN
                array_fields_deliveryman := ARRAY []:: TEXT [];
            
        END IF;
        IF
            ( TG_OP = 'INSERT' AND TG_TABLE_NAME = 'hand_over' ) THEN
                EXECUTE'INSERT INTO logs ( action, table_name, record_id, changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING TG_OP,
                TG_TABLE_NAME,
                NEW.ID,
                hstore_to_array ( hstore ( NEW ) ),
                NEW.creator_id,
                'Handover Receipt',
                'Create';
            
        END IF;
        IF
            ( TG_OP = 'UPDATE' AND TG_TABLE_NAME = 'hand_over' AND OLD.deleted_at IS NULL AND NEW.deleted_at IS NOT NULL ) THEN
                EXECUTE'INSERT INTO logs (action, table_name, record_id, before_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING TG_OP,
                TG_TABLE_NAME,
                NEW.ID,
                hstore_to_array ( hstore ( OLD ) ),
                NEW.creator_id,
                'Handover Receipt',
                'Delete';
            
        END IF;
        IF
            (
                TG_OP <> 'INSERT' 
                AND TG_TABLE_NAME = 'hand_over' 
                AND exist ( hstore ( NEW ) - hstore ( OLD ), 'status' ) 
                AND NEW.status = status_handed_over 
                ) THEN
                EXECUTE'SELECT COUNT(*) FROM orders o WHERE o.hand_over_id::INT = $1 AND (o.status = $2 OR o.status = $3)' INTO total_order USING NEW.ID,
                status_awaiting_pickup,
                status_collecting;
            EXECUTE'INSERT INTO logs (action, table_name, record_id, changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING TG_OP,
            TG_TABLE_NAME,
            NEW.ID,
            ARRAY [ total_order ],
            NEW.creator_id,
            'Finish Handover',
            'Total Waybill';
            EXECUTE'INSERT INTO logs (action, table_name, record_id, changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING TG_OP,
            TG_TABLE_NAME,
            NEW.ID,
            hstore_to_array ( slice ( hstore ( NEW ), array_fields_deliveryman ) ),
            NEW.creator_id,
            'Change',
            'Deliveryman Information';
            
        END IF;
        IF
            (
                TG_OP <> 'INSERT' 
                AND TG_TABLE_NAME = 'hand_over' 
                AND ( ARRAY_LENGTH( array_fields_hand_over_information, 1 ) > 0 ) 
                AND ( hstore(NEW) - hstore(OLD) ) ?| array_fields_hand_over_information 
                ) THEN
                EXECUTE'INSERT INTO logs (action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9))' USING TG_OP,
                TG_TABLE_NAME,
                NEW.ID,
                hstore_to_array ( slice ( hstore ( NEW ), array_fields_hand_over_information ) ),
                hstore_to_array ( slice ( hstore ( NEW ), array_fields_hand_over_information ) ),
                hstore_to_array ( slice ( hstore ( OLD ), array_fields_hand_over_information ) ),
                NEW.creator_id,
                'Change',
                'Handover Change Information';
            
        END IF;
        IF
            ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND ( exist ( hstore ( NEW ) - hstore ( OLD ), 'hand_over_id' ) ) ) THEN
                EXECUTE'INSERT INTO logs ( action, table_name, record_id, changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING TG_OP,
                'hand_over',
                NEW.hand_over_id,
                ARRAY [ NEW.display_id ],
                NEW.creator_id,
                'Hand over',
                'Order';
            
        END IF;
        RETURN NEW;
    
    END $BODY$ LANGUAGE plpgsql VOLATILE COST 100`);
        await queryRunner.query(`CREATE TRIGGER "trigger_log_hand_over" AFTER INSERT OR UPDATE OR DELETE ON "public"."hand_over" FOR EACH ROW EXECUTE PROCEDURE "public"."fn_log_hand_over"(${OrderFFMStatus.Awaiting3PLPickup}, ${OrderFFMStatus.PickedUp3PL}, ${HandOverStatus.handedOver}, '{carrier_id,warehouse_id,note}', '{delivery_man_code,delivery_man_name}'); 
        `);
        await queryRunner.query(`CREATE TRIGGER "trigger_log_order_hand_over" AFTER UPDATE OR DELETE ON "public"."orders" FOR EACH ROW EXECUTE PROCEDURE "public"."fn_log_hand_over"(${OrderFFMStatus.Awaiting3PLPickup}, ${OrderFFMStatus.PickedUp3PL}, ${HandOverStatus.handedOver}, '{carrier_id,warehouse_id,note}', '{delivery_man_code,delivery_man_name}'); 
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TRIGGER trigger_log_order_hand_over On orders`);
        await queryRunner.query(`DROP TRIGGER trigger_log_hand_over ON hand_over`);
        // await queryRunner.query(`DROP FUNCTION fn_log_hand_over`);
        await queryRunner.query(`
        CREATE OR REPLACE FUNCTION "public"."fn_log_hand_over"()
        RETURNS "pg_catalog"."trigger" AS $BODY$
                          DECLARE
                              changes hstore;
                              before_changes hstore;
                                          after_changes hstore;
                              parent_table_name TEXT;
                              record_id TEXT;
                              record_id_column_name TEXT;
                              parent_id TEXT;
                              parent_id_column_name TEXT;
                              parent_id_ref_column_name TEXT;
                              ACTION TEXT;
                              array_fields_delivery TEXT [];
                              creator_column_name TEXT;
                                          type TEXT;
                                          event TEXT;
                                          type_status TEXT;
                                          event_status TEXT;
                              creator_id TEXT;
                                          finish_status TEXT;
                                          total_order TEXT; 
                                                                              picked_up_status TEXT; 
                                                                              update_hand_over TIMESTAMP;
                          BEGIN 
                              ACTION := TG_OP;
                              parent_table_name := TG_ARGV[0];
                              creator_column_name := TG_ARGV[1];
                              record_id_column_name := TG_ARGV[2];
                              parent_id_column_name := TG_ARGV[3]; -- name column parent_id in table item
                              array_fields_delivery := TG_ARGV[4]::text[];
                              parent_id_ref_column_name := TG_ARGV[5]; -- name column id parent table 
                                          finish_status := TG_ARGV[6];
                                                                              picked_up_status := TG_ARGV[7];
                              
                                          IF parent_table_name IS NULL OR parent_table_name = 'null' THEN parent_table_name := NULL; END IF;
                              IF record_id_column_name IS NULL OR record_id_column_name = 'null' THEN record_id_column_name := 'id'; END IF;
                              IF parent_id_ref_column_name IS NULL OR parent_id_ref_column_name = 'null' THEN parent_id_ref_column_name := 'id'; END IF;
                              IF creator_column_name IS NULL OR creator_column_name = 'null' THEN creator_column_name := 'last_updated_by'; END IF;
                              IF array_fields_delivery IS NULL THEN array_fields_delivery := ARRAY[]::TEXT[]; END IF;
                                          
                                          IF TG_TABLE_NAME = 'orders' AND TG_OP = 'INSERT' THEN event := 'Add'; type := 'Sales Order(s)'; END IF; 
                                          IF TG_TABLE_NAME = 'hand_over' AND TG_OP = 'INSERT' THEN event := 'Create'; type := 'Handover Receipt'; END IF; 
                                          
                              IF EXISTS (
                                  SELECT * 
                                  FROM INFORMATION_SCHEMA.COLUMNS 
                                  WHERE TABLE_NAME = TG_TABLE_NAME 
                                  AND COLUMN_NAME = creator_column_name
                              ) THEN 
                                  IF TG_OP = 'DELETE' 
                                          THEN creator_id := hstore(OLD)->creator_column_name; 
                                          ELSE creator_id := hstore(NEW)->creator_column_name; 
                                  END IF;
                              ELSE
                                  IF creator_id IS NULL 
                                  AND creator_column_name IS NOT NULL
                                  AND parent_table_name IS NOT NULL 
                                  AND parent_id_ref_column_name IS NOT NULL THEN 
                                      EXECUTE 'SELECT ' || QUOTE_IDENT(creator_column_name) || ' "creator_id" FROM ' || QUOTE_IDENT(parent_table_name) || ' WHERE ' || QUOTE_IDENT(parent_id_ref_column_name) || '::TEXT = $1' 
                                      INTO creator_id 
                                      USING hstore(CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END)->parent_id_column_name;
                                  END IF;
                              END IF;
                                          
                                          
                              
                              IF (TG_OP = 'DELETE') THEN 
                                  record_id := hstore(OLD) -> record_id_column_name;
                                  parent_id := hstore(OLD) -> parent_id_column_name;
                                  IF parent_id = 'null' THEN parent_id := NULL; END IF;
                                  
                                  EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, before_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9))' USING parent_table_name, parent_id, TG_OP, TG_TABLE_NAME, record_id, hstore_to_array(hstore(OLD)), creator_id, type, event;
                  
                              ELSE 
                                  record_id := hstore(NEW) -> record_id_column_name;
                                  parent_id := hstore(NEW) -> parent_id_column_name;
                                  IF parent_id = 'null' THEN parent_id := NULL; END IF;
                                                  
                                                  after_changes := hstore(NEW);
                                  IF (TG_OP <> 'INSERT') THEN
                                      before_changes := hstore(OLD);
                                      changes := hstore(NEW) - hstore(OLD);
                                  ELSE
                                      changes := hstore(NEW);
                                  END IF;
                                                  
                                                  IF (TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'hand_over' AND (ARRAY_LENGTH(array_fields_delivery, 1) > 0) AND (changes) ?| array_fields_delivery) THEN 
                                                           
                                                      EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name, parent_id, TG_OP, TG_TABLE_NAME, record_id, hstore_to_array(slice(changes, array_fields_delivery)),hstore_to_array(slice(hstore(NEW), array_fields_delivery)), hstore_to_array(slice(before_changes, array_fields_delivery)), creator_id, 'Change', 'Deliveryman Information';	 
                   
                                                  END IF;
                                                  
                                                  IF (TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'hand_over' AND exist(hstore(NEW), 'status') AND exist(hstore(NEW) - hstore(OLD), 'status')) THEN 
                                                                                                      EXECUTE 'SELECT COUNT(o.id) FROM orders o WHERE o.hand_over_id::TEXT = $1' INTO total_order USING record_id;
      --                                             	RAISE NOTICE 'SELECT COUNT(o.id) FROM orders o WHERE o.hand_over_id::TEXT = $1 GROUP BY o.id %', record_id;
      -- 																							
                                                                                                      IF total_order IS NULL OR total_order = 'null' THEN total_order := 0; END IF;
                                                                                                      RAISE NOTICE 'total_order %', total_order;
                                                                                                      
                                                      IF (NEW.status::TEXT = finish_status) THEN event_status := 'Finish'; type_status := 'Hand over';  ELSE event_status := 'Update status'; type_status := 'Hand over'; END IF;
                                                      
                                                      EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10))' USING parent_table_name, parent_id, 'STATUS', TG_TABLE_NAME, record_id, ARRAY[total_order], ARRAY[total_order], creator_id, type_status, event_status;
                                                  
                                                  END IF;
                                                  
                                                  
                                                  IF (TG_OP = 'INSERT' AND TG_TABLE_NAME = 'hand_over') THEN 
                                                                                              
                                                  EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10))' USING parent_table_name, parent_id, TG_OP, TG_TABLE_NAME, record_id, ARRAY[NEW.total], ARRAY[NEW.total], creator_id, type, event;
                                                   
                                                  END IF;
                                                  
                                                                  
                                  IF (TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND exist(hstore(NEW) - hstore(OLD), parent_id_column_name)) THEN 
                                                   
                                                   type := 'Sales Order(s)';
      -- 																						 RAISE NOTICE 'change %', hstore(NEW) - hstore(OLD);
                                                                                               IF exist(hstore(NEW) - hstore(OLD), 'update_hand_over_at') THEN update_hand_over := NEW.update_hand_over_at; ELSE update_hand_over := NULL; END IF;
                                                                                               
                                                   IF (hstore(NEW)->parent_id_column_name IS NOT NULL) THEN 
                                                                                                      IF (hstore(OLD)->parent_id_column_name IS NOT NULL) THEN 
                                                                                                           EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, before_changes, creator_id, type, event, update_at) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10))' USING parent_table_name, hstore(OLD)->parent_id_column_name, TG_OP, 'order_hand_over', record_id, ARRAY[hstore(OLD)->parent_id_column_name], creator_id, type, 'Remove', update_hand_over; 
                                                      END IF;
                                                      
                                                                                                      event:= 'Add';
                                                                                                      parent_id := hstore(NEW)->parent_id_column_name; 
                                                   ELSE 
                                                                                                      event:= 'Remove'; 
                                                   END IF;
                                                   
                                                   EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, type, event, update_at) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11), ($12))' USING parent_table_name, parent_id, TG_OP, 'order_hand_over', record_id, ARRAY[hstore(NEW)->parent_id_column_name], ARRAY[hstore(NEW)->parent_id_column_name], ARRAY[hstore(OLD)->parent_id_column_name], creator_id, type, event, update_hand_over;
                                                   
                                                  END IF;
                                                  
                                              END IF;
                                               
                                                              IF (TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND exist(hstore(NEW) - hstore(OLD), 'status') AND picked_up_status = NEW.status::TEXT) THEN  
                                                               IF exist(hstore(NEW) - hstore(OLD), 'update_hand_over_at') THEN update_hand_over := NEW.update_hand_over_at; ELSE update_hand_over := NULL; END IF;
                                                                EXECUTE 'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, type, event, update_at) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11), ($12))' USING parent_table_name, parent_id, TG_OP, 'order_hand_over', record_id, ARRAY[NEW.status], ARRAY[NEW.status], ARRAY[OLD.status], creator_id, 'parcels', 'Hand over', update_hand_over; 
                                                              END IF;			
                                                  
                              RETURN NEW;
                          END
                          $BODY$
        LANGUAGE plpgsql VOLATILE
        COST 100`);

        await queryRunner.query(`CREATE TRIGGER "trigger_log_hand_over" AFTER INSERT OR UPDATE OR DELETE ON "public"."hand_over" FOR EACH ROW EXECUTE PROCEDURE "public"."fn_log_hand_over"(NULL, 'last_updated_by', NULL, NULL, '{delivery_man_name,delivery_man_code}', NULL, ${HandOverStatus.handedOver}, ${OrderFFMStatus.PickedUp3PL}); 
        `);

        await queryRunner.query(`CREATE TRIGGER "trigger_log_order_hand_over" AFTER UPDATE OR DELETE ON "public"."orders" FOR EACH ROW EXECUTE PROCEDURE "public"."fn_log_hand_over"('hand_over', 'last_updated_by', 'display_id', 'hand_over_id', '{}', 'id', ${HandOverStatus.handedOver}, ${OrderFFMStatus.PickedUp3PL}); 
        `);

        await queryRunner.query(`
        CREATE OR REPLACE FUNCTION "public"."fn_update_last_status_order"()
        RETURNS "pg_catalog"."trigger" AS $BODY$
                            DECLARE 
                            code varchar;
                                    status_cancel INT;
                                    status_awaiting_pickup INT;
                                    status_collecting INT;
                                    status_handover_new INT;
                                                        status_handovered INT;
                                    total_order INT;
                                    
                            BEGIN 
                                            status_cancel := TG_ARGV[0];   
                                            status_awaiting_pickup := TG_ARGV[1];   
                                            status_collecting := TG_ARGV[2];   
                                            status_handover_new := TG_ARGV[3]; 
                                                                        status_handovered := TG_ARGV[4]; 
                                                
                                            IF (exist(hstore(NEW) - hstore(OLD), 'status')) THEN UPDATE orders o SET last_update_status = current_timestamp WHERE o.id = NEW.id; END IF; 
                                IF (TG_TABLE_NAME = 'orders' AND TG_OP = 'UPDATE') THEN 	
                                                IF (exist(hstore(NEW), 'hand_over_id') AND exist(hstore(NEW) - hstore(OLD), 'status')) THEN 
                                                    IF (NEW.hand_over_id > 0) THEN 
                                                        IF (NEW.status = status_cancel OR NEW.status = status_collecting) 
                                                                                                        THEN 
                                                                                                                EXECUTE 'UPDATE orders o SET hand_over_id = null WHERE o.id = $1' USING NEW.id;
                                                                                                        ELSE
                                                                                                            IF (NEW.status = status_awaiting_pickup) 
                                                                                                                        THEN 
                                                                                                                                EXECUTE 'UPDATE hand_over ho SET status = $1 WHERE ho.id = $2' USING status_handover_new, NEW.hand_over_id;
                                                                                                                        ELSE 
                                                                                                                                total_order := 0;
                                                                        EXECUTE 'SELECT COUNT(*) FROM orders o WHERE o.hand_over_id::INT = $1 AND (o.status = $2 OR o.status = $3)' INTO total_order USING NEW.hand_over_id, status_awaiting_pickup, status_collecting; 
                                                                            IF (total_order = 0) THEN 
                                                                                EXECUTE 'UPDATE hand_over ho SET status = $1 WHERE ho.id = $2' USING status_handovered, NEW.hand_over_id;
                                                                            END IF;
                                                                                                                END IF; 
                                                        END IF; 
                                                        
                                                    END IF; 
                                                END IF; 
                                            END IF; 
                                            RETURN NEW;
                            END;
                            $BODY$
        LANGUAGE plpgsql VOLATILE
        COST 100`);
    }

}
