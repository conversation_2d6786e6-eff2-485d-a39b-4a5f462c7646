import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1749542276945 implements MigrationInterface {
    name = 'migration1749542276945'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ADD "platform" character varying`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ALTER COLUMN "type" SET DEFAULT 'onepage'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" DROP COLUMN "platform"`);
    }

}
