import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1751425128720 implements MigrationInterface {
  name = 'migration1751425128720';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" ADD "return_country_id" character varying`);
    await queryRunner.query(
      `CREATE INDEX "IDX_d3f569145c0e06ec730361288d" ON "orders" ("return_country_id") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d3f569145c0e06ec730361288d"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "return_country_id"`);
  }
}
