import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1749527384867 implements MigrationInterface {
    name = 'migration1749527384867'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ADD "platform_id" character varying(250)`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ADD "platform_name" character varying(250)`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ADD "parent_shop_id" character varying(250)`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ADD "parent_shop_name" character varying(250)`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ALTER COLUMN "type" DROP DEFAULT`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ALTER COLUMN "type" SET DEFAULT 'onepage'`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" DROP COLUMN "parent_shop_name"`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" DROP COLUMN "parent_shop_id"`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" DROP COLUMN "platform_name"`);
        await queryRunner.query(`ALTER TABLE "marketplace_integration" DROP COLUMN "platform_id"`);
    }

}
