import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1688460478187 implements MigrationInterface {
    name = 'migration1688460478187'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "hand_over" ADD "note" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "hand_over" DROP COLUMN "note"`);
    }

}
