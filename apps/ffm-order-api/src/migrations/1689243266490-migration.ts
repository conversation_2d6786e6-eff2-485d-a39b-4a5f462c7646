import { MigrationInterface, QueryRunner } from "typeorm";

export class migration1689243266490 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE 
        OR REPLACE FUNCTION "public"."fn_insert_changes_tags_ticket" ( ) RETURNS "pg_catalog"."trigger" AS $BODY$ DECLARE
        creator_id INT;
    rsId INT;
    BEGIN
        IF
            ( TG_OP = 'DELETE' ) THEN
                rsId = OLD.id_ticket;
            ELSE rsId = NEW.id_ticket;
            
        END IF;
        EXECUTE'SELECT rs.last_updated_by FROM raise_ticket rs WHERE rs.id = $1' INTO creator_id USING rsId;
        IF
            ( TG_OP = 'DELETE' ) THEN
                EXECUTE'INSERT INTO logs (action, table_name, record_id, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING TG_OP,
                'raise_ticket_tag',
                OLD.id_ticket,
                ARRAY [ OLD.id_tag ],
                creator_id,
                'Remove',
                'Tag';
            ELSE EXECUTE'INSERT INTO logs (action, table_name, record_id, changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7))' USING TG_OP,
            'raise_ticket_tag',
            NEW.id_ticket,
            ARRAY [ NEW.id_tag ],
            creator_id,
            'Add',
            'Tag';
            
        END IF;
        RETURN NEW;
    
    END $BODY$ LANGUAGE plpgsql VOLATILE COST 100`);
        await queryRunner.query(`CREATE TRIGGER "trigger_insert_changes_tags_ticket" AFTER INSERT OR UPDATE OR DELETE ON "public"."ticket_tag" FOR EACH ROW EXECUTE PROCEDURE "public"."fn_insert_changes_tags_ticket"();`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TRIGGER trigger_insert_changes_tags_ticket ON ticket_tag`);
        await queryRunner.query(`DROP FUNCTION fn_insert_changes_tags_ticket();`);
        
    }

}
