import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1688615119862 implements MigrationInterface {
    name = 'migration1688615119862'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD "pack_date" TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "pack_date"`);
    }

}
