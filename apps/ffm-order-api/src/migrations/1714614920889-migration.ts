import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1714614920889 implements MigrationInterface {
    name = 'migration1714614920889'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "raise_ticket" DROP CONSTRAINT "UQ_DISPLAY_TYPE_ORDER"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "UQ_DISPLAY_TYPE_ORDER" ON "raise_ticket" ("order_id", "type") WHERE (deleted_at IS NULL)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."UQ_DISPLAY_TYPE_ORDER"`);
        await queryRunner.query(`ALTER TABLE "raise_ticket" ADD CONSTRAINT "UQ_DISPLAY_TYPE_ORDER" UNIQUE ("order_id", "type")`);
    }

}
