import { MigrationInterface, QueryRunner } from 'typeorm';

export class createTriggerLogOrderProductCustomer1748516684000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TRIGGER IF EXISTS trigger_log_notes ON notes`);
    await queryRunner.query(`CREATE TRIGGER "trigger_log_notes" AFTER INSERT OR UPDATE OR DELETE ON "public"."notes" FOR EACH ROW EXECUTE PROCEDURE "public"."fn_log_system"('orders', creator_id, NULL, 'order_id', '{deleted_at,creator_id,id,order_id,created_at,updated_at}', NULL)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TRIGGER IF EXISTS trigger_log_notes ON notes`);
    await queryRunner.query(`CREATE TRIGGER "trigger_log_notes" AFTER INSERT OR UPDATE OR DELETE ON "public"."notes" FOR EACH ROW EXECUTE PROCEDURE "public"."fn_log_system"('orders', NULL, NULL, 'order_id', '{deleted_at,creator_id,id,order_id,created_at,updated_at}', NULL)`);
  }
}
