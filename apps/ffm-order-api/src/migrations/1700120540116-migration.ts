import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1700120540116 implements MigrationInterface {
    name = 'migration1700120540116'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "carrier_configurations" ADD "is_insurance" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "carrier_configurations" ADD "insurance_type" integer`);
        await queryRunner.query(`ALTER TABLE "carrier_configurations" ADD "order_value" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "carrier_configurations" DROP COLUMN "order_value"`);
        await queryRunner.query(`ALTER TABLE "carrier_configurations" DROP COLUMN "insurance_type"`);
        await queryRunner.query(`ALTER TABLE "carrier_configurations" DROP COLUMN "is_insurance"`);
    }

}
