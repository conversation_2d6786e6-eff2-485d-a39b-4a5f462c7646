import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1751356109353 implements MigrationInterface {
  name = 'migration1751356109353';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" ADD "type" integer NOT NULL DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "type"`);
  }
}
