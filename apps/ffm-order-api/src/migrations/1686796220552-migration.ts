import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1686796220552 implements MigrationInterface {
    name = 'migration1686796220552'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_6b31d10a0f9b4db79b0ae248d0" ON "orders" ("external_id", "company_partner_id", "company_id") WHERE (company_id IS NOT NULL and external_id IS NOT NULL and company_partner_id IS NOT NULL)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_6b31d10a0f9b4db79b0ae248d0"`);
    }

}
