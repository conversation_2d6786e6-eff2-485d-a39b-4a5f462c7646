import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1688113407257 implements MigrationInterface {
    name = 'migration1688113407257'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "hand_over" ADD "warehouse_id" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "hand_over" DROP COLUMN "warehouse_id"`);
    }

}
