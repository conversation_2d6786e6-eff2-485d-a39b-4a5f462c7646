import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Max<PERSON>ength,
  Min<PERSON>eng<PERSON>,
  ValidateNested,
} from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { OrderExportEnum } from '../enums/order-search-recipient.enum';
import { TypeChangeWarehouse, TypeOrder, TypeTemplateDownloadOrder } from '../enums/order-status.enum';
import {
  TypeUpdate3PL,
  TypeUpdateOrderStatus,
  TypeUploadOrderInformation,
} from '../enums/type-update-order-status.enum';
import { NoteDto } from './note.dto';
import { TypeOfTagEnum } from '../enums/tag.enum';
import { TagAction } from '../enums/raise-ticket.enum';
import { PostCodeTransform } from 'core/decorators/post-code-transform.decorator';

export class UpdateInternalNoteOrderDto {
  @ApiProperty()
  @IsOptional()
  internalNote: string;
}
export class UpdateMultiInternalNoteDto {
  @ApiProperty()
  @IsNotEmpty()
  internalNote: string;

  @ApiProperty()
  @IsNotEmpty()
  orderIds: number[];
}

export class ProductDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsOptional()
  orderId?: number;

  @ApiProperty()
  @IsNotEmpty()
  productId?: number;

  @ApiProperty()
  @IsNotEmpty()
  productDetail?: any;

  @ApiProperty()
  @IsNotEmpty()
  productName?: string;

  @ApiProperty()
  @IsNotEmpty()
  quantity?: number;

  @ApiProperty()
  @IsNotEmpty()
  price?: number;

  @ApiProperty()
  @IsNotEmpty()
  weight?: number;
}

export class TagDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsOptional()
  creatorId?: number;

  @ApiProperty()
  @IsOptional()
  lastUpdatedBy?: number;

  @ApiProperty()
  @IsNotEmpty()
  content?: string;

  @ApiProperty()
  @IsNotEmpty()
  color?: string;

  @ApiProperty()
  @IsOptional()
  @EnumTransform(TypeOfTagEnum)
  @IsEnum(TypeOfTagEnum, { each: true })
  type?: TypeOfTagEnum;

  @ApiProperty({
    isArray: true,
  })
  @IsNotEmpty()
  @ArrayNotEmpty()
  @EnumTransform(TypeOfTagEnum)
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  modules: TypeOfTagEnum[];

  @ApiProperty()
  @IsOptional()
  note?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsBoolean()
  is3PLTag: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  createdTagAt?: Date;
}

export class UpdateProductDto {
  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  @IsNotEmpty()
  status: OrderFFMStatus;

  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'Products must be an array' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: ProductDto,
    isArray: true,
  })
  @Type(() => ProductDto)
  products: ProductDto[];
}

export class UpdateWarehouseDto {
  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  @IsNotEmpty()
  status: OrderFFMStatus;

  @ApiProperty()
  @IsNotEmpty()
  warehouseId?: number;
}

export class UpdateStatusOutForDeliveryDto {
  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsNotEmpty()
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  status?: OrderFFMStatus;

  @ApiProperty()
  @IsOptional()
  note: string;
}

export class UpdateStatusDto {
  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsNotEmpty()
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  status?: OrderFFMStatus;

  @ApiProperty()
  @IsOptional()
  reasonNote: string;

  @ApiProperty()
  @IsOptional()
  reasonId: number;
}

export class MultiStatusDto {
  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsNotEmpty()
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  status?: OrderFFMStatus;

  @ApiProperty({ required: false, isArray: true })
  // @ValidateNested({ each: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'OrderIds must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  @ArrayMaxSize(1000)
  ids?: number[];

  @ApiProperty({
    required: false,
    enum: TypeUpdateOrderStatus,
  })
  @IsOptional()
  @IsEnum(TypeUpdateOrderStatus)
  @EnumTransform(TypeUpdateOrderStatus)
  type?: TypeUpdateOrderStatus;

  @ApiProperty()
  @IsOptional()
  reasonNote: string;

  @ApiProperty()
  @IsOptional()
  reasonId: number;
}

export class MultiUpdateWarehouseDto {
  @ApiProperty({ required: false, isArray: true })
  // @ValidateNested({ each: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'OrderIds must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  @ArrayMaxSize(1001)
  ids?: number[];

  @ApiProperty()
  @IsNotEmpty()
  warehouseId?: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isWithoutWaybill?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  stillChangeWarehouse?: boolean;

  @ApiProperty({
    enum: TypeChangeWarehouse,
  })
  @IsNotEmpty()
  @IsEnum(TypeChangeWarehouse)
  @EnumTransform(TypeChangeWarehouse)
  typeChangeWarehouse?: TypeChangeWarehouse;
}

export class MultiUpdateTagDto {
  @ApiProperty({ required: false, isArray: true })
  // @ValidateNested({ each: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'OrderIds must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  ids?: number[];

  @ValidateNested({ each: true })
  @ApiProperty({
    type: TagDto,
    isArray: true,
  })
  @Type(() => TagDto)
  @IsOptional()
  tags: TagDto[];

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isKeepTags: boolean;

  @ApiProperty({
    enum: TagAction,
  })
  @IsNotEmpty()
  @IsEnum(TagAction)
  @EnumTransform(TagAction)
  type?: TagAction;
}

export class MultiDownloadDto {
  @ApiProperty({
    required: false,
    enum: TypeTemplateDownloadOrder,
  })
  @IsNotEmpty()
  @IsEnum(TypeTemplateDownloadOrder)
  // @EnumTransform(TypeTemplateDownloadOrder)
  type?: TypeTemplateDownloadOrder;

  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'OrderIds must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  @ArrayMaxSize(5001)
  ids?: number[];

  @ApiProperty({
    required: false,
    enum: OrderExportEnum,
    isArray: true,
  })
  @IsEnum(OrderExportEnum, { each: true })
  @EnumTransform(OrderExportEnum)
  @IsArray()
  @IsOptional()
  columns?: OrderExportEnum[];

  @ApiProperty({
    required: false,
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  titles?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isVietnamese?: boolean;
}

export class CarrierDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsNotEmpty()
  carrierId?: number;

  @ApiProperty()
  @IsOptional()
  orderId?: number;

  @ApiProperty()
  @IsOptional()
  waybillNumber?: string;

  @ApiProperty()
  @IsOptional()
  waybillNote?: string;

  @ApiProperty()
  @DateTransform()
  @IsOptional()
  customerEDD?: Date;

  @ApiProperty()
  @DateTransform()
  @IsOptional()
  carrierEDD?: Date;
}

export class CustomerDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsOptional()
  recipientName?: string;

  @ApiProperty()
  @IsOptional()
  recipientPhone?: string;

  @ApiProperty()
  @IsOptional()
  recipientEmail?: string;

  @ApiProperty()
  @IsOptional()
  recipientAddress?: string;

  @ApiProperty()
  @IsOptional()
  recipientAddressNote?: string;

  @ApiProperty()
  @IsOptional()
  recipientWard?: string;

  @ApiProperty()
  @IsOptional()
  recipientWardId?: string;

  @ApiProperty()
  @IsOptional()
  recipientDistrict?: string;

  @ApiProperty()
  @IsOptional()
  recipientDistrictId?: string;

  @ApiProperty()
  @IsOptional()
  recipientProvince?: string;

  @ApiProperty()
  @IsOptional()
  recipientProvinceId?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  recipientPostCode?: string;

  @ApiProperty()
  @IsOptional()
  recipientCountry?: string;

  @ApiProperty()
  @IsOptional()
  recipientCountryId?: string;

  @ApiProperty()
  @IsOptional()
  creatorId?: number;

  @ApiProperty()
  @IsOptional()
  companyId?: number;
}

export class UpdateCarrierDto {
  @ApiProperty({
    type: CarrierDto,
  })
  @Type(() => CarrierDto)
  @IsNotEmpty()
  carrier?: CarrierDto;

  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  @IsOptional()
  status?: OrderFFMStatus;

  @ApiProperty({
    required: false,
    enum: TypeUpdate3PL,
  })
  @IsOptional()
  @IsEnum(TypeUpdate3PL)
  @EnumTransform(TypeUpdate3PL)
  type?: TypeUpdate3PL;
}

export class UpdateRecipientDto {
  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  @IsNotEmpty()
  status: OrderFFMStatus;

  @ApiProperty({
    type: CustomerDto,
  })
  @Type(() => CustomerDto)
  @IsNotEmpty()
  customer?: CustomerDto;
}

export class CreateOrderDto {
  @ApiProperty()
  @IsOptional()
  clientId?: number;

  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsOptional()
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  status?: OrderFFMStatus;

  @ApiProperty()
  @IsOptional()
  reasonNote: string;

  @ApiProperty()
  @IsOptional()
  reasonId: number;

  @ApiProperty()
  @IsOptional()
  warehouseId?: number;

  @ApiProperty()
  @IsOptional()
  returnWarehouseId?: number;

  @ApiProperty()
  @IsNotEmpty()
  countryId?: string;

  @ApiProperty()
  @IsOptional()
  countryCode?: string;

  @ApiProperty()
  @IsOptional()
  orderCarrierId?: number;

  @ApiProperty()
  @IsOptional()
  discount?: number;

  @ApiProperty()
  @IsOptional()
  surcharge?: number;

  @ApiProperty()
  @IsOptional()
  shippingFee?: number;

  @ApiProperty()
  @IsOptional()
  paid?: number;

  @ApiProperty()
  @IsOptional()
  totalPrice?: number;

  @ApiProperty()
  @IsOptional()
  serviceTLS?: number;

  @ApiProperty()
  @IsOptional()
  serviceCS?: number;

  @ApiProperty()
  @IsOptional()
  serviceFFM?: number;

  @ApiProperty()
  @IsOptional()
  serviceInsurance?: number;

  @ApiProperty()
  @IsOptional()
  subTotal?: number;

  // @IsNotEmpty()
  // @ArrayNotEmpty({ message: 'Products must be an array' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: ProductDto,
    isArray: true,
  })
  @Type(() => ProductDto)
  @IsOptional()
  products: ProductDto[];

  @ValidateNested({ each: true })
  @ApiProperty({
    type: TagDto,
    isArray: true,
  })
  @Type(() => TagDto)
  @IsOptional()
  tags: TagDto[];

  @ValidateNested({ each: true })
  @ApiProperty({
    type: TagDto,
    isArray: true,
  })
  @Type(() => TagDto)
  @IsOptional()
  warehouseTags: TagDto[];

  @ApiProperty({
    type: NoteDto,
  })
  @Type(() => NoteDto)
  @IsOptional()
  note?: NoteDto;

  @ApiProperty({
    type: CarrierDto,
  })
  @Type(() => CarrierDto)
  @IsOptional()
  @ValidateNested()
  carrier?: CarrierDto;

  @ApiProperty({
    type: CustomerDto,
  })
  @Type(() => CustomerDto)
  @IsOptional()
  @ValidateNested()
  customer?: CustomerDto;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  customerEDD?: Date;

  @ApiProperty()
  @IsOptional()
  proofOfChange?: string;

  @ApiProperty()
  @IsOptional()
  externalId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  notInODZ?: boolean;

  @ApiProperty()
  @IsOptional()
  waybillNote?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isIgnoreDuplicate?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isReconciledCod?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isIgnoreNeedRetryTicket?: boolean;

  @ApiProperty({
    required: false,
    enum: TypeOrder,
  })
  @IsOptional()
  @IsEnum(TypeOrder)
  @EnumTransform(TypeOrder)
  type?: TypeOrder;

}

export class OrderNewDto {
  @ApiProperty()
  @IsNotEmpty()
  clientId?: number;

  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsNotEmpty()
  countryId?: string;

  @ApiProperty()
  @IsNotEmpty()
  countryCode?: string;

  @ApiProperty()
  @IsNotEmpty()
  warehouseId?: number;

  @ApiProperty()
  @IsNotEmpty()
  discount?: number;

  @ApiProperty()
  @IsNotEmpty()
  surcharge?: number;

  @ApiProperty()
  @IsNotEmpty()
  shippingFee?: number;

  @ApiProperty()
  @IsNotEmpty()
  paid?: number;

  @ApiProperty()
  @IsNotEmpty()
  totalPrice?: number;

  @ApiProperty()
  @IsNotEmpty()
  serviceTLS?: number;

  @ApiProperty()
  @IsNotEmpty()
  serviceCS?: number;

  @ApiProperty()
  @IsNotEmpty()
  serviceFFM?: number;

  @ApiProperty()
  @IsNotEmpty()
  serviceInsurance?: number;

  @ApiProperty()
  @IsNotEmpty()
  subTotal?: number;

  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'Products must be an array' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: ProductDto,
    isArray: true,
  })
  @Type(() => ProductDto)
  products: ProductDto[];

  @ApiProperty({
    type: NoteDto,
  })
  @Type(() => NoteDto)
  @IsOptional()
  note?: NoteDto;

  @ApiProperty({
    type: CarrierDto,
  })
  @Type(() => CarrierDto)
  @IsOptional()
  carrier?: CarrierDto;

  @ApiProperty({
    type: CustomerDto,
  })
  @Type(() => CustomerDto)
  @IsNotEmpty()
  customer?: CustomerDto;

  @ApiProperty()
  @IsOptional()
  externalId?: string;
}

export class MultiOrderDto {
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'Orders must be an array' })
  @ValidateNested({ each: true })
  @ArrayMaxSize(20)
  @ApiProperty({
    type: OrderNewDto,
    isArray: true,
  })
  @Type(() => OrderNewDto)
  data: OrderNewDto[];
}
export class UpdateOrderDto extends PartialType(CreateOrderDto) {}

export class UpdateOrderHandOverDto {
  @ApiProperty()
  @IsNotEmpty()
  warehouseId?: number;

  @ApiProperty()
  @IsNotEmpty()
  search?: string;

  @ApiProperty()
  @IsOptional()
  handOverId?: number;

  @ApiProperty({
    required: false,
    enum: TypeUpdateOrderStatus,
  })
  @IsOptional()
  @IsEnum(TypeUpdateOrderStatus)
  @EnumTransform(TypeUpdateOrderStatus)
  type?: TypeUpdateOrderStatus;
}

export class ScanWaybill {
  @ApiProperty()
  @IsNotEmpty()
  search?: string;

  @ApiProperty()
  @IsOptional()
  handOverId?: number;

  @ApiProperty()
  @IsOptional()
  warehouseId?: number;

  @ApiProperty()
  @IsOptional()
  carrierId?: number;

  @ApiProperty({
    required: false,
    enum: TypeUpdateOrderStatus,
  })
  @IsOptional()
  @IsEnum(TypeUpdateOrderStatus)
  @EnumTransform(TypeUpdateOrderStatus)
  type?: TypeUpdateOrderStatus;
}

export class RevertOrder {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'soCode must be an array' })
  @ArrayTransform()
  @ArrayMaxSize(1000)
  soCode?: string[];
}

export class SupportChangeOrder {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'soCode must be an array' })
  @ArrayTransform()
  @ArrayMaxSize(1000)
  soCode?: string[];

  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  @IsNotEmpty()
  status: OrderFFMStatus;
}

export class SupportDuplicateOrder {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'soCode must be an array' })
  @ArrayTransform()
  @ArrayMaxSize(1000)
  soCode?: string[];
}

export class UploadOrderInformation {
  @ApiProperty({
    required: false,
    enum: TypeUploadOrderInformation,
  })
  @IsEnum(TypeUploadOrderInformation)
  @EnumTransform(TypeUploadOrderInformation)
  @IsNotEmpty()
  type: TypeUploadOrderInformation;
}

export class PrintWaybillDto {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'soCodes must be an array' })
  @ArrayTransform()
  @ArrayMaxSize(100)
  soCodes?: string[];

  @ApiProperty()
  @IsNotEmpty()
  carrierOrderId?: number;
}

export class DataVTPDto {
  @ApiProperty()
  @IsOptional()
  ORDER_NUMBER?: string;

  @ApiProperty()
  @IsOptional()
  ORDER_REFERENCE?: string;

  @ApiProperty()
  @IsOptional()
  ORDER_STATUS?: string;

  @ApiProperty()
  @IsOptional()
  STATUS_NAME?: string;

  @ApiProperty()
  @IsOptional()
  NOTE?: string;
}

export class WebhookVTPDto {
  @ApiProperty()
  @IsOptional()
  DATA?: DataVTPDto;

  @ApiProperty()
  @IsOptional()
  TOKEN?: string;
}

export class WebhookVNPDto {
  @ApiProperty()
  @IsOptional()
  data?: DataVNPDto[];

  @ApiProperty()
  @IsOptional()
  sendDate?: Date;
}

export class WebhookSPXDto {
  @ApiProperty()
  @IsOptional()
  status?: string;

  @ApiProperty()
  @IsOptional()
  status_code?: string;

  @ApiProperty()
  @IsOptional()
  message?: string;

  @ApiProperty()
  @IsOptional()
  tracking_no?: any;

  @ApiProperty()
  @IsOptional()
  tracking_link?: string;

  @ApiProperty()
  @IsOptional()
  order_id?: string;

  @ApiProperty()
  @IsOptional()
  order_id_link?: string;

  @ApiProperty()
  @IsOptional()
  timestamp: number;

  @ApiProperty()
  @IsOptional()
  latest_shipping_fee?: number;

  @ApiProperty()
  @IsOptional()
  latest_chargeable_weight?: string;

  @ApiProperty()
  @IsOptional()
  cod_amount?: number;

  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsOptional()
  driver_phone_number?: string;
}

export class DataVNPDto {
  @ApiProperty()
  @IsOptional()
  undeliverableReason?: string;

  @ApiProperty()
  @IsOptional()
  itemCode?: string;

  @ApiProperty()
  @IsOptional()
  status?: string;

  @ApiProperty()
  @IsOptional()
  saleOrderCode?: string;

  @ApiProperty()
  @IsOptional()
  statusTime?: Date;
}

export class WebhookGHTKDto {
  @ApiProperty()
  @IsOptional()
  partner_id?: string;

  @ApiProperty()
  @IsOptional()
  label_id?: string;

  @ApiProperty()
  @IsOptional()
  status_id?: number;

  @ApiProperty()
  @IsOptional()
  action_time?: Date;

  @ApiProperty()
  @IsOptional()
  reason_code?: string;

  @ApiProperty()
  @IsOptional()
  reason?: string;

  @ApiProperty()
  @IsOptional()
  weight?: number;

  @ApiProperty()
  @IsOptional()
  fee?: number;

  @ApiProperty()
  @IsOptional()
  pick_money?: number;

  @ApiProperty()
  @IsOptional()
  return_part_package?: number;
}
export class WebhookJNTThailandDto {
  @ApiProperty({})
  @IsOptional()
  DATA: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  TOKEN?: string;
}

export class EditOrderCODDto {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'soCode must be an array' })
  @ArrayTransform()
  @ArrayMaxSize(1000)
  soCode?: string[];
}

export class ExpeditedReconciliationDto {
  @IsOptional()
  @ApiProperty({})
  qualified: Record<string, any>[];

  @IsOptional()
  @ApiProperty({})
  warning: Record<string, any>[];
}

export class CheckSyncStatusDto {
  @ApiProperty()
  @IsOptional()
  ids?: number[] | string[];

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  time?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;
}
