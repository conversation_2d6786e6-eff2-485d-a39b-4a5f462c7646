import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayNotEmpty,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
} from 'class-validator';
import {
  MethodTicket,
  RaiseTicketNoteEnum,
  RaiseTicketStatusEnum,
  RaiseTicketTypeEnum,
  TagAction,
  TicketAppointmentStatusEnum,
  TypeAssigneeTicket,
} from '../enums/raise-ticket.enum';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { Type } from 'class-transformer';
import { TypeOfTagEnum } from '../enums/tag.enum';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';

export class CreateRaiseTicketDto {
  @ApiProperty()
  @IsNotEmpty()
  orderId?: number;

  @ApiProperty({
    enum: RaiseTicketStatusEnum,
    default: RaiseTicketStatusEnum.New,
  })
  @IsOptional()
  @IsEnum(RaiseTicketStatusEnum)
  @EnumTransform(RaiseTicketStatusEnum)
  status?: RaiseTicketStatusEnum;

  @ApiProperty({
    enum: RaiseTicketTypeEnum,
  })
  @IsOptional()
  @IsEnum(RaiseTicketTypeEnum)
  @EnumTransform(RaiseTicketTypeEnum)
  type?: RaiseTicketTypeEnum;

  @ApiProperty({ required: false })
  @IsNumber(undefined, { each: true })
  @IsOptional()
  assignee?: number;
}

export class BulkRaiseTicketDto {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'OrderIds must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  @ArrayMaxSize(1000)
  ids?: number[];

  @ApiProperty({
    enum: RaiseTicketStatusEnum,
    default: RaiseTicketStatusEnum.New,
  })
  @IsOptional()
  @IsEnum(RaiseTicketStatusEnum)
  @EnumTransform(RaiseTicketStatusEnum)
  status?: RaiseTicketStatusEnum;

  @ApiProperty({
    enum: RaiseTicketTypeEnum,
  })
  @IsOptional()
  @IsEnum(RaiseTicketTypeEnum)
  @EnumTransform(RaiseTicketTypeEnum)
  type?: RaiseTicketTypeEnum;

  @ApiProperty({
    enum: MethodTicket,
  })
  @IsOptional()
  @IsEnum(MethodTicket)
  @EnumTransform(MethodTicket)
  methodTicket?: MethodTicket;
}

export class TicketTagDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsOptional()
  creatorId?: number;

  @ApiProperty()
  @IsOptional()
  lastUpdatedBy?: number;

  @ApiProperty()
  @IsNotEmpty()
  content?: string;

  @ApiProperty()
  @IsNotEmpty()
  color?: string;

  @ApiProperty()
  @IsOptional()
  @EnumTransform(TypeOfTagEnum)
  @IsEnum(TypeOfTagEnum, { each: true })
  type?: TypeOfTagEnum;

  @ApiProperty({
    isArray: true,
  })
  @IsNotEmpty()
  @ArrayNotEmpty()
  @EnumTransform(TypeOfTagEnum)
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  modules: TypeOfTagEnum[];

  @ApiProperty()
  @IsOptional()
  note?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsBoolean()
  is3PLTag: boolean;
}

export class UpdateRaiseTicketDto {
  @ApiProperty({ required: false })
  @IsNumber(undefined, { each: true })
  @IsOptional()
  assignee?: number;

  @ApiProperty({
    enum: RaiseTicketStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(RaiseTicketStatusEnum)
  @EnumTransform(RaiseTicketStatusEnum)
  status?: RaiseTicketStatusEnum;

  @ApiProperty({
    required: false,
    enum: RaiseTicketTypeEnum,
  })
  @IsOptional()
  @IsEnum(RaiseTicketTypeEnum)
  @EnumTransform(RaiseTicketTypeEnum)
  type?: RaiseTicketTypeEnum;

  @IsOptional()
  @ApiProperty({
    type: TicketTagDto,
    isArray: true,
  })
  @Type(() => TicketTagDto)
  tags?: TicketTagDto[];
}
export class CloseRaiseTicketDto {
  @ApiProperty({
    enum: RaiseTicketStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(RaiseTicketStatusEnum)
  @EnumTransform(RaiseTicketStatusEnum)
  status?: RaiseTicketStatusEnum;

  @ApiProperty()
  @IsOptional()
  cancelReason?: string;
}

export class UpdateBulkRaiseTicketDto {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'ids must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  @ArrayMaxSize(1000)
  ids?: number[];

  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'assigneeIds must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  assigneeIds?: number[];

  @ApiProperty({
    enum: TypeAssigneeTicket,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsEnum(TypeAssigneeTicket)
  @EnumTransform(TypeAssigneeTicket)
  type?: TypeAssigneeTicket;
}

export class DeleteBulkRaiseTicketDto {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'ids must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, {each: true})
  @ArrayMaxSize(1000)
  ids?: number[];
}

export class UpdateTagBulkRaiseTicketDto {
  @ApiProperty({ required: false, isArray: true })
  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'ids must be an array' })
  @ArrayTransform()
  @IsNumber(undefined, { each: true })
  @ArrayMaxSize(1000)
  ids?: number[];

  @IsNotEmpty()
  @ApiProperty({
    type: TicketTagDto,
    isArray: true,
  })
  @ArrayNotEmpty({ message: 'tags must be an array' })
  @Type(() => TicketTagDto)
  tags?: TicketTagDto[];

  @ApiProperty({ required: true, default: false })
  @IsNotEmpty()
  isKeepOldTag?: boolean;

  @ApiProperty({
    enum: TagAction,
  })
  @IsNotEmpty()
  @IsEnum(TagAction)
  @EnumTransform(TagAction)
  type?: TagAction;
}

export class CreateRaiseTicketNoteDto {
  @ApiProperty()
  @IsNotEmpty()
  ticketId?: number;

  @ApiProperty()
  @IsOptional()
  content?: string;

  @ApiProperty({
    enum: RaiseTicketNoteEnum,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsEnum(RaiseTicketNoteEnum)
  @EnumTransform(RaiseTicketNoteEnum)
  type?: RaiseTicketNoteEnum;
}

export class CreateRaiseTicketAppointmentDto {
  @ApiProperty()
  @IsNotEmpty()
  ticketId?: number;

  @ApiProperty()
  @DateTransform()
  @IsNotEmpty()
  appointmentSchedule?: Date;

  @ApiProperty()
  @IsNotEmpty()
  content?: string;
}
export class UpdateRaiseTicketAppointmentDto {
  @ApiProperty()
  @IsNotEmpty()
  id?: number;

  @ApiProperty({
    enum: TicketAppointmentStatusEnum,
  })
  @IsOptional()
  @IsEnum(TicketAppointmentStatusEnum)
  @EnumTransform(TicketAppointmentStatusEnum)
  status?: TicketAppointmentStatusEnum;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isDelete?: boolean;
}