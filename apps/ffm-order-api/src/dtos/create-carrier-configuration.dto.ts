import {ApiProperty} from '@nestjs/swagger';
import {IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength,} from 'class-validator';
import {EnumTransform} from 'core/decorators/enum-transform.decorator';
import {CarrierCode} from "core/enums/carrier-code.enum";
import { InsuranceType } from '../enums/carrier-configuration.enum';

export class CreateCarrierConfigurationDTO {
  @ApiProperty({required: true, enum: CarrierCode})
  @IsEnum(CarrierCode)
  @EnumTransform(CarrierCode)
  carrierCode: CarrierCode;

  @ApiProperty({required: true})
  @IsString()
  carrierId: string;

  @ApiProperty({required: true})
  @IsNumber()
  countryId: number;

  @ApiProperty({required: true})
  @IsNotEmpty()
  extraData: Record<string, any>;

  @ApiProperty({required: false})
  @IsOptional()
  @IsBoolean()
  isDefault: boolean;

  @ApiProperty({required: false})
  @IsString()
  @IsOptional()
  note: string;

  @ApiProperty({required: false})
  @IsString()
  @IsOptional()
  @MaxLength(50)
  displayName: string;
  
  @ApiProperty({required: false})
  @IsOptional()
  @IsBoolean()
  isInsurance: boolean;

  @ApiProperty({ enum: InsuranceType})
  @IsOptional()
  @IsEnum(InsuranceType)
  @EnumTransform(InsuranceType)
  insuranceType: InsuranceType;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  orderValue: number;
}