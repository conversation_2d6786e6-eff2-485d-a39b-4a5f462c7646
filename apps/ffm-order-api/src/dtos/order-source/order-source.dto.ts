import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayNotEmpty,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { Type } from 'class-transformer';
import { StatusMIEnum, TypeMIEnum, TypePlatformEnum } from '../../enums/marketplace-integration.enum';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';

export class CreateOrderSourceDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsOptional()
  shopUrl: string;

  @ApiProperty()
  @IsOptional()
  apiKey: string;

  @ApiProperty()
  @IsNotEmpty()
  countryId: string;

  @ApiProperty()
  @IsNotEmpty()
  clientId: number;

  @ApiProperty()
  @IsOptional()
  @EnumTransform(TypeMIEnum)
  @IsEnum(TypeMIEnum, { each: true })
  type?: TypeMIEnum;

  @ApiProperty()
  @IsOptional()
  @EnumTransform(StatusMIEnum)
  @IsEnum(StatusMIEnum, { each: true })
  status?: StatusMIEnum;
  
  @ApiProperty({required: false})
  @IsOptional()
  @IsBoolean()
  isSyncOrder: boolean;

  @ValidateIf(o => o.type === TypeMIEnum.miaoshou)
  @IsString()
  @IsNotEmpty()
  shopId: string;
  
  @ValidateIf(o => o.type === TypeMIEnum.miaoshou)
  @IsString()
  @IsNotEmpty()
  shopName: string;

  @ValidateIf(o => o.type === TypeMIEnum.miaoshou)
  @IsString()
  @IsNotEmpty()
  platformId: string;

  @ValidateIf(o => o.type === TypeMIEnum.miaoshou)
  @IsString()
  @IsNotEmpty()
  platformName: string;

  @ValidateIf(o => o.type === TypeMIEnum.miaoshou)
  @IsNotEmpty()
  @EnumTransform(TypePlatformEnum)
  @IsEnum(TypePlatformEnum)
  platform: TypePlatformEnum;

  @ApiProperty()
  @IsOptional()
  parentShopId: string;

  @ApiProperty()
  @IsOptional()
  parentShopName: string;
}

export class UpdateOrderSourceDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsNotEmpty()
  countryId: string;

  @ApiProperty()
  @IsNotEmpty()
  clientId: number;

  @ApiProperty()
  @IsOptional()
  @EnumTransform(StatusMIEnum)
  @IsEnum(StatusMIEnum, { each: true })
  status?: StatusMIEnum;
}
