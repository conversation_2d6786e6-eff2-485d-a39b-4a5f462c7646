import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayNotEmpty,
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  MaxLength,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { OrderExportEnum } from '../enums/order-search-recipient.enum';
import { ExternalOrderType, TypeOrder, TypeTemplateDownloadOrder } from '../enums/order-status.enum';
import { CarrierCode } from 'core/enums/carrier-code.enum';
import { PostCodeTransform } from 'core/decorators/post-code-transform.decorator';

export class NoteDto {
  @ApiProperty()
  @IsNotEmpty()
  content: string;
}

export class TagAgDto {
  @ApiProperty()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsOptional()
  id: string;
}

export class ProductComboItem {
  @ApiProperty()
  @IsOptional()
  sku?: string;

  @ApiProperty()
  @IsOptional()
  quantity?: number;
}

export class ProductDto {
  @ApiProperty()
  @IsNotEmpty()
  sku: number;

  @ApiProperty()
  @IsNotEmpty()
  quantity: number;

  @ApiProperty()
  @IsNotEmpty()
  price: number;

  @ApiProperty()
  @IsNotEmpty()
  weight: number;

  @ApiProperty()
  @IsOptional()
  productId?: number;

  @ApiProperty()
  @IsOptional()
  productDetail?: string;

  @ApiProperty()
  @IsOptional()
  productName?: string;

  @ApiProperty()
  @IsOptional()
  isCombo?: boolean;

  @IsOptional()
  @ArrayNotEmpty({ message: 'comboItems must be an array' })
  @ApiProperty({
    type: ProductComboItem,
    isArray: true,
  })
  @Type(() => ProductComboItem)
  comboItems?: ProductComboItem[];
}

export class CarrierDto {
  @ApiProperty()
  @IsOptional()
  carrierId?: number;

  @ApiProperty()
  @IsNotEmpty()
  carrierCode?: string;

  @ApiProperty()
  @IsOptional()
  waybillNumber?: string;

  @ApiProperty()
  @IsOptional()
  waybillNote?: string;

  @ApiProperty()
  @DateTransform()
  @IsOptional()
  customerEDD?: Date;
}

export class CustomerDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsNotEmpty()
  name?: string;

  @ApiProperty()
  @IsNotEmpty()
  phone?: string;

  @ApiProperty()
  @IsNotEmpty()
  address?: string;

  @ApiProperty()
  @IsNotEmpty()
  ward?: string;

  @ApiProperty()
  @IsOptional()
  district?: string;

  @ApiProperty()
  @IsOptional()
  province?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  postCode?: string;
}

export class SyncStatusOrderDto {
  @ApiProperty({
    required: false,
    enum: OrderFFMStatus,
  })
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  @IsNotEmpty()
  status: OrderFFMStatus;

  @ApiProperty()
  @IsNotEmpty()
  so?: string;

  @ApiProperty()
  @IsNotEmpty()
  external?: string;
}

export class HvnetCancelStatusDto {
  @ApiProperty()
  @IsNotEmpty()
  so?: string;

  @ApiProperty()
  @IsNotEmpty()
  external?: string;
}

export class HvnetSyncTagsDto {
  @ApiProperty()
  @IsNotEmpty()
  so?: string;

  @ApiProperty()
  @IsNotEmpty()
  external?: string;
  
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @ApiProperty({
    type: TagAgDto,
    isArray: true,
  })
  @Type(() => TagAgDto)
  tags?: TagAgDto[];
}
export class SyncManualDto {
  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ApiProperty({
    required: false,
    enum: ExternalOrderType,
  })
  @IsEnum(ExternalOrderType)
  @EnumTransform(ExternalOrderType)
  @IsNotEmpty()
  type: ExternalOrderType;

  @ApiProperty()
  @IsOptional()
  displayIds?: string;
}
export class SyncOrderDto {
  @ApiProperty()
  @IsNotEmpty()
  clientId?: number;

  @ApiProperty()
  @IsOptional()
  seller?: string;

  @ApiProperty()
  @IsOptional()
  displayId?: string;

  @ApiProperty()
  @IsNotEmpty()
  countryId?: string;

  @ApiProperty()
  @IsNotEmpty()
  discount?: number;

  @ApiProperty()
  @IsNotEmpty()
  surcharge?: number;

  @ApiProperty()
  @IsNotEmpty()
  shippingFee?: number;

  @ApiProperty()
  @IsNotEmpty()
  paid?: number;

  @ApiProperty()
  @IsNotEmpty()
  subTotal?: number;

  @ApiProperty()
  @IsOptional()
  internalNote?: string;

  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'Products must be an array' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: ProductDto,
    isArray: true,
  })
  @Type(() => ProductDto)
  products: ProductDto[];

  @ApiProperty({
    type: NoteDto,
  })
  @Type(() => NoteDto)
  @IsOptional()
  note?: NoteDto;

  @ApiProperty({
    type: CarrierDto,
  })
  @Type(() => CarrierDto)
  @IsOptional()
  carrier?: CarrierDto;

  @ApiProperty({
    type: CustomerDto,
  })
  @Type(() => CustomerDto)
  @IsNotEmpty()
  customer: CustomerDto;

  @ApiProperty()
  @IsNotEmpty()
  externalId: string;

  @ApiProperty()
  @DateTransform()
  @IsOptional()
  customerEDD?: Date;

  @IsOptional()
  @ApiProperty({
    type: TagAgDto,
    isArray: true,
  })
  @Type(() => TagAgDto)
  tags?: TagAgDto[];

  @ApiProperty({
    required: false,
    enum: ExternalOrderType,
  })
  @IsEnum(ExternalOrderType)
  @EnumTransform(ExternalOrderType)
  @IsOptional()
  type?: ExternalOrderType;

  @ApiProperty({
    required: false,
    enum: CarrierCode,
  })
  @IsEnum(CarrierCode)
  @EnumTransform(CarrierCode)
  @IsOptional()
  carrierCode?: CarrierCode;

  @ApiProperty()
  @IsOptional()
  waybillNumber?: string;

  @ApiProperty()
  @IsOptional()
  waybillNote?: string;

  @ApiProperty()
  @IsOptional()
  orderSource?: string;

  @ApiProperty()
  @IsOptional()
  waybillURL?: string;

  @ApiProperty()
  @IsOptional()
  projectId?: number;

  @ApiProperty()
  @IsOptional()
  projectName?: string;

  @ApiProperty()
  @IsOptional()
  domainName?: string;

  @ApiProperty({
    required: false,
    enum: TypeOrder,
  })
  @IsEnum(TypeOrder)
  @EnumTransform(TypeOrder)
  @IsOptional()
  typeOrder?: TypeOrder;
}
