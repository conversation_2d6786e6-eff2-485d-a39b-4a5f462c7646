import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { CarrierCode } from 'core/enums/carrier-code.enum';
import { TypeUpdate3PL } from '../enums/type-update-order-status.enum';
import { PostCodeTransform } from 'core/decorators/post-code-transform.decorator';
import { Order } from '../entities/order.entity';

export class CreateOrderPartnerDto {
  @ApiProperty({ required: true, enum: CarrierCode })
  @IsEnum(CarrierCode)
  @EnumTransform(CarrierCode)
  carrierCode: CarrierCode;

  @ApiProperty({ required: true })
  @IsNumber()
  orderId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  keyId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  channelCode: string;

  @ApiProperty({ required: false, enum: TypeUpdate3PL })
  @IsOptional()
  @IsEnum(TypeUpdate3PL)
  @EnumTransform(TypeUpdate3PL)
  type?: TypeUpdate3PL;

  @ApiProperty()
  @IsOptional()
  province?: string;

  @ApiProperty()
  @IsOptional()
  district?: string;

  @ApiProperty()
  @IsOptional()
  ward?: string;

  @ApiProperty()
  @IsOptional()
  provinceId?: string;

  @ApiProperty()
  @IsOptional()
  districtId?: string;

  @ApiProperty()
  @IsOptional()
  wardId?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  postCode?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  postCodeId?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  partialDelivery?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  shippingMethod?: number;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  requiredUponDelivery?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  orderCancellationFeeCharged?: string;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  exchangeOrReturn?: number;

  @ApiProperty()
  @IsOptional()
  @PostCodeTransform()
  allowMutualCheck?: number;

  @ApiProperty()
  @IsOptional()
  waybillNote?: string;
}

export class CancelAnywayDto {
  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({ required: false, enum: TypeUpdate3PL })
  @IsNotEmpty()
  @IsEnum(TypeUpdate3PL)
  @EnumTransform(TypeUpdate3PL)
  type?: TypeUpdate3PL;
}

export class CorrectionOrderDto {
  @ApiProperty({ required: false })
  @IsOptional()
  newOrder: Order;

  @ApiProperty({ required: false })
  @IsOptional()
  oldOrder: Order;
}
