import { ApiProperty } from '@nestjs/swagger';
import {
    ArrayNotEmpty,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class ProductDto {
  @ApiProperty()
  @IsNotEmpty()
  sku: string;

  @ApiProperty()
  @IsNotEmpty()
  quantity: number;

  @ApiProperty()
  @IsNotEmpty()
  price: number;
}

export class CreateOrderDto {
    @ApiProperty()
    @IsNotEmpty()
    countryId?: number;

    @ApiProperty()
    @IsNotEmpty()
    shopId?: number;

    @ApiProperty()
    @IsNotEmpty()
    discount?: number;

    @ApiProperty()
    @IsNotEmpty()
    totalPrice?: number;

    @ApiProperty()
    @IsNotEmpty()
    surcharge?: number;

    @ApiProperty()
    @IsNotEmpty()
    shippingFee?: number;

    @ApiProperty()
    @IsNotEmpty()
    paid?: number;

    @ApiProperty()
    @IsNotEmpty()
    subTotal?: number;

    @IsNotEmpty()
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @ApiProperty({
        type: ProductDto,
        isArray: true,
    })
    @Type(() => ProductDto)
    products: ProductDto[];

    @ApiProperty()
    @IsNotEmpty()
    externalId: string;

    @ApiProperty()
    @IsNotEmpty()
    externalProjectId?: number;

    @ApiProperty()
    @IsNotEmpty()
    externalProjectName?: string;
    
    @ApiProperty()
    @IsOptional()
    recipientAddress?: string;

    @ApiProperty()
    @IsNotEmpty()
    recipientCountry?: string;

    @ApiProperty()
    @IsOptional()
    recipientDistrict?: string;

    @ApiProperty()
    @IsNotEmpty()
    recipientName?: string;

    @ApiProperty()
    @IsNotEmpty()
    recipientPhone?: string;

    @ApiProperty()
    @IsOptional()
    recipientPostCode?: string;

    @ApiProperty()
    @IsNotEmpty()
    recipientProvince?: string;

    @ApiProperty()
    @IsOptional()
    recipientWard?: string;
}