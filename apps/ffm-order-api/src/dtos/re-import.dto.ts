import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ArrayNotEmpty, IsEnum, IsNotEmpty, IsOptional, ValidateNested } from "class-validator";
import { EnumTransform } from "core/decorators/enum-transform.decorator";
import { OrderFFMStatus } from "core/enums/order-ffm-status.enum";
import { ReImportTypeEnum } from "../enums/re-import.enum";

export class ProductItemDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsOptional()
  orderId?: number;

  @ApiProperty()
  @IsOptional()
  itemId?: number;

  @ApiProperty()
  @IsNotEmpty()
  productId?: number;

  @ApiProperty()
  @IsNotEmpty()
  good?: number;

  @ApiProperty()
  @IsNotEmpty()
  damaged?: number;

  @ApiProperty()
  @IsNotEmpty()
  damagedBy3pl?: number;

  @ApiProperty()
  @IsNotEmpty()
  lost?: number;

  @ApiProperty()
  @IsNotEmpty()
  lostBy3pl?: number;

  @ApiProperty()
  @IsNotEmpty()
  needed?: number;

  @ApiProperty()
  @IsOptional()
  properties?: string;

}

export class CreateReImportDto {
  @ApiProperty()
  @IsNotEmpty()
  orderId?: number;
  
  @ApiProperty()
  @IsOptional()
  @IsEnum(OrderFFMStatus)
  @EnumTransform(OrderFFMStatus)
  statusBeforeChanged?: OrderFFMStatus;

  @IsNotEmpty()
  @ArrayNotEmpty({ message: 'Products must be an array' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: ProductItemDto,
    isArray: true
  })
  @Type(() => ProductItemDto)
  products: ProductItemDto[];

  @ApiProperty({
    required: false,
    enum: ReImportTypeEnum,
  })
  // @IsOptional()
  @IsNotEmpty()
  @IsEnum(ReImportTypeEnum)
  @EnumTransform(ReImportTypeEnum)
  type?: ReImportTypeEnum;
}  
