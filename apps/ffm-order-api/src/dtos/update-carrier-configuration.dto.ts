import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { CommonStatus } from 'core/enums/common-status.enum';
import { CarrierCode } from 'core/enums/carrier-code.enum';
import { InsuranceType } from '../enums/carrier-configuration.enum';

export class UpdateCarrierConfigurationDto {
  @ApiProperty({ required: true, enum: CarrierCode })
  @IsEnum(CarrierCode)
  @EnumTransform(CarrierCode)
  carrierCode: CarrierCode;

  @ApiProperty({ required: true })
  @IsString()
  carrierId: string;

  @ApiProperty({ required: true })
  @IsString()
  channelCode: string;

  @ApiProperty({ required: true })
  @IsNumber()
  countryId: number;

  @ApiProperty({ required: false, enum: CommonStatus })
  @IsEnum(CommonStatus)
  @EnumTransform(CommonStatus)
  @IsOptional()
  status: CommonStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isDefault: boolean;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  note: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  displayName: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isInsurance: boolean;

  @ApiProperty({ enum: InsuranceType })
  @IsOptional()
  @IsEnum(InsuranceType)
  @EnumTransform(InsuranceType)
  insuranceType: InsuranceType;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  orderValue: number;
  
    
  @ApiProperty({required: true})
  @IsNotEmpty()
  extraData: Record<string, any>;
}
