export enum StatusMIEnum {
  connected = 1,
  disconnected = 2,
  failedToConnect = 3,
}

export enum TypeMIEnum {
  onepage = "onepage",
  lazada = "lazada",
  amazon = "amazon",
  wooCommerce = "wooCommerce",
  shopee = "shopee",
  tiktok = "tiktok",
  miaoshou = "miaoshou"
}

export enum TypePlatformEnum {
  shopee = TypeMIEnum.shopee,
  tiktok = TypeMIEnum.tiktok
}

export enum TypeMIFailEnum {
  ProductsNotExists = 1,
  LocaltionNotExists = 2,
  ContactNotExists = 3,
}
