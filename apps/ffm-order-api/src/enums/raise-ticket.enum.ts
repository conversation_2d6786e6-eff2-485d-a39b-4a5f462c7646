export enum RaiseTicketStatusEnum {
  New = 1, //Những ticket mới, chưa đư<PERSON><PERSON> gán ngườ<PERSON> xử lý.
  Assigned = 2, //Ticket đã gán người xử lý.
  InProcess = 3, //Ticket đang được xử lý.
  FollowUp = 4, //Ticket đã được xử lý, đang trong quá trình theo dõi.
  Resolved = 5, //Ticket đã được xử lý thành công và đơn hàng đã đến trạng thái cuối.
  Closed = 6, // Những ticket PT gặp vấn đề không tiếp tục xử lý
  'Re-Open' = 7, //Ticket resolved/ closed nhưng bị mở lại, chưa có người xử lý.
}

export enum RaiseTicketNoteEnum {
  NoRespond = 1,
  SuccessfullyInformedCx = 2,
  ChangeRequireDeliveryTime = 3,
  CoordinatedWithRider = 4,
  CoordinatedWith3PLCS = 5,
  CanNotContactRider = 6,
  CanNotCall = 7,
  ArriveEarly = 8,
  PoorNetwork = 9,
  RiderIsFault = 10,
  WrongCode = 11,
  TooLongToWait = 12,
  CxNotAroundOutOfTown = 13,
  DenyOrder = 14,
  DoubleOrder = 15,
  BuyInAnotherShop = 16,
  InsistOnOpening = 17,
  ODZ = 18,
  OutOfBudget = 19,
  NotAsExpected = 20,
  IncorrectNumber = 21,
  NoOrder = 22,
  WrongAddress = 23,
  WrongCOD = 24,
  Emergency = 25,
  Deposit = 26,
  PTsFault = 27,
  CussFault = 28,
  AlreadyFiledAsClaim = 29,
  ClaimApproved = 30,
  ClaimRejected = 31,
  AlreadyCompensated = 32,
  //---------------------------------------------
  OutOfMoney = 33,
  NotAtHomeOutOfTown = 34,
  NotAnswer = 35,
  JokingBoomDenyOrder = 36,
  OutOfConnection = 37,
  NotAsAds = 38,
  WaitTooLong = 39,
  WrongInfo = 40,
  RequestCocheck = 41,
  CannotContactRider = 42,
  DuplicatedOrder = 43,
  NoOrderWOEvidence = 44,
  CanceledBeforeHandOver = 45,
  CanceledAfterHandOver = 46,
  WrongDeliverySchedule = 47,
  WrongAdvice = 48,
  NoDelivery = 49,
  RudeUncooperativeRider = 50,
  OutOfDeliveryZone = 51,
  IncorrectTrackingNumber3PLODZ = 52,
  PackedWrongProductModelquantity = 53,
  ImproperlyPackaged = 54,
  ProductIsDefective = 55,
  LostSwappedGoods = 56,
  NotNotifyBuyer = 57,
  SuccessfullyInformedBuyer = 58,
  LostDamaged = 59,
  AlreadyClaimed = 60,
  AlreadyReimbursed = 61,
  CxDidNotPickUp = 62,
}

export enum RaiseTicketTypeEnum {
  RushDelivery = 1,
  RetryDelivery = 2,
  NotifyDelivery = 3,
  Claim = 4,
}

export enum RaiseTicketTypeUpdateEnum {
  Manual = 1,
  BulkCreate = 2,
}

export enum TypeAssigneeTicket {
  PriorityAssignment = 1,
  EvenDistribution = 2,
}

export enum MethodTicket {
  onlyNewStatus = 1,
  reOpen = 2,
}

export enum TagAction {
  addTag = 1,
  deleteTag = 2,
}

export enum TicketTypeSearch {
  ticketIdOrSO = 'ticketIdOrSO',
  waybill = 'waybill',
  phoneNumber = 'phoneNumber',
  externalId = 'externalId',
}

export enum TypeSortTicket {
  noOfDeliveries = 'noOfDeliveries',
  noOfCareResults = 'noOfCareResults',
  lastUpdateStatus = 'lastUpdateStatus',
  updatedAt = 'updatedAt',
  createdAt = 'createdAt',
  lastUpdateResults = 'lastUpdateResults',
}

export enum TypeSearchTicketFilter {
  CreationTime = 1,
  StatusChanges = 2,
  NoteUpdatedTime = 3,
}

export enum TicketAppointmentStatusEnum {
  Upcoming = 1,
  Processed = 2,
  Missed = 3,
}
export enum TicketAppointmentTypeSearch {
  Today = 1,
  Processed = 2,
  Missed = 3,
}
