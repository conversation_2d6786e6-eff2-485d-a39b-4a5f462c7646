export enum TypeCreateOrder {
  Manual = 1,
  BulkUpdate = 2,
}

export enum CarrieStatusEnum {
  activated = 'activated',
  canceled = 'canceled',
}

export enum ExternalOrderType {
  ag = 'ag',
  pancake = 'pancake',
  onepage = 'onepage',
  hvnet = 'hvnet',
  tiktok = 'tiktok',
  shopee = 'shopee',
  miaoshou = 'miaoshou',
  other = 'other',
}

export enum TypeTemplateDownloadOrder {
  'THJ&T' = 'J&T Express Thailand',
  THNinjaVan = 'NinjaVan Thailand',
  PHNinjaVan = 'NinjaVan Philippines',
  MANinjaVan = 'NinjaVan Malaysia',
  MMNinjaVan = 'NinjaVan Myanmar',
  'PHJ&T' = 'J&T Express Philippines',
  'IDJ&T' = 'J&T Indonesia',
  BestExpress = 'Best Express Malaysia',
  FlashExpress = 'Flash Express Malaysia',
  THFlashExpress = 'Flash Express Thailand',
  KerryExpress = 'Kerry Express Thailand',
  Nimbus = 'Nimbus India',
  NimbusManually = 'Nimbus India Manually',
  AnousithExpress = 'Anousith Express',
  JNE = 'JNE Indonesia',
  HALExcel = 'HAL Express Excel',
  HAL = 'HAL Express',
  HALWaybill = 'HAL Waybill',
  AnousithWaybill = 'Anousith Waybill',
  TemplatePurchase = 'Template Purchase',
  Wedo = 'Wedo',
  UsaDropShip = 'UsaDropShip',
  Custom = 'Custom',
  Collection = 'Collection',
  CollectionPDF = 'Collection (PDF)',
  Mixay = 'Mixay',
  PurchaseDropship = 'PurchaseDropship',
  RegionBy3PL = 'Region by 3PL',
  PurchaseChineseLao = 'Purchase Chinese Lao',
  CollectionPurchase = 'Collection Purchase',
  CollectionDropshipLao = 'Collection Dropship Lao',
  CollectionDropshipThailand = 'Collection Dropship Thailand',
  CollectionDropshipBDG = 'Collection Dropship BDG',
  StartCollectPack = 'Start Collect & Pack',
  VNWaybill = 'Waybill đóng hàng mẫu',
  PosLaju = 'Pos Laju',
}

export enum TypeSearchOrderFilter {
  CreationTime = 1,
  StatusChanges = 2,
  LastUpdated = 3,
  CollectAndPack = 4,
}

export enum TypeSortOrder {
  noOfDeliveries = 'noOfDeliveries',
  recipientName = 'recipientName',
  productQty = 'productQty',
  subTotal = 'subTotal',
  lastUpdateStatus = 'lastUpdateStatus',
  updateHandOverAt = 'updateHandOverAt',
  updatedAt = 'updatedAt',
  createdAt = 'createdAt',
  totalWeight = 'virtual_total_weight',
}

export enum SortType {
  DESC = 'DESC',
  ASC = 'ASC',
}

export enum WarningType {
  ODZ = 1,
  CancelWaybill = 2,
  Duplicate = 3,
  ReconciledCod = 4,
  NeedRetryTicket = 5,
}

export enum WarningStatus {
  Enable = 1,
  Disable = 2,
}

export enum ODZEnableType {
  API = 1,
  Address = 2,
  Off = 3,
}

export enum SyncInfoEnum {
  Full = 1,
  ALittle = 2,
}

export enum TypeChangeWarehouse {
  originWarehouse = 1,
  returnWarehouse = 2,
}

export enum TypeOrder {
  normal = 0,
  dropship = 1
}