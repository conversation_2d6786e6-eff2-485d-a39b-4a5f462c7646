import { CarrierCode, CountryID } from 'core/enums/carrier-code.enum';

export interface ICarrierField {
  name: string;
  title: string;
  description?: string;
  extraService?: boolean;
}

export const CARRIER_CONFIGURATION_FIELDS: Record<CarrierCode, ICarrierField[]> = {
  [CarrierCode.jtexpress]: [
    {
      name: 'key',
      title: 'Secret key',
      description: '',
    },
    {
      name: 'eCompanyId',
      title: 'E-company ID',
      description: '',
    },
  ],
  [CarrierCode.ninjavan]: [
    {
      name: 'key',
      title: 'Secret key',
      description: '',
    },
  ],
  [CarrierCode.kerryexpress]: [
    {
      name: 'key',
      title: 'App key',
      description: 'App key',
    },
    {
      name: 'merchant_id',
      title: 'Merchant ID or mobile no.',
      description: 'Merchant ID or mobile no.',
    },
    {
      name: 'appKey',
      title: 'AppKey Sorting',
      description: '',
    },
    {
      name: 'appId',
      title: 'AppId Sorting',
      description: '',
    },
  ],
  [CarrierCode.flashexpress]: [
    {
      name: 'key',
      title: 'Secret key',
      description: '',
    },
  ],
  [CarrierCode.nimbus]: [
    {
      name: 'password',
      title: 'Password',
      description: '',
    },
  ],
  [CarrierCode.bluedart]: [
    {
      name: 'customerCode',
      title: 'Customer Code',
      description: '',
    },
    {
      name: 'clientId',
      title: 'API Key',
      description: '',
    },
    {
      name: 'clientSecret',
      title: 'API Secret',
      description: '',
    },
    {
      name: 'shippingLicense',
      title: 'Shipping API License',
      description: '',
    },
  ],
  [CarrierCode.poslaju]: [],
  [CarrierCode.jneexpress]: [],
  [CarrierCode.anousith]: [],
  [CarrierCode.hal]: [],
  [CarrierCode.system]: [],
  [CarrierCode.lalamove]: [],
  [CarrierCode.shopee]: [],
  [CarrierCode.tiktokshop]: [],
  [CarrierCode.usadropship]: [],
  [CarrierCode.mixayexpress]: [],
  [CarrierCode.miaoshou]: [],
  [CarrierCode.viettelpost]: [
    {
      name: 'key',
      title: 'Secret key',
      description: '',
    },
    // {
    //   name: 'partialDelivery', //Giao một phần
    //   title: 'Partial Delivery',
    //   description: '',
    // },
  ],
  [CarrierCode.vnpost]: [
    {
      name: 'customerCode',
      title: 'Customer Code',
      description: '',
    },
    {
      name: 'contractCode',
      title: 'Contract Code',
      description: '',
    },
    {
      name: 'serviceCode',
      title: 'Service Code',
      description: '',
    },
    {
      name: 'shippingMethod', // Hình thức gửi hàng
      title: 'Shipping Method',
      description: '',
      extraService: true,
    },
    {
      name: 'requiredUponDelivery', // Yêu cầu khi phát hàng
      title: 'Required Upon Delivery',
      description: '',
      extraService: true,
    },
    // {
    //   name: 'orderCancellationFeeCharged',  //Thu phí hủy đơn hàng
    //   title: 'Order Cancellation Fee Charged',
    //   description: '',
    // },
    // {
    //   name: 'exchangeOrReturn', //Dịch vụ đổi, trả hàng
    //   title: 'Exchange / Return',
    //   description: '',
    // },
  ],
  [CarrierCode.ghtk]: [
    {
      name: 'token',
      title: 'Token',
      description: '',
    },
  ],
  [CarrierCode.wedo]: [
    {
      name: 'key',
      title: 'Token',
      description: '',
    },
    {
      name: 'whCode',
      title: 'Store Code',
      description: '',
    },
    {
      name: 'channelCode',
      title: 'Channel Short Code',
      description: '',
    },
  ],
  [CarrierCode.bestexpress]: [
    {
      name: 'password',
      title: 'Password',
      description: '',
    },
    {
      name: 'bankCardOwner',
      title: 'Bank Card Owner',
      description: '',
    },
    {
      name: 'bankCode',
      title: 'Bank Code',
      description: '',
    },
    {
      name: 'bankCardNo',
      title: 'Bank Card No',
      description: '',
    },
  ],
  [CarrierCode.spxexpress]: [
    {
      name: 'key',
      title: 'Secret key',
      description: '',
    },
    // {
    //   name: 'allowMutualCheck', // Cho xem hàng, không cho thử
    //   title: 'Allow Mutual Check',
    //   description: '',
    //   extraService: true,
    // },
  ],
  [CarrierCode.jntbangkok]: [
    {
      name: 'password',
      title: 'Password',
      description: '',
    },
    {
      name: 'apiAccount',
      title: 'API Account',
      description: '',
    },
    {
      name: 'key',
      title: 'Secret key',
      description: '',
    },
  ],
};

export const CARRIER_CONFIGURATION_EXTRA_SERVICE_FIELDS: Record<CarrierCode, any> = {
  [CarrierCode.miaoshou]: [],
  [CarrierCode.jtexpress]: [],
  [CarrierCode.ninjavan]: [],
  [CarrierCode.kerryexpress]: [],
  [CarrierCode.flashexpress]: [],
  [CarrierCode.nimbus]: [],
  [CarrierCode.bluedart]: [],
  [CarrierCode.poslaju]: [],
  [CarrierCode.jneexpress]: [],
  [CarrierCode.anousith]: [],
  [CarrierCode.hal]: [],
  [CarrierCode.system]: [],
  [CarrierCode.lalamove]: [],
  [CarrierCode.shopee]: [],
  [CarrierCode.tiktokshop]: [],
  [CarrierCode.usadropship]: [],
  [CarrierCode.mixayexpress]: [],
  [CarrierCode.viettelpost]: [
    {
      name: 'partialDelivery', //Giao một phần
    },
  ],
  [CarrierCode.vnpost]: [
    {
      name: 'shippingMethod', // Hình thức gửi hàng
    },
    {
      name: 'requiredUponDelivery', // Yêu cầu khi phát hàng
    },
    {
      name: 'orderCancellationFeeCharged', //Thu phí hủy đơn hàng
    },
    {
      name: 'exchangeOrReturn', //Dịch vụ đổi, trả hàng
    },
  ],
  [CarrierCode.ghtk]: [],
  [CarrierCode.wedo]: [],
  [CarrierCode.bestexpress]: [],
  [CarrierCode.spxexpress]: [
    {
      name: 'allowMutualCheck', // Cho xem hàng, không cho thử
    },
  ],
  [CarrierCode.jntbangkok]: [],
};
export const CARRIER_CONFIGURATION_COUNTRIES = {
  [CarrierCode.jtexpress]: {
    [CountryID.TH]: 1,
    [CountryID.PH]: 1,
  },
  [CarrierCode.ninjavan]: {
    [CountryID.TH]: 1,
    [CountryID.PH]: 1,
    65: 1,
    [CountryID.MY]: 1,
  },
  [CarrierCode.bestexpress]: {
    [CountryID.VN]: 1,
    [CountryID.TH]: 1,
  },
  [CarrierCode.vnpost]: {
    [CountryID.VN]: 1,
  },
  [CarrierCode.system]: {},
  [CarrierCode.shopee]: {},
  [CarrierCode.tiktokshop]: {},
  [CarrierCode.kerryexpress]: {
    [CountryID.TH]: 1,
  },
  [CarrierCode.flashexpress]: {
    [CountryID.MY]: 1,
    [CountryID.TH]: 1,
  },
  [CarrierCode.nimbus]: {
    [CountryID.IN]: 1,
  },
  [CarrierCode.bluedart]: {
    [CountryID.IN]: 1,
  },
  [CarrierCode.anousith]: {
    [CountryID.LA]: 1,
  },
  [CarrierCode.hal]: {
    [CountryID.LA]: 1,
  },
  [CarrierCode.lalamove]: {
    [CountryID.PH]: 1,
  },
  [CarrierCode.wedo]: {
    [CountryID.US]: 1,
  },
  [CarrierCode.usadropship]: {
    [CountryID.US]: 1,
  },
  [CarrierCode.viettelpost]: {
    [CountryID.VN]: 1,
  },
  [CarrierCode.ghtk]: {
    [CountryID.VN]: 1,
  },
  [CarrierCode.mixayexpress]: {
    [CountryID.LA]: 1,
  },
  [CarrierCode.spxexpress]: {
    [CountryID.VN]: 1,
  },
  [CarrierCode.jntbangkok]: {
    [CountryID.TH]: 1,
  },
  [CarrierCode.miaoshou]: {
    [CountryID.TH]: 1,
    [CountryID.CN]: 1,
    [CountryID.VN]: 1,
    [CountryID.ID]: 1,
    [CountryID.PH]: 1,
  },
};

export const CARRIER_CONFIGURATION_REASON = {
  [CarrierCode.jtexpress]: {
    [CountryID.TH]: [
      {
        key: 'Receiver is not home and no answer the phone',
        value: 'NCC-Cannot contact with Customer',
      },
      {
        key: 'Receiver phone number is wrong',
        value: 'NCC-Cannot contact with Customer',
      },
      {
        key: 'Customer requests to make new appointment',
        value: 'CNGD-Customer change the date of delivery',
      },
      {
        key: 'Refuse delivery',
        value: 'JRO-Order rejected by Customer',
      },
      {
        key: 'Return package',
        value: 'JRO-Order rejected by Customer',
      },
      {
        key: 'The recipient address is unknown',
        value: 'WRD-Wrong Address of Customer',
      },
      {
        key: 'Receiver request self pick up',
        value: 'CHNL-Change of the delivery location',
      },
      {
        key: 'Remainder Parcel at DC',
        value: 'B3PL-Issue by 3PL',
      },
      {
        key: 'Miss sort at delivery branch',
        value: 'B3PL-Issue by 3PL',
      },
      {
        key: 'Damaged label',
        value: 'B3PL-Issue by 3PL',
      },
      {
        key: 'Return Register by',
        value: 'RR-Return Register',
      },
    ],
    [CountryID.PH]: [
      {
        key: 'The customer is not around',
        value: 'NCC-Cannot contact with Customer',
      },
      {
        key: 'The phone went unanswered or power off',
        value: 'NCC-Cannot contact with Customer',
      },
      {
        key: 'Customer Reschedule',
        value: 'CNGD-Customer change the date of delivery',
      },
      {
        key: 'Customer is Not Available on holidays',
        value: 'CNGD-Customer change the date of delivery',
      },
      {
        key: 'Customer Rejected',
        value: 'JRO-Order rejected by Customer',
      },
      {
        key: 'Wrong code',
        value: 'WRD-Wrong Address of Customer',
      },
      {
        key: 'Address Information Unknown/Incorrect',
        value: 'WRD-Wrong Address of Customer',
      },
      {
        key: 'Multiple waybill in one item',
        value: 'B3PL-Issue by 3PL',
      },
      {
        key: 'Departed but no Arrival Parcel',
        value: 'B3PL-Issue by 3PL',
      },
      {
        key: 'Misroute by DC',
        value: 'B3PL-Issue by 3PL',
      },
      {
        key: 'Self-pick parcels',
        value: 'SCL-Customer self collection parcels',
      },
      {
        key: 'Return Register by',
        value: 'RR-Return Register',
      },
    ],
  },
  [CarrierCode.jntbangkok]: [
    {
      key: 'Receiver is not home and no answer the phone',
      value: 'NCC-Cannot contact with Customer',
    },
    {
      key: 'Receiver phone number is wrong',
      value: 'NCC-Cannot contact with Customer',
    },
    {
      key: 'Customer requests to make new appointment',
      value: 'CNGD-Customer change the date of delivery',
    },
    {
      key: 'Refuse delivery',
      value: 'JRO-Order rejected by Customer',
    },
    {
      key: 'Return package',
      value: 'JRO-Order rejected by Customer',
    },
    {
      key: 'The recipient address is unknown',
      value: 'WRD-Wrong Address of Customer',
    },
    {
      key: 'Receiver request self pick up',
      value: 'CHNL-Change of the delivery location',
    },
    {
      key: 'Remainder Parcel at DC',
      value: 'B3PL-Issue by 3PL',
    },
    {
      key: 'Miss sort at delivery branch',
      value: 'B3PL-Issue by 3PL',
    },
    {
      key: 'Damaged label',
      value: 'B3PL-Issue by 3PL',
    },
    {
      key: 'Return Register by',
      value: 'RR-Return Register',
    },
  ],
  [CarrierCode.ninjavan]: [
    {
      key: 'Our Ninja was at location, but could not reach the recipient',
      value: 'NCC-Cannot contact with Customer',
    },
    {
      key: 'Customer requested for change of delivery date',
      value: 'CNGD-Customer change the date of delivery',
    },
    {
      key: 'Customer requested for change of delivery date',
      value: 'CNGD-Customer change the date of delivery',
    },
    {
      key: 'Cancel due to delivery delay',
      value: 'CNGD-Customer change the date of delivery',
    },
    {
      key: 'Customer changed mind',
      value: 'JRO-Order rejected by Customer',
    },
    {
      key: 'Customer did not provide reason',
      value: 'JRO-Order rejected by Customer',
    },
    {
      key: 'No order was placed',
      value: 'JRO-Order rejected by Customer',
    },
    {
      key: 'Item was rejected at doorstep',
      value: 'JRO-Order rejected by Customer',
    },
    {
      key: 'Delivery failed due to inaccurate address details provided to NinjaVan',
      value: 'WRD-Wrong Address of Customer',
    },
    {
      key: 'Our Ninja could not access the delivery location',
      value: 'NCCL-Not access the delivery location',
    },
    {
      key: 'The customer requested for a change of the delivery location',
      value: 'CHNL-Change of the delivery location',
    },
    {
      key: "Delivery failed due to unexpected issues at NinjaVan's end",
      value: 'B3PL-Issue by 3PL',
    },
    {
      key: 'Delivery failed due to vehicular breakdown',
      value: 'B3PL-Issue by 3PL',
    },
    {
      key:
        "Delivery failed due to unexpected issues at NinjaVan's end. We're very sorry & we'll have your parcel delivered the next working day",
      value: 'B3PL-Issue by 3PL',
    },
    {
      key: 'Driver cannot find location - customer unreachable',
      value: 'B3PL-Issue by 3PL',
    },
    {
      key: 'Delivery failed due to natural disasters or nationwide emergencies',
      value: 'DMG-Issue by natural disasters or nationwide emergencies',
    },
    {
      key: 'Return Register by',
      value: 'RR-Return Register',
    },
  ],
  [CarrierCode.bestexpress]: [],
  [CarrierCode.flashexpress]: {
    [CountryID.MY]: [
      {
        key:
          'Unable to contact, the shipment has not been delivered yet, we will deliver it again as soon as possible.',
        value: 'NCC-Cannot contact with Customer',
      },
      {
        key:
          'Receiver rescheduled, the shipment has not been delivered yet, we will deliver it again as soon as possible',
        value: 'CNGD-Customer change the date of delivery',
      },
      {
        key: 'Consignee rejected the parcel, your parcel was not delivered successfully',
        value: 'JRO-Order rejected by Customer',
      },
      {
        key:
          'Retain for delivery the next day due to remote area, the shipment has not been delivered yet, we will deliver it again as soon as possible.',
        value: 'B3PL-Issue by 3PL',
      },
    ],
    [CountryID.TH]: [],
  },
  [CarrierCode.kerryexpress]: [],
  [CarrierCode.nimbus]: [],
  [CarrierCode.poslaju]: [],
  [CarrierCode.jneexpress]: [],
  [CarrierCode.bluedart]: [],
  [CarrierCode.anousith]: [],
  [CarrierCode.hal]: [],
  [CarrierCode.lalamove]: [],
  [CarrierCode.wedo]: [],
  [CarrierCode.system]: [],
  [CarrierCode.shopee]: [],
  [CarrierCode.tiktokshop]: [],
  [CarrierCode.usadropship]: [],
  [CarrierCode.viettelpost]: [],
  [CarrierCode.mixayexpress]: [],
  [CarrierCode.ghtk]: [],
  [CarrierCode.vnpost]: [],
  [CarrierCode.spxexpress]: [],
};

export const CARRIER_BY_COUNTRIES = {
  [CountryID.ID]: [CarrierCode.jneexpress, CarrierCode.jtexpress, CarrierCode.miaoshou],
  [CountryID.IN]: [CarrierCode.nimbus, CarrierCode.bluedart, CarrierCode.miaoshou],
  [CountryID.LA]: [CarrierCode.anousith, CarrierCode.hal, CarrierCode.mixayexpress],
  [CountryID.MM]: [CarrierCode.ninjavan],
  [CountryID.CN]: [CarrierCode.miaoshou],
  [CountryID.MY]: [
    CarrierCode.jtexpress,
    CarrierCode.ninjavan,
    CarrierCode.bestexpress,
    CarrierCode.flashexpress,
    CarrierCode.poslaju,
    CarrierCode.shopee,
    CarrierCode.tiktokshop,
    CarrierCode.miaoshou
  ],
  [CountryID.PH]: [CarrierCode.jtexpress, CarrierCode.ninjavan, CarrierCode.lalamove, CarrierCode.miaoshou],
  [CountryID.TH]: [
    CarrierCode.jtexpress,
    CarrierCode.ninjavan,
    CarrierCode.kerryexpress,
    CarrierCode.flashexpress,
    CarrierCode.bestexpress,
    CarrierCode.jntbangkok,
    CarrierCode.miaoshou
  ],
  [CountryID.US]: [CarrierCode.wedo, CarrierCode.usadropship],
  [CountryID.VN]: [
    CarrierCode.viettelpost,
    CarrierCode.bestexpress,
    CarrierCode.ghtk,
    CarrierCode.vnpost,
    CarrierCode.spxexpress,
    CarrierCode.miaoshou
  ],
};
