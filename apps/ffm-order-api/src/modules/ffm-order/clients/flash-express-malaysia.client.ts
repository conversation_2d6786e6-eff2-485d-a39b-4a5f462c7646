import { Injectable, InternalServerErrorException } from '@nestjs/common';
import axios from 'axios';
import * as _ from 'lodash';
import { isEmpty, isNil, orderBy } from 'lodash';
import * as crypto from 'crypto';
import { Order } from '../../../entities/order.entity';
import { IWarehouse } from '../services/order-partners.service';
import { OrderCarrier } from '../../../entities/order-carrier.entity';

interface BaseResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface IWebhookResponse {
  webHookStatusEnabled: boolean;
  webHookStatusUrl?: string;
  webHookWeightEnabled: boolean;
  webHookWeightUrl?: string;
  webHookPriceEnabled: boolean;
  webHookPriceUrl?: string;
  webHookCourierEnabled: boolean;
  webHookCourierUrl?: string;
  webHookRoutesEnabled: boolean;
  webHookRoutesUrl?: string;
  updatedAt: number;
}

export interface IMyFlashTracking {
  pno: string;
  origPno: string;
  returnedPno: string;
  customaryPno: string;
  state: number;
  stateText: string;
  stateChangeAt: number;
  routes: {
    routedAt: number;
    routeAction: string;
    message: string;
    state: number;
  }[];
}

export interface IMyFlashOrder {
  pno: string;
  subPno: string;
  mchId: string;
  outTradeNo: string;
  sortCode: string;
  dstStoreName: string;
  sortingLineCode: string;
  earlyFlightEnabled: boolean;
  packEnabled: boolean;
  upcountryCharge: boolean;
}

const errorMapper = {
  0: 'Internal server error',
  1000: 'Failed to submit',
  1001: 'customer not found',
  1002: 'invalid signature',
  1003: 'Order is exists',
  1004: 'Sender address does not match',
  1005: 'Destination address does not match',
  1006: 'order not found',
  1007: 'warehouse not found',
  1008: 'COD Service is not registered',
  1009: 'COD amount cannot be negative value',
  1010: 'Imcomplete schedule of pickup is exists, no schedule of pickup is required',
  1011: "This courier notification can't be cancelled",
  1015: 'Parcel is picked up, not allowed to cancel',
  1018: "Either sender's address and warehouse no should be sent",
  1019: 'pno and mchId not match',
  1021: 'Postal Code should be 5 digit number',
  1022: "Sender's address are not in service range yet",
};

@Injectable()
export class FlashExpressMalaysiaClient {
  private request = axios.create({
    baseURL: process.env.FLASH_MY_BASE_API,
    headers: {
      'Accept-Language': 'en',
      'Content-Type': 'application/x-www-form-urlencoded',
      Accept: 'application/json',
    },
  });
  private accountId: string;
  private key: string;

  constructor(accountId: string, key: string) {
    this.key = key;
    this.accountId = accountId;
  }

  encryptData(data: Record<string, any>) {
    // data.body = 'test';
    data.mchId = this.accountId;
    data.nonceStr = crypto.randomBytes(12).toString('hex');
    const stringEncode =
      orderBy(
        Object.keys(data).filter(i =>
          typeof data[i] === 'string' ? !isEmpty(data[i]) : !isNil(data[i]),
        ),
        [i => i.toLowerCase()],
        ['asc'],
      )
        .map(i => `${i}=${data[i]}`)
        .join('&') + `&key=${this.key}`;
    console.log('stringEncode', stringEncode);
    const sign = crypto
      .createHash('sha256')
      .update(stringEncode)
      .digest('hex');
    data.sign = sign.toUpperCase();
    return sign;
  }

  async requestApi<T>(url: string, data: Record<string, any>): Promise<T> {
    this.encryptData(data);
    try {
      const response = await this.request.post(url, new URLSearchParams(data).toString());
      console.log(response);
      const res = response.data as BaseResponse<T>;
      if (res.code == 1) {
        return res.data;
      }
      throw { response };
    } catch (e) {
      throw new InternalServerErrorException(e.response.data);
    }
  }

  async getWebhookInfo() {
    const res = await this.requestApi<IWebhookResponse>(
      '/gw/fda/open/standard/webhook/setting/infos',
      {},
    );
    if (!res.webHookRoutesEnabled || res.webHookRoutesUrl != process.env.FLASH_MY_WEBHOOK) {
      await this.integrateWebhook();
    }
    return res;
  }

  async integrateWebhook() {
    if (!process.env.FLASH_MY_WEBHOOK) {
      return;
    }
    const res = await this.requestApi<{
      webhookApiCode: number;
      url: string;
      state;
      updateAt: number;
    }>('/open/v1/setting/web_hook_service', {
      serviceCategory: 1,
      url: process.env.FLASH_MY_WEBHOOK,
      webhookApiCode: 4,
    });
    return res;
  }

  public async createOrder(order: Order, warehouse: IWarehouse): Promise<IMyFlashOrder> {
    const data = {
      outTradeNo: order.displayId,
      expressCategory: 1,
      srcName: warehouse.name,
      srcPhone: warehouse.phoneNumber,
      srcProvinceName: warehouse.addressSplit[2]?.toUpperCase(),
      srcCityName: warehouse.addressSplit[1]?.toUpperCase(),
      srcPostalCode: warehouse.postCode,
      srcDetailAddress: warehouse.fullAddress,
      dstName: order.recipientName,
      dstPhone: order.recipientPhone,
      dstProvinceName: order.recipientProvince?.toUpperCase(),
      dstCityName: order.recipientDistrict?.toUpperCase(),
      dstDetailAddress: order.recipientAddress,
      dstPostalCode: order.recipientPostCode,
      articleCategory: 99,
      remark: order.products
        .map(item => {
          const {
            product: { name },
            sku,
          } = item.productDetail || {};
          return `${item.quantity} x ${sku} - ${name}`;
        })
        .join(', ')
        .substring(0, 200),
      codAmount:
        500000 ||
        (order.totalPrice ||
          order?.subTotal + order?.shippingFee + order?.surcharge - order?.discount - order?.paid) *
          100,
      codEnabled:
        (order.totalPrice ||
          order?.subTotal + order?.shippingFee + order?.surcharge - order?.discount - order?.paid) >
        0,
      weight: !_.isNil(order.totalWeight) ? order.totalWeight : _.sum(order.products.map(i => i.weight)) || 100,
      itemValue:
        (order.totalPrice ||
          order?.subTotal + order?.shippingFee + order?.surcharge - order?.discount - order?.paid) *
        100,
      itemName: order.displayId,
      width: 10,
      length: 10,
      height: 5,
      insured: 0,
    };
    console.log(`request order data - ${order.displayId}`, data);
    const response = await this.requestApi<IMyFlashOrder>('/open/v3/orders', data);
    return response;
  }

  public async update(order: Order, warehouse: IWarehouse): Promise<IMyFlashOrder> {
    const data = {
      pno: order.lastCarrier.waybillNumber,
      outTradeNo: order.displayId,
      expressCategory: 1,
      srcName: warehouse.name,
      srcPhone: warehouse.phoneNumber,
      srcProvinceName: warehouse.addressSplit[2]?.toUpperCase(),
      srcCityName: warehouse.addressSplit[1]?.toUpperCase(),
      srcPostalCode: warehouse.postCode,
      srcDetailAddress: warehouse.fullAddress,
      dstName: order.recipientName,
      dstPhone: order.recipientPhone,
      dstProvinceName: order.recipientProvince?.toUpperCase(),
      dstCityName: order.recipientDistrict?.toUpperCase(),
      dstDetailAddress: order.recipientAddress,
      dstPostalCode: order.recipientPostCode,
      articleCategory: 99,
      remark: order.products
        .map(item => {
          const {
            product: { name },
            sku,
          } = item.productDetail || {};
          return `${item.quantity} x ${sku} - ${name}`;
        })
        .join(', ')
        .substring(0, 200),
      codAmount:
        (order.totalPrice ||
          order?.subTotal + order?.shippingFee + order?.surcharge - order?.discount - order?.paid) *
        100,
      itemValue:
        (order.totalPrice ||
          order?.subTotal + order?.shippingFee + order?.surcharge - order?.discount - order?.paid) *
        100,
      itemName: order.displayId,
      codEnabled:
        (order.totalPrice ||
          order?.subTotal + order?.shippingFee + order?.surcharge - order?.discount - order?.paid) >
        0,
      weight: !_.isNil(order.totalWeight) ? order.totalWeight : _.sum(order.products.map(i => i.weight * i.quantity)) || 100,
      width: 10,
      length: 10,
      height: 5,
      insured: 0,
    };
    console.log(`request order data - ${order.displayId}`, data);
    const response = await this.requestApi<IMyFlashOrder>('/open/v1/orders/modify', data);
    console.log('flash-MY', response);
    return response;
  }

  public async cancelOrder(order: OrderCarrier) {
    const response = await this.requestApi(`/open/v1/orders/${order.waybillNumber}/cancel`, {});
    return response;
  }

  public async trackOrderData(...trackings: string[]) {
    const response = await this.requestApi<IMyFlashTracking>('/open/v1/orders/routesBatch', {
      pnos: trackings.join(','),
    });
    return response;
  }
}
