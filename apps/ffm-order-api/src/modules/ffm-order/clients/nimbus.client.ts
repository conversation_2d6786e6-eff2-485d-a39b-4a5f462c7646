import { Injectable } from '@nestjs/common';
import axios, { AxiosResponse, ResponseType } from 'axios';
import { Order } from '../../../entities/order.entity';
import { IWarehouse } from '../services/order-partners.service';
import * as _ from 'lodash';
import { isEmpty, sum } from 'lodash';
import { OrderCarrier } from '../../../entities/order-carrier.entity';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { decode, JwtPayload } from 'jsonwebtoken';
import * as pdf from 'pdf-parse';

// const EXCLUDES_SERVICES = process.env.NIMBUS_EX_SERVICES?.split(',') || [];
const SERVICES = process.env.NIMBUS_SERVICES?.split(',') || [];

const ODZ_MESSAGES = ['not servicable', 'not operational', 'not serviceable', 'not accepted'];

interface IServiceability {
  id: string;
  name: string;
  freight_charges: number;
  cod_charges: number;
  total_charges: number;
  edd: string;
  min_weight: number;
  chargeable_weight: number;
  reverse_qc: boolean;
  'reverse ': boolean;
}

interface BaseResponse {
  response?: any;
  error?: any;
}

export enum NimbusStatus {
  IT = 'IT',
  OFD = 'OFD',
  DL = 'DL',
  EX = 'EX',
  RT = 'RT',
  'RT-IT' = 'RT-IT',
  'RT-DL' = 'RT-DL',
  PP = 'PP',
  LT = 'LT',
}

export interface INimBusTracking {
  id: string;
  order_id: string;
  order_number: string;
  created: string;
  awb_number: string;
  rto_awb: string;
  courier_id: string;
  warehouse_id: string;
  rto_warehouse_id: string;
  status: string;
  rto_status: string;
  shipment_info: string;
  history: INimbusHistory[];
}

interface INimbusHistory {
  status_code: NimbusStatus;
  location: string;
  event_time: string;
  message: string;
}

export interface INimbusOrder {
  order_id: number;
  shipment_id: number;
  awb_number: string;
  courier_id: string;
  courier_name: string;
  status: string;
  additional_info: string;
  payment_type: string;
  label: string;
  waybill_url: string;
  destination_code?: string;
}

@Injectable()
export class NimbusClient {
  private request = axios.create({
    baseURL: process.env.NIMBUS_BASE_API,
  });
  private username: string;
  private password: string;
  private token: string;
  private expireToken: number;
  private redisService: RedisCacheService;

  constructor(username: string, password: string, redisService: RedisCacheService) {
    this.username = username;
    this.password = password;
    this.redisService = redisService;
  }

  async getAccessToken(): Promise<string> {
    const tokenKey = `nimbus-token-${this.username}`;
    const token = await this.redisService.get<string>(tokenKey);
    if (token) {
      return token;
    }
    try {
      const response = await axios.post(
        `${process.env.NIMBUS_BASE_API}/users/login`,
        {
          email: this.username,
          password: this.password,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      this.token = response.data.data;
      const data = decode(this.token) as JwtPayload;
      this.expireToken = data.exp;
      const ttl = this.expireToken - Math.floor(Date.now() / 1000) - 60;
      console.log('updated nimbus token', tokenKey, this.token, ttl);
      await this.redisService.set(tokenKey, this.token, {
        ttl,
      });
      return this.token;
    } catch (e) {
      console.log('update nimbus token error', e);
      throw e;
    }
  }

  public async createOrder(
    order: Order,
    warehouse: IWarehouse,
  ): Promise<{ response?: INimbusOrder; error?: any }> {
    const { services, error } = await this.checkOdz(order, warehouse);
    if (error) {
      return { error };
    }
    if (isEmpty(services)) {
      return {
        error: {
          message: `${order.displayId} ODZ address`,
        },
      };
    }
    let err;
    for (const service of services) {
      const data: Record<string, any> = {
        'order_number': order.displayId,
        'payment_type': order.totalPrice == 0 ? 'prepaid' : 'cod',
        'package_weight': !_.isNil(order.totalWeight) ? order.totalWeight : _.sum(
          order.products.map(i => i.weight),
        ) || 100,
        'package_length': 10,
        'package_breadth': 10,
        'package_height': 10,
        'consignee': {
          'name': order.recipientName,
          'address': `${order?.recipientAddress}, ${[order.recipientWard, order.recipientDistrict, order.recipientProvince].filter(i => !!i).join(', ')}`,
          'city': order.recipientDistrict,
          'state': order.recipientProvince,
          'pincode': order.recipientPostCode,
          'phone': order.recipientPhone,
        },
        pickup: {
          warehouse_name: warehouse?.name,
          name: warehouse?.name,
          address: warehouse?.fullAddress,
          city: warehouse.addressSplit[1]?.toUpperCase(),
          state: warehouse.addressSplit[2]?.toUpperCase(),
          pincode: warehouse.postCode,
          phone: warehouse.phoneNumber,
          gst_number: warehouse.gstNo,
        },
        'request_auto_pickup': 'yes',
        'request_auto_ship': 'yes',
        'order_items': order.products
          .map((item) => {
            const { product: { name, price }, sku } = item.productDetail || {};
            return {
              'name': `${sku} - ${name}`,
              'qty': `${item.quantity}`,
              'price': order.totalPrice / sum(order.products.map(i => i.quantity)),
              'sku': sku,
            };
          }),
        is_insurance: '0',
        courier_id: service,
      };
      // if (order.totalPrice) {
      data.order_amount = order.totalPrice;
      // }
      console.log(`request order data - ${order.displayId}`, data);
      const { response, error } = await this.requestApi({
        url: '/shipments',
        method: 'POST',
        data,
      });
      if (response) {
        return { response: response.data };
      }
      console.log('error', error.message);
      err = error;
      for (const odzMessage of ODZ_MESSAGES) {
        if (error.message.includes(odzMessage)) {
          return { error };
        }
      }
    }
    return { error: err };
  }

  public async cancelOrder(order: OrderCarrier): Promise<{ response?: INimbusOrder; error?: any }> {
    return this.requestApi({
      url: '/shipments/cancel',
      method: 'POST',
      data: {
        awb: order.waybillNumber,
      },
    });
  }

  private async requestApi({
    url,
    data,
    method,
    responseType,
  }: {
    url: string;
    data?: Record<string, any>;
    method: 'GET' | 'POST' | 'DELETE' | 'PUT';
    responseType?: ResponseType;
  }): Promise<BaseResponse> {
    try {
      const token = await this.getAccessToken();
      const response = await this.request.request({
        url,
        data,
        method,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        responseType,
      });
      console.log('response', response.config, response);
      if (!response.data.status) {
        return {
          error: response.data,
        };
      }
      return {
        response: response.data,
      };
    } catch (e) {
      console.log('e', JSON.stringify(e.response.data));
      return {
        error: e?.response?.data || {},
      };
    }
  }

  public async trackOrderData(trackings: string[]): Promise<INimBusTracking[]> {
    const { response } = await this.requestApi({
      url: '/shipments/track/bulk',
      method: 'POST',
      data: {
        awb: trackings,
      },
    });
    return response.data;
  }

  public async checkOdz(
    order: Order,
    warehouse?: IWarehouse,
  ): Promise<{ services?: string[]; error? }> {
    const { response, error } = await this.requestApi({
      url: '/courier/serviceability',
      method: 'POST',
      data: {
        origin: warehouse.postCode || '110077',
        destination: order.recipientPostCode,
        payment_type: order.totalPrice == 0 ? 'prepaid' : 'cod',
        order_amount: order.totalPrice || 1,
        weight: !_.isNil(order.totalWeight) ? order.totalWeight : _.sum(order.products.map(i => i.weight * i.quantity)) || 100,
        length: '10',
        breadth: '10',
        height: '10',
      },
    });
    if (error) {
      return { error };
    }
    const data = (response.data as IServiceability[])
      .filter(i => i.total_charges)
      .reduce((prev, i) => {
        prev[i.id] = 1;
        return prev;
      }, {} as Record<string, number>);
    return { services: process.env.NIMBUS_SERVICES?.split(',').filter(i => data[i]) };
  }

  async readWaybill(id?: number, label?: string) {
    const extraData = {} as INimbusOrder;
    if (id) {
      let attempts = 2,
        res: AxiosResponse;
      while (attempts) {
        let cookie = 'ci_session=c81csf7g8rkg8qki29fjtkimvhjc3pfu';
        res = await axios({
          method: 'post',
          url: 'https://ship.nimbuspost.com/shipping/generate_label',
          data: new URLSearchParams({
            'shipping_ids[]': `${id}`,
          }),
          headers: {
            'content-type': 'application/x-www-form-urlencoded',
            'x-requested-with': 'XMLHttpRequest',
            'user-agent':
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            cookie,
          },
        });
        if (!res.data) {
          cookie = res.headers['set-cookie'][0].split(';')[0];
        } else {
          break;
        }
        attempts--;
      }
      if (res.data.success) {
        label = res.data.success;
      }
      extraData.order_id = id;
    }
    if (label) {
      const bill = await axios({
        url: label,
        method: 'GET',
        responseType: 'arraybuffer',
      });
      const data = await pdf(bill.data);
      const waybillData = data.text?.split('\n');
      extraData.destination_code = waybillData
        .find(i => i.includes('Destination Code:'))
        ?.replace('Destination Code:', '')
        ?.trim();
      const weightIdx = waybillData.findIndex(i => i.includes('WEIGHT :'));
      if (weightIdx >= 0) {
        extraData.courier_name = waybillData[weightIdx + 1]?.trim();
      }
      extraData.waybill_url = label;
    }
    return extraData;
  }
}
