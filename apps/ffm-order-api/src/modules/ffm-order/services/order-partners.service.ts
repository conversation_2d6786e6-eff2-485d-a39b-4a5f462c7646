import {
  Amqp<PERSON>onnection,
  default<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  OnModuleInit,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CARRIER_CONFIGURATION_REASON } from 'apps/ffm-order-api/src/constants/carriers.constants';
import {
  ALLOW_UPDATE_IN_DELIVERY,
  NOT_ALLOW_UPDATE_CARRIER_ORDER_V2,
  NOT_ALLOW_UPDATE_STATUS_BY_3PL_OUT_FOR_DELIVERY,
} from 'apps/ffm-order-api/src/constants/order.constants';
import { District } from 'apps/ffm-order-api/src/entities/address_district_3pl.entity';
import { PostCode } from 'apps/ffm-order-api/src/entities/address_post_code_3pl.entity';
import { Province } from 'apps/ffm-order-api/src/entities/address_province_3pl.entity';
import { Ward } from 'apps/ffm-order-api/src/entities/address_ward_3pl.entity';
import { ODZConfiguration } from 'apps/ffm-order-api/src/entities/odz-configuration.entity';
import { ODZ } from 'apps/ffm-order-api/src/entities/odz.entity';
import { OrderCarrierHistories } from 'apps/ffm-order-api/src/entities/order-carrier-history.entity';
import { OrderCarrier } from 'apps/ffm-order-api/src/entities/order-carrier.entity';
import { Tag } from 'apps/ffm-order-api/src/entities/tags.entity';
import {
  CarrieStatusEnum,
  ExternalOrderType,
  ODZEnableType,
  WarningStatus,
  WarningType,
} from 'apps/ffm-order-api/src/enums/order-status.enum';
import { FilterODZ } from 'apps/ffm-order-api/src/filters/odz.filter';
import axios from 'axios';
import { Queue } from 'bullmq';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { CarrierCode, CountryID } from 'core/enums/carrier-code.enum';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { SystemIdEnum } from 'core/enums/user-type.enum';
import * as FormData from 'form-data';
import {
  chunk,
  cloneDeep,
  differenceWith,
  filter,
  find,
  isArray,
  isEmpty,
  isNil,
  last,
  orderBy,
  reduce,
  startsWith,
  uniqBy,
} from 'lodash';
import * as moment from 'moment-timezone';
import * as pdf from 'pdf-parse';
import { Brackets, FindCondition, In, IsNull, LessThan, Not, Repository } from 'typeorm';
import { RedisCacheService } from '../../../../../../core/cache/services/redisCache.service';
import { CommonStatus } from '../../../../../../core/enums/common-status.enum';
import { rmqErrorsHandler } from '../../../../../../core/handlers/rmq-errors.handler';
import StringUtils from '../../../../../../core/utils/StringUtils';
import {
  CancelAnywayDto,
  CorrectionOrderDto,
  CreateOrderPartnerDto,
} from '../../../dtos/create-order-partner.dto';
import { CarrierConfiguration } from '../../../entities/carrier-configuration.entity';
import { Carrier } from '../../../entities/carrier.entity';
import { Order } from '../../../entities/order.entity';
import { TypeUpdate3PL, TypeUpdateOrderStatus } from '../../../enums/type-update-order-status.enum';
import { BestExpressMalaysiaClient } from '../clients/best-express-malaysia.client';
import { FlashExpressMalaysiaClient } from '../clients/flash-express-malaysia.client';
import { FlashExpressThailandClient } from '../clients/flash-express-thailand.client';
import { JntExpressPhilippinesClient } from '../clients/jnt-express-philippines.client';
import { JntExpressThailandClient } from '../clients/jnt-express-thailand.client';
import { KerryThailandClient } from '../clients/kerry-thailand.client';
import { INimbusOrder, NimbusClient } from '../clients/nimbus.client';
import { NinjaVanClient, NJV_COUNTRY_CODES } from '../clients/ninjavan.client';
import { ODZService } from './odz.service';
import { OrderService } from './order.service';
import { TagService } from './tags.service';
// import { AddressMapping3PL } from 'apps/ffm-order-api/src/entities/address_mapping_3pl.entity';
import { Logs } from 'apps/ffm-order-api/src/entities/logs.entity';
import { ODZConfigurationItem } from 'apps/ffm-order-api/src/entities/odz-configuration-item.entity';
import { OrderTag } from 'apps/ffm-order-api/src/entities/order-tag.entity';
import { OrderWarning } from 'apps/ffm-order-api/src/entities/order_warning.entity';
import { ReImport } from 'apps/ffm-order-api/src/entities/re-import.entity';
import { TypeOfTagEnum } from 'apps/ffm-order-api/src/enums/tag.enum';
import cheerio from 'cheerio';
import { RawResponse } from 'core/raw/raw-response';
import { XMLParser } from 'fast-xml-parser';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { BestExpressClient } from '../clients/bestexpress.client';
import { BlueDartClient } from '../clients/bluedart.client';
import { GHTKClient } from '../clients/ghtk.client';
import { ShopeeExpressClient } from '../clients/spx-malaysia.client';
import { ViettelPostClient } from '../clients/viettel-post.client';
import { VNPostClient } from '../clients/vn-post.client';
import { WeDoClient } from '../clients/wedo.client';
import {
  IAnousithTracking,
  IFlashCrawlOrder,
  IHalTracking,
  IJneTracking,
  IKerryTrackingData,
  IPosLajuTracking,
  IReason,
  IUpdateOrderTracking,
  ORDER_RETURN_STATUSES,
  ORDER_SHIPPING_STATUSES,
  parseAnousithTrackings,
  parseBestMyTrackings,
  parseBestThTrackings,
  parseBluedartTracking,
  parseFlashKerryTrackings,
  parseFlashMyTrackings,
  parseHalTrackings,
  parseJneIdTrackings,
  parseJntThTrackings,
  parseJntTrackings,
  parseNimbusTracking,
  parseNJVTrackings,
  parseNJVWebhook,
  parsePosLajuTrackings,
  parseShopeeTrackings,
  parseJntBangkokTrackings
} from '../utils/tracking-parser';
import { WWPROXY } from '../utils/wwproxy';
import { SPXVNClient } from '../clients/spx-vn.client';
import { FFMQueueTrackingOrderService } from './queue-tracking-order.service';
import { BestExpressThailandClient } from '../clients/best-express-thailand.client';
import { FfmToAgOrderCarrierDto } from 'apps/ffm-order-api/src/dtos/ag-order/ffm-to-ag-order-carrier.dto';
import { User } from 'apps/ffm-order-api/src/read-entities/identity/user.entity';
import { JntBangkokClient } from '../clients/jnt-bangkok.client';

export interface ISenderInfo {
  senderName: string;
  senderPhone: string;
  senderAddress: string;
  senderProvince: string;
  senderDistrict: string;
  senderWard: string;
  senderProvinceId?: string | number;
  senderDistrictId?: string | number;
  senderWardId?: string | number;
  senderPostCode: string;
  carrierId: string;
  warehouseId: string;
}

export interface IWarehouse {
  id: string;
  name: string;
  phoneNumber: string;
  address?: any;
  countryCode?: string;
  fullAddress: string;
  addressSplit?: string[];
  provinceId: string;
  districtId: string;
  communeId: string;
  postCode: string;
  gstNo: string;
  isOnSender?: boolean;
  senderInformations?: ISenderInfo[];
}
export interface ICrawlOrder {
  code: 200 | 500;
  message: string;
  order: string;
}
@Injectable()
@Processor('order-ffm')
export class OrderCarriersService implements OnModuleInit {
  constructor(
    @InjectRepository(ReImport, orderConnection)
    private reImportRepository: Repository<ReImport>,
    @InjectRepository(OrderCarrierHistories, orderConnection)
    private carrierHistoryRepository: Repository<OrderCarrierHistories>,
    @InjectRepository(OrderCarrier, orderConnection)
    private orderCarrierRepository: Repository<OrderCarrier>,
    @InjectRepository(Carrier, orderConnection)
    private carrierRepository: Repository<Carrier>,
    @InjectRepository(Order, orderConnection)
    private orderRepository: Repository<Order>,
    // @InjectRepository(AddressMapping3PL, orderConnection)
    // private addressMapping3PLRepository: Repository<AddressMapping3PL>,
    @InjectQueue('order-ffm') private queue: Queue,
    private readonly amqpConnection: AmqpConnection,
    private readonly orderService: OrderService,
    @InjectRepository(CarrierConfiguration, orderConnection)
    private carrierConfigurationRepository: Repository<CarrierConfiguration>,
    @InjectRepository(ODZConfiguration, orderConnection)
    private odzConfigRepository: Repository<ODZConfiguration>,
    @InjectRepository(Province, orderConnection)
    private provinceRepository: Repository<Province>,
    @InjectRepository(District, orderConnection)
    private districtRepository: Repository<District>,
    @InjectRepository(Ward, orderConnection)
    private wardRepository: Repository<Ward>,
    @InjectRepository(PostCode, orderConnection)
    private postCodeRepository: Repository<PostCode>,
    @InjectRepository(OrderWarning, orderConnection)
    private owRepository: Repository<OrderWarning>,
    @InjectRepository(OrderTag, orderConnection)
    private otRepository: Repository<OrderTag>,
    @InjectRepository(Logs, orderConnection)
    private logRepository: Repository<Logs>,
    @InjectRepository(Tag, orderConnection)
    private tagRepository: Repository<Tag>,
    @InjectRepository(ODZConfigurationItem, orderConnection)
    private odzConfigurationItemRepository: Repository<ODZConfigurationItem>,
    private readonly redisService: RedisCacheService,
    private readonly ffmQueueTrackingOrderService: FFMQueueTrackingOrderService,
    private odzService: ODZService,
    private tagService: TagService,
  ) {}

  async onModuleInit() {
    await this.scheduleScanOrder();
  }

  async getCurrentProxy(force = false) {
    try {
      if (!process.env.PROXY_KEY) {
        return null;
      }
      const proxyKey = `wwproxy-host`;
      const current = await this.redisService.get<string>(proxyKey);
      if (!force && current) {
        return current;
      }
      const res = await axios.get('https://wwproxy.com/api/client/proxy/available', {
        params: {
          provinceId: -1,
          key: process.env.PROXY_KEY,
        },
      });
      const proxy = res.data.data?.proxy;
      await this.redisService.set(proxyKey, proxy, { ttl: 300 });
      console.log('new proxy', proxy);
      return proxy;
    } catch (e) {
      console.log('proxy error', e);
      return null;
    }
  }

  async getCurrentProxyWithCheck() {
    return new Promise(async resolve => {
      let attempts = 20;
      while (attempts > 0) {
        const prox = await this.getCurrentProxy();
        if (!prox) {
          resolve(null);
          return;
        }
        try {
          await axios.get('https://google.com', {
            httpsAgent: new HttpsProxyAgent('http://' + prox),
          });
          console.log('proxy pass', prox);
          return resolve(prox);
        } catch (e) {
          attempts--;
          console.log('proxy error', prox, attempts);
          await new Promise(re => setTimeout(re, 2000));
        }
      }
      return resolve(null);
    });
  }

  async scheduleScanOrder() {
    const jobName = 'scan-tracking-orders';
    try {
      const repeatableJobs = await this.queue.getRepeatableJobs();
      console.log('repeatable', repeatableJobs);
      for (const job of repeatableJobs) {
        if (job.id !== jobName) {
          continue;
        }
        await this.queue.removeRepeatableByKey(job.key);
      }

      const queue = await this.queue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            cron: '*/20 * * * *',
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: false,
        },
      );

      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm'),
      );
    } catch (e) {
      console.log(e);
    }
  }

  @Process('scan-tracking-orders')
  async scanTrackingOrders() {
    try {
      await Promise.all([
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.jtexpress,
          countryCode: 63,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.jtexpress,
          countryCode: 66,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.ninjavan,
          countryCode: 66,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.ninjavan,
          countryCode: 65,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.ninjavan,
          countryCode: 63,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.bestexpress,
          countryCode: 60,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.bestexpress,
          countryCode: 66,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.bestexpress,
          countryCode: 84,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.flashexpress,
          countryCode: 60,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.flashexpress,
          countryCode: 66,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.kerryexpress,
          countryCode: 66,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.nimbus,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.jneexpress,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.poslaju,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.anousith,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.bluedart,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.hal,
        }),
        this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
          code: CarrierCode.shopee,
        }),
        // this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
        //   code: CarrierCode.jntbangkok,
        // }),
      ]);
    } catch (e) {}
    return {};
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'scan-tracking-order',
    queue: 'ffm-queue-scan-tracking-order',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async scanTrackingData({
    code,
    countryCode,
    cursor: lastCursor,
  }: {
    code: CarrierCode;
    countryCode: number;
    cursor: number;
  }) {
    console.log(code, countryCode);
    let cursor: number;
    const scanKey = `tracking.${code}.${countryCode}`;
    if (!lastCursor) {
      const last = await this.redisService.get(scanKey);
      if (last) {
        console.log('conflict scanning tracking', countryCode, code);
        return new Nack();
      }
    }
    await this.redisService.set(scanKey, 1, { ttl: 3600 });
    switch (code) {
      case CarrierCode.ninjavan:
        cursor = await this.scanNinjaVanOrder(null, lastCursor, countryCode);
        break;
      case CarrierCode.bestexpress:
        if (countryCode == 60) {
          cursor = await this.scanBestMyOrder(null, lastCursor);
        }
        if (countryCode == 84) {
          cursor = await this.scanBestVNOrder(null, lastCursor);
        }
        if (countryCode == 66) {
          cursor = await this.scanBestThOrder(null, lastCursor);
        }
        break;
      case CarrierCode.flashexpress:
        if (countryCode == 60) {
          cursor = await this.scanFlashMyOrder(null, lastCursor);
        }
        if (countryCode == 66) {
          cursor = await this.scanFlashThOrder(null, lastCursor);
        }
        break;
      case CarrierCode.kerryexpress:
        if (countryCode == 66) {
          cursor = await this.scanKerryThOrder(null, lastCursor);
        }
        break;
      case CarrierCode.jtexpress:
        if (countryCode == 63) {
          cursor = await this.scanJntPhiOrder(null, lastCursor);
        }
        if (countryCode == 66) {
          cursor = await this.scanJntThOrder(null, lastCursor);
        }
        break;
      case CarrierCode.nimbus:
        cursor = await this.scanNimbusOrder(null, lastCursor);
        break;
      case CarrierCode.jneexpress:
        cursor = await this.scanJneIdOrder(null, lastCursor);
        break;
      case CarrierCode.poslaju:
        cursor = await this.scanPosMyOrder(null, lastCursor);
        break;
      case CarrierCode.anousith:
        cursor = await this.scanAnousithOrder(null, lastCursor);
        break;
      case CarrierCode.bluedart:
        cursor = await this.scanBluedartOrder(null, lastCursor);
        break;
      case CarrierCode.hal:
        cursor = await this.scanHalOrder(null, lastCursor);
        break;
      case CarrierCode.shopee:
        cursor = await this.scanShopeeOrder(null, lastCursor);
        break;
      case CarrierCode.jntbangkok:
        cursor = await this.scanJntBangkokOrder(null, lastCursor);
        break;
    }
    if (cursor) {
      console.log('next cursor', code, countryCode, cursor);
      await this.amqpConnection.publish('ffm-order', 'scan-tracking-order', {
        code,
        countryCode,
        cursor,
      });
    } else {
      await this.redisService.del(scanKey);
    }
    return new Nack();
  }

  async getScanOrder({
    id,
    limit = 200,
    cursor,
    code,
    countryCode,
  }: {
    id?: number;
    limit?: number;
    cursor?: number;
    code: CarrierCode;
    countryCode?: number;
  }) {
    const query = this.orderRepository
      .createQueryBuilder('o')
      .innerJoinAndMapMany('o.carriers', 'o.carriers', 'oc')
      .where('o.status IN (:...statuses)', {
        statuses: ORDER_SHIPPING_STATUSES,
      })
      .andWhere('oc.status = :status', { status: CarrieStatusEnum.activated })
      .innerJoin('oc.carrier', 'occ')
      .andWhere('occ.code = :code', { code })
      .andWhere('oc.waybill_number IS NOT NULL')
      .take(limit || 200)
      .orderBy('o.id', 'ASC');
    if (cursor) {
      query.andWhere('o.id > :cursor', { cursor });
    }
    if (countryCode) {
      query.andWhere('o.countryId = :country', { country: `${countryCode}` });
    }
    if (id) {
      query.where('oc.id = :id', { id });
    }
    console.log('start scan from cursor', query.getQueryAndParameters());
    const orders = await query.getMany();
    console.log('scan from cursor', cursor, orders.length);
    return orders;
  }

  async scanJntPhiOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.jtexpress,
      countryCode: 63,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const trackingNumbers = group.map(i => i.lastCarrier.waybillNumber);
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const client = new JntExpressPhilippinesClient(
          process.env.PHI_JT_KEY,
          process.env.PHI_JT_CUS_ID,
          process.env.PHI_JT_EC_ID,
        );
        const data = await client.trackOrderData(...trackingNumbers);
        for (const tracking of data) {
          const order = orderMap[tracking?.billcode];
          const data = parseJntTrackings(tracking.details);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking?.billcode,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jtexpress]?.[63],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.billcode,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jtexpress]?.[63],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanJntPhiOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanJntPhiOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanBluedartOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.bluedart,
      countryCode: 91,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 50)) {
      try {
        const trackingNumbers = group.map(i => i.lastCarrier.waybillNumber);
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const res = await axios.get('https://api.bluedart.com/servlet/RoutingServlet', {
          params: {
            handler: 'tnt',
            action: 'custawbquery',
            loginid: 'BOM00001',
            awb: 'awb',
            numbers: trackingNumbers.join(','),
            format: 'xml',
            lickey: '4ecbd06dc0b9737d69120029cb05c9df',
            verno: 1,
            scan: 1,
          },
        });
        const json = new XMLParser({ ignoreAttributes: false }).parse(res.data);
        let data = json?.ShipmentData?.Shipment;
        if (!isArray(data)) {
          data = [data];
        }
        for (const tracking of data) {
          const order = orderMap[tracking['@_WaybillNo']];
          const data = parseBluedartTracking(tracking);
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking['@_WaybillNo'],
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bluedart],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking['@_WaybillNo'],
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bluedart],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanBluedartOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanBluedartOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanNimbusOrder(id?: number, cursor?: number) {
    if (!process.env.NIMBUS_USERNAME) {
      return;
    }
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.nimbus,
      countryCode: 91,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 50)) {
      try {
        const trackingNumbers = group.map(i => i.lastCarrier.waybillNumber);
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const client = new NimbusClient(
          process.env.NIMBUS_USERNAME,
          process.env.NIMBUS_PASSWORD,
          this.redisService,
        );
        const data = await client.trackOrderData(trackingNumbers);
        for (const tracking of data) {
          const order = orderMap[tracking.awb_number];
          const data = parseNimbusTracking(tracking);
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.awb_number,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.nimbus],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.awb_number,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.nimbus],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanNimbusOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanNimbusOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanPosMyOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.poslaju,
      countryCode: 60,
      cursor,
    });
    const syncOrders = [];
    for (const group1 of chunk(orders, 10)) {
      await Promise.all(
        chunk(group1, 2).map(async group => {
          try {
            const orderMap = reduce(
              group,
              (prev, item) => {
                prev[item.lastCarrier.waybillNumber] = item;
                return prev;
              },
              {} as Record<string, Order>,
            );
            const res = await this.retryFunc(async () => {
              const proxy = await this.getCurrentProxyWithCheck();
              console.log('using proxy', proxy);
              return await axios.post(
                'https://ttu-svc.pos.com.my/api/trackandtrace/v1/request',
                {
                  connote_ids: group.map(i => i.lastCarrier.waybillNumber),
                  culture: 'en',
                },
                {
                  headers: {
                    'content-type': 'application/json',
                    // 'user-agent': undefined,
                    origin: 'https://tracking.pos.com.my',
                    'user-agent':
                      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept-Encoding': 'gzip, deflate, br',
                    host: 'ttu-svc.pos.com.my',
                    Connection: 'keep-alive',
                    accept: 'application/json, text/plain, */*',
                  },
                  httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
                  timeout: 30000,
                },
              );
            });
            for (const tracking of res.data.data as IPosLajuTracking[]) {
              const order = orderMap[tracking.connote_id];
              const data = parsePosLajuTrackings(tracking);
              if (!order || isEmpty(data)) {
                continue;
              }
              await this.amqpConnection.publish(
                'ffm-order-tracking-data',
                'update-order-tracking-v2-1',
                {
                  waybill: tracking.connote_id,
                  updates: data,
                  orderId: order.id,
                  reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.poslaju]?.[60],
                } as IUpdateOrderTracking,
              );
              // const { data: result } = await this.amqpConnection.request<{
              //   data: ICrawlOrder;
              // }>({
              //   exchange: 'ffm-order-tracking-data',
              //   routingKey: 'new-update-order-tracking-2-1',
              //   payload: {
              //     waybill: tracking.connote_id,
              //     updates: data,
              //     orderId: order.id,
              //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.poslaju]?.[60],
              //   },
              //   timeout: 10000,
              // });

              // console.log('🚀🚀🚀 ~ OrderCarriersService ~ chunk ~ result:', result);
              // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
            }
          } catch (e) {
            console.log('get tracking data error', e);
          }
        }),
      );
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanPosMyOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanJneIdOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.jneexpress,
      countryCode: 62,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );

        await Promise.all(
          group.map(async i => {
            try {
              const wwproxy = new WWPROXY(this.redisService);
              const proxy = await wwproxy.getCurrentProxyWithCheck();

              console.log('using proxy', proxy);

              console.log(
                `start scan ${i.lastCarrier.waybillNumber}`,
                `https://cekresi.jne.co.id/${
                  i.lastCarrier.waybillNumber
                }?verify=${i?.recipientPhone?.slice(-5)}`,
              );
              const res = await axios.get(
                `https://cekresi.jne.co.id/${
                  i.lastCarrier.waybillNumber
                }?verify=${i?.recipientPhone?.slice(-5)}`,
                {
                  headers: {
                    lang: 'en',
                    Referer: 'https://www.jne.co.id/',
                    Accept:
                      'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                  },
                  httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
                },
              );
              const html = res.data;
              const $ = cheerio.load(html);
              const histories: IJneTracking[] = [];
              $('div.x_panel div.x_content ul.timeline li').map((i, el) => {
                const status = $(el)
                  .find('h2 a')
                  .text();
                const time = $(el)
                  .find('div.byline span')
                  .text();
                console.log(
                  'status',
                  $(el)
                    .find('h2 a')
                    .text(),
                );
                const item: IJneTracking = {
                  status,
                  updatedAt: moment.tz(time, 'DD-MM-YYYY HH:mm', 'Asia/Jakarta').toDate(),
                };
                histories.push(item);
              });
              if (isEmpty(histories)) {
                return;
              }
              console.log('res', histories);
              const order = orderMap[i.lastCarrier.waybillNumber];
              const data = parseJneIdTrackings(histories);
              if (!order || order.lastCarrier.updatedAt == data[0]?.updatedAt) {
                console.log(
                  `skip update ${order.lastCarrier.waybillNumber}`,
                  order.lastCarrier.updatedAt,
                  data[0]?.updatedAt,
                );
                return res.data;
              }
              await this.amqpConnection.publish(
                'ffm-order-tracking-data',
                'update-order-tracking-v2-1',
                {
                  waybill: order.lastCarrier.waybillNumber,
                  updates: data,
                  orderId: order.id,
                  reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jneexpress]?.[66],
                } as IUpdateOrderTracking,
              );
              // const { data: result } = await this.amqpConnection.request<{
              //   data: ICrawlOrder;
              // }>({
              //   exchange: 'ffm-order-tracking-data',
              //   routingKey: 'new-update-order-tracking-2-1',
              //   payload: {
              //     waybill: order.lastCarrier.waybillNumber,
              //     updates: data,
              //     orderId: order.id,
              //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jneexpress]?.[66],
              //   },
              //   timeout: 10000,
              // });

              // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanJneIdOrder ~ result:', result);
              // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
              return res.data;
            } catch (e) {
              console.log(`get tracking data error ${i.lastCarrier.waybillNumber}`, e);
            }
          }),
        );
      } catch (e) {
        console.log('get tracking data error', e);
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanJneIdOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanJntThOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.jtexpress,
      countryCode: 66,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );

        await Promise.all(
          group.map(async i => {
            try {
              console.log(`start scan ${i.lastCarrier.waybillNumber}`);
              const phone = i.recipientPhone?.slice(-4);
              if (phone?.length !== 4) {
                return;
              }
              const res = await axios.post(
                `https://jtapi.jtexpress.co.th/jts-tha-website-api/api/v2/track/orderTrack`,
                {
                  billCode: i.lastCarrier.waybillNumber,
                  lang: 'en',
                  phoneVerify: phone,
                },
                {
                  headers: {
                    lang: 'en',
                  },
                },
              );
              const tracking = res.data?.data;
              const order = orderMap[tracking?.billCode];
              const data = parseJntThTrackings(tracking?.details);
              if (!order || order.lastCarrier.updatedAt == data[0]?.updatedAt) {
                console.log(
                  `skip update ${tracking?.billCode}`,
                  order.lastCarrier.updatedAt,
                  data[0]?.updatedAt,
                );
                return res.data;
              }
              await this.amqpConnection.publish(
                'ffm-order-tracking-data',
                'update-order-tracking-v2-1',
                {
                  waybill: tracking?.billCode,
                  updates: data,
                  orderId: order.id,
                  reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jtexpress]?.[66],
                } as IUpdateOrderTracking,
              );
              // const { data: result } = await this.amqpConnection.request<{
              //   data: ICrawlOrder;
              // }>({
              //   exchange: 'ffm-order-tracking-data',
              //   routingKey: 'new-update-order-tracking-2-1',
              //   payload: {
              //     waybill: tracking.billCode,
              //     updates: data,
              //     orderId: order.id,
              //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jtexpress]?.[66],
              //   },
              //   timeout: 10000,
              // });

              // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanJntThOrder ~ result:', result);
              // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
              return res.data;
            } catch (e) {
              console.log(`get tracking data error ${i.lastCarrier.waybillNumber}`, e);
            }
          }),
        );
      } catch (e) {
        console.log('get tracking data error', e);
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanJntThOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async retryFunc<T>(
    func: () => Promise<T>,
    onError?: (e) => Promise<void>,
    skipError?: (e) => boolean,
    attempts = 10,
    timeout = 2000,
  ): Promise<T> {
    while (attempts > 0) {
      try {
        const res = await func();
        return res;
      } catch (e) {
        attempts--;
        if (!attempts) {
          throw e;
        }
        if (onError) {
          await onError(e);
        }
        if (skipError && skipError(e)) {
          throw e;
        }
        await new Promise(resolve => setTimeout(resolve, timeout));
      }
    }
  }

  async scanNinjaVanOrder(id?: number, cursor?: number, countryCode?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.ninjavan,
      cursor,
      countryCode,
    });
    const syncOrders = [];
    for (const group of chunk(orders)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        await Promise.all(
          group.map(async i => {
            try {
              const res = await this.retryFunc(
                async () => {
                  const proxy = await this.getCurrentProxyWithCheck();
                  return await axios.get(
                    `https://walrus.ninjavan.co/${
                      NJV_COUNTRY_CODES[i.countryId]
                    }/dash/1.2/public/orders`,
                    {
                      params: {
                        tracking_id: i.lastCarrier.waybillNumber,
                      },
                      httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
                      timeout: 30000,
                    },
                  );
                },
                async e => {
                  console.log('tracking error', e?.response?.data);
                  if (e?.response?.status == 429) {
                    await this.getCurrentProxy(true);
                  }
                },
                e => e?.response?.status !== 429,
              );
              const tracking = res?.data;
              const order = orderMap[tracking.tracking_id];
              const data = parseNJVTrackings(tracking?.events);
              if (!order || order.lastCarrier.updatedAt == data[0]?.updatedAt) {
                return res?.data;
              }
              await this.amqpConnection.publish(
                'ffm-order-tracking-data',
                'update-order-tracking-v2-1',
                {
                  waybill: tracking.tracking_id,
                  updates: data,
                  orderId: order.id,
                  reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.ninjavan],
                } as IUpdateOrderTracking,
              );

              // const { data: result } = await this.amqpConnection.request<{
              //   data: ICrawlOrder;
              // }>({
              //   exchange: 'ffm-order-tracking-data',
              //   routingKey: 'new-update-order-tracking-2-1',
              //   payload: {
              //     waybill: tracking.tracking_id,
              //     updates: data,
              //     orderId: order.id,
              //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.ninjavan],
              //   },
              //   timeout: 10000,
              // });
              // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanNinjaVanOrder ~ result:', result);
              // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
              return res?.data;
            } catch (e) {
              console.log(`get tracking data error ${i.lastCarrier.waybillNumber}`, e);
            }
          }),
        );
      } catch (e) {
        console.log('get tracking njv data error', e);
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanNinjaVanOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanBestMyOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.bestexpress,
      countryCode: 60,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const client = new BestExpressMalaysiaClient(
          process.env.BEST_MY_USERNAME,
          process.env.BEST_MY_PASSWORD,
          this.redisService,
          'https://v9-cc.800best.com',
        );
        const trackings = await client.trackOrderData(group.map(i => i.lastCarrier.waybillNumber));
        for (const tracking of trackings) {
          const order = orderMap[tracking.expressId];
          const data = parseBestMyTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.expressId,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bestexpress],
            } as IUpdateOrderTracking,
          );

          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.expressId,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bestexpress],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanBestMyOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanBestMyOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanBestVNOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.bestexpress,
      countryCode: 84,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const client = new BestExpressMalaysiaClient(
          process.env.BEST_VN_USERNAME,
          process.env.BEST_VN_PASSWORD,
          this.redisService,
          'https://v9-cc.800best.com',
        );
        const trackings = await client.trackOrderData(group.map(i => i.lastCarrier.waybillNumber));
        for (const tracking of trackings) {
          const order = orderMap[tracking.expressId];
          const data = parseBestMyTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.expressId,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bestexpress],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.expressId,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bestexpress],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanBestVNOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanBestVNOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanBestThOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.bestexpress,
      countryCode: 66,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const client = new BestExpressThailandClient({
          userAccount: process.env.BEST_TH_USERNAME,
          password: process.env.BEST_TH_PASSWORD,
        });
        const { trackings } = await client.trackOrderData(
          group.map(i => i.lastCarrier.waybillNumber),
        );
        for (const tracking of trackings) {
          const order = orderMap[tracking.mailNo];
          const data = parseBestThTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.mailNo,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bestexpress],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.mailNo,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.bestexpress],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanBestThOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanBestThOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanHalOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({ id, code: CarrierCode.hal, countryCode: 856, cursor });
    const syncOrders = [];
    for (const group of chunk(orders, 1)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const resData = await Promise.all(
          group.map(async i => {
            console.log(`start scan ${i.lastCarrier.waybillNumber}`);
            try {
              const res = await axios.get(
                `https://hal.hal-logistics.la/api/v1/orders/tracking/${i.lastCarrier.waybillNumber}?lang=english`,
              );
              if (res.data) {
                return res.data;
              }
              return null;
            } catch (e) {
              console.log(`scan ${i.lastCarrier.waybillNumber} error`, e);
              return null;
            }
          }),
        );
        const trackings = resData as IHalTracking[];
        for (const tracking of trackings) {
          if (!tracking?.shipment_info?.bill_number) {
            continue;
          }
          const order = orderMap[tracking.shipment_info.bill_number];
          const data = parseHalTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.shipment_info.bill_number,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.hal]?.[60],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.shipment_info.bill_number,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.hal]?.[60],
          //   },
          //   timeout: 10000,
          // });
          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanHalOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
      await new Promise(re => setTimeout(re, 1000));
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanHalOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanShopeeOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.shopee,
      countryCode: CountryID.MY,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const dataShopee = [];
        const dataFlash = [];
        const orderMap = reduce(
          group,
          (prev, item) => {
            if (startsWith(item.lastCarrier.waybillNumber, 'M')) {
              dataFlash.push(item);
            } else dataShopee.push(item);
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );

        if (dataShopee?.length > 0) {
          const resDataShopee = await Promise.all(
            dataShopee?.map(async i => {
              try {
                const res = await new ShopeeExpressClient().trackOrderData(
                  i.lastCarrier.waybillNumber,
                );
                console.log(222, res);

                if (res) {
                  return res;
                }
                return null;
              } catch (e) {
                return null;
              }
            }),
          );

          for (const tracking of resDataShopee) {
            if (!tracking?.sls_tracking_number) {
              continue;
            }
            const order = orderMap[tracking.sls_tracking_number];
            const data = parseShopeeTrackings(tracking);
            if (!order) {
              continue;
            }

            await this.amqpConnection.publish(
              'ffm-order-tracking-data',
              'update-order-tracking-v2-1',
              {
                waybill: tracking.sls_tracking_number,
                updates: data,
                orderId: order.id,
                reason: null,
              } as IUpdateOrderTracking,
            );
            // const { data: result } = await this.amqpConnection.request<{
            //   data: ICrawlOrder;
            // }>({
            //   exchange: 'ffm-order-tracking-data',
            //   routingKey: 'new-update-order-tracking-2-1',
            //   payload: {
            //     waybill: tracking.sls_tracking_number,
            //     updates: data,
            //     orderId: order.id,
            //     reason: null,
            //   },
            //   timeout: 10000,
            // });

            // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanShopeeOrder ~ result:', result);
            // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
          }
        }

        if (dataFlash?.length > 0) {
          const resData = (
            await axios.post(
              'https://www.flashexpress.my/webApi/tools/tracking',
              { search: dataFlash.map(i => i.lastCarrier.waybillNumber).join(' ') },
              {
                headers: {
                  Origin: 'https://www.flashexpress.my',
                  Referer: 'https://www.flashexpress.my/',
                  'User-Agent':
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
                  'content-type': 'application/json',
                  Cookie:
                    'aliyungf_tc=383615c17a241ef3d0e23c90747e4b0bb620c27199ffb3980b65d1a0ec843cd1',
                },
              },
            )
          ).data;
          const trackings = resData?.data?.list as IFlashCrawlOrder[];
          console.log('trackings', trackings);
          for (const tracking of trackings) {
            const order = orderMap[tracking.search_no];
            const data = parseFlashMyTrackings(tracking);
            if (!order) {
              continue;
            }
            await this.amqpConnection.publish(
              'ffm-order-tracking-data',
              'update-order-tracking-v2-1',
              {
                waybill: tracking.search_no,
                updates: data,
                orderId: order.id,
                reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.flashexpress]?.[60],
              } as IUpdateOrderTracking,
            );
            // const { data: result } = await this.amqpConnection.request<{
            //   data: ICrawlOrder;
            // }>({
            //   exchange: 'ffm-order-tracking-data',
            //   routingKey: 'new-update-order-tracking-2-1',
            //   payload: {
            //     waybill: tracking.search_no,
            //     updates: data,
            //     orderId: order.id,
            //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.flashexpress]?.[60],
            //   },
            //   timeout: 10000,
            // });

            // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanShopeeOrder ~ result:', result);
            // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
          }
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanShopeeOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanAnousithOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.anousith,
      countryCode: 856,
      cursor,
    });

    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const resData = await Promise.all(
          group.map(async i => {
            try {
              const res = await axios.post(
                'https://pro.api.anousith.express/graphql',
                {
                  operationName: 'ItemsV2',
                  variables: {
                    where: {
                      searchMultiple: i.lastCarrier.waybillNumber,
                      isDeleted: 0,
                    },
                  },
                  query:
                    'query ItemsV2($where:ItemV2WhereInput){itemsV2(where:$where){data{trackingId\ncreatedDate\noriginReceiveDate\noriginSendDate\ndestReceiveDate\ndestSendDate\nsendCompleteDate\nreceiveBackwardDate\nisBackward\nbackwardDetail\n}}}',
                },
                {
                  headers: {
                    'content-type': 'application/json',
                    Referer: 'https://app.anousith-express.com/',
                  },
                },
              );
              if (res.data.data.itemsV2?.data) {
                return res.data.data.itemsV2.data[0];
              }
              return null;
            } catch (e) {
              return null;
            }
          }),
        );
        const trackings = resData as IAnousithTracking[];
        console.log('trackings', trackings);
        for (const tracking of trackings) {
          if (!tracking) {
            continue;
          }
          const order = orderMap[tracking.trackingId];
          const data = parseAnousithTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.trackingId,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.anousith]?.[60],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.trackingId,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.anousith]?.[60],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanAnousithOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanAnousithOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanFlashMyOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.flashexpress,
      countryCode: 60,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const wwproxy = new WWPROXY(this.redisService);
        const proxy = await wwproxy.getCurrentProxyWithCheck();

        console.log('using proxy', proxy);

        const resData = (
          await axios.post(
            'https://www.flashexpress.my/webApi/tools/tracking',
            { search: group.map(i => i.lastCarrier.waybillNumber).join(' ') },
            {
              headers: {
                Origin: 'https://www.flashexpress.my',
                Referer: 'https://www.flashexpress.my/',
                'User-Agent':
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
                'content-type': 'application/json',
                Cookie:
                  'aliyungf_tc=383615c17a241ef3d0e23c90747e4b0bb620c27199ffb3980b65d1a0ec843cd1',
              },
              httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
              timeout: 30000,
            },
          )
        )?.data;
        console.log('resData', resData?.data);
        const trackings = resData?.data?.list as IFlashCrawlOrder[];
        console.log('trackings', trackings);
        for (const tracking of trackings) {
          const order = orderMap[tracking.search_no];
          const data = parseFlashMyTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.search_no,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.flashexpress]?.[60],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.search_no,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.flashexpress]?.[60],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanFlashMyOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanFlashMyOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanFlashThOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.flashexpress,
      countryCode: 66,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );
        const resData = (
          await axios.post(
            'https://www.flashexpress.com/webApi/tools/tracking',
            { search: group.map(i => i.lastCarrier.waybillNumber).join(' ') },
            {
              headers: {
                Origin: 'https://www.flashexpress.com',
                Referer: 'https://www.flashexpress.com/',
                'User-Agent':
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
                'content-type': 'application/json',
              },
            },
          )
        ).data;
        const trackings = resData?.data?.list as IFlashCrawlOrder[];
        for (const tracking of trackings) {
          const order = orderMap[tracking.search_no];
          const data = parseFlashMyTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.search_no,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.flashexpress]?.[66],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.search_no,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.flashexpress]?.[66],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanFlashThOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanFlashThOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanKerryThOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.kerryexpress,
      countryCode: 66,
      cursor,
    });
    const syncOrders = [];
    for (const group of chunk(orders, 20)) {
      try {
        const orderMap = reduce(
          group,
          (prev, item) => {
            prev[item.lastCarrier.waybillNumber] = item;
            return prev;
          },
          {} as Record<string, Order>,
        );

        const res = await axios({
          method: 'post',
          url: 'https://th.kerryexpress.com/website-api/api/Tracking/v1/Encryption/Consignment',
          data: {
            consignment_no: group.map(i => i.lastCarrier.waybillNumber),
          },
        });
        if (!res.data?.result) {
          throw new Error('can not get transport');
        }
        const resData = (
          await axios({
            method: 'post',
            url: `https://th.kex-express.com/en/track/v2/?track=${res.data?.result}`,
            params: { tracking_no: group.map(i => i.lastCarrier.waybillNumber)?.join(',') },
            headers: {
              'kett-lang': 'en',
              Origin: 'https://th.kex-express.com',
              'User-Agent':
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36',
            },
            timeout: 10000,
          })
        ).data;
        const trackings = resData as IKerryTrackingData[];
        console.log(
          '🚬 ~ OrderCarriersService ~ scanKerryThOrder ~ IKerryTrackingData:',
          trackings,
        );
        for (const tracking of trackings) {
          const order = orderMap[tracking.tracking_no];
          const data = parseFlashKerryTrackings(tracking);
          if (!order) {
            continue;
          }
          await this.amqpConnection.publish(
            'ffm-order-tracking-data',
            'update-order-tracking-v2-1',
            {
              waybill: tracking.tracking_no,
              updates: data,
              orderId: order.id,
              reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.kerryexpress],
            } as IUpdateOrderTracking,
          );
          // const { data: result } = await this.amqpConnection.request<{
          //   data: ICrawlOrder;
          // }>({
          //   exchange: 'ffm-order-tracking-data',
          //   routingKey: 'new-update-order-tracking-2-1',
          //   payload: {
          //     waybill: tracking.tracking_no,
          //     updates: data,
          //     orderId: order.id,
          //     reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.kerryexpress],
          //   },
          //   timeout: 10000,
          // });

          // console.log('🚀🚀🚀 ~ OrderCarriersService ~ scanKerryThOrder ~ result:', result);
          // if (!isEmpty(result) && result?.code == 200) syncOrders.push(result?.order);
        }
      } catch (e) {
        console.log(
          `get tracking data error ${group.map(i => i.lastCarrier.waybillNumber).join(', ')}`,
          e,
        );
      }
    }
    // console.log('🚀 ~ OrderCarriersService ~ scanKerryThOrder ~ syncOrders:', syncOrders);
    // if (!isEmpty(syncOrders)) {
    //   for (const group of chunk(syncOrders, 500)) {
    //     await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
    //       orders: group,
    //     });
    //   }
    // }
    return last(orders)?.id;
  }

  async scanJntBangkokOrder(id?: number, cursor?: number) {
    const orders = await this.getScanOrder({
      id,
      code: CarrierCode.jntbangkok,
      countryCode: 66,
      cursor,
    });
    for (const order of orders) {
      try {
        const client = new JntBangkokClient(
          order?.lastCarrier?.config?.extraData?.key,
          order?.lastCarrier?.config?.carrierId,
          order?.lastCarrier?.config?.extraData?.password,
          order?.lastCarrier?.config?.extraData?.apiAccount,
        )
        const { data, error } = await client.trackingOrder(order);
        if(error){
          console.log(`Order ${order?.displayId} error`, error);
          continue;
        }
        if(data?.billCode != order?.lastCarrier?.waybillNumber){
          console.log(`Order ${order?.displayId} not have waybill ${data?.billCode}`);
          continue;
        } 
        if(isEmpty(data?.tracesList)){
          console.log(`Order ${order?.displayId} no data`);
          continue;
        } 
        const updates = parseJntBangkokTrackings(data?.tracesList);

        await this.amqpConnection.publish(
          'ffm-order-tracking-data',
          'update-order-tracking-v2-1',
          {
            waybill: data?.billCode,
            updates: updates,
            orderId: order.id,
            reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jntbangkok],
          } as IUpdateOrderTracking,
        );
      } catch (e) {
        console.log(
          `get tracking data error ${order?.lastCarrier?.waybillNumber}`,
          e,
        );
      }
    }
    return last(orders)?.id;
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'update-order-tracking',
    queue: 'ffm-queue-update-order-tracking',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async handleOrderTracking({ waybill, updates, orderId, reason }: IUpdateOrderTracking) {
    if (isEmpty(updates)) {
      return new Nack();
    }
    for (let i = updates.length - 1; i >= 0; i--) {
      const current = updates[i];
      const next = updates[i - 1];
      if (next?.updatedAt && next.updatedAt === current.updatedAt) {
        console.log('add 1 seconds');
        updates[i - 1].updatedAt = moment(next.updatedAt)
          .add(1, 'second')
          .toDate();
      }
    }

    const [order, reImportOrder] = await Promise.all([
      this.orderRepository.findOne({
        where: {
          id: orderId,
        },
        relations: ['carriers'],
      }),
      this.reImportRepository.findOne({
        where: qb => {
          qb.andWhere({
            orderId,
          });
        },
      }),
    ]);

    if (!order || order.lastCarrier?.waybillNumber !== waybill) {
      console.log(`update-order-tracking ${waybill} order not found`);
      return new Nack(false);
    }
    const lastUpdate = updates[0];
    let force = false;
    if (
      [OrderFFMStatus.Returned, OrderFFMStatus.Delivered, OrderFFMStatus.Canceled].includes(
        lastUpdate.status,
      )
    ) {
      force = true;
    }
    console.log('lastUpdate', lastUpdate, order.lastCarrier.updatedAt);
    if (!lastUpdate) {
      console.log(`update-order-tracking ${waybill} order's updates not found`);
      return new Nack(false);
    }
    let shouldUpdate = true;
    if (
      !force &&
      order.lastCarrier.updatedAt &&
      order.lastCarrier.updatedAt >= new Date(lastUpdate.updatedAt)
    ) {
      console.log(`update-order-tracking ${waybill} order already updated`);
      shouldUpdate = false;
      if (order.lastCarrier.updatedAt > new Date(lastUpdate.updatedAt)) {
        return new Nack(false);
      }
    }
    let nextStatus = lastUpdate.status;
    if (updates.find(i => i.status === OrderFFMStatus.InReturn) && nextStatus < 16) {
      nextStatus = OrderFFMStatus.InReturn;
    }
    if (ORDER_RETURN_STATUSES.includes(order.status)) {
      nextStatus = [OrderFFMStatus.Delivered, OrderFFMStatus.Returned].includes(nextStatus)
        ? nextStatus
        : undefined;
    }
    console.log('nextStatus', nextStatus, orderId);
    if (
      [
        OrderFFMStatus.Awaiting3PLPickup,
        OrderFFMStatus.Collecting,
        OrderFFMStatus.AwaitingCollection,
      ]?.includes(nextStatus) &&
      [
        OrderFFMStatus.InTransit,
        OrderFFMStatus.Stocked3PL,
        OrderFFMStatus.InDelivery,
        OrderFFMStatus.FailedDelivery,
        OrderFFMStatus.Delivered,
        OrderFFMStatus.AwaitingReturn,
        OrderFFMStatus.InReturn,
        OrderFFMStatus.Returned,
      ]?.includes(order?.status)
    ) {
      return new Nack(false);
    }

    //block update finance status
    if (
      [
        OrderFFMStatus.Delivered,
        OrderFFMStatus.ReturnedStocked,
        OrderFFMStatus.ReturnedCompleted,
        OrderFFMStatus.DeliveredCompleted,
        OrderFFMStatus.ReturnedDamagedBy3PL,
        OrderFFMStatus.LostBy3PL,
        OrderFFMStatus.DamagedByWH,
        OrderFFMStatus.LostByWH,
        OrderFFMStatus.DamagedCompleted,
        OrderFFMStatus.LostCompleted,
      ]?.includes(order?.status) ||
      !!reImportOrder?.id
    ) {
      console.log('Not update order in finally status!');
      return new Nack(false);
    }

    const reasonData: IReason = {
      reasonCode: '',
      reasonNote: '',
    };

    if (
      [
        OrderFFMStatus.FailedDelivery,
        OrderFFMStatus.InReturn,
        OrderFFMStatus.AwaitingReturn,
      ]?.includes(nextStatus) &&
      !!updates?.[0]?.note
    ) {
      const _reason = find(reason, function(o) {
        return updates?.[0]?.note?.search(o.key) > -1;
      });
      if (!!_reason?.value) {
        reasonData.reasonCode = _reason?.value;
        reasonData.reasonNote = updates?.[0]?.note;
      } else {
        reasonData.reasonCode = `Other`;
        reasonData.reasonNote = updates?.[0]?.note;
      }
    }
    // return new Nack();
    try {
      if (shouldUpdate) {
        await this.orderCarrierRepository
          .createQueryBuilder()
          .update()
          .set({
            updatedAt: lastUpdate.updatedAt,
            updates: updates,
            lastUpdatedBy: SystemIdEnum.system,
            status:
              !isNil(nextStatus) && nextStatus === OrderFFMStatus.Canceled
                ? CarrieStatusEnum.canceled
                : CarrieStatusEnum.activated,
          })
          .where(
            force
              ? {
                  id: order.lastCarrier.id,
                }
              : [
                  {
                    id: order.lastCarrier.id,
                    updatedAt: LessThan(lastUpdate.updatedAt),
                  },
                  {
                    id: order.lastCarrier.id,
                    updatedAt: IsNull(),
                  },
                ],
          )
          .execute();

        if (nextStatus === OrderFFMStatus.Canceled) {
          await this.owRepository
            .createQueryBuilder()
            .insert()
            .into(OrderWarning)
            .values({
              orderId,
              warningType: WarningType.CancelWaybill,
              status: WarningStatus.Enable,
              carrierId: 0,
            })
            .orIgnore()
            .execute();
        }
      }
      if (!nextStatus || order.status === nextStatus || nextStatus === OrderFFMStatus.Canceled) {
        console.log(`update-order-tracking ${waybill} order status should not update`);
        return new Nack(false);
      }

      // const orderCarrierHistory = await this.carrierHistoryRepository.findOne({
      //   where: {
      //     companyId: order?.companyId,
      //     orderId,
      //     orderCarrierId: order.lastCarrier.id,
      //     updatedAt: lastUpdate.updatedAt
      //   }
      // })
      //
      // if (!orderCarrierHistory) {
      // }

      if (!!updates && updates?.length > 0) {
        const logCarriers = updates
          ?.map((item: any) => {
            return plainToInstance(OrderCarrierHistories, {
              companyId: order?.companyId,
              orderId,
              orderCarrierId: order?.lastCarrier?.id,
              note: item?.note,
              status: item?.status,
              updatedAt: item?.updatedAt,
              countryId: order?.countryId,
            });
          })
          .filter(i => !!i.updatedAt && !!i.orderCarrierId && !!i.status);
        await this.carrierHistoryRepository.upsert(
          uniqBy(logCarriers, i => `${i.updatedAt}.${i.orderCarrierId}.${i.status}`),
          ['updatedAt', 'orderCarrierId', 'status'],
        );
      }
      await this.amqpConnection.publish('ffm-order', 'stock-update-status-order-v2', {
        id: orderId,
        status: nextStatus,
        user: {
          id: SystemIdEnum.system,
          companyId: order.companyId,
        },
        typeUpdateStatus: TypeUpdateOrderStatus.By3PL,
        lastUpdateStatus: lastUpdate?.updatedAt,
        reason: reasonData,
        isUpdateStatus3PL: true,
      });
    } catch (e) {
      throw e;
    }
    console.log(order, nextStatus);
    return order;
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'update-order-tracking-v2',
    queue: 'ffm-queue-update-order-tracking-v2',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async handleOrderTrackingV2({
    waybill,
    updates,
    orderId,
    reason,
    lastDataUpdate,
  }: IUpdateOrderTracking) {
    if (isEmpty(updates)) {
      return new Nack();
    }

    const [order, reImportOrder] = await Promise.all([
      this.orderRepository.findOne({
        where: {
          id: orderId,
        },
        relations: ['carriers'],
      }),
      this.reImportRepository.findOne({
        where: qb => {
          qb.andWhere({
            orderId,
          });
        },
      }),
    ]);

    if (!order || order.lastCarrier?.waybillNumber !== waybill) {
      console.log(`update-order-tracking ${waybill} order not found`);
      return new Nack(false);
    }

    if (lastDataUpdate) {
      updates = uniqBy(
        [lastDataUpdate, ...(order.lastCarrier?.updates || [])],
        i => `${i.updatedAt}.${i.status}`,
      );
    }

    if (updates?.length > 1)
      for (let i = updates.length - 1; i >= 0; i--) {
        const current = updates[i];
        const next = updates[i - 1];
        if (next?.updatedAt && next.updatedAt === current.updatedAt) {
          console.log('add 1 seconds');
          updates[i - 1].updatedAt = moment(next.updatedAt)
            .add(1, 'second')
            .toDate();
        }
      }

    // Chặn không cập nhật từ 3PL khi trạng thái đơn hàng đang ở trạng thái cuối
    if (
      NOT_ALLOW_UPDATE_STATUS_BY_3PL_OUT_FOR_DELIVERY?.includes(order?.status) ||
      !!reImportOrder?.id
    ) {
      console.log(`update-order-tracking ${waybill} order status should not update`);
      return new Nack(false);
    }

    const lastUpdate = updates[0];
    let force = false;
    if (
      [OrderFFMStatus.Returned, OrderFFMStatus.Delivered, OrderFFMStatus.Canceled].includes(
        lastUpdate.status,
      )
    ) {
      force = true;
    }
    console.log('lastUpdate', lastUpdate, order.lastCarrier.updatedAt);
    if (!lastUpdate) {
      console.log(`update-order-tracking ${waybill} order's updates not found`);
      return new Nack(false);
    }
    let shouldUpdate = true;
    if (
      !force &&
      order.lastCarrier.updatedAt &&
      order.lastCarrier.updatedAt >= new Date(lastUpdate.updatedAt)
    ) {
      console.log(`update-order-tracking ${waybill} order already updated`);
      shouldUpdate = false;
      if (order.lastCarrier.updatedAt > new Date(lastUpdate.updatedAt)) {
        return new Nack(false);
      }
    }
    let nextStatus = lastUpdate.status;
    if (ORDER_RETURN_STATUSES.includes(order.status)) {
      nextStatus = [OrderFFMStatus.Delivered, OrderFFMStatus.Returned].includes(nextStatus)
        ? nextStatus
        : undefined;
    }
    console.log('nextStatus', nextStatus, orderId);
    if (
      [
        OrderFFMStatus.Awaiting3PLPickup,
        OrderFFMStatus.Collecting,
        OrderFFMStatus.AwaitingCollection,
      ]?.includes(nextStatus) &&
      [
        OrderFFMStatus.InTransit,
        OrderFFMStatus.Stocked3PL,
        OrderFFMStatus.InDelivery,
        OrderFFMStatus.FailedDelivery,
        OrderFFMStatus.Delivered,
        OrderFFMStatus.AwaitingReturn,
        OrderFFMStatus.InReturn,
        OrderFFMStatus.Returned,
      ]?.includes(order?.status)
    ) {
      console.log(`update-order-tracking ${waybill} order status should not update`);
      return new Nack(false);
    }

    // return new Nack();
    try {
      if (shouldUpdate) {
        await this.orderCarrierRepository
          .createQueryBuilder()
          .update()
          .set({
            updatedAt: lastUpdate.updatedAt,
            updates: updates,
            lastUpdatedBy: SystemIdEnum.system,
            status:
              !isNil(nextStatus) && nextStatus === OrderFFMStatus.Canceled
                ? CarrieStatusEnum.canceled
                : CarrieStatusEnum.activated,
          })
          .where(
            force
              ? {
                  id: order.lastCarrier.id,
                }
              : [
                  {
                    id: order.lastCarrier.id,
                    updatedAt: LessThan(lastUpdate.updatedAt),
                  },
                  {
                    id: order.lastCarrier.id,
                    updatedAt: IsNull(),
                  },
                ],
          )
          .execute();

        if (nextStatus === OrderFFMStatus.Canceled) {
          await this.owRepository
            .createQueryBuilder()
            .insert()
            .into(OrderWarning)
            .values({
              orderId,
              warningType: WarningType.CancelWaybill,
              status: WarningStatus.Enable,
              carrierId: 0,
            })
            .orIgnore()
            .execute();
        }
      }
      if (!nextStatus || order.status === nextStatus || nextStatus === OrderFFMStatus.Canceled) {
        console.log(`update-order-tracking ${waybill} order status should not update`);
        return new Nack(false);
      }

      if (!!updates && updates?.length > 0) {
        let logCarriers = updates
          ?.map((item: any) => {
            return plainToInstance(OrderCarrierHistories, {
              companyId: order?.companyId,
              orderId,
              orderCarrierId: order?.lastCarrier?.id,
              note: item?.note,
              status: item?.status,
              updatedAt: item?.updatedAt,
              countryId: order?.countryId,
            });
          })
          .filter(i => !!i.updatedAt && !!i.orderCarrierId && !!i.status);
        logCarriers = uniqBy(logCarriers, i => `${i.updatedAt}.${i.orderCarrierId}.${i.status}`);

        let listOrderCarrierLog = await this.carrierHistoryRepository.find({
          where: qb => {
            qb.andWhere({
              companyId: order?.companyId,
              orderCarrierId: order?.lastCarrier?.id,
            });
          },
        });
        listOrderCarrierLog = uniqBy(
          listOrderCarrierLog,
          i => `${i.updatedAt}.${i.orderCarrierId}.${i.status}`,
        );
        const updateStatusOrders = differenceWith(
          logCarriers,
          listOrderCarrierLog,
          (newOd, oldOd) =>
            new Date(newOd.updatedAt).getTime() == new Date(oldOd.updatedAt).getTime() &&
            Number(newOd.orderCarrierId) == Number(oldOd.orderCarrierId) &&
            Number(newOd.status) == Number(oldOd.status),
        );
        await this.carrierHistoryRepository.upsert(logCarriers, [
          'updatedAt',
          'orderCarrierId',
          'status',
        ]);
        console.log('list-data-sync-late', updateStatusOrders);

        if (updateStatusOrders?.length > 0) {
          if (updateStatusOrders?.length > 0) {
            const sortDataUpdate = orderBy(updateStatusOrders, 'updatedAt', 'asc');
            console.log('sortDataUpdate', sortDataUpdate);
            for (const el of sortDataUpdate) {
              const reasonData: IReason = {
                reasonCode: '',
                reasonNote: '',
              };

              if (
                [
                  OrderFFMStatus.FailedDelivery,
                  OrderFFMStatus.InReturn,
                  OrderFFMStatus.AwaitingReturn,
                ]?.includes(el?.status) &&
                !!el?.note
              ) {
                const _reason = find(reason, function(o) {
                  return el?.note?.search(o.key) > -1;
                });
                if (!!_reason?.value) {
                  reasonData.reasonCode = _reason?.value;
                  reasonData.reasonNote = el?.note;
                } else {
                  reasonData.reasonCode = `Other`;
                  reasonData.reasonNote = el?.note;
                }
              }
              try {
                await this.amqpConnection.request({
                  exchange: 'ffm-order',
                  routingKey: 'stock-update-status-order-v2',
                  payload: {
                    id: orderId,
                    status: el?.status,
                    user: {
                      id: SystemIdEnum.system,
                      companyId: order.companyId,
                    },
                    typeUpdateStatus: TypeUpdateOrderStatus.By3PL,
                    lastUpdateStatus: el?.updatedAt,
                    reason: reasonData,
                    isUpdateStatus3PL: true,
                  },
                  timeout: 10000,
                });
              } catch (error) {
                console.log('🚀 ~ OrderCarriersService ~ error:', error);
              }
            }
          }
        }
      }
    } catch (e) {
      throw e;
    }
    console.log(order, nextStatus);
    return new Nack();
  }

  async getLabel(id: number) {
    const order = await this.orderCarrierRepository.findOne(id, { relations: ['carrier'] });
    if (!order) {
      return;
    }
    if (order.carrier?.code === CarrierCode.nimbus) {
      if (
        order.extraData &&
        order.extraData.waybill_url &&
        order.extraData.destination_code &&
        order.extraData.courier_name
      ) {
        return order.extraData;
      }
      const label = order.extraData?.waybill_url;
      let id;
      const config = await this.carrierConfigurationRepository.findOne({
        carrierCode: CarrierCode.nimbus,
        isDefault: true,
      });
      const client = new NimbusClient(
        config.carrierId,
        config.extraData.password,
        this.redisService,
      );
      if (!label) {
        const data = await client.trackOrderData([order.waybillNumber]);
        id = data[0]?.id;
      }
      const extraData = await client.readWaybill(id, label);
      if (!order.extraData) {
        order.extraData = {} as INimbusOrder;
      }
      order.extraData = { ...extraData, ...order.extraData };
      await this.orderCarrierRepository.update({ id }, { extraData: order.extraData });
      return order.extraData;
    }
  }

  async getWaybill(waybills: string[]) {
    const orders = await this.orderCarrierRepository.find({ waybillNumber: In(waybills) });
    return orders.map(i => ({
      waybill: i.waybillNumber,
      events: i.updates,
    }));
  }

  async createOrder(data: CreateOrderPartnerDto, user: Record<string, any>) {
    let valid = true;
    const message = [];

    const extraService = {
      partialDelivery: data?.partialDelivery,
      shippingMethod: data?.shippingMethod,
      requiredUponDelivery: data?.requiredUponDelivery,
      orderCancellationFeeCharged: data?.orderCancellationFeeCharged,
      exchangeOrReturn: data?.exchangeOrReturn,
      allowMutualCheck: data?.allowMutualCheck,
    };
    const order = await this.orderRepository.findOne({
      where: { id: data.orderId, companyId: user.companyId },
      relations: ['products', 'carriers'],
    });
    if (!order) {
      throw new BadRequestException('Order is not exists');
    }
    if (order.status !== OrderFFMStatus.Confirmed) {
      if (NOT_ALLOW_UPDATE_CARRIER_ORDER_V2?.includes(order.status)) {
        valid = false;
        message.push("Order's status is invalid");
      }
    }
    if (
      !!order.lastCarrier &&
      !!order.lastCarrier?.waybillNumber &&
      order.lastCarrier?.status != 'canceled'
    ) {
      valid = false;
      message.push('Tracking number already existed');
    }
    const condition: FindCondition<CarrierConfiguration> = {
      carrierCode: data.carrierCode,
      countryId: Number(order.countryId),
      status: CommonStatus.activated,
      companyId: user.companyId,
    };

    if (data.keyId) {
      condition.carrierId = data.keyId;
      condition.channelCode = data?.channelCode ? data?.channelCode : condition.carrierId;
    } else {
      condition.isDefault = true;
    }

    const [config, { data: warehouse }] = await Promise.all([
      this.carrierConfigurationRepository.findOne(condition),
      this.amqpConnection.request({
        exchange: 'ffm-catalog-service-warehouses',
        routingKey: 'get-ffm-warehouse',
        payload: { id: order.warehouseId },
        timeout: 10000,
      }) as Promise<{ data: IWarehouse }>,
    ]);
    if (!config) {
      valid = false;
      message.push('3PL partner is not exists');
    }

    const [
      carrier,
      {
        data: { province, district, ward },
      },
    ] = await Promise.all([
      this.carrierRepository.findOne({ code: config.carrierCode }),
      this.amqpConnection.request({
        exchange: 'order-service',
        routingKey: 'get-location',
        payload: {
          wardId: warehouse.communeId,
          provinceId: warehouse.provinceId,
          districtId: warehouse.districtId,
        },
        timeout: 10000,
      }) as any,
    ]);

    const { warningEnable, odz } = await this.checkODZ(order, user?.companyId, carrier?.id);
    if (!!odz && !data?.province) {
      order.orderWarnings = [
        plainToInstance(OrderWarning, {
          warningType: WarningType.ODZ,
          carrierId: order?.lastCarrier?.carrierId,
          status: warningEnable ? WarningStatus.Enable : WarningStatus.Disable,
        }),
      ];
    }

    warehouse.addressSplit = [ward?.name, district?.name, province?.name];

    if (order.recipientPostCode) {
      order.recipientPostCode = `${order.recipientPostCode}`;
      const length = order.recipientPostCode.length;
      if (length < 5) {
        for (let i = 0; i < 5 - length; i++) {
          order.recipientPostCode = '0' + order.recipientPostCode;
        }
      }
    }
    if (!!data?.province) order.recipientProvince = data.province;
    if (!!data?.district) order.recipientDistrict = data.district;
    if (!!data?.ward) order.recipientWard = data.ward;
    if (!!data?.postCode) order.recipientPostCode = data.postCode;
    //update
    if (!data?.province && !data?.district && !data?.ward && !data?.postCode) {
      const [recipientProvince, recipientDistrict, recipientWard] = await Promise.all([
        this.provinceRepository.findOne({
          ffmProvinceId: order?.recipientProvinceId,
        }),
        this.districtRepository.findOne({
          ffmDistrictId: order?.recipientDistrictId,
        }),
        this.wardRepository.findOne({
          ffmWardId: order?.recipientWardId,
        }),
      ]);

      if (recipientProvince && recipientDistrict && recipientWard) {
        order.recipientProvince = recipientProvince?.name;
        order.recipientDistrict = recipientDistrict?.name;
        order.recipientWard = recipientWard?.name;
      }
    }

    if (!valid) throw new BadRequestException(message?.join('. '));

    const extraData = config.extraData;
    let orderData, trackingNumber: string;
    switch (config.carrierCode) {
      case CarrierCode.vnpost: {
        const senderInfo = find(
          warehouse?.senderInformations,
          (it: ISenderInfo) => it.carrierId == carrier?.id?.toString(),
        );

        let wh: IWarehouse = warehouse;
        console.log(senderInfo);
        if (senderInfo && warehouse?.isOnSender) {
          wh = {
            ...wh,
            provinceId: senderInfo?.senderProvinceId?.toString() ?? wh?.provinceId,
            districtId: senderInfo?.senderDistrictId?.toString() ?? wh?.districtId,
            communeId: senderInfo?.senderWardId?.toString() ?? wh?.communeId,
            name: senderInfo?.senderName,
            fullAddress: senderInfo?.senderAddress,
            phoneNumber: senderInfo?.senderPhone,
            addressSplit: [
              senderInfo?.senderWard,
              senderInfo?.senderDistrict,
              senderInfo?.senderProvince,
            ],
          };
        }

        const client = new VNPostClient(
          {
            userAccount: config.carrierId,
            customerCode: extraData.customerCode,
            contractCode: extraData.contractCode,
            serviceCode: extraData.serviceCode,
            shippingMethod: extraData.shippingMethod,
            requiredUponDelivery: extraData.requiredUponDelivery,
            orderCancellationFeeCharged: extraData.orderCancellationFeeCharged,
            exchangeOrReturn: extraData.exchangeOrReturn,
          },
          this.redisService,
        );
        const { response, error, code, message } = await client.createOrder(
          order,
          wh,
          extraService,
        );
        if (code == '500') {
          console.log('error', error, code, message);
          throw new BadRequestException({
            code,
            statusCOde: code,
            error,
            message,
          });
        }
        orderData = { ...response, ...extraService };
        trackingNumber = response.itemCode;
        break;
      }
      case CarrierCode.viettelpost: {
        const senderInfo = find(
          warehouse?.senderInformations,
          (it: ISenderInfo) => it.carrierId == carrier?.id?.toString(),
        );

        let wh: IWarehouse = warehouse;
        if (senderInfo && warehouse?.isOnSender) {
          wh = {
            ...wh,
            name: senderInfo?.senderName,
            fullAddress: senderInfo?.senderAddress,
            phoneNumber: senderInfo?.senderPhone,
            addressSplit: [
              senderInfo?.senderWard,
              senderInfo?.senderDistrict,
              senderInfo?.senderProvince,
            ],
          };
        }
        const client = new ViettelPostClient(
          {
            userAccount: config.carrierId,
            password: extraData.key,
            partialDelivery: extraData.partialDelivery,
            exchangeOrReturn: extraData.exchangeOrReturn,
          },
          this.redisService,
        );
        const { response, error, code, message } = await client.createOrder(
          order,
          wh,
          extraService,
        );

        if (!isNil(error)) {
          console.log('error', error, code);
          throw new BadRequestException({
            code,
            statusCOde: code,
            error,
            message,
          });
        }
        orderData = { ...response, ...extraService };
        trackingNumber = response.ORDER_NUMBER;
        break;
      }
      case CarrierCode.spxexpress: {
        const senderInfo = find(
          warehouse?.senderInformations,
          (it: ISenderInfo) => it.carrierId == carrier?.id?.toString(),
        );

        let wh: IWarehouse = warehouse;
        if (senderInfo && warehouse?.isOnSender) {
          wh = {
            ...wh,
            name: senderInfo?.senderName,
            fullAddress: senderInfo?.senderAddress,
            phoneNumber: senderInfo?.senderPhone,
            addressSplit: [
              senderInfo?.senderWard,
              senderInfo?.senderDistrict,
              senderInfo?.senderProvince,
            ],
          };
        }
        const client = new SPXVNClient({
          userAccount: config.carrierId,
          password: extraData.key,
          allowMutualCheck: extraData.allowMutualCheck,
        });
        const { data, error } = await client.createOrder(order, wh, extraService);

        if (!data || !data.orders || data.orders.length === 0) {
          console.log('error', error);
          throw new BadRequestException(error);
        }
        orderData = { ...data.orders[0], ...extraService };
        trackingNumber = data.orders[0].tracking_no;
        break;
      }
      case CarrierCode.ghtk: {
        const senderInfo = find(
          warehouse?.senderInformations,
          (it: ISenderInfo) => it.carrierId == carrier?.id?.toString(),
        );

        let wh: IWarehouse = warehouse;
        if (senderInfo && warehouse?.isOnSender) {
          wh = {
            ...wh,
            name: senderInfo?.senderName,
            fullAddress: senderInfo?.senderAddress,
            phoneNumber: senderInfo?.senderPhone,
            addressSplit: [
              senderInfo?.senderWard,
              senderInfo?.senderDistrict,
              senderInfo?.senderProvince,
            ],
          };
        }

        const client = new GHTKClient(
          {
            userAccount: config.carrierId,
            token: extraData.token,
          },
          this.redisService,
        );
        const { response, error, code, message } = await client.createOrder(order, wh);

        if (!isNil(error)) {
          console.log('error', error, code);
          throw new BadRequestException({
            code,
            statusCOde: code,
            error,
            message,
          });
        }
        orderData = response;
        trackingNumber = response.tracking_id;
        break;
      }
      case CarrierCode.wedo: {
        const client = new WeDoClient({
          userAccount: config.carrierId,
          token: extraData.key,
          whCode: extraData.whCode,
          channelCode: extraData.channelCode,
        });
        const { response, error, code } = await client.createOrder(order, warehouse);

        if (!isNil(error)) {
          console.log('error', error, code);
          throw new BadRequestException(code || error);
        }
        orderData = response;
        trackingNumber = response.trackNumber;
        break;
      }
      case CarrierCode.flashexpress:
        switch (config.countryId) {
          case 60: {
            const client = new FlashExpressMalaysiaClient(config.carrierId, extraData.key);
            const res = await client.createOrder(order, warehouse);
            orderData = res;
            trackingNumber = res.pno;
            break;
          }
          case 66: {
            const client = new FlashExpressThailandClient(config.carrierId, extraData.key);
            const res = await client.createOrder(order, warehouse);
            orderData = res;
            trackingNumber = res.pno;
            break;
          }
        }
        break;
      case CarrierCode.kerryexpress:
        switch (config.countryId) {
          case 66: {
            const senderInfo = find(
              warehouse?.senderInformations,
              (it: ISenderInfo) => it.carrierId == carrier?.id?.toString(),
            );

            let wh: IWarehouse = warehouse;
            if (senderInfo && warehouse?.isOnSender) {
              wh = {
                ...wh,
                name: senderInfo?.senderName,
                fullAddress: senderInfo?.senderAddress,
                phoneNumber: senderInfo?.senderPhone,
                addressSplit: [
                  senderInfo?.senderWard,
                  senderInfo?.senderDistrict,
                  senderInfo?.senderProvince,
                ],
              };
            }

            const client = new KerryThailandClient(
              extraData.key,
              config.carrierId,
              extraData.merchant_id,
              extraData.appKey,
              extraData.appId,
            );
            const { response, error } = await client.createOrder(order, wh);
            if (error) {
              throw new InternalServerErrorException(error.status_desc);
            }
            orderData = response;
            trackingNumber = response.con_no;
            break;
          }
        }
        break;
      case CarrierCode.jtexpress:
        switch (config.countryId) {
          case 63: {
            const client = new JntExpressPhilippinesClient(
              extraData.key,
              config.carrierId,
              extraData.eCompanyId,
            );
            const res = await client.createOrder(order, warehouse);
            if (res.reason) {
              throw new InternalServerErrorException(res);
            }
            orderData = res;
            trackingNumber = res.mailno;
            break;
          }
          case 66: {
            const client = new JntExpressThailandClient(
              extraData.key,
              config.carrierId,
              extraData.eCompanyId,
            );
            const res = await client.createOrder(order, warehouse);
            if (res.reason) {
              throw new InternalServerErrorException(res);
            }
            orderData = res;
            trackingNumber = res.mailno;
            break;
          }
        }
        break;
      case CarrierCode.jntbangkok:{
        const client = new JntBangkokClient(
          extraData.key,
          config.carrierId,
          extraData.password,
          extraData.apiAccount,
        );
        const { data, error } = await client.createOrder(order, warehouse, `${carrier?.id}`);
        if (error) {
          console.log('error', error);
          const details = error?.error?.details || [];
          const message = details[0]?.message;
          const field = details[0]?.field;
          let errorMessage;
          if (message) {
            errorMessage = message;
            if (field) {
              errorMessage = field + ' ' + errorMessage;
            }
          }
          throw new BadRequestException(errorMessage || error?.error || error);
        }
        orderData = data?.data;
        trackingNumber = data?.data?.billCode;
        break;
      }
      case CarrierCode.ninjavan: {
        const client = new NinjaVanClient(
          extraData.key,
          config.carrierId,
          config.countryId,
          this.redisService,
        );
        const { response, error } = await client.createOrder(order, warehouse);
        if (error) {
          console.log('error', error);
          const details = error?.error?.details || [];
          const message = details[0]?.message;
          const code = details[0]?.code || details[0]?.status;
          const field = details[0]?.field;
          let errorMessage;
          if (message) {
            errorMessage = message;
            if (field) {
              errorMessage = field + ' ' + errorMessage;
            }
          }
          if (code) errorMessage = code;
          throw new BadRequestException(errorMessage || error?.error || error);
        }
        orderData = response;
        trackingNumber = response.tracking_number;
        break;
      }
      case CarrierCode.bluedart: {
        const client = new BlueDartClient(
          {
            loginId: config.carrierId,
            clientId: extraData.clientId,
            clientSecret: extraData.clientSecret,
            customerCode: extraData.customerCode,
            shippingLicense: extraData.shippingLicense,
            location: extraData.location,
          },
          this.redisService,
        );
        const { response, error } = await client.createOrder(order, warehouse);
        if (error) {
          console.log('error', error);
          const message = error?.message;
          let errorMessage;
          if (message) {
            errorMessage = message;
          }
          throw new BadRequestException(errorMessage || error?.error || error);
        }
        orderData = response;
        trackingNumber = response.AWBNo;
        orderData.waybill_url = response.AWBPrintContent;
        orderData = response;
        break;
      }
      case CarrierCode.bestexpress:
        switch (config.countryId) {
          case 66: {
            const senderInfo = find(
              warehouse?.senderInformations,
              (it: ISenderInfo) => it.carrierId == carrier?.id?.toString(),
            );

            let wh: IWarehouse = warehouse;
            if (senderInfo && warehouse?.isOnSender) {
              wh = {
                ...wh,
                name: senderInfo?.senderName,
                fullAddress: senderInfo?.senderAddress,
                phoneNumber: senderInfo?.senderPhone,
                addressSplit: [
                  senderInfo?.senderWard,
                  senderInfo?.senderDistrict,
                  senderInfo?.senderProvince,
                ],
              };
            }
            const client = new BestExpressThailandClient({
              userAccount: config.carrierId,
              password: extraData.password,
              bankCardOwner: extraData.bankCardOwner,
              bankCode: extraData.bankCode,
              bankCardNo: extraData.bankCardNo,
            });

            const data = await client.createOrder(order, wh);
            if (!data?.result || data?.errorCode) {
              const message = `${data?.remark}-${data?.errorCode}-${data?.errorDescription}`;
              throw new BadRequestException(message);
            }
            orderData = data;
            trackingNumber = data?.mailNo;
            break;
          }

          case 84: {
            const client = new BestExpressClient(
              config.carrierId,
              extraData.password,
              this.redisService,
            );
            const dataCategory: any = await this.amqpConnection.request({
              exchange: 'ffm-catalog-service',
              routingKey: 'ffm-find-category',
              payload: {},
              timeout: 10000,
            });
            const categories = [];
            if (dataCategory?.data?.length > 0) {
              dataCategory?.data.forEach((it: any) => {
                categories[it?.id] = it?.name;
              });
            }

            const { response, error } = await client.createOrder(order, warehouse, categories);

            console.log(`request order data - ${order.displayId}`, response, error);

            if (error) {
              console.log('error', error);
              const message = error?.Message;
              let errorMessage;
              if (message) {
                errorMessage = message;
              }
              throw new BadRequestException(errorMessage || error?.error || error);
            }
            orderData = response;
            trackingNumber = response.Code;
            orderData = { ...data, ...response };
            break;
          }
        }
        break;
      case CarrierCode.nimbus: {
        const client = new NimbusClient(config.carrierId, extraData.password, this.redisService);
        const { response, error } = await client.createOrder(order, warehouse);
        if (error) {
          console.log('error', error);
          const message = error?.message;
          let errorMessage;
          if (message) {
            errorMessage = message;
          }
          throw new BadRequestException(errorMessage || error?.error || error);
        }
        orderData = response;
        trackingNumber = response.awb_number;
        orderData.waybill_url = response.label;
        const data = await client.readWaybill(null, response.label);
        orderData = { ...data, ...response };
        break;
      }
    }
    if (trackingNumber) {
      const orderPartner: OrderCarrier = {
        orderId: order.id,
        waybillNumber: trackingNumber,
        waybillNote: data?.waybillNote,
        extraData: orderData,
        carrierId: carrier?.id,
        lastUpdatedBy: user.id,
        creatorId: user.id,
        updatedAt: new Date(),
        typeUpdate: data?.type ?? TypeUpdate3PL.ROD,
        config,
        status: CarrieStatusEnum.activated,
      } as OrderCarrier;

      // await this.orderCarrierRepository.update({
      //   orderId: order.id
      // }, {
      //   lastUpdatedBy: user.id,
      //   status: CarrieStatusEnum.canceled
      // });

      if (
        data?.carrierCode == CarrierCode.bestexpress &&
        order?.countryId == '60' &&
        order?.lastCarrier?.carrier?.code == CarrierCode.ninjavan
      ) {
        await this.amqpConnection.publish('ffm-order', 'auto-create-odz', {
          user,
          odz: plainToInstance(ODZ, {
            companyId: user?.companyId,
            carrierId: order?.lastCarrier?.carrierId,
            countryId: Number(order?.countryId),
            provinceId: order?.recipientProvinceId,
            districtId: order?.recipientDistrictId,
            wardId: order?.recipientWardId,
            postCode: order?.recipientPostCode,
            note: `Auto-add ODZ ${order?.displayId}`,
            isSystemCreated: true,
            creatorId: SystemIdEnum.system,
            lastUpdatedBy: SystemIdEnum.system,
          }),
        });
      }

      const saved = await this.orderCarrierRepository
        .upsert(orderPartner, ['orderId', 'carrierId', 'waybillNumber'])
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });

      console.log('🚀 ~ OrderCarriersService ~ createOrder ~ saved:', saved);

      await this.orderRepository.update(order.id, {
        waybillNote: data?.waybillNote,
        lastUpdatedBy: user.id,
      });

      try {
        await Promise.race([
          this.ffmQueueTrackingOrderService.handleOrderTrackingDataNew({
            config,
            id: order.id,
            trackingNumber,
            user,
            orderROD: !!data?.province ? data : null,
            extraData: orderData,
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('⏰ Timeout sau 10s')), 10000),
          ),
        ]);
      } catch (err) {
        console.log('🚀🚀🚀 ~ OrderCarriersService ~ createOrder ~ err:', err);
        if (order.status == OrderFFMStatus.Confirmed || trackingNumber) {
          await this.orderRepository.update(order.id, {
            status: OrderFFMStatus.AwaitingCollection,
            lastUpdatedBy: user.id,
          });
        }
      }

      await Promise.all([
        this.amqpConnection.publish('ffm-order', 'warning-order', {
          orderId: order?.id,
        }),
        !!odz &&
          !data?.province &&
          this.owRepository
            .createQueryBuilder()
            .insert()
            .into(OrderWarning)
            .values({
              warningType: WarningType.ODZ,
              carrierId: carrier?.id,
              status: warningEnable ? WarningStatus.Enable : WarningStatus.Disable,
              orderId: order?.id,
            })
            .orIgnore()
            .execute(),
      ]);

      order.lastUpdatedBy = user.id;
      if (order.status === OrderFFMStatus.Confirmed) {
        order.status = OrderFFMStatus.AwaitingCollection;
      }
      orderPartner.id = saved.raw?.id;
      order.carriers = [orderPartner, ...(order.carriers || [])];

      return order;
    }
    throw new BadRequestException('3PL is not supported');
  }

  // @RabbitRPC({
  //   exchange: 'ffm-order',
  //   routingKey: 'handle-order-tracking-data',
  //   queue: 'ffm-queue-handle-order-tracking-data',
  //   allowNonJsonMessages: true,
  //   errorHandler: defaultNackErrorHandler,
  // })
  // async handleOrderTrackingData({ config, id, trackingNumber, user, orderROD, extraData }) {
  //   console.log('handle-order-tracking-data', id, trackingNumber, user);
  //   await this.orderCarrierRepository.update(
  //     {
  //       orderId: id,
  //       waybillNumber: Not(trackingNumber),
  //     },
  //     {
  //       lastUpdatedBy: user.id,
  //       status: CarrieStatusEnum.canceled,
  //     },
  //   );
  //   const order = await this.orderRepository.findOne({
  //     where: qb => {
  //       qb.andWhere({
  //         id,
  //       });
  //     },
  //     relations: ['tags', 'carriers'],
  //   });
  //   const _update: any = {};
  //   order.lastUpdatedBy = user.id;

  //   if (extraData?.courier_name) {
  //     _update.tags = order?.tags;
  //     const tagInFFM = await this.tagService.getTagByContent(
  //       [extraData?.courier_name],
  //       order?.companyId,
  //       order?.countryId,
  //     );
  //     if (tagInFFM?.[0]) {
  //       const tagLookup = find(order?.tags, (it: Tag) => it.id == tagInFFM?.[0]?.id);
  //       if (!tagLookup) _update.tags.push(tagInFFM?.[0]);
  //     } else {
  //       const newTag = new Tag();
  //       newTag.content = extraData?.courier_name;
  //       newTag.companyId = order?.companyId;
  //       newTag.color = '#D4E0FF';
  //       newTag.is3PLTag = true;
  //       _update.tags.push(newTag);
  //     }
  //   }

  //   //update nguoc lai vao address3pl
  //   if (!!orderROD) {
  //     if (
  //       order?.recipientProvince ||
  //       order?.recipientDistrict ||
  //       order?.recipientWard ||
  //       order?.recipientPostCode
  //     ) {
  //       await Promise.all([
  //         order?.recipientProvince &&
  //           this.provinceRepository.update(orderROD?.provinceId, {
  //             ffmProvinceId: order?.recipientProvinceId,
  //             ffmProvinceName: order?.recipientProvince,
  //           }),
  //         order?.recipientDistrict &&
  //           this.districtRepository.update(orderROD?.districtId, {
  //             ffmDistrictId: order?.recipientDistrictId,
  //             ffmDistrictName: order?.recipientDistrict,
  //           }),
  //         order?.recipientWard &&
  //           this.wardRepository.update(orderROD?.wardId, {
  //             ffmWardId: order?.recipientWardId,
  //             ffmWardName: order?.recipientWard,
  //           }),
  //         order?.recipientPostCode &&
  //           this.postCodeRepository.update(orderROD?.postCode, {
  //             ffmPostCodeName: order?.recipientPostCode,
  //           }),
  //       ]);
  //     }
  //     _update.recipientProvince = orderROD?.province;
  //     _update.recipientDistrict = orderROD?.district;
  //     _update.recipientWard = orderROD?.ward;
  //     _update.recipientPostCode = Number(orderROD?.postCode);
  //     _update.isMapping3pl = true;
  //   }
  //   if (order.status === OrderFFMStatus.Confirmed) {
  //     _update.status = OrderFFMStatus.AwaitingCollection;
  //   }

  //   if (_update) {
  //     _update.updatedAt = new Date();
  //     _update.lastUpdatedBy = user.id;
  //     _update.tags = order?.tags;
  //     if (extraData?.courier_name) {
  //       const tagInFFM = await this.tagService.getTagByContent(
  //         [extraData?.courier_name],
  //         order?.companyId,
  //         order?.countryId,
  //       );
  //       if (tagInFFM?.[0]) {
  //         const tagLookup = find(order?.tags, (it: Tag) => it.id == tagInFFM?.[0]?.id);
  //         if (!tagLookup) _update.tags.push(tagInFFM?.[0]);
  //       } else {
  //         const newTag = new Tag();
  //         newTag.content = extraData?.courier_name;
  //         newTag.companyId = order?.companyId;
  //         newTag.color = '#D4E0FF';
  //         newTag.is3PLTag = true;
  //         newTag.countryId = order?.countryId;
  //         newTag.modules = [TypeOfTagEnum.Order];
  //         _update.tags.push(newTag);
  //       }
  //     }

  //     //update nguoc lai vao address3pl
  //     if (!!orderROD) {
  //       if (
  //         order?.recipientProvince ||
  //         order?.recipientDistrict ||
  //         order?.recipientWard ||
  //         order?.recipientPostCode
  //       ) {
  //         await Promise.all([
  //           order?.recipientProvince &&
  //             this.provinceRepository.update(orderROD?.provinceId, {
  //               ffmProvinceId: order?.recipientProvinceId,
  //               ffmProvinceName: order?.recipientProvince,
  //             }),
  //           order?.recipientDistrict &&
  //             this.districtRepository.update(orderROD?.districtId, {
  //               ffmDistrictId: order?.recipientDistrictId,
  //               ffmDistrictName: order?.recipientDistrict,
  //             }),
  //           order?.recipientWard &&
  //             this.wardRepository.update(orderROD?.wardId, {
  //               ffmWardId: order?.recipientWardId,
  //               ffmWardName: order?.recipientWard,
  //             }),
  //           order?.recipientPostCode &&
  //             this.postCodeRepository.update(orderROD?.postCode, {
  //               ffmPostCodeName: order?.recipientPostCode,
  //             }),
  //         ]);
  //       }
  //       _update.recipientProvince = orderROD?.province;
  //       _update.recipientDistrict = orderROD?.district;
  //       _update.recipientWard = orderROD?.ward;
  //       _update.recipientPostCode = Number(orderROD?.postCode);
  //       _update.isMapping3pl = true;
  //     }
  //     _update.tags = [OrderFFMStatus.New, OrderFFMStatus.AwaitingStock].includes(order?.status)
  //       ? _update.tags
  //       : !isEmpty(_update.tags)
  //       ? _update?.tags.filter(x => x.id != -1 || x.content != 'Stock Priority')
  //       : [];
  //     const result = await this.orderRepository
  //       .save({
  //         ...order,
  //         ..._update,
  //       })
  //       .catch(err => {
  //         if (err?.driverError) console.log('error', err?.driverError?.detail);
  //         return err;
  //       });

  //     if (!!order.externalId) {
  //       await this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
  //         id: order.id,
  //         user: {
  //           id: user.id,
  //           companyId: order.companyId,
  //         },
  //       });
  //     }
  //   }

  //   await this.orderService.syncWaybill(
  //     {
  //       waybillNumber: trackingNumber,
  //       carrierCode: config.carrierCode,
  //       displayId: order.displayId,
  //       externalId: order.externalId,
  //     },
  //     order,
  //   );
  //   // order.carriers = [saved, ...(order.carriers || [])];

  //   if (!!config.carrierCode && config.carrierCode == CarrierCode.viettelpost) {
  //     await this.amqpConnection.publish('ffm-order', 'waybill-order-vtp-data', {
  //       config,
  //       id,
  //       trackingNumber,
  //       extraData,
  //     });
  //   }

  //   if (!!config.carrierCode && config.carrierCode == CarrierCode.ghtk) {
  //     await this.amqpConnection.publish('ffm-order', 'waybill-order-ghtk-data', {
  //       config,
  //       id,
  //       trackingNumber,
  //       extraData,
  //     });
  //   }
  //   return order;
  // }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'waybill-order-vtp-data',
    queue: 'ffm-queue-waybill-order-vtp-data',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async waybillOrderVtpData({ config, id, trackingNumber, extraData }) {
    if (!!config.carrierCode && config.carrierCode == CarrierCode.viettelpost) {
      const client = new ViettelPostClient(
        {
          userAccount: config?.carrierId,
          password: config?.extraData?.key,
          soCodes: [trackingNumber],
        },
        this.redisService,
      );
      const { data, code } = await client.getDataWaybill();
      console.log(
        'update vtp extra waybill',
        data,
        code,
        {
          orderId: id,
          waybillNumber: trackingNumber,
        },
        {
          extraData: {
            ...extraData,
            REGION: data,
          },
        },
      );
      if (data && data?.length > 0) {
        await this.orderCarrierRepository.update(
          {
            orderId: id,
            waybillNumber: trackingNumber,
          },
          {
            extraData: {
              ...extraData,
              REGION: data,
            },
          },
        );
      }
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'waybill-order-ghtk-data',
    queue: 'ffm-queue-waybill-order-ghtk-data',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async waybillOrderGHTKData({ config, id, trackingNumber, extraData }) {
    if (!!config.carrierCode && config.carrierCode == CarrierCode.ghtk && !extraData?.waybillUrl) {
      const client = new GHTKClient(
        {
          userAccount: config?.carrierId,
          token: config?.extraData?.token,
        },
        this.redisService,
      );
      const bill = await client.printWaybill(trackingNumber);
      // console.log(222, bill);
      if (!bill) {
        throw { message: 'Bill not found' };
      }
      const filename = `${id}_${trackingNumber}.pdf`;
      console.log('GHTK', filename);

      const form = new FormData();
      form.append('file', bill, filename);
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      const response = await axios.request({
        method: 'POST',
        baseURL: 'https://pos.pages.fm/api/v1',
        url: '/contents',
        params: {
          api_key: 'b1722c77279f4d5f81ee10e6a0ac4e43',
        },
        data: form,
        headers,
        timeout: 60000,
      });
      if (!response.data?.content_url)
        throw new Error(
          `Cannot upload attachment to pancake, response ${StringUtils.getString(response)}`,
        );
      console.log('GHTK', response?.data);
      const waybillUrl = response.data?.content_url;

      if (waybillUrl) {
        await this.orderCarrierRepository.update(
          {
            orderId: id,
            waybillNumber: trackingNumber,
          },
          {
            extraData: {
              ...extraData,
              waybillUrl,
            },
          },
        );
      }
    }
    return new Nack(false);
  }

  async cancelAnyway(id: number, user: Record<string, any>, data: CancelAnywayDto) {
    const order = await this.orderCarrierRepository
      .createQueryBuilder('oc')
      .where('oc.id = :id', { id })
      .leftJoinAndSelect('oc.order', 'o', 'o.id = oc.order_id')
      .andWhere('o.company_id = :companyId', {
        companyId: user.companyId,
      })
      .andWhere('o.id = :oId', {
        oId: data?.orderId,
      })
      .getOne();
    if (!order?.order) throw new BadRequestException('Order is not exists');

    await this.amqpConnection.publish('ffm-order', 'sync-cancel-waybill-order', {
      id: data?.orderId,
      lastUpdatedBy: user.id,
    });

    let actor = 'FFM';
    if (![SystemIdEnum?.system, SystemIdEnum?.ag]?.includes(user?.id)) {
      const { data: UData } = await this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'get-users-by-ids',
        payload: { ids: [user?.id] },
        timeout: 10000,
      });

      const users = UData as User[];
      actor = `${users[0]?.displayId ?? users[0]?.id}-${users[0]?.name}`;
    }

    const carrierAgDto = new FfmToAgOrderCarrierDto();
    carrierAgDto.displayId = order?.order?.displayId;
    carrierAgDto.externalId = order?.order?.externalId;
    carrierAgDto.carrierCode = "";
    carrierAgDto.waybillNumber = "";
    carrierAgDto.actor = actor;
    if (order?.order?.externalCode == ExternalOrderType.ag)
      await this.amqpConnection.publish('order-service', 'sync-carrier-from-ffm', carrierAgDto);

    //update lai address name
    const [cancel3PL, orderWarning] = await Promise.all([
      this.orderCarrierRepository.update(order.id, {
        lastUpdatedBy: user.id,
        status: CarrieStatusEnum.canceled,
        updatedAt: new Date(),
        typeUpdate: data?.type,
      }),
      this.owRepository
        .createQueryBuilder()
        .insert()
        .into(OrderWarning)
        .values({
          orderId: Number(data?.orderId),
          warningType: WarningType.CancelWaybill,
          status: WarningStatus.Enable,
          carrierId: 0,
        })
        .orIgnore()
        .execute(),
      this.owRepository.delete({
        orderId: Number(data?.orderId),
        warningType: WarningType.ODZ,
        // isOdz3pl: false,
      }),
    ]);
    return cancel3PL;
  }

  async cancelOrder(id: number, user: Record<string, any>, data?: any) {
    const order = await this.orderCarrierRepository
      .createQueryBuilder('oc')
      .where('oc.id = :id', { id })
      .leftJoinAndSelect('oc.order', 'o', 'o.id = oc.order_id')
      .andWhere('o.company_id = :companyId', {
        companyId: user.companyId,
      })
      .getOne();
    if (!order?.order) {
      throw new BadRequestException({
        message: 'Order is not exists',
        code: 400,
      });
    }
    if (NOT_ALLOW_UPDATE_CARRIER_ORDER_V2.includes(order.order?.status)) {
      throw new BadRequestException({
        message: "Order's status is invalid",
        code: 400,
      });
    }

    const config = order.config;
    if (!config || !order.extraData) {
      throw new ForbiddenException({
        message: 'Order cannot be canceled',
        code: 400,
      });
    }
    const extraData = config.extraData;
    let canceled = false;
    switch (config.carrierCode) {
      case CarrierCode.flashexpress:
        switch (config.countryId) {
          case 60: {
            canceled = true;
            break;
          }
          case 66: {
            canceled = true;
            break;
          }
        }
        break;
      case CarrierCode.jtexpress:
        switch (config.countryId) {
          case 63: {
            const client = new JntExpressPhilippinesClient(
              extraData.key,
              config.carrierId,
              extraData.eCompanyId,
            );
            const res = await client.cancelOrder(order);
            if (res.reason) {
              throw new InternalServerErrorException(res.message);
            }
            canceled = res.success == 'true';
            break;
          }
          case 66: {
            const client = new JntExpressThailandClient(
              extraData.key,
              config.carrierId,
              extraData.eCompanyId,
            );
            const res = await client.cancelOrder(order);
            if (res.reason) {
              throw new InternalServerErrorException(res.message);
            }
            canceled = res.success == 'true';
            break;
          }
        }
        break;
      case CarrierCode.ninjavan: {
        const client = new NinjaVanClient(
          extraData.key,
          config.carrierId,
          config.countryId,
          this.redisService,
        );
        const { error } = await client.cancelOrder(order);
        if (error) {
          const details = error?.error?.details || [];
          const code = details[0]?.code || details[0]?.status;
          throw new BadRequestException(code || details[0]?.message || error?.error || error);
        }
        canceled = true;
        break;
      }
      case CarrierCode.nimbus: {
        const client = new NimbusClient(config.carrierId, extraData.password, this.redisService);
        const { error } = await client.cancelOrder(order);
        if (error) {
          const message = error?.message;
          throw new BadRequestException(message || error?.error || error);
        }
        canceled = true;
        break;
      }
      case CarrierCode.bluedart: {
        const client = new BlueDartClient(
          {
            loginId: config.carrierId,
            clientId: extraData.clientId,
            clientSecret: extraData.clientSecret,
            customerCode: extraData.customerCode,
            shippingLicense: extraData.shippingLicense,
            location: extraData.location,
          },
          this.redisService,
        );
        const { error } = await client.cancelOrder(order);
        if (error) {
          const message = error?.message;
          throw new BadRequestException(message || error?.error || error);
        }
        canceled = true;
        break;
      }
      case CarrierCode.viettelpost: {
        const client = new ViettelPostClient(
          {
            userAccount: config.carrierId,
            password: extraData.key,
          },
          this.redisService,
        );
        const { response, error, code } = await client.cancelOrder(order);
        if (error) {
          const message = error?.message;
          throw new BadRequestException(message || error?.error || error);
        }
        canceled = true;
        break;
      }
      case CarrierCode.spxexpress: {
        const client = new SPXVNClient({
          userAccount: config.carrierId,
          password: extraData.key,
        });
        const { data, error } = await client.cancelOrder(order);
        if (error.length > 0) {
          throw new BadRequestException(error);
        }
        canceled = true;
        break;
      }
      case CarrierCode.vnpost: {
        const client = new VNPostClient(
          {
            userAccount: config.carrierId,
            customerCode: extraData.customerCode,
            contractCode: extraData.contractCode,
            serviceCode: extraData.serviceCode,
          },
          this.redisService,
        );
        const { response, error, code } = await client.cancelOrder(order);
        if (error) {
          const message = error?.message;
          throw new BadRequestException(message || error?.error || error);
        }
        canceled = true;
        break;
      }
      case CarrierCode.ghtk: {
        const client = new GHTKClient(
          {
            userAccount: config.carrierId,
            token: extraData.token,
          },
          this.redisService,
        );
        const { response, error, code } = await client.cancelOrder(order);
        if (error) {
          const message = error?.message;
          throw new BadRequestException(message || error?.error || error);
        }
        canceled = true;
        break;
      }
      case CarrierCode.bestexpress: {
        switch (config.countryId) {
          case 66: {
            const client = new BestExpressThailandClient({
              userAccount: config.carrierId,
              password: extraData.password,
            });

            const data = await client.cancelOrder(order);
            if (!data?.result || data?.errorCode) {
              const message = `${data?.remark}-${data?.errorCode}-${data?.errorDescription}`;
              throw new BadRequestException(message);
            }
            canceled = true;
            break;
          }
          case 84: {
            const client = new BestExpressClient(
              config.carrierId,
              extraData.password,
              this.redisService,
            );
            console.log('best', config.carrierId, extraData.password);

            const { response, error } = await client.cancelOrder(order);
            console.log(222, response, error);

            if (error) {
              const message = error?.message;
              throw new BadRequestException(message || error?.error || error);
            }
            canceled = true;
            break;
          }
        }
        break;
      }
      case CarrierCode.jntbangkok: {
        const client = new JntBangkokClient(
          extraData.key,
          config.carrierId,
          extraData.password,
          extraData.apiAccount,
        );
        const { response, error } = await client.cancelOrder(order);
        if (error) {
          const message = error?.message;
          throw new BadRequestException(message || error?.error || error);
        }
        canceled = true;
        break;
      }
    }
    if (canceled) {
      await Promise.all([
        this.orderCarrierRepository.update(order.id, {
          lastUpdatedBy: user.id,
          status: CarrieStatusEnum.canceled,
          updatedAt: new Date(),
          waybillNote: data?.reason ?? '',
          typeUpdate: data?.type ?? TypeUpdate3PL.CancelDelivery,
        }),
        this.owRepository
          .createQueryBuilder()
          .insert()
          .into(OrderWarning)
          .values({
            orderId: order?.orderId,
            warningType: WarningType.CancelWaybill,
            status: WarningStatus.Enable,
            carrierId: 0,
          })
          .orIgnore()
          .execute(),
        this.owRepository.delete({
          orderId: order?.orderId,
          warningType: WarningType.ODZ,
          // isOdz3pl: false,
        }),
      ]);

      let actor = 'FFM';
      if (![SystemIdEnum?.system, SystemIdEnum?.ag]?.includes(user?.id)) {
        const { data: UData } = await this.amqpConnection.request({
          exchange: 'identity-service-roles',
          routingKey: 'get-users-by-ids',
          payload: { ids: [user?.id] },
          timeout: 10000,
        });

        const users = UData as User[];
        actor = `${users[0]?.displayId ?? users[0]?.id}-${users[0]?.name}`;
      }

      const carrierAgDto = new FfmToAgOrderCarrierDto();
      carrierAgDto.displayId = order?.order?.displayId;
      carrierAgDto.externalId = order?.order?.externalId;
      carrierAgDto.carrierCode = "";
      carrierAgDto.waybillNumber = "";
      carrierAgDto.actor = actor;
      if (order?.order?.externalCode == ExternalOrderType.ag)
        await this.amqpConnection.publish('order-service', 'sync-carrier-from-ffm', carrierAgDto);

      await this.amqpConnection.publish('ffm-order', 'sync-cancel-waybill-order', {
        id: order?.orderId,
        lastUpdatedBy: user.id,
      });
      return true;
    }
    throw new ForbiddenException({
      message: '3PL is not supported',
      code: 400,
    });
  }

  async updateTracking(id: number) {
    const order = await this.orderCarrierRepository.findOne(id, {
      relations: ['carrier', 'order'],
    });
    console.log('a', order);
    const countryCode = order?.order?.countryId;
    switch (order?.carrier?.code) {
      case CarrierCode.ninjavan:
        await this.scanNinjaVanOrder(id);
        break;
      case CarrierCode.bestexpress:
        if (countryCode == '60') {
          await this.scanBestMyOrder(id);
        }
        if (countryCode == '84') {
          await this.scanBestVNOrder(id);
        }
        if (countryCode == '66') {
          await this.scanBestThOrder(id);
        }
        break;
      case CarrierCode.flashexpress:
        if (countryCode == '60') {
          await this.scanFlashMyOrder(id);
        }
        if (countryCode == '66') {
          await this.scanFlashThOrder(id);
        }
        break;
      case CarrierCode.kerryexpress:
        if (countryCode == '66') {
          await this.scanKerryThOrder(id);
        }
        break;
      case CarrierCode.jtexpress:
        if (countryCode == '63') {
          await this.scanJntPhiOrder(id);
        }
        if (countryCode == '66') {
          await this.scanJntThOrder(id);
        }
        break;
      case CarrierCode.nimbus:
        await this.scanNimbusOrder(id);
        break;
      case CarrierCode.jneexpress:
        await this.scanJneIdOrder(id);
        break;
      case CarrierCode.poslaju:
        await this.scanPosMyOrder(id);
        break;
      case CarrierCode.anousith:
        await this.scanAnousithOrder(id);
        break;
      case CarrierCode.bluedart:
        await this.scanBluedartOrder(id);
        break;
      case CarrierCode.hal:
        await this.scanHalOrder(id);
        break;
    }
  }

  async updateOrder(id: number, user?: Record<string, any>, dataOrder?: CorrectionOrderDto) {
    const { oldOrder, newOrder } = dataOrder;
    const query = this.orderRepository
      .createQueryBuilder('o')
      .innerJoinAndSelect('o.carriers', 'oc', 'o.id = oc.order_id')
      .innerJoinAndSelect('o.products', 'op')
      .where('oc.id = :id', { id });

    if (user) {
      query.andWhere('o.company_id = :companyId', {
        companyId: user.companyId,
      });
    }
    const order = await query.getOne();
    const config = order.lastCarrier.config;
    if (!config || !order.lastCarrier.extraData) {
      throw new ForbiddenException('Order cannot update');
    }
    const extraData = config.extraData;
    const { data: warehouse }: { data: IWarehouse } = await this.amqpConnection.request({
      exchange: 'ffm-catalog-service-warehouses',
      routingKey: 'get-ffm-warehouse',
      payload: { id: order.warehouseId },
      timeout: 10000,
    });

    if (!!oldOrder && config?.carrierCode == CarrierCode.vnpost) {
      return await this.vnpOrderCorrection(oldOrder, newOrder, warehouse, config);
    }
    // console.log("🚬 ~ OrderCarriersService ~ updateOrder ~ order:", order);
    if (!order) {
      throw new BadRequestException('Order is not exists');
    }
    if (!ALLOW_UPDATE_IN_DELIVERY.includes(order.status)) {
      throw new BadRequestException("Order's status is invalid");
    }

    const {
      data: { province, district, ward },
    }: any = await this.amqpConnection.request({
      exchange: 'order-service',
      routingKey: 'get-location',
      payload: {
        wardId: warehouse.communeId,
        provinceId: warehouse.provinceId,
        districtId: warehouse.districtId,
      },
      timeout: 10000,
    });
    warehouse.addressSplit = [ward?.name, district?.name, province?.name];
    let orderData, trackingNumber: string;
    switch (config.carrierCode) {
      case CarrierCode.jtexpress:
        switch (config.countryId) {
          case 63: {
            const client = new JntExpressPhilippinesClient(
              extraData.key,
              config.carrierId,
              extraData.eCompanyId,
            );
            const res = await client.update(order, warehouse);
            if (res.reason) {
              throw new InternalServerErrorException(res.message);
            }
            orderData = res;
            trackingNumber = res.mailno;
          }
          case 66: {
            const client = new JntExpressThailandClient(
              extraData.key,
              config.carrierId,
              extraData.eCompanyId,
            );
            const res = await client.update(order, warehouse);
            if (res.reason) {
              throw new InternalServerErrorException(res.message);
            }
            orderData = res;
            trackingNumber = res.mailno;
          }
        }
        break;

      case CarrierCode.spxexpress:
        const client = new SPXVNClient({
          userAccount: config.carrierId,
          password: extraData.key,
        });
        const { data, error } = await client.updateOrder(order, warehouse);
        if (!data || !data.orders || data.orders.length === 0) {
          console.log('error', error);
          throw new BadRequestException(error);
        }
        orderData = { ...data.orders[0], allowMutualCheck: 1 };
        trackingNumber = data.orders[0].tracking_no;
        break;
      // case CarrierCode.ninjavan: {
      //   const client = new NinjaVanClient(extraData.key, config.carrierId, config.countryId, this.redisService);
      //   const {response, error} = await client.cancelOrder(order);
      //   if (error) {
      //     const details = error?.error?.details || [];
      //     throw new BadRequestException(details[0]?.message || error?.error || error);
      //   }
      //   canceled = true;
      // }
    }
    if (trackingNumber) {
      await this.orderCarrierRepository.update(
        {
          id: order.lastCarrier.id,
        },
        {
          waybillNumber: trackingNumber,
          extraData: orderData,
          lastUpdatedBy: user?.id || SystemIdEnum.system,
          updatedAt: new Date(),
        },
      );
      order.carriers[0].extraData = orderData;
      order.carriers[0].waybillNumber = trackingNumber;
      order.carriers[0].lastUpdatedBy = user?.id || SystemIdEnum.system;
      order.carriers[0].updatedAt = new Date();
      console.log('order waybill updated', order);
      return order;
    }
    throw new BadRequestException('3PL is not supported');
  }

  async vnpOrderCorrection(
    oldOrder: Order,
    order: Order,
    warehouse?: Record<string, any>,
    config?: Record<string, any>,
  ) {
    console.log('🚬 ~ OrderCarriersService ~ config:', config);

    let weight = 0;
    let subTotal = 0;
    for (let index = 0; index < order?.products.length; index++) {
      const prod: any = order?.products[index];
      weight += prod?.weight ?? 0;
      subTotal += (prod?.quantity ?? 0) * (prod?.price ?? 0);
    }

    const cod =
      (subTotal ?? 0) +
      (order?.shippingFee ?? 0) -
      (order?.discount ?? 0) +
      (order?.surcharge ?? 0) -
      (order?.paid ?? 0);

    weight = order.totalWeight ?? weight;

    let status = false;
    if (oldOrder.totalWeight != weight) {
      status = true;
    }
    order.totalWeight = weight;

    if (oldOrder.totalPrice != cod) {
      status = true;
      order.totalPrice = cod;
    } else order.totalPrice = null;

    if (
      oldOrder.recipientAddress != order.recipientAddress ||
      oldOrder.recipientDistrictId != order.recipientDistrictId ||
      oldOrder.recipientProvinceId != order.recipientProvinceId ||
      oldOrder.recipientWardId != order.recipientWardId ||
      oldOrder.recipientName != order.recipientName ||
      oldOrder.recipientPhone != order.recipientPhone ||
      oldOrder.recipientPostCode != order.recipientPostCode ||
      oldOrder.recipientEmail != order.recipientEmail
    ) {
      status = true;
    }
    if (status) {
      const senderInfo = find(
        warehouse?.senderInformations,
        (it: ISenderInfo) => it.carrierId == config?.carrierId?.toString(),
      );
      let wh: IWarehouse = warehouse as IWarehouse;
      if (senderInfo && warehouse?.isOnSender) {
        wh = {
          ...wh,
          name: senderInfo?.senderName,
          fullAddress: senderInfo?.senderAddress,
          phoneNumber: senderInfo?.senderPhone,
          addressSplit: [
            senderInfo?.senderWard,
            senderInfo?.senderDistrict,
            senderInfo?.senderProvince,
          ],
        };
      }

      const client = new VNPostClient(
        {
          userAccount: config.carrierId,
          customerCode: config?.extraData.customerCode,
          contractCode: config?.extraData.contractCode,
          serviceCode: config?.extraData.serviceCode,
          shippingMethod: config?.extraData.shippingMethod,
          requiredUponDelivery: config?.extraData.requiredUponDelivery,
          orderCancellationFeeCharged: config?.extraData.orderCancellationFeeCharged,
          exchangeOrReturn: config?.extraData.exchangeOrReturn,
        },
        this.redisService,
      );
      const res = await client.updateOrder(order, wh, oldOrder?.lastCarrier);
      if (res?.code == '500') throw new Error(res?.message);
      return res;
    }
    throw new Error("VnpOrderCorrection's not ready");
  }

  async handleNimbusWebhook({ order_number }) {
    const order = await this.orderRepository.findOne(
      { displayId: order_number },
      {
        relations: ['carriers'],
      },
    );
    if (!order?.lastCarrier?.id) {
      return;
    }
    return this.scanNimbusOrder(order.lastCarrier.id);
  }

  async handleNjvWebhook(data: Record<string, any>, _headers: Record<string, any>) {
    const { tracking_id, shipper_order_ref_no, status, timestamp } = data;
    const [orderCarrier, orderTags] = await Promise.all([
      this.orderCarrierRepository.findOne({
        where: {
          waybillNumber: tracking_id,
        },
      }),
      this.tagRepository
        .createQueryBuilder('tag')
        .andWhere(
          new Brackets(qb => {
            qb.where('tag.content in (:...contents)', { contents: ['NJV', 'Urgent'] });
          }),
        )
        .andWhere('tag.country_id = :countryId', { countryId: CountryID.MY })
        .andWhere('tag.company_id = :companyId', {
          companyId: process.env.ENV === 'production' ? '3' : '55',
        })
        .andWhere(`ARRAY[${TypeOfTagEnum.Order}] && tag.modules`)
        .getMany(),
    ]);

    const lookupTags = [];
    orderTags.forEach(element => {
      lookupTags[element?.content] = element;
    });
    if (!orderCarrier || !orderCarrier?.orderId) return;

    const order = await this.orderRepository.findOne({ id: orderCarrier?.orderId });
    if (!orderCarrier) {
      if (status != 'Pending Pickup') {
        return;
      }
    }
    const lastUpdate = parseNJVWebhook(data);
    console.log(
      '🚬 ~ OrderCarriersService ~ handleNjvWebhook ~ lookupTags:',
      lookupTags,
      orderTags,
    );
    console.log('🚬 ~ OrderCarriersService ~ handleNjvWebhook ~ order:', order?.tags);
    const tags = cloneDeep(order?.tags ?? []);
    if (
      [
        OrderFFMStatus.PickedUp3PL,
        OrderFFMStatus.InTransit,
        OrderFFMStatus.InDelivery,
        OrderFFMStatus.FailedDelivery,
        OrderFFMStatus.AwaitingReturn,
        OrderFFMStatus.InReturn,
        OrderFFMStatus.Stocked3PL,
      ]?.includes(lastUpdate?.status)
    ) {
      let tagData = null;
      if (lastUpdate?.raw?.delivery_exception?.failure_reason?.search(/No order was placed/i) > -1)
        tagData = lookupTags['Urgent'];
      else {
        if (
          [
            'Customer changed their mind',
            'Cash not ready',
            'Office address, but no one to receive',
            'Residential address, but no one to receive',
            'Driver cannot find location - customer unreachable',
            'Change of address/inaccurate address provided',
          ].some(
            reason =>
              lastUpdate?.raw?.delivery_exception?.failure_reason?.search(
                new RegExp(reason, 'i'),
              ) !== -1,
          )
        )
          tagData = lookupTags['NJV'];
      }
      console.log('🚬 ~ OrderCarriersService ~ handleNjvWebhook ~ tagData:', tagData, tags);

      if (tagData?.id) {
        const checkTag = find(tags, { id: tagData?.id });
        if (!checkTag?.id) {
          tags.push(tagData);
          const odTags = await this.otRepository.save({ id_order: order?.id, id_tag: tagData?.id });
          const log = await this.orderService.saveLogTag(
            tags,
            order?.tags ?? [],
            `${SystemIdEnum?.system}`,
            `${order?.id}`,
          );
          await this.logRepository.save(log).catch(err => {
            if (err?.driverError) console.log(err?.driverError?.detail);
            return err;
          });
        }
      }
    }

    if (lastUpdate?.status)
      await this.amqpConnection.publish('ffm-order-tracking-data', 'update-order-tracking-v2-1', {
        waybill: tracking_id,
        updates: orderBy(
          uniqBy([lastUpdate, ...(orderCarrier?.updates || [])], 'updatedAt'),
          'updatedAt',
          'desc',
        ),
        orderId: orderCarrier?.orderId,
        reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.ninjavan],
      } as IUpdateOrderTracking);

    const orderExtra = orderCarrier?.extraData || {};
    if (orderCarrier?.config && !orderExtra?.waybill_url) {
      console.log('he', orderExtra);
      await this.amqpConnection.publish('ffm-order', 'handle-njv-waybill', { tracking_id });
    }
  }

  // async handleNimbusWebhook(data: Record<string, any>, headers: Record<string, any>) {
  //   const {tracking_id, shipper_order_ref_no, status, timestamp} = data;
  //   const orderCarrier = await this.orderCarrierRepository.findOne({
  //     where: {
  //       waybillNumber: tracking_id
  //     },
  //   })
  //   if (!orderCarrier) {
  //     if (status != 'Pending Pickup') {
  //       return;
  //     }
  //     const displayId = (shipper_order_ref_no?.split('_') || [])[0];
  //     const order = await this.orderRepository.findOne({displayId});
  //     if (order) {
  //       const orderPartner: OrderCarrier = {
  //         orderId: order.id,
  //         waybillNumber: tracking_id,
  //         lastUpdatedBy: SystemIdEnum.system,
  //         creatorId: SystemIdEnum.system,
  //         updatedAt: new Date(timestamp),
  //         typeUpdate: TypeUpdate3PL.CarrierInformation,
  //         extraData: {
  //           waybill_url: ''
  //         },
  //         status: CarrieStatusEnum.activated
  //       } as OrderCarrier;
  //       await this.orderCarrierRepository.upsert(orderPartner, ['orderId', 'carrierId', 'waybillNumber']);
  //     } else {
  //       return;
  //     }
  //   }
  //   const lastUpdate = parseNJVWebhook(data);
  //   await this.amqpConnection.publish(
  //     'ffm-order',
  //     'update-order-tracking',
  //     {
  //       waybill: tracking_id,
  //       updates: orderBy(uniqBy([lastUpdate, ...(orderCarrier.updates || [])], 'updatedAt'), 'updatedAt', 'desc'),
  //       orderId: orderCarrier.orderId,
  //       reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.ninjavan]
  //     } as IUpdateOrderTracking,
  //   );
  //   const orderExtra = orderCarrier.extraData || {};
  //   if (orderCarrier.config && !orderExtra.waybill_url) {
  //     console.log('he', orderExtra);
  //     await this.amqpConnection.publish(
  //       'ffm-order',
  //       'handle-njv-waybill',
  //       {tracking_id}
  //     );
  //   }
  // }

  async handleJntThaiWebhook(data: Record<string, any>, sign: string, raw: string) {
    const { traces, billcode, txlogisticid } = data;
    const orderCarrier = await this.orderCarrierRepository
      .createQueryBuilder()
      .where({
        waybillNumber: billcode,
      })
      .getOne();
    if (!orderCarrier) {
      return new RawResponse({
        logisticproviderid: 'JT',
        responseitems: {
          reason: 'Order not found',
          success: 'false',
        },
      });
    }
    const config = plainToInstance(CarrierConfiguration, orderCarrier?.config);
    if (!config) {
      return new RawResponse({
        logisticproviderid: 'JT',
        responseitems: {
          reason: 'Order not found',
          success: 'false',
        },
      });
    }
    const client = new JntExpressThailandClient(
      config.extraData.key,
      config.carrierId,
      config.extraData.eCompanyId,
    );
    if (!client.checkWebhook(raw, sign)) {
      return new RawResponse({
        logisticproviderid: 'JT',
        responseitems: {
          reason: 'data digest is invalid',
          success: 'false',
        },
      });
    }
    const updates = parseJntThTrackings(traces);
    // console.log(222, updates);

    await this.amqpConnection.publish('ffm-order-tracking-data', 'update-order-tracking-v2-1', {
      waybill: billcode,
      updates,
      orderId: orderCarrier.orderId,
      reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.jtexpress]?.[66],
    } as IUpdateOrderTracking);

    return new RawResponse({
      logisticproviderid: 'JT',
      responseitems: {
        billcode,
        txlogisticid,
        reason: '',
        success: 'true',
      },
    });
  }

  async handleNjvSelfWebhook(data: Record<string, any>) {
    const { tracking_id, events } = data;
    const orderCarrier = await this.orderCarrierRepository.findOne({
      where: {
        waybillNumber: tracking_id,
      },
    });
    if (!orderCarrier) {
      return null;
    }
    await this.amqpConnection.publish('ffm-order-tracking-data', 'update-order-tracking-v2-1', {
      waybill: tracking_id,
      updates: parseNJVTrackings(events),
      orderId: orderCarrier.orderId,
      reason: CARRIER_CONFIGURATION_REASON?.[CarrierCode.ninjavan],
    } as IUpdateOrderTracking);
  }

  async checkODZ(order: Order, companyId: number, carrierId: number) {
    if (!order.recipientPostCode) {
      return { warningEnable: false, odz: false };
    }
    const carrier = await this.carrierRepository.findOne(carrierId);
    if (carrier.code == CarrierCode.nimbus) {
      // const condition: FindCondition<CarrierConfiguration> = {
      //   carrierCode: carrier.code,
      //   countryId: Number(order.countryId),
      //   status: CommonStatus.activated,
      //   companyId: order.companyId,
      //   isDefault: true
      // }
      //
      // const config = await this.carrierConfigurationRepository.findOne(condition);
      // const client = new NimbusClient(config.carrierId, config.extraData.password, this.redisService);
      // const services = await client.checkOdz(
      //   order
      // );
      // return isEmpty(services);
      return { warningEnable: false, odz: false };
    }
    const checkOdz = await this.odzConfigurationItemRepository
      .createQueryBuilder('items')
      .leftJoinAndSelect('items.odzConfig', 'odz')
      .andWhere('odz.company_id = :companyId', { companyId })
      .andWhere('odz.countryId = :countryId', { countryId: Number(order?.recipientCountryId) })
      .andWhere('items.carrierId = :carrierId', { carrierId })
      .andWhere('items.type = :type', { type: ODZEnableType.Address })
      .getOne();

    const params: FilterODZ = {
      countryId: Number(order?.recipientCountryId),
      districtId: order?.recipientDistrictId,
      carrierIds: [carrierId],
    };
    if (!!order?.recipientWardId) params.wardId = order.recipientWardId;
    if (!!order?.recipientPostCode) params.postCode = order.recipientPostCode?.toString();

    const odz = await this.odzService
      .getODZQueryBuilder(params, null, null, {
        user: {
          companyId,
        },
      })
      .getOne();

    // if (!!checkOdz && !!odz?.id) return true;
    return {
      warningEnable: checkOdz?.type != ODZEnableType.Off ? true : false,
      odz: odz ? true : false,
    };
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'sync-cancel-waybill-order',
    queue: 'ffm-queue-cancel-waybill-order',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async handleUpdateOrderCancelWaybill({ id, lastUpdatedBy }) {
    const order = await this.orderRepository.findOne({
      where: {
        id,
      },
    });

    //update lai address name
    const recipientProvince = await this.provinceRepository.findOne({
      ffmProvinceId: order?.recipientProvinceId,
    });
    const recipientDistrict = await this.districtRepository.findOne({
      ffmDistrictId: order?.recipientDistrictId,
    });
    const recipientWard = await this.wardRepository.findOne({
      ffmWardId: order?.recipientWardId,
    });
    const recipientPostCode = await this.postCodeRepository.findOne({
      ffmPostCodeName: order?.recipientPostCode,
    });

    await this.orderRepository.update(order.id, {
      recipientProvince: recipientProvince
        ? recipientProvince?.ffmProvinceName
        : order?.recipientProvince,
      recipientDistrict: recipientDistrict
        ? recipientDistrict?.ffmDistrictName
        : order?.recipientDistrict,
      recipientWard: recipientWard ? recipientWard?.ffmWardName : order?.recipientWard,
      recipientPostCode: recipientPostCode
        ? recipientPostCode?.ffmPostCodeName
        : order?.recipientPostCode,
      isMapping3pl: false,
      lastUpdatedBy,
    });
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'handle-njv-waybill',
    queue: 'ffm-queue-handle-njv-waybill',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async handleNinjaVanWaybill({ tracking_id }) {
    const orderCarrier = await this.orderCarrierRepository.findOne({
      where: {
        waybillNumber: tracking_id,
      },
    });
    const orderExtra = orderCarrier.extraData || {};
    if (!orderCarrier.config || orderExtra.waybill_url) {
      return new Nack(false);
    }
    try {
      const {
        carrierId,
        countryId,
        extraData,
        companyId,
      } = orderCarrier.config as CarrierConfiguration;
      const client = new NinjaVanClient(extraData.key, carrierId, countryId, this.redisService);
      const bill = await client.getWaybillFile(orderCarrier.waybillNumber);
      if (!bill) {
        throw { message: 'Bill not found' };
      }
      const wwproxy = new WWPROXY(this.redisService);
      const proxy = await wwproxy.getCurrentProxyWithCheck();
      console.log('🐔  ~ OrderCarriersService ~ handleNinjaVanWaybill ~ proxy:', proxy);

      const filename = tracking_id + '.pdf';
      const form = new FormData();
      form.append('file', bill, filename);
      const headers = {
        'Content-Type': 'multipart/form-data',
      };
      const response = await axios.request({
        method: 'POST',
        baseURL: 'https://pos.pages.fm/api/v1',
        url: '/contents',
        params: {
          api_key: 'b1722c77279f4d5f81ee10e6a0ac4e43',
        },
        data: form,
        headers,
        httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
        timeout: 60000,
      });
      if (!response.data?.content_url)
        throw new Error(
          `Cannot upload attachment to pancake, response ${StringUtils.getString(response)}`,
        );
      orderExtra.waybill_url = response.data?.content_url;
      const data = await pdf(bill);
      const waybillData = data.text?.split('\n');
      const areaCodeIdx = waybillData.indexOf('F O R   N I N J A   V A N   U S E');
      if (areaCodeIdx) {
        orderExtra.area_code = waybillData[areaCodeIdx - 1];

        if (['OOZ', 'X'].includes(waybillData[areaCodeIdx - 1])) {
          const checkOdz = await this.odzConfigurationItemRepository
            .createQueryBuilder('items')
            .leftJoinAndSelect('items.odzConfig', 'odz')
            .andWhere('odz.company_id = :companyId', { companyId })
            .andWhere('odz.countryId = :countryId', { countryId: countryId })
            .andWhere('items.carrierId = :carrierId', { carrierId: orderCarrier?.carrierId })
            .andWhere('items.type = :type', { type: ODZEnableType.API })
            .getOne();

          await this.owRepository
            .createQueryBuilder()
            .insert()
            .into(OrderWarning)
            .values({
              orderId: orderCarrier?.orderId,
              warningType: WarningType.ODZ,
              isOdz3pl: true,
              carrierId: orderCarrier?.carrierId,
              status: !isNil(checkOdz) ? WarningStatus.Enable : WarningStatus.Disable,
            })
            .orIgnore()
            .execute();
        }
      }
      return await this.orderCarrierRepository.update(orderCarrier.id, { extraData: orderExtra });
    } catch (e) {
      console.log('🐔  ~ OrderCarriersService ~ handleNinjaVanWaybill ~ e:', e);
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'backup-manual-ffm-to-agsale',
    queue: 'ffm-queue-backup-manual-ffm-to-agsale',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async getOrderFFMInformation({ listSOCode }) {
    if (isEmpty(listSOCode)) return 'list SO Code is empty!';
    const listOrder = await this.orderRepository
      .createQueryBuilder('o')
      .select([
        'o.id',
        'o.displayId',
        'o.status',
        'o.externalId',
        'o.lastUpdateStatus',
        'o.reasonNote',
        'o.clientId',
      ])
      .where('o.displayId IN (:...listSOCode)', { listSOCode })
      .andWhere(`o.external_code = 'ag'`)
      .getMany();

    const syncOrders = [];
    for (const item of listOrder) {
      syncOrders?.push({
        displayId: item?.displayId,
        externalId: item?.externalId,
        status: item?.status,
        reason: '',
        description: item?.reasonNote ?? '',
        actor: 'FFM',
        updatedAt: moment(item?.lastUpdateStatus)?.valueOf(),
        clientId: item.clientId,
      });
    }
    if (!isEmpty(syncOrders)) {
      for (const group of chunk(syncOrders, 500)) {
        await this.amqpConnection.publish('order-service', 'bulk-sync-status-from-ffm', {
          orders: group,
          force: true,
        });
      }
    }
    return new Nack();
  }
}
