import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { orderConnection } from 'core/constants/database-connection.constant';
import { Not, Repository } from 'typeorm';
import { CarrierConfiguration } from '../../../entities/carrier-configuration.entity';
import { CreateCarrierConfigurationDTO } from '../../../dtos/create-carrier-configuration.dto';
import {
  CARRIER_CONFIGURATION_COUNTRIES,
  CARRIER_CONFIGURATION_EXTRA_SERVICE_FIELDS,
  CARRIER_CONFIGURATION_FIELDS,
} from '../../../constants/carriers.constants';
import { find, isEmpty, isNil, isUndefined, omitBy } from 'lodash';
import { CommonStatus } from 'core/enums/common-status.enum';
import { UpdateCarrierConfigurationDto } from '../../../dtos/update-carrier-configuration.dto';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { CarrierConfigurationFilter } from '../../../filters/carrier-configuration.filter';
import { CheckCarrierConfigurationDto } from '../../../dtos/check-carrier-configuration.dto';
import { CarrierCode, CountryID } from 'core/enums/carrier-code.enum';
import { JntExpressPhilippinesClient } from '../clients/jnt-express-philippines.client';
import { JntExpressThailandClient } from '../clients/jnt-express-thailand.client';
import { NinjaVanClient } from '../clients/ninjavan.client';
import { RedisCacheService } from '../../../../../../core/cache/services/redisCache.service';
import { FlashExpressMalaysiaClient } from '../clients/flash-express-malaysia.client';
import { KerryThailandClient } from '../clients/kerry-thailand.client';
import { FlashExpressThailandClient } from '../clients/flash-express-thailand.client';
import { NimbusClient } from '../clients/nimbus.client';
import { InsuranceType } from 'apps/ffm-order-api/src/enums/carrier-configuration.enum';
import { BlueDartClient } from '../clients/bluedart.client';
import { WeDoClient } from '../clients/wedo.client';
import { ViettelPostClient } from '../clients/viettel-post.client';
import { BestExpressClient } from '../clients/bestexpress.client';
import { VNPostClient } from '../clients/vn-post.client';
import { Logs } from 'apps/ffm-order-api/src/entities/logs.entity';
import { SPXVNClient } from '../clients/spx-vn.client';
import { BestExpressThailandClient } from '../clients/best-express-thailand.client';

@Injectable()
export class CarrierConfigurationsService {
  constructor(
    @InjectRepository(CarrierConfiguration, orderConnection)
    private carrierConfigurationRepository: Repository<CarrierConfiguration>,
    @InjectRepository(Logs, orderConnection)
    private logRepository: Repository<Logs>,
    private readonly redisService: RedisCacheService,
  ) {}

  async find(pagination: PaginationOptions, filters: CarrierConfigurationFilter) {
    const { status, carrier, countryId, companyId } = filters;
    return this.carrierConfigurationRepository.findAndCount({
      take: pagination.limit,
      skip: pagination.skip,
      where: omitBy({ status, carrierCode: carrier, countryId, companyId }, isNil),
    });
  }

  async getCarrierRequireField(carrierCode: CarrierCode, req) {
    let result = CARRIER_CONFIGURATION_FIELDS[carrierCode];
    if (carrierCode == CarrierCode.bestexpress && req?.headers?.['country-ids'] != CountryID.TH) {
      result = result.filter(field => field.name === 'password');
    }
    return result;
  }

  async get3plConfigHistory(id: number) {
    const logs = await this.logRepository
      .createQueryBuilder('logs')
      .where(`logs.table_name = 'carrier_configurations'`)
      .andWhere('logs.record_id = :id', { id })
      .getMany();
    return logs;
  }

  async findOne(
    carrierCode: CarrierCode,
    companyId: number,
    countryId: number,
    carrierId: string,
    channelCode: string,
  ) {
    return this.carrierConfigurationRepository.findOne({
      carrierCode,
      countryId,
      companyId,
      carrierId,
      channelCode,
    });
  }

  async count(filters: CarrierConfigurationFilter) {
    const { status, carrier, companyId, countryId } = filters;
    const data = await this.carrierConfigurationRepository
      .createQueryBuilder()
      .where(omitBy({ status, carrierCode: carrier, countryId, companyId }, isNil))
      .select('COUNT(*)', 'count')
      .addSelect('status', 'status')
      .groupBy('status')
      .getRawMany();
    for (const item of data) {
      item.status = CommonStatus[item.status];
    }
    return data;
  }

  async update(data: UpdateCarrierConfigurationDto, companyId: number, userId: number) {
    const {
      carrierCode,
      countryId,
      carrierId,
      isDefault,
      status,
      note,
      displayName,
      insuranceType,
      isInsurance,
      channelCode,
      extraData,
    } = data;

    const config = await this.carrierConfigurationRepository.findOne({
      carrierCode,
      countryId,
      companyId,
      carrierId,
      channelCode,
    });
    if (!config) {
      throw new BadRequestException('3PL account is not exist');
    }

    if (isDefault) {
      await this.carrierConfigurationRepository
        .update(
          {
            carrierCode,
            countryId,
            companyId,
            // displayName,
            channelCode: Not(channelCode),
          },
          { isDefault: false, lastUpdatedBy: userId },
        )
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });
    }
    let orderValue: number = null;
    if (isInsurance) {
      if (!insuranceType) throw new BadRequestException('You must choose Insurance Type!');
      if (data?.insuranceType == InsuranceType.COD) orderValue = 1;
      if (data?.insuranceType == InsuranceType.InputValue) {
        if (!data.orderValue) throw new BadRequestException('You must input Order Value!');
        orderValue = data?.orderValue;
      }
    }
    return this.carrierConfigurationRepository
      .save({
        ...config,
        isDefault,
        note,
        status,
        displayName,
        isInsurance,
        insuranceType: !isInsurance ? null : data?.insuranceType,
        orderValue: !isInsurance ? null : orderValue,
        extraData,
        lastUpdatedBy: userId,
      } as CarrierConfiguration)
      .catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
  }

  async create(data: CreateCarrierConfigurationDTO, companyId: number, userId: number) {
    const {
      carrierCode,
      countryId,
      carrierId,
      extraData,
      isDefault,
      note,
      displayName,
      insuranceType,
      isInsurance,
    } = data;
    const isSupported = (CARRIER_CONFIGURATION_COUNTRIES[carrierCode] || {})[countryId];
    if (!isSupported) {
      throw new BadRequestException('3PL account has not supported for this country');
    }
    const requiredFields = CARRIER_CONFIGURATION_FIELDS[carrierCode];
    if (!requiredFields) {
      throw new BadRequestException('3PL account is not supported');
    }
    const saveExtraData: Record<string, any> = {};
    for (const requiredField of requiredFields) {
      if (requiredField.name == 'contractCode' && carrierCode == CarrierCode.vnpost) {
        saveExtraData[requiredField.name] = extraData[requiredField.name];
      } else if (isEmpty(extraData[requiredField.name])) {
        throw new BadRequestException(`${requiredField.title} is empty`);
      }
      saveExtraData[requiredField.name] = extraData[requiredField.name];
    }
    if (!isEmpty(CARRIER_CONFIGURATION_EXTRA_SERVICE_FIELDS[carrierCode])) {
      for (const item of CARRIER_CONFIGURATION_EXTRA_SERVICE_FIELDS[carrierCode]) {
        saveExtraData[item.name] = extraData[item.name];
      }
    }
    const channelCode = find(requiredFields, { name: 'channelCode' });

    const config = await this.carrierConfigurationRepository.findOne({
      carrierCode,
      countryId,
      companyId,
      carrierId,
      channelCode:
        isNil(channelCode) || !saveExtraData?.[channelCode?.name]
          ? carrierId
          : extraData?.[channelCode?.name],
    });
    if (config) {
      throw new BadRequestException('3PL account has existed');
    }

    await this.check(data);

    if (isDefault) {
      await this.carrierConfigurationRepository
        .update(
          {
            carrierCode,
            countryId,
            companyId,
            // displayName,
          },
          { isDefault: false, lastUpdatedBy: userId },
        )
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });
    }
    let orderValue: number = null;
    if (isInsurance) {
      if (!insuranceType) throw new BadRequestException('You must choose Insurance Type!');
      if (data?.insuranceType == InsuranceType.COD) orderValue = 1;
      if (data?.insuranceType == InsuranceType.InputValue) {
        if (!data.orderValue) throw new BadRequestException('You must input Order Value!');
        orderValue = data?.orderValue;
      }
    }
    return this.carrierConfigurationRepository
      .save({
        extraData: saveExtraData,
        carrierId,
        companyId,
        countryId,
        carrierCode,
        creatorId: userId,
        isDefault,
        status: CommonStatus.activated,
        note,
        displayName,
        isInsurance,
        insuranceType: !isInsurance ? null : data?.insuranceType,
        orderValue: !isInsurance ? null : orderValue,
        channelCode:
          isNil(channelCode) || !saveExtraData?.[channelCode?.name]
            ? carrierId
            : extraData?.[channelCode?.name],
        lastUpdatedBy: userId,
      } as CarrierConfiguration)
      .catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
  }

  async check(data: CheckCarrierConfigurationDto) {
    const { carrierCode, carrierId, extraData, countryId } = data;
    const requiredFields = CARRIER_CONFIGURATION_FIELDS[carrierCode];
    const isSupported = (CARRIER_CONFIGURATION_COUNTRIES[carrierCode] || {})[countryId];
    if (!isSupported) {
      throw new BadRequestException('3PL account has not supported for this country');
    }
    if (!requiredFields) {
      throw new BadRequestException('3PL account is not supported');
    }
    for (const requiredField of requiredFields) {
      if (isEmpty(extraData[requiredField.name])) {
        throw new BadRequestException(`${requiredField.title} is empty`);
      }
    }
    let isValid = false;
    switch (carrierCode) {
      case CarrierCode.jtexpress:
        switch (countryId) {
          case 63: {
            const client = new JntExpressPhilippinesClient(
              extraData.key,
              carrierId,
              extraData.eCompanyId,
            );
            const res = await client.trackOrderData('************');
            isValid = !['S01', 'S02', 'S03', 'S04', 'S08'].includes(res[0]?.reason);
            if (!isValid) {
              throw new BadRequestException(res[0]?.message);
            }
            break;
          }
          case 66: {
            const client = new JntExpressThailandClient(
              extraData.key,
              carrierId,
              extraData.eCompanyId,
            );
            const res = await client.trackOrderData('************');
            console.log('res', res);
            isValid = !['S01', 'S02', 'S03', 'S04', 'S08'].includes(res[0]?.reason);
            if (!isValid) {
              throw new BadRequestException(res[0]?.message);
            }
            break;
          }
        }
        break;
      case CarrierCode.ninjavan: {
        const client = new NinjaVanClient(extraData.key, carrierId, countryId, this.redisService);
        try {
          const res = await client.getNjvAccessToken();
          console.log('res', res);
          isValid = true;
        } catch (e) {
          throw new BadRequestException(e?.response?.data);
        }
        break;
      }
      case CarrierCode.bestexpress: {
        switch (countryId) {
          case 66:
            {
              const client = new BestExpressThailandClient({
                userAccount: carrierId,
                password: extraData.password,
              });
              try {
                const res = await client.trackOrderData(['']);
                console.log('res', res);
                isValid = true;
              } catch (e) {
                console.log('e', e);
                throw e;
              }
            }
            break;
          case 84:
            {
              const client = new BestExpressClient(
                carrierId,
                extraData.password,
                this.redisService,
              );
              try {
                const res = await client.getAccessToken();
                console.log('res', res);
                isValid = !!res;
              } catch (e) {
                throw new BadRequestException(e?.response?.data);
              }
            }
            break;
        }
        break;
      }
      case CarrierCode.nimbus: {
        const client = new NimbusClient(carrierId, extraData.password, this.redisService);
        try {
          const res = await client.getAccessToken();
          console.log('res', res);
          isValid = true;
        } catch (e) {
          throw new BadRequestException(e?.response?.data);
        }
        break;
      }
      case CarrierCode.bluedart: {
        const client = new BlueDartClient(
          {
            loginId: carrierId,
            clientId: extraData.clientId,
            clientSecret: extraData.clientSecret,
            customerCode: extraData.customerCode,
            shippingLicense: extraData.shippingLicense,
            location: extraData.location,
          },
          this.redisService,
        );
        try {
          const res = await client.getAccessToken();
          console.log('res', res);
          isValid = true;
        } catch (e) {
          throw new BadRequestException(e?.response?.data);
        }
        break;
      }
      case CarrierCode.flashexpress: {
        switch (countryId) {
          case 60:
            {
              const client = new FlashExpressMalaysiaClient(carrierId, extraData.key);
              try {
                const res = await client.getWebhookInfo();
                console.log('res', res);
                isValid = true;
              } catch (e) {
                console.log('e', e);
                throw new BadRequestException(e?.response?.data);
              }
            }
            break;
          case 66:
            {
              const client = new FlashExpressThailandClient(carrierId, extraData.key);
              try {
                const res = await client.getWebhookInfo();
                console.log('res', res);
                isValid = true;
              } catch (e) {
                console.log('e', e);
                throw new BadRequestException(e?.response?.data);
              }
            }
            break;
        }
        break;
      }
      case CarrierCode.kerryexpress: {
        switch (countryId) {
          case 66:
            {
              const client = new KerryThailandClient(
                extraData.key,
                carrierId,
                extraData.merchant_id,
                extraData.appKey,
                extraData.appId,
              );
              try {
                const res = await client.trackOrderData(['']);
                console.log('res', res);
                isValid = true;
              } catch (e) {
                console.log('e', e);
                throw e;
              }
            }
            break;
        }
        break;
      }
      case CarrierCode.wedo: {
        const client = new WeDoClient({
          userAccount: carrierId,
          token: extraData.key,
          whCode: extraData.whCode,
          channelCode: extraData.channelCode,
        });
        try {
          const res = await client.validAccount();
          const channel = find(res?.data ?? [], {
            channelCode: extraData.channelCode,
            whCode: extraData.whCode,
          });
          if (isUndefined(channel)) throw new BadRequestException('3PL account is invalid');
          isValid = true;
        } catch (e) {
          console.log('e', e);
          throw new BadRequestException(e?.response?.message);
        }
        break;
      }

      case CarrierCode.viettelpost: {
        const client = new ViettelPostClient(
          {
            userAccount: carrierId,
            password: extraData.key,
          },
          this.redisService,
        );
        try {
          const res = await client.validAccount();
          if (res?.code == 200) isValid = true;
        } catch (e) {
          console.log('e', e);
          throw new BadRequestException(e?.response?.message);
        }
        break;
      }

      case CarrierCode.vnpost: {
        isValid = true;
        break;
      }

      case CarrierCode.ghtk: {
        isValid = true;
        break;
      }

      case CarrierCode.spxexpress: {
        const client = new SPXVNClient({
          userAccount: carrierId,
          password: extraData.key,
        });
        try {
          const res = await client.validAccount();
          if (res?.data.match_result) isValid = true;
        } catch (e) {
          console.log('e', e);
          throw new BadRequestException(e?.response?.message);
        }
        break;
      }
      case CarrierCode.jntbangkok: {
        isValid = true;
        break;
      }
    }
    if (!isValid) {
      throw new BadRequestException('3PL account is invalid');
    }
    return isValid;
  }
}
