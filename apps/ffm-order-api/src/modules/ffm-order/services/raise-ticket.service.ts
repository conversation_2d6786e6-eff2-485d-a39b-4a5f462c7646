import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  BulkRaiseTicketDto,
  CloseRaiseTicketDto,
  CreateRaiseTicketAppointmentDto,
  CreateRaiseTicketDto,
  CreateRaiseTicketNoteDto,
  DeleteBulkRaiseTicketDto,
  TicketTagDto,
  UpdateBulkRaiseTicketDto,
  UpdateRaiseTicketAppointmentDto,
  UpdateRaiseTicketDto,
  UpdateTagBulkRaiseTicketDto,
} from 'apps/ffm-order-api/src/dtos/create-raise-ticket.dto';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { RaiseTicket } from 'apps/ffm-order-api/src/entities/raise-ticket.entity';
import {
  MethodTicket,
  RaiseTicketNoteEnum,
  RaiseTicketStatusEnum,
  RaiseTicketTypeEnum,
  RaiseTicketTypeU<PERSON><PERSON><PERSON>num,
  TagAction,
  TicketAppointment<PERSON>tatusEnum,
  TicketAppointmentTypeSearch,
  TicketTypeSearch,
  TypeAssigneeTicket,
  TypeSearchTicketFilter,
  TypeSortTicket,
} from 'apps/ffm-order-api/src/enums/raise-ticket.enum';
import { FilterRaiseTicket } from 'apps/ffm-order-api/src/filters/raise-ticket.filter';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import {
  cloneDeep,
  concat,
  intersectionBy,
  isEmpty,
  isNil,
  isUndefined,
  orderBy,
  reduce,
  split,
  uniq,
} from 'lodash';
import { $enum } from 'ts-enum-util';
import { Between, Brackets, ILike, In, IsNull, Not, Repository } from 'typeorm';
import * as moment from 'moment-timezone';
import {
  CHANGE_STATUS_TICKET,
  NEXT_STATUS_TICKET,
  ORDER_FFM_STATUS,
  RAISE_TICKET_CODE,
  RAISE_TICKET_NOTE,
  RAISE_TICKET_STATUS,
  RAISE_TICKET_TYPE,
  UPDATE_TICKET_RUSH_STATUS,
} from 'apps/ffm-order-api/src/constants/raise-ticket.constants';
import { SystemIdEnum, UserType } from 'core/enums/user-type.enum';
import {
  AmqpConnection,
  defaultNackErrorHandler,
  Nack,
  RabbitRPC,
} from '@golevelup/nestjs-rabbitmq';
import { Permission } from 'core/enums/permission-ffm.enum';
import { UserStatus } from 'core/enums/user-status.enum';
import { RaiseTicketNote } from 'apps/ffm-order-api/src/entities/raise-ticket-note.entity';
import { Logs } from 'apps/ffm-order-api/src/entities/logs.entity';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import xlsx from 'node-xlsx';
import { User } from 'core/entities/identity/user.entity';
import { TagService } from './tags.service';
import { TypeOfTagEnum } from 'apps/ffm-order-api/src/enums/tag.enum';
import { Tag } from 'apps/ffm-order-api/src/entities/tags.entity';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { TicketTag } from 'apps/ffm-order-api/src/entities/ticket-tag.entity';
import {
  TagMethodType,
  TagOperatorType,
} from 'apps/ffm-order-api/src/enums/order-search-recipient.enum';
import { TagDto } from 'apps/ffm-order-api/src/dtos/create-order.dto';
import {
  SortType,
  TypeSearchOrderFilter,
  WarningType,
} from 'apps/ffm-order-api/src/enums/order-status.enum';
import { TicketLogs } from 'apps/ffm-order-api/src/entities/ticket-logs.entity';
import { LAST_STATUS } from 'apps/ffm-order-api/src/constants/order.constants';
import { TicketAppointment } from 'apps/ffm-order-api/src/entities/ticket-appointment.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { OrderWarning } from 'apps/ffm-order-api/src/entities/order_warning.entity';

@Injectable()
export class RaiseTicketService {
  constructor(
    @InjectRepository(RaiseTicket, orderConnection)
    private raiseTicketRepository: Repository<RaiseTicket>,
    @InjectRepository(RaiseTicketNote, orderConnection)
    private raiseTicketNoteRepository: Repository<RaiseTicketNote>,
    @InjectRepository(Order, orderConnection)
    private orderRepository: Repository<Order>,
    private readonly amqpConnection: AmqpConnection,
    @InjectRepository(Logs, orderConnection)
    private logRepository: Repository<Logs>,
    @InjectRepository(TicketLogs, orderConnection)
    private ticketLogRepository: Repository<TicketLogs>,
    @InjectRepository(TicketTag, orderConnection)
    private ticketTagRepository: Repository<TicketTag>,
    @InjectRepository(TicketAppointment, orderConnection)
    private taRepository: Repository<TicketAppointment>,
    @InjectRepository(OrderWarning, orderConnection)
    private owRepository: Repository<OrderWarning>,
    private tagService: TagService,
    @InjectQueue('raise-ticket')
    private raiseTicketQueue: Queue,
  ) {}

  onModuleInit() {
    this.scheduleAutoProcessTicketAppointment();
  }

  async genDisplayId(type: RaiseTicketTypeEnum): Promise<string> {
    const nowTime = moment()
      .startOf('day')
      ?.valueOf();

    await this.raiseTicketRepository.query(`CREATE SEQUENCE IF NOT EXISTS raise_ticket_seq;`);
    const increment = await this.raiseTicketRepository.query(
      `select nextval('raise_ticket_seq') as id`,
    );

    let idInDay = increment[0]?.id;
    // console.log(nowTime);
    // console.log(idInDay);

    if (nowTime > idInDay) {
      idInDay = Number(nowTime) + 1;
      await this.raiseTicketRepository.query(
        `ALTER SEQUENCE raise_ticket_seq RESTART WITH ${idInDay};`,
      );
    }

    const newId = idInDay - nowTime;
    console.log(newId, idInDay, nowTime);

    const displayId = `${RAISE_TICKET_CODE[type]}-${moment()
      .startOf('day')
      ?.format('MMDDYY')}-${newId < 10 ? `0${newId}` : newId}`;

    return displayId;
  }

  async bulkRaiseTicket(
    data: BulkRaiseTicketDto,
    request?: Record<string, any>,
  ): Promise<Record<string, any>> {
    const { companyId, type, isAdmin, warehouses } = request?.user;

    const mQuery = this.orderRepository.createQueryBuilder('order');
    if (!isAdmin && type == $enum(UserType).getKeyOrDefault(UserType.employee, null)) {
      mQuery.andWhere('order.warehouse_id IN (:...warehouses)', { warehouses });
    }
    mQuery
      .andWhere('order.companyId = :companyId', { companyId })
      .andWhere('order.id IN (:...ids)', { ids: data?.ids });

    const queryCount = this.raiseTicketRepository
      .createQueryBuilder('rt')
      .andWhere({ companyId })
      // .andWhere('rt.deleted_at IS NULL')
      .andWhere({
        createdAt: Between(
          new Date(
            moment()
              .startOf('day')
              .format('YYYY-MM-DDTHH:mm:ssZ'),
          ),
          new Date(
            moment()
              .endOf('day')
              .format('YYYY-MM-DDTHH:mm:ssZ'),
          ),
        ),
      })
      .select(['id as id']);

    const [orders, tickets, ticketInDays] = await Promise.all([
      mQuery.getMany(),
      this.raiseTicketRepository.find({
        orderId: In(data?.ids),
        companyId: request?.user?.companyId,
        type: data?.type,
        deletedAt: null,
      }),
      queryCount.getRawMany(),
    ]);

    let _identity = ticketInDays?.length ?? 0;

    const lookUpOrder = reduce(
      orders,
      (prev: Record<string, any>[], next: Order) => {
        prev[next?.id] = next;
        return prev;
      },
      [],
    );

    const lookUpTicket = reduce(
      tickets,
      (prev: Record<string, any>[], next: RaiseTicket) => {
        prev[next?.orderId] = next;
        return prev;
      },
      [],
    );

    const result: Record<string, any> = {
      success: [],
      error: [],
    };

    const params: RaiseTicket[] = [];

    for (const id of data?.ids) {
      if (!lookUpOrder[id]) {
        result.error.push({
          id,
          code: 'SO_0009',
        });
      } else {
        if (!!lookUpTicket[id] && lookUpTicket[id]?.type == data?.type) {
          const ticket = lookUpTicket[id];
          if (
            [RaiseTicketStatusEnum.Resolved, RaiseTicketStatusEnum.Closed]?.includes(ticket?.status)
          ) {
            if (data?.methodTicket == MethodTicket.reOpen) {
              params.push(
                plainToInstance(RaiseTicket, {
                  ...ticket,
                  assignee: null,
                  typeUpdate: RaiseTicketTypeUpdateEnum.BulkCreate,
                  status: RaiseTicketStatusEnum['Re-Open'],
                }),
              );
            } else {
              result.error.push({
                id,
                code: 'TIX_0011',
                displayId: lookUpOrder?.[id]?.displayId,
              });
            }
          } else {
            result.error.push({
              id,
              code: 'TIX_0002',
              displayId: lookUpOrder?.[id]?.displayId,
            });
          }
        } else {
          _identity += 1;
          const displayId = `${RAISE_TICKET_CODE[data?.type]}-${moment()
            .startOf('day')
            ?.format('MMDDYY')}-${_identity < 10 ? `0${_identity}` : _identity}`;
          params.push(
            plainToInstance(RaiseTicket, {
              orderId: id,
              status: RaiseTicketStatusEnum.New,
              typeUpdate: RaiseTicketTypeUpdateEnum.BulkCreate,
              type: data?.type,
              displayId: `${displayId}`,
              creatorId: request?.user?.id,
              lastUpdatedBy: request?.user?.id,
              companyId,
            }),
          );
        }
      }
    }
    const res = await this.raiseTicketRepository.save(params).catch(err => {
      console.log(err?.driverError);
      return err;
    });
    result.success = res;

    if (data?.type == RaiseTicketTypeEnum.RetryDelivery) {
      await Promise.all(
        params.map(async item => {
          this.removeNeedRetryTicketWarningByTicket(item?.orderId);
        }),
      );
    }

    return result;
  }

  async createRaiseTicket(
    data: CreateRaiseTicketDto,
    request?: Record<string, any>,
  ): Promise<RaiseTicket> {
    const { companyId, type, isAdmin, warehouses, id } = request?.user;

    const mQuery = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.tickets', 'tickets');
    if (!isAdmin && type == $enum(UserType).getKeyOrDefault(UserType.employee, null)) {
      mQuery.andWhere('order.warehouse_id IN (:...warehouses)', { warehouses });
    }
    mQuery
      .andWhere('order.companyId = :companyId', { companyId })
      .andWhere('order.id = :id', { id: data?.orderId });

    const [order, checkRaiseTicket] = await Promise.all([
      mQuery.getOne(),
      this.raiseTicketRepository.findOne({
        orderId: data?.orderId,
        companyId: request?.user?.companyId,
        type: data?.type,
      }),
    ]);

    if (!order)
      throw new BadRequestException({ code: 'SO_0009', message: 'Không tìm thấy đơn hàng' });
    const ptManager = await this.checkRolePtManager(id, Permission.ptManager);
    const assigneeIds = order?.tickets?.map(x => x?.assignee);
    if (!ptManager && !assigneeIds.includes(id)) {
      throw new BadRequestException({
        code: 'TIX_0015',
        message: `Không có quyền cập nhật thông tin ticket.`,
      });
    }
    let raiseTicket = plainToInstance(RaiseTicket, {
      orderId: data?.orderId,
      status: RaiseTicketStatusEnum.New,
      type: data?.type,
      creatorId: request?.user?.id,
      lastUpdatedBy: request?.user?.id,
      companyId: request?.user?.companyId,
    });

    if (!!checkRaiseTicket) {
      if (
        [RaiseTicketStatusEnum.Resolved, RaiseTicketStatusEnum.Closed]?.includes(
          checkRaiseTicket?.status,
        )
      )
        raiseTicket = plainToInstance(RaiseTicket, {
          ...checkRaiseTicket,
          assignee: null,
          status: RaiseTicketStatusEnum['Re-Open'],
        });
      else
        throw new BadRequestException({
          code: 'TIX_0002',
          message: `Đơn hàng ${order?.displayId} đã tồn tại ticket loại này!`,
        });
    } else {
      const displayId = await this.genDisplayId(data?.type);
      raiseTicket.displayId = `${displayId}`;
    }

    if (data?.assignee) {
      raiseTicket.assignee = data?.assignee;
      raiseTicket.assigneeIds = [data?.assignee];
      raiseTicket.lastUpdateAssignee = new Date();
      raiseTicket.status = RaiseTicketStatusEnum.Assigned;
    }

    const result = await this.raiseTicketRepository.save(raiseTicket).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(
          err?.driverError?.constraint == 'UQ_DISPLAY_TICKET'
            ? { code: 'TIX_0001' }
            : err?.driverError?.constraint == 'UQ_DISPLAY_TYPE_ORDER'
            ? { code: 'TIX_0002' }
            : err?.driverError?.detail,
        );
      }
      return err;
    });

    if (raiseTicket.type == RaiseTicketTypeEnum.RetryDelivery) {
      await this.removeNeedRetryTicketWarningByTicket(raiseTicket.orderId);
    }
    return result;
  }

  async viewRaiseTicket(
    tid?: number,
    request?: Record<string, any>,
    headers?: Record<string, any>,
  ): Promise<RaiseTicket> {
    const { companyId, id } = request?.user;
    const mQuery = this.raiseTicketRepository.createQueryBuilder('rt');
    mQuery.andWhere({ companyId });
    mQuery.andWhere([{ id: isNaN(Number(tid)) ? 0 : tid }, { displayId: ILike(String(tid)) }]);

    mQuery.leftJoinAndSelect('rt.order', 'order');
    mQuery.leftJoinAndSelect('rt.notes', 'notes');
    mQuery.leftJoinAndSelect('rt.tags', 'tags');
    mQuery.leftJoinAndSelect('rt.ticketAppointments', 'ticketAppointments');
    mQuery.leftJoinAndSelect('order.carriers', 'carriers');
    mQuery.leftJoinAndSelect('order.products', 'products');
    mQuery.leftJoinAndSelect('order.notes', 'orderNotes');
    mQuery.leftJoinAndSelect('tags.ticketTags', 'ticketTags', 'rt.id = ticketTags.id_ticket');

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds))
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
    }
    mQuery.addOrderBy('notes.createdAt', 'DESC');
    mQuery.addOrderBy('ticketTags.created_at', 'DESC');

    const ptManager = await this.checkRolePtManager(id, Permission.ptManager);

    if (!ptManager) {
      // mQuery.andWhere('rt.status IN (:...statusPtView)', { statusPtView: [RaiseTicketStatusEnum.FollowUp, RaiseTicketStatusEnum.Assigned, RaiseTicketStatusEnum?.InProcess] });
      // mQuery.andWhere('rt.assignee = :assigneeId', { assigneeId: id });
    }

    const data = await mQuery.getOne();
    if (!data) throw new ForbiddenException('');

    if (data?.isSpotlight) {
      await this.raiseTicketRepository.update({ id: data?.id }, { isSpotlight: false });
    }

    return data;
  }

  async queryRaiseTicket(
    mQuery: any,
    filters: FilterRaiseTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const { companyId, id } = request?.user;
    const {
      orderStatus,
      search,
      types,
      status,
      carrierIds,
      from,
      to,
      clientIds,
      assigneeIds,
      tagIds,
      tagMethod,
      operator,
      resultIds,
      isResultNull,
      query,
      typeSearch,
      productIds,
      typeFilter,
      isAssigneeNull,
    } = filters;

    mQuery.leftJoin('rt.order', 'order');
    mQuery.leftJoin('rt.notes', 'notes');
    mQuery.leftJoin('rt.tags', 'tags');
    mQuery.leftJoin('rt.ticketAppointments', 'ta');
    mQuery.leftJoin('order.carriers', 'carriers', `carriers.status = 'activated'`);
    mQuery.leftJoin('order.reason', 'reason');
    mQuery.leftJoin('order.products', 'products');
    mQuery.andWhere({
      companyId,
    });

    if (types) {
      mQuery.andWhere('rt.type IN (:...types)', { types });
    }

    if (!!orderStatus) {
      mQuery.andWhere('order.status IN (:...orderStatus)', { orderStatus });
    }

    if (carrierIds) {
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (clientIds) {
      mQuery.andWhere('order.client_id IN (:...clientIds)', { clientIds });
    }

    if (assigneeIds) {
      if (isAssigneeNull) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('rt.assignee IN (:...assigneeIds)', { assigneeIds }).orWhere(
              'rt.assignee IS NULL',
            );
          }),
        );
      } else {
        mQuery.andWhere('rt.assignee IN (:...assigneeIds)', { assigneeIds });
      }
    } else {
      if (isAssigneeNull) {
        mQuery.andWhere('rt.assignee IS NULL');
      }
    }

    if (tagIds) {
      const subQb = this.ticketTagRepository
        .createQueryBuilder('tt')
        .select('tt.id_ticket', 'id_ticket')
        .where('tt.id_tag IN (:...tagIds)', { tagIds: filters.tagIds })
        .groupBy('tt.id_ticket');

      if (tagMethod == TagMethodType.Include) {
        if (operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT tt.id_tag) = ${tagIds?.length}`);
          mQuery.andWhere(`rt.id IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
        }
        if (operator == TagOperatorType.Or) {
          mQuery.andWhere('tags.id IN (:...tagIds)', { tagIds: tagIds });
        }
      }

      if (tagMethod == TagMethodType.Exclude) {
        if (operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT tt.id_tag) = ${tagIds?.length}`);
          mQuery
            .andWhere(`rt.id NOT IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
        if (operator == TagOperatorType.Or) {
          mQuery
            .andWhere(`rt.id NOT IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
      }
    }

    if (resultIds) {
      if (isResultNull) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('notes.type IN (:...resultIds)', { resultIds }).orWhere('notes.type IS NULL');
          }),
        );
      } else {
        mQuery.andWhere('notes.type IN (:...resultIds)', { resultIds });
      }
    } else {
      if (isResultNull) {
        mQuery.andWhere('notes.type IS NULL');
      }
    }

    if (!!query) {
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('rt.display_id ILIKE :query', {
            query: `%${query}%`,
          })
            .orWhere('order.display_id ILIKE :query')
            .orWhere('order.recipient_phone ILIKE :query')
            .orWhere('order.external_id ILIKE :query')
            .orWhere('carriers.waybill_number ILIKE :query');
        }),
      );
    }

    if (search) {
      let similar = search;
      const splitSearch = split(similar, ';');
      if (splitSearch.length > 0) {
        similar = splitSearch.join('|');
        if (!typeSearch) {
          mQuery.andWhere(
            new Brackets(qb => {
              qb.where('rt.display_id SIMILAR TO :similar', {
                similar: `%(${similar})%`,
              })
                .orWhere('order.display_id SIMILAR TO :similar')
                .orWhere('order.recipient_phone SIMILAR TO :similar')
                .orWhere('order.external_id SIMILAR TO :similar')
                .orWhere('carriers.waybill_number SIMILAR TO :similar');
            }),
          );
        } else {
          if (typeSearch == TicketTypeSearch.ticketIdOrSO)
            mQuery.andWhere(
              new Brackets(qb => {
                qb.where('rt.display_id SIMILAR TO :similar', {
                  similar: `%(${similar})%`,
                }).orWhere('order.display_id SIMILAR TO :similar');
              }),
            );
          else if (typeSearch == TicketTypeSearch.waybill)
            mQuery.andWhere(
              new Brackets(qb => {
                qb.where('carriers.waybill_number SIMILAR TO :similar', {
                  similar: `%(${similar})%`,
                });
              }),
            );
          else if (typeSearch == TicketTypeSearch.phoneNumber)
            mQuery.andWhere(
              new Brackets(qb => {
                qb.where('order.recipient_phone SIMILAR TO :similar', {
                  similar: `%(${similar})%`,
                });
              }),
            );
          else if (typeSearch == TicketTypeSearch.externalId)
            mQuery.andWhere(
              new Brackets(qb => {
                qb.where('order.external_id SIMILAR TO :similar', {
                  similar: `%(${similar})%`,
                });
              }),
            );
        }
      }
    }

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds))
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
    }
    const ptManager = await this.checkRolePtManager(id, Permission.ptManager);
    // console.log(ptManager);

    if (!ptManager && !filters?.orderId) {
      // mQuery.andWhere('rt.status NOT IN (:...statusPtView)', { statusPtView: [RaiseTicketStatusEnum.New, RaiseTicketStatusEnum?.['Re-Open']] });
      mQuery.andWhere('rt.assignee = :assigneeId', { assigneeId: id });
    }
    mQuery.andWhere('rt.deleted_at IS NULL');

    if (productIds) {
      mQuery.andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    switch (typeFilter) {
      default:
        if (!!from)
          mQuery.andWhere('rt.created_at >= :from', {
            from:
              from ??
              moment()
                .subtract(30, 'days')
                .startOf('day')
                .toDate(),
          });
        if (!!to)
          mQuery.andWhere('rt.created_at <= :to', {
            to:
              to ??
              moment()
                .endOf('day')
                .toDate(),
          });

        if (status) {
          mQuery.andWhere('rt.status IN (:...status)', { status });
        }

        break;
      case TypeSearchTicketFilter.StatusChanges:
        mQuery
          .leftJoin(TicketLogs, 'logs', `(rt.id::VARCHAR = logs.record_id)`)
          .andWhere(`logs.type = 'Status'`);
        if (status) {
          mQuery.andWhere(`logs.changes[1]::INT IN (:...status)`, { status });
          // .leftJoin(TicketLogs, 'och', 'och.order_id = order.id');
        }
        if (!!from) {
          mQuery.andWhere('logs.created_at >= :from', {
            from:
              from ??
              moment()
                .subtract(30, 'days')
                .startOf('day')
                .toDate(),
          });
        }
        if (!!to) {
          mQuery.andWhere('logs.created_at <= :to', {
            to:
              to ??
              moment()
                .endOf('day')
                .toDate(),
          });
        }

        break;
      case TypeSearchTicketFilter.NoteUpdatedTime:
        if (!!from)
          mQuery.andWhere('notes.created_at >= :from', {
            from:
              from ??
              moment()
                .subtract(30, 'days')
                .startOf('day')
                .toDate(),
          });
        if (!!to)
          mQuery.andWhere('notes.created_at <= :to', {
            to:
              to ??
              moment()
                .endOf('day')
                .toDate(),
          });

        if (status) {
          mQuery.andWhere('rt.status IN (:...status)', { status });
        }

        break;
    }
    return mQuery;
  }

  async queryOrderByTicket(
    ids: any,
    filters: FilterRaiseTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const { companyId, id } = request?.user;
    const { from, to, productIds } = filters;

    const mQuery = this.orderRepository.createQueryBuilder('order');
    mQuery.select([
      'order.clientId',
      'order.displayId',
      'order.status',
      'order.id',
      'order.warehouseId',
      'order.customerEDD',
      'order.recipientAddress',
      'order.recipientPhone',
      'order.recipientName',
      'order.reasonId',
      'order.reasonNote',
      'order.reasonCode',
      'order.noOfDeliveries',
      'order.externalId',
      'order.seller',
      'products.id',
      'products.productId',
      'products.quantity',
      'products.weight',
      'carriers.id',
      'carriers.carrierId',
      'carriers.waybillNumber',
      'carriers.waybillNote',
      'reason.prefix',
      'reason.reason',
      'reason.description',
    ]);
    mQuery.leftJoin('order.carriers', 'carriers', `carriers.status = 'activated'`);
    mQuery.leftJoin('order.reason', 'reason');
    mQuery.leftJoin('order.products', 'products');
    mQuery.andWhere({
      companyId,
    });
    mQuery.andWhere('order.id IN (:...ids)', { ids });

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds))
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
    }
    // const ptManager = await this.checkRolePtManager(id, Permission.ptManager);
    // console.log(ptManager);

    // if (!ptManager && !filters?.orderId) {
    //   // mQuery.andWhere('rt.status NOT IN (:...statusPtView)', { statusPtView: [RaiseTicketStatusEnum.New, RaiseTicketStatusEnum?.['Re-Open']] });
    //   mQuery.andWhere('rt.assignee = :assigneeId', { assigneeId: id });
    // }
    return mQuery.getMany();
  }

  async checkRolePtManager(id: number, role: number): Promise<boolean> {
    const { data: result } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'check-roles',
      payload: { id, role: `${BigInt(role)}` },
      timeout: 10000,
    });
    // console.log(result);
    if (isEmpty(result)) {
      return false;
    }
    return true;
  }

  async searchRaiseTicket(
    _pagination: PaginationOptions,
    filters: FilterRaiseTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<RaiseTicket[]> {
    const { query } = filters;
    const { companyId } = request?.user;
    if (!query) return [];
    const mQuery = this.raiseTicketRepository.createQueryBuilder('rt');

    mQuery.select(['rt.id', 'rt.displayId', 'rt.status', 'rt.type', 'rt.createdAt', 'rt.assignee']);
    mQuery.andWhere('rt.deleted_at IS NULL');
    mQuery.andWhere({
      companyId,
    });
    mQuery.leftJoin('rt.order', 'order');
    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds))
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
    }
    mQuery.orderBy('rt.createdAt', 'DESC');

    if (!!query) {
      const displayIds = split(query, ',');
      if (displayIds?.length > 0) {
        mQuery.andWhere('rt.displayId IN (:...displayIds)', { displayIds });
      } else return [];
    }
    // console.log(mQuery.getQueryAndParameters());

    return mQuery.getMany();
  }

  async listRaiseTicket(
    pagination: PaginationOptions,
    filters: FilterRaiseTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { orderId } = filters;

    let mQuery = this.raiseTicketRepository.createQueryBuilder('rt');

    if (pagination) {
      mQuery.take(pagination.limit).skip(pagination.skip);
    }

    mQuery.select(
      !!orderId
        ? [
            'rt.id',
            'rt.displayId',
            'rt.status',
            'rt.type',
            'rt.createdAt',
            'rt.updatedAt',
            'rt.assignee',
          ]
        : [
            'rt.id',
            'rt.orderId',
            'rt.displayId',
            'rt.creatorId',
            'rt.status',
            'rt.type',
            'rt.companyId',
            'rt.lastUpdatedBy',
            'rt.createdAt',
            'rt.updatedAt',
            'rt.assignee',
            'rt.isSpotlight',
            'rt.assigneeIds',
            'rt.lastUpdateStatus',
            // 'order.clientId',
            // 'order.displayId',
            // 'order.status',
            // 'order.id',
            // 'order.warehouseId',
            // 'order.customerEDD',
            // 'order.recipientAddress',
            // 'order.recipientPhone',
            // 'order.recipientName',
            // 'order.reasonId',
            // 'order.reasonNote',
            // 'order.reasonCode',
            'order.noOfDeliveries',
            // 'order.externalId',
            // 'order.seller',
            // 'products.id',
            // 'products.productId',
            // 'products.quantity',
            // 'products.weight',
            // 'carriers.id',
            // 'carriers.carrierId',
            // 'carriers.waybillNumber',
            // 'carriers.waybillNote',
            'notes.content',
            'notes.id',
            'notes.type',
            'notes.createdAt',
            'lastResult.content',
            'lastResult.id',
            'lastResult.type',
            'lastResult.createdAt',
            // 'reason.prefix',
            // 'reason.reason',
            // 'reason.description',
            'tags.id',
            'tags.content',
            'tags.companyId',
            'tags.color',
            'tags.type',
          ],
    );

    mQuery = await this.queryRaiseTicket(mQuery, filters, headers, request);
    mQuery.leftJoinAndSelect('tags.ticketTags', 'ticketTags', 'rt.id = ticketTags.id_ticket');
    mQuery.leftJoin('rt.lastResult', 'lastResult');

    if (filters?.ticketAppointmentTypeSearch == TicketAppointmentTypeSearch.Today) {
      mQuery.andWhere('ta.appointment_schedule::date = CURRENT_DATE and ta.status = 1');
    }
    if (filters?.ticketAppointmentTypeSearch == TicketAppointmentTypeSearch.Processed) {
      mQuery.andWhere('ta.appointment_schedule::date > CURRENT_DATE and ta.status = 1');
    }
    if (filters?.ticketAppointmentTypeSearch == TicketAppointmentTypeSearch.Missed) {
      mQuery.andWhere('ta.status = 3');
    }
    if (
      filters?.sortBy &&
      filters?.sortBy != TypeSortTicket.noOfCareResults &&
      filters?.sortBy != TypeSortTicket.lastUpdateResults
    ) {
      if (filters?.sortBy == TypeSortTicket.noOfDeliveries) {
        mQuery.orderBy(`order.${filters?.sortBy}`, `${filters?.sortType}`);
      } else {
        mQuery.orderBy(`rt.${filters?.sortBy}`, `${filters?.sortType}`);
      }
    } else {
      mQuery.orderBy('rt.updatedAt', 'DESC');
    }
    if (!!orderId) {
      mQuery.andWhere('rt.orderId = :orderId', { orderId });
      mQuery.leftJoinAndSelect('rt.notes', 'notess');
      mQuery.addOrderBy('notess.createdAt', 'DESC');
    }
    const data = await mQuery.getMany();

    for (const item of data) {
      item.noOfCareResults = !isEmpty(item?.notes) ? item?.notes?.length : 0;
      item.lastUpdateResults = item?.lastNote ? item?.lastNote?.createdAt : null;
    }
    const orderIds = uniq(data?.map(x => x?.orderId));
    const orders = !isEmpty(orderIds)
      ? await this.queryOrderByTicket(orderIds, filters, headers, request)
      : [];

    const result = filters?.sortBy
      ? orderBy(data, [filters.sortBy], [filters.sortType == SortType.DESC ? 'desc' : 'asc'])
      : data;

    const orderLookUp: Record<string, any> = reduce(
      orders,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    for (const item of result) {
      item.tags = item?.tags?.sort(
        (a, b) => Number(b?.ticketTags[0]?.created_at) - Number(a?.ticketTags[0]?.created_at),
      );
      if (item?.orderId) item['order'] = orderLookUp[item?.orderId];
    }

    return result;
  }

  async countRaiseTicket(
    filters: FilterRaiseTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    let mQuery = this.raiseTicketRepository.createQueryBuilder('rt');
    mQuery
      .distinctOn(['rt.id'])
      .select('rt.id')
      .addSelect('rt.status AS rt_status')
      .addSelect('COUNT ( DISTINCT "rt"."id" )', 'count')
      .addSelect(
        'COUNT ( DISTINCT CASE WHEN ta.appointment_schedule::date = CURRENT_DATE and ta.status = 1 THEN ta."id" ELSE NULL END )',
        'Today',
      )
      .addSelect(
        'COUNT ( DISTINCT CASE WHEN ta.appointment_schedule::date > CURRENT_DATE and ta.status = 1 THEN ta."id" ELSE NULL END )',
        'Processed',
      )
      .addSelect('COUNT ( DISTINCT CASE WHEN ta.status = 3 THEN ta."id" ELSE NULL END )', 'Missed');
    mQuery = await this.queryRaiseTicket(mQuery, filters, headers, request);
    mQuery.groupBy('rt.id');
    const data = await mQuery.getRawMany();
    const result = reduce(
      data,
      (prev: any, item) => {
        if (!prev['Today']) prev['Today'] = 0;
        if (!prev['Processed']) prev['Processed'] = 0;
        if (!prev['Missed']) prev['Missed'] = 0;

        if (!prev[RaiseTicketStatusEnum[item.rt_status]]) {
          prev[RaiseTicketStatusEnum[item.rt_status]] = 0;
        }
        if (filters?.ticketAppointmentTypeSearch) {
          if (item[TicketAppointmentTypeSearch[filters?.ticketAppointmentTypeSearch]] != 0)
            prev[RaiseTicketStatusEnum[item.rt_status]] += Number(item?.count);
        } else {
          prev[RaiseTicketStatusEnum[item.rt_status]] += Number(item?.count);
        }

        prev['Today'] += Number(item?.Today);
        prev['Processed'] += Number(item?.Processed);
        prev['Missed'] += Number(item?.Missed);
        return prev;
      },
      {},
    );

    return result;
  }

  async getTicketAppointmentsData(
    pagination: PaginationOptions,
    filters: FilterRaiseTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    let mQuery = this.raiseTicketRepository.createQueryBuilder('rt');
    mQuery.distinctOn(['rt.id']).select(['rt.id', 'rt.displayId']);
    mQuery = await this.queryRaiseTicket(mQuery, filters, headers, request);
    const data = await mQuery.getRawMany();
    const ticketIds = data.map(item => item.rt_id);
    const displayIdLookup = Object.fromEntries(data.map(item => [item.rt_id, item.rt_display_id]));
    let dataAppointments: any[] = [];

    if (ticketIds.length > 0) {
      const now = moment().startOf('day');
      // Today appointments
      if (filters.ticketAppointmentType == 'Today') {
        const todayBatch = await this.taRepository
          .createQueryBuilder('ta')
          .where('ta.ticketId IN (:...ticketIds)', { ticketIds })
          .andWhere('ta.status = :status', { status: TicketAppointmentStatusEnum.Upcoming })
          .andWhere('DATE(ta.appointmentSchedule) = :today', { today: now.format('YYYY-MM-DD') })
          .take(pagination?.limit)
          .skip(pagination?.skip)
          .addOrderBy('ABS(EXTRACT(EPOCH FROM (NOW() - ta.appointmentSchedule)))', 'ASC')
          .getMany();
        dataAppointments.push(
          ...todayBatch.map(ta => ({
            ...ta,
            displayTicketId: displayIdLookup[ta.ticketId] || null,
            status: TicketAppointmentStatusEnum[ta.status] ?? ta.status,
          })),
        );
      }

      if (filters.ticketAppointmentType == 'Upcoming') {
        // Upcoming appointments
        const upcomingBatch = await this.taRepository
          .createQueryBuilder('ta')
          .where('ta.ticketId IN (:...ticketIds)', { ticketIds })
          .andWhere('ta.status = :status', { status: TicketAppointmentStatusEnum.Upcoming })
          .andWhere('DATE(ta.appointmentSchedule) > :today', { today: now.format('YYYY-MM-DD') })
          .take(pagination?.limit)
          .skip(pagination?.skip)
          .addOrderBy('ABS(EXTRACT(EPOCH FROM (NOW() - ta.appointmentSchedule)))', 'ASC')
          .getMany();
        dataAppointments.push(
          ...upcomingBatch.map(ta => ({
            ...ta,
            displayTicketId: displayIdLookup[ta.ticketId] || null,
            status: TicketAppointmentStatusEnum[ta.status] ?? ta.status,
          })),
        );
      }

      if (filters.ticketAppointmentType == 'Missed') {
        // Missed appointments
        const missedBatch = await this.taRepository
          .createQueryBuilder('ta')
          .where('ta.ticketId IN (:...ticketIds)', { ticketIds })
          .andWhere('ta.status = :status', { status: TicketAppointmentStatusEnum.Missed })
          .take(pagination?.limit)
          .skip(pagination?.skip)
          .addOrderBy('ABS(EXTRACT(EPOCH FROM (NOW() - ta.appointmentSchedule)))', 'ASC')
          .getMany();
        dataAppointments.push(
          ...missedBatch.map(ta => ({
            ...ta,
            displayTicketId: displayIdLookup[ta.ticketId] || null,
            status: TicketAppointmentStatusEnum[ta.status] ?? ta.status,
          })),
        );
      }
    }

    return dataAppointments;
  }

  async updateRaiseTicket(
    data: UpdateRaiseTicketDto,
    tid: number,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<RaiseTicket> {
    const { companyId, id } = request?.user;
    const countryIds = headers['country-ids']?.split(',');

    const { assignee, status, type } = data;
    const raiseTicket = await this.raiseTicketRepository
      .createQueryBuilder('rt')
      .leftJoinAndSelect('rt.tags', 'tags')
      .addSelect('order.status')
      .leftJoin('rt.order', 'order')
      .andWhere({ companyId })
      .andWhere([{ id: isNaN(Number(tid)) ? 0 : tid }, { displayId: ILike(String(tid)) }])
      .getOne();
    const oldTicket = cloneDeep(raiseTicket);
    const ptManager = await this.checkRolePtManager(id, Permission.ptManager);

    if (!ptManager && id != raiseTicket?.assignee) {
      throw new BadRequestException({
        code: 'TIX_0015',
        message: `Không có quyền cập nhật thông tin ticket.`,
      });
    }

    if (!raiseTicket) throw new BadRequestException({ message: `Ticket này không tồn tại!` });

    if (!!status && status != raiseTicket?.status) {
      if (
        [RaiseTicketStatusEnum.Resolved].includes(raiseTicket?.status) &&
        [
          OrderFFMStatus.ReturnedCompleted,
          OrderFFMStatus.DeliveredCompleted,
          OrderFFMStatus.ReturnedDamagedBy3PL,
          OrderFFMStatus.DamagedByWH,
          OrderFFMStatus.LostByWH,
          OrderFFMStatus.LostBy3PL,
          OrderFFMStatus.DamagedCompleted,
          OrderFFMStatus.LostCompleted,
          OrderFFMStatus.Delivered,
        ].includes(raiseTicket?.order?.status)
      )
        throw new BadRequestException({
          message: 'Không thể chuyển trạng thái khi ticket ở trạng thái Đã xử lý!',
        });

      if (!NEXT_STATUS_TICKET[raiseTicket?.status].includes(status))
        throw new BadRequestException({
          code: 'SO_0005',
          message: 'User chọn trạng thái không cho phép để chuyển tiếp',
        });
      raiseTicket.status = status;
    }

    if (!!assignee && assignee != raiseTicket.assignee) {
      if (
        [RaiseTicketStatusEnum.Resolved, RaiseTicketStatusEnum.Closed]?.includes(
          raiseTicket?.status,
        )
      )
        throw new BadRequestException({ code: 'TIX_0003' });
      raiseTicket.assignee = assignee;
      raiseTicket.assigneeIds = concat(raiseTicket?.assigneeIds ?? [], [Number(assignee)]);
      raiseTicket.lastUpdateAssignee = new Date();
      if (
        [RaiseTicketStatusEnum.New, RaiseTicketStatusEnum?.['Re-Open']]?.includes(
          raiseTicket?.status,
        )
      )
        raiseTicket.status = RaiseTicketStatusEnum.Assigned;
      if (
        [RaiseTicketStatusEnum.InProcess, RaiseTicketStatusEnum.FollowUp]?.includes(
          raiseTicket?.status,
        )
      )
        raiseTicket.isSpotlight = true;
    }

    if (!!type && type != raiseTicket?.type) {
      if (raiseTicket.assignee)
        throw new BadRequestException({
          message: `Ticket ${raiseTicket?.displayId} đã được gắn người xử lý`,
        });
      if (!ptManager) throw new ForbiddenException();

      const checkExistType = await this.raiseTicketRepository.findOne({
        orderId: raiseTicket?.orderId,
        type: type,
      });

      if (checkExistType)
        throw new BadRequestException({
          message: `Đã tồn tại Ticket ${raiseTicket?.displayId} cùng loại và cùng đơn hàng ${raiseTicket?.orderId}!`,
        });
      raiseTicket.type = type;
    }
    raiseTicket.lastUpdatedBy = id;

    const tags = [];
    if (!isEmpty(data?.tags)) {
      const tagInFFM = await this.tagService.getTagByContent(
        uniq(data?.tags?.map(tag => tag?.content)),
        companyId,
        countryIds[0],
      );
      for (const item of data?.tags) {
        const tagLookup = tagInFFM.find(x => x?.content == item?.content);
        if (!item.id && tagLookup)
          await this.tagService.updateTag(
            { ...tagLookup, modules: uniq([...tagLookup.modules, TypeOfTagEnum.Ticket]) } as TagDto,
            tagLookup.id,
            request,
            headers,
          );
        const tag = tagLookup
          ? tagLookup
          : {
              ...item,
              creatorId: item?.creatorId ? item?.creatorId : request?.user?.id,
              lastUpdatedBy: item?.lastUpdatedBy ? item?.lastUpdatedBy : request?.user?.id,
              companyId,
              countryId: countryIds[0],
              modules: uniq([...item?.modules, TypeOfTagEnum.Ticket]),
            };
        tags.push(plainToInstance(Tag, tag));
        // tags.push(plainToInstance(Tag, tag));
      }
    }
    raiseTicket.tags = tags;
    // throw new BadRequestException("-------------");
    const result = await this.raiseTicketRepository.save(raiseTicket).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    await this.amqpConnection.publish('ffm-order', 'log-ticket-tags', {
      oldTicket,
      user: request?.user,
      ticket: result,
    });
    return result;
  }

  async deleteRaiseTicket(
    tid: number,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<RaiseTicket> {
    const { companyId, id } = request?.user;

    const raiseTicket = await this.raiseTicketRepository
      .createQueryBuilder('rt')
      .leftJoinAndSelect('rt.notes', 'notes')
      .andWhere({ companyId })
      .andWhere([{ id: isNaN(Number(tid)) ? 0 : tid }, { displayId: ILike(String(tid)) }])
      .getOne();

    if (!raiseTicket) throw new BadRequestException({ message: `Ticket này không tồn tại!` });

    if (!isEmpty(raiseTicket.notes))
      throw new BadRequestException({
        message: `Cannot delete ticket because ticket has been processed.`,
        code: 'TIX_0013',
      });

    raiseTicket.lastUpdatedBy = id;
    raiseTicket.deletedAt = new Date();

    const result = await this.raiseTicketRepository.save(raiseTicket).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });
    return result;
  }

  async changeStatusRaiseTicket(
    data: CloseRaiseTicketDto,
    tid: number,
    request?: Record<string, any>,
  ): Promise<RaiseTicket> {
    const { companyId, id } = request?.user;
    const { status, cancelReason } = data;
    const raiseTicket = await this.raiseTicketRepository
      .createQueryBuilder('rt')
      .addSelect('order.status')
      .leftJoin('rt.order', 'order')
      .andWhere({ companyId })
      .andWhere({ id: isNaN(Number(tid)) ? 0 : tid })
      .getOne();

    if (!raiseTicket) throw new BadRequestException({ message: `Ticket này không tồn tại!` });
    if (
      raiseTicket.status == RaiseTicketStatusEnum.Closed &&
      data.status == RaiseTicketStatusEnum.Closed
    )
      throw new BadRequestException({ code: 'TIX_0009', message: `Ticket's already closed.` });

    const ptManager = await this.checkRolePtManager(id, Permission.ptManager);

    if (!ptManager && id != raiseTicket?.assignee) {
      throw new BadRequestException({
        code: 'TIX_0015',
        message: `Không có quyền cập nhật thông tin ticket.`,
      });
    }

    if (data.status == RaiseTicketStatusEnum.Closed && !data?.cancelReason)
      throw new BadRequestException({
        code: 'TIX_0005',
        message: `Bắt buộc nhập lý do để đóng ticket!`,
      });

    if (!!status && status != raiseTicket?.status) {
      if (
        status != RaiseTicketStatusEnum.Closed &&
        [RaiseTicketStatusEnum.Resolved].includes(raiseTicket?.status) &&
        [
          OrderFFMStatus.ReturnedCompleted,
          OrderFFMStatus.DeliveredCompleted,
          OrderFFMStatus.ReturnedDamagedBy3PL,
          OrderFFMStatus.DamagedByWH,
          OrderFFMStatus.LostByWH,
          OrderFFMStatus.LostBy3PL,
          OrderFFMStatus.DamagedCompleted,
          OrderFFMStatus.LostCompleted,
          OrderFFMStatus.Delivered,
        ].includes(raiseTicket?.order?.status)
      ) {
        throw new BadRequestException({
          message: 'Không thể chuyển trạng thái khi ticket ở trạng thái Đã xử lý!',
        });
      }
      if (!NEXT_STATUS_TICKET[raiseTicket?.status].includes(status))
        throw new BadRequestException({
          code: 'SO_0005',
          message: 'User chọn trạng thái không cho phép để chuyển tiếp',
        });
      raiseTicket.status = status;
    }
    if (cancelReason) raiseTicket.cancelReason = cancelReason;
    raiseTicket.lastUpdatedBy = id;
    raiseTicket.lastUpdateStatus = new Date();

    if (data.status == RaiseTicketStatusEnum['Re-Open']) raiseTicket.assignee = null;
    const result = await this.raiseTicketRepository.save(raiseTicket).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    return result;
  }

  async updateBulkRaiseTicket(
    data: UpdateBulkRaiseTicketDto,
    request?: Record<string, any>,
  ): Promise<Record<string, any>> {
    const { companyId, id } = request?.user;
    const { assigneeIds, ids, type } = data;

    const result: Record<string, any> = {
      success: [],
      error: [],
    };

    const { data: listAssignee } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: assigneeIds },
      timeout: 10000,
    });

    const activeAssigneeIds = [];
    for (const item of listAssignee) {
      if (
        assigneeIds.includes(item?.id) &&
        item?.status == $enum(UserStatus).getKeyOrDefault(UserStatus.active, null)
      ) {
        activeAssigneeIds.push(item?.id);
      }
    }

    const mQuery = this.raiseTicketRepository.createQueryBuilder('rt');
    mQuery.andWhere('rt.id IN (:...ids)', { ids });
    mQuery.andWhere({ companyId });
    mQuery.andWhere('rt.deleted_at IS NULL');

    const raiseTickets = await mQuery.getMany(); // list ticket cần assignee
    const raiseTicketsUpdate = [];
    let i = 0;

    if (!type) throw new BadRequestException('Chưa chọn loại chia Ticket');
    if (type === TypeAssigneeTicket.PriorityAssignment) {
      const orderIds = raiseTickets.map(x => x?.orderId);
      const lastTicketAddAssignee = this.raiseTicketRepository
        .createQueryBuilder()
        .select('order_id')
        .addSelect('max(last_update_assignee)', 'last_update_assignee')
        .where(`order_id IN (:...orderIds)`, { orderIds })
        .andWhere(`assignee_ids[array_length(assignee_ids, 1)] IN (:...activeAssigneeIds)`, {
          activeAssigneeIds,
        })
        .groupBy('order_id');

      const orderOfListTicket = await this.orderRepository
        .createQueryBuilder('o')
        .select('rt.id', 'ticketId')
        .addSelect('o.id', 'orderId')
        .addSelect('rt.assignee', 'assignee')
        .addSelect('rt.assignee_ids', 'assigneeIds')
        .addSelect('last_ticket_add_assignee.last_update_assignee', 'lastUpdateAssignee')
        .where(`o.id IN (:...orderIds)`, { orderIds })
        .andWhere(`rt.assignee_ids[array_length(assignee_ids, 1)] IN (:...activeAssigneeIds)`, {
          activeAssigneeIds,
        })
        .leftJoin('o.tickets', 'rt')
        .innerJoin(
          `(${lastTicketAddAssignee.getQuery()})`,
          'last_ticket_add_assignee',
          'last_ticket_add_assignee.last_update_assignee = rt.last_update_assignee',
          lastTicketAddAssignee.getParameters(),
        )
        .getRawMany();

      const raiseTicketType2 = []; //Ticket được phát sinh từ đơn hàng đã có ticket được raise trước đó chưa assign hoặc đã assign

      for (const item of raiseTickets) {
        const order = orderOfListTicket.find(x => x?.orderId === item?.orderId);
        if (order) {
          item.assignee = Number(order?.assigneeIds[order?.assigneeIds.length - 1]);
          item.assigneeIds = concat(item?.assigneeIds ?? [], [
            Number(order?.assigneeIds[order?.assigneeIds.length - 1]),
          ]);
          item.lastUpdateAssignee = new Date();
          if ([RaiseTicketStatusEnum.New, RaiseTicketStatusEnum['Re-Open']].includes(item?.status))
            item.status = RaiseTicketStatusEnum.Assigned;
          raiseTicketsUpdate.push(item);
        } else {
          raiseTicketType2.push(item);
        }
      }

      for (const item of raiseTicketType2) {
        if (activeAssigneeIds.includes(item?.assignee)) {
          result.error.push({
            displayId: item?.displayId,
            message: `Ticket ${item?.displayId} có cùng người xử lý trong danh sách!`,
          });
          continue;
        }

        if ([RaiseTicketStatusEnum.Resolved, RaiseTicketStatusEnum.Closed].includes(item?.status)) {
          result.error.push({
            code: 'TIX_0003',
            displayId: item?.displayId,
            message: `Ticket ${item?.displayId} không cho phép đổi người xử lý`,
          });
          continue;
        }

        item.lastUpdatedBy = id;
        item.assignee = Number(activeAssigneeIds[i]);
        item.assigneeIds = concat(item?.assigneeIds ?? [], [Number(activeAssigneeIds[i])]);
        item.lastUpdateAssignee = new Date();
        i++;
        if (i == activeAssigneeIds.length) i = 0;

        if (
          [RaiseTicketStatusEnum.InProcess, RaiseTicketStatusEnum.FollowUp].includes(item?.status)
        )
          item.isSpotlight = true;

        if ([RaiseTicketStatusEnum.New, RaiseTicketStatusEnum['Re-Open']].includes(item?.status))
          item.status = RaiseTicketStatusEnum.Assigned;

        raiseTicketsUpdate.push(item);
      }
    } else if (type === TypeAssigneeTicket.EvenDistribution) {
      for (const item of raiseTickets) {
        if (activeAssigneeIds.includes(item?.assignee)) {
          result.error.push({
            displayId: item?.displayId,
            message: `Ticket ${item?.displayId} có cùng người xử lý trong danh sách!`,
          });
          continue;
        }

        if ([RaiseTicketStatusEnum.Resolved, RaiseTicketStatusEnum.Closed].includes(item?.status)) {
          result.error.push({
            code: 'TIX_0003',
            displayId: item?.displayId,
            message: `Ticket ${item?.displayId} không cho phép đổi người xử lý`,
          });
          continue;
        }

        item.lastUpdatedBy = id;
        item.assignee = Number(activeAssigneeIds[i]);
        item.assigneeIds = concat(item?.assigneeIds ?? [], [Number(activeAssigneeIds[i])]);
        i++;
        if (i == activeAssigneeIds.length) i = 0;

        if (
          [RaiseTicketStatusEnum.InProcess, RaiseTicketStatusEnum.FollowUp].includes(item?.status)
        )
          item.isSpotlight = true;

        if ([RaiseTicketStatusEnum.New, RaiseTicketStatusEnum['Re-Open']].includes(item?.status))
          item.status = RaiseTicketStatusEnum.Assigned;

        raiseTicketsUpdate.push(item);
      }
    }
    const res = await this.raiseTicketRepository.save(raiseTicketsUpdate).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    result.success = res ?? [];

    return result;
  }

  async createRaiseTicketNotes(
    data: CreateRaiseTicketNoteDto,
    request?: Record<string, any>,
  ): Promise<RaiseTicketNote> {
    const { companyId, id } = request?.user;

    const mQuery = this.raiseTicketRepository.createQueryBuilder('rt');

    mQuery
      .addSelect('order.status')
      .leftJoin('rt.order', 'order')
      .andWhere('rt.companyId = :companyId', { companyId })
      .andWhere('rt.id = :id', { id: data?.ticketId });
    const raiseTicket = await mQuery.getOne();

    if (!raiseTicket)
      throw new BadRequestException({ code: 'TIX_0004', message: 'Không tồn tại ticket!' });

    if (
      [RaiseTicketStatusEnum.Resolved].includes(raiseTicket?.status) &&
      [
        OrderFFMStatus.ReturnedCompleted,
        OrderFFMStatus.DeliveredCompleted,
        OrderFFMStatus.ReturnedDamagedBy3PL,
        OrderFFMStatus.DamagedByWH,
        OrderFFMStatus.LostByWH,
        OrderFFMStatus.LostBy3PL,
        OrderFFMStatus.DamagedCompleted,
        OrderFFMStatus.LostCompleted,
        OrderFFMStatus.Delivered,
      ].includes(raiseTicket?.order?.status)
    )
      throw new BadRequestException({
        message: 'Không thể cập nhật kết quả xử lý khi ticket ở trạng thái Đã xử lý!',
      });

    const ptManager = await this.checkRolePtManager(id, Permission.ptManager);

    const raiseTicketNote: any = plainToInstance(RaiseTicketNote, {
      ticketId: data?.ticketId,
      type: data?.type,
      content: data?.content,
      creatorId: request?.user?.id,
      companyId: request?.user?.companyId,
    });

    if (!ptManager && id != raiseTicket?.assignee) {
      throw new BadRequestException({
        code: 'TIX_0015',
        message: `Không có quyền cập nhật thông tin ticket.`,
      });
    }

    const result = await this.raiseTicketNoteRepository.save(raiseTicketNote).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    if (!!raiseTicket && result) {
      const status = CHANGE_STATUS_TICKET?.[raiseTicket?.type]?.[data?.type]?.status;

      if (status != null && status != raiseTicket?.status) {
        raiseTicket.status = status;
        raiseTicket.lastUpdatedBy = id;
      }
    }
    raiseTicket.lastResultId = result.id;
    await this.raiseTicketRepository.save({ ...raiseTicket, updatedAt: new Date() }).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    return result;
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'order-update-ticket-status',
    queue: 'ffm-queue-order-update-ticket-status',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async orderUpdateTicket({ id, user, status }) {
    if (!!id) {
      if (status == OrderFFMStatus.Delivered) {
        await this.raiseTicketRepository
          .update(
            {
              orderId: id,
              status: Not(RaiseTicketStatusEnum.Closed),
            },
            {
              status: RaiseTicketStatusEnum.Resolved,
              lastUpdatedBy: SystemIdEnum.system,
            },
          )
          .catch(err => {
            if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
            return err;
          });
      } else if (UPDATE_TICKET_RUSH_STATUS.includes(status)) {
        await this.raiseTicketRepository
          .update(
            {
              orderId: id,
              type: In([RaiseTicketTypeEnum.RushDelivery, RaiseTicketTypeEnum.NotifyDelivery]),
              status: Not(RaiseTicketStatusEnum.Closed),
            },
            {
              status: RaiseTicketStatusEnum.Resolved,
              lastUpdatedBy: SystemIdEnum.system,
            },
          )
          .catch(err => {
            if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
            return err;
          });
      } else if ([OrderFFMStatus.InReturn, OrderFFMStatus.FailedDelivery].includes(status)) {
        await this.raiseTicketRepository
          .update(
            {
              orderId: id,
              type: RaiseTicketTypeEnum.NotifyDelivery,
              status: Not(RaiseTicketStatusEnum.Closed),
            },
            {
              status: RaiseTicketStatusEnum.Resolved,
              lastUpdatedBy: SystemIdEnum.system,
            },
          )
          .catch(err => {
            if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
            return err;
          });
      }
    }
    return new Nack();
  }

  async historyRaiseTicket(rid: number, request?: Record<string, any>): Promise<any[]> {
    const { companyId } = request?.user;
    const raiseTicket = await this.raiseTicketRepository.findOne({
      where: qb => {
        qb.andWhere([
          {
            id: isNaN(Number(rid)) ? 0 : rid,
          },
          {
            displayId: ILike(rid),
          },
        ]);
        qb.andWhere({
          companyId,
        });
      },
    });
    const [logData, newLogData, tagLog] = await Promise.all([
      // this.logRepository.find({
      //   where: qb => {
      //     qb.andWhere([
      //       {
      //         tableName: 'raise_ticket',
      //         recordId: raiseTicket?.id,
      //       },
      //       {
      //         parentTableName: 'raise_ticket',
      //         parentId: raiseTicket?.id,
      //       },
      //     ]);
      //     qb.andWhere([
      //       {
      //         type: Not('Status'),
      //       },
      //       {
      //         type: 'Status',
      //         beforeChanges: Not(IsNull()),
      //       },
      //     ]);
      //   },
      //   order: {
      //     createdAt: 'DESC',
      //     id: 'DESC',
      //   },
      // }),
      this.logRepository
        .createQueryBuilder('log')
        .where(
          new Brackets(qb => {
            qb.where('log.tableName = :tableName and log.recordId = :recordId', {
              tableName: 'raise_ticket',
              recordId: raiseTicket?.id,
            }).orWhere('log.parentTableName = :parentTableName and log.parentId = :parentId', {
              parentTableName: 'raise_ticket',
              parentId: raiseTicket?.id,
            });
          }),
        )
        .andWhere(
          new Brackets(qb => {
            qb.where('log.type != :notType', {
              notType: 'Status',
            }).orWhere('log.type = :type and log.beforeChanges IS NOT NULL', { type: 'Status' });
          }),
        )
        .orderBy('log.createdAt', 'DESC')
        .addOrderBy('log.id', 'DESC')
        .andWhere(`log.created_at <= '2024-10-01'`)
        .getMany(),
      this.ticketLogRepository
        .createQueryBuilder('log')
        .where(
          new Brackets(qb => {
            qb.where('log.tableName = :tableName and log.recordId = :recordId', {
              tableName: 'raise_ticket',
              recordId: raiseTicket?.id,
            }).orWhere('log.parentTableName = :parentTableName and log.parentId = :parentId', {
              parentTableName: 'raise_ticket',
              parentId: raiseTicket?.id,
            });
          }),
        )
        .andWhere(
          new Brackets(qb => {
            qb.where('log.type != :notType', {
              notType: 'Status',
            }).orWhere('log.type = :type and log.beforeChanges IS NOT NULL', { type: 'Status' });
          }),
        )
        .orderBy('log.createdAt', 'DESC')
        .addOrderBy('log.id', 'DESC')
        .andWhere(`log.created_at > '2024-10-01'`)
        .getMany(),
      this.logRepository
        .createQueryBuilder('l')
        .where(`l.table_name = 'raise_ticket_tag'`)
        .andWhere('l.record_id = :id', { id: raiseTicket?.id })
        .andWhere('l.event IS NOT NULL')
        .andWhere('l.type IS NOT NULL')
        .getMany(),
    ]);

    const data = newLogData.concat(logData);
    const ticketLog = data?.map((item: any) => {
      if (item.type === 'Status') {
        return {
          ...item,
          changes: [
            $enum(RaiseTicketStatusEnum).getKeyOrDefault(Number(item?.changes?.[0]), null),
            item?.changes?.[1],
          ],
          beforeChanges: [
            $enum(RaiseTicketStatusEnum).getKeyOrDefault(Number(item?.beforeChanges?.[0]), null),
          ],
          afterChanges: [
            $enum(RaiseTicketStatusEnum).getKeyOrDefault(Number(item?.afterChanges?.[0]), null),
            item?.changes?.[1],
          ],
        };
      } else if (item.type === 'Ticket type') {
        return {
          ...item,
          changes: [$enum(RaiseTicketTypeEnum).getKeyOrDefault(Number(item?.changes?.[0]), null)],
          beforeChanges: [
            $enum(RaiseTicketTypeEnum).getKeyOrDefault(Number(item?.beforeChanges?.[0]), null),
          ],
          afterChanges: [
            $enum(RaiseTicketTypeEnum).getKeyOrDefault(Number(item?.afterChanges?.[0]), null),
          ],
        };
      } else if (item.type === 'Raise Ticket Notes') {
        // console.log(item);
        if (!!item?.changes) {
          item.event = '';
          let type,
            content = '';
          item?.changes.map((e: any, index: number) => {
            if (index % 2 == 0 && ['type', 'content']?.includes(e)) {
              if (e == 'type')
                type = !!item?.changes[index + 1]
                  ? $enum(RaiseTicketNoteEnum).getKeyOrDefault(
                      Number(item?.changes[index + 1]),
                      null,
                    )
                  : '';
              if (e == 'content') content = item?.changes[index + 1] ?? '';
            }
          });
          if (!!type) {
            item.event = `Added Result`;
            item.beforeChanges = [content ?? '-'];
            item.changes = [type];
          }
        }
        return item;
      } else return item;
    });

    // const groupedTagLogs = [];
    // tagLog.forEach(log => {
    //   const key = `${log.updatedAt}_${log.event}`;
    //   if (!groupedTagLogs[key]) {
    //     groupedTagLogs[key] = {
    //       ...log,
    //       changes: [],
    //       beforeChanges: [],
    //     };
    //   }
    //   if (log.changes && log.changes.length > 0) groupedTagLogs[key].changes.push(log.changes[0]);
    //   if (log.beforeChanges && log.beforeChanges.length > 0)
    //     groupedTagLogs[key].beforeChanges.push(log.beforeChanges[0]);
    // });

    // const tagLogResult = Object.values(groupedTagLogs).map(group => {
    //   return group;
    // });

    const result = [...ticketLog, ...tagLog].sort((a, b) => {
      if (a.updatedAt > b.updatedAt) {
        return -1;
      }
      if (a.updatedAt < b.updatedAt) {
        return 1;
      }
      return 0;
    });
    return result;
  }

  async exportExcel(
    filters: FilterRaiseTicket,
    request?: Record<string, any>,
    headers?: Record<string, string>,
  ) {
    const { orderId, ids } = filters;

    let mQuery = this.raiseTicketRepository.createQueryBuilder('rt');
    mQuery = await this.queryRaiseTicket(mQuery, filters, headers, request);
    if (ids) mQuery.andWhere('rt.id IN (:...ids)', { ids });
    if (!!orderId) mQuery.andWhere('rt.orderId = :orderId', { orderId });
    // if (!orderId) mQuery.orderBy('order.displayId', 'DESC');
    mQuery.addOrderBy('rt.updatedAt', 'DESC');
    mQuery.leftJoin('carriers.carrier', 'carrier');
    // mQuery.leftJoin('rt.tags', 'tags');
    mQuery.select([
      'rt.id',
      'rt.orderId',
      'rt.displayId',
      'rt.creatorId',
      'rt.status',
      'rt.type',
      'rt.companyId',
      'rt.lastUpdatedBy',
      'rt.createdAt',
      'rt.updatedAt',
      'rt.assignee',
      'rt.isSpotlight',
      'rt.assigneeIds',
      'rt.lastUpdateStatus',
      'order.clientId',
      'order.displayId',
      'order.status',
      'order.id',
      'order.warehouseId',
      'order.customerEDD',
      'order.recipientAddress',
      'order.recipientPhone',
      'order.recipientName',
      'order.reasonId',
      'order.reasonNote',
      'order.reasonCode',
      'order.seller',
      'carriers.id',
      'carriers.carrierId',
      'carriers.waybillNumber',
      'carriers.waybillNote',
      'carrier.name',
      'notes.content',
      'notes.id',
      'notes.type',
      'notes.createdAt',
      'reason.prefix',
      'reason.reason',
      'reason.description',
      'reason.description',
      'tags.content',
    ]);

    mQuery.addOrderBy('rt.createdAt', 'DESC');

    const result = await mQuery.getMany();
    let userIds = [];
    for (const item of result) {
      userIds.push(item?.creatorId, item?.assignee, item?.order?.clientId);
    }

    userIds = uniq(userIds);
    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: userIds },
      timeout: 10000,
    });
    const users = uData as User[];
    const userLookup: Record<string, User> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    const columns = [
      'No',
      'Assignee',
      'Waybill number',
      'SO code',
      'Ticket Code',
      'Ticket Type',
      'Client Name',
      'Order status',
      'Carrier',
      'Ticket status',
      'Created',
      'Time',
      'Last support',
      'Note',
      'Order Reason',
      'Last support time',
      'No. of Care Results',
      'Creator',
      'Sale Rept',
      'Tag',
      'Last Update Results',
    ];
    const data: unknown[][] = [columns];

    for (let index = 0; index < result.length; index++) {
      const item = result[index];
      const rows = [...Array(1)].map(() => Array(columns.length).fill(null));
      let reason = '';
      if (item?.order?.reasonId) {
        if (item?.order?.reason?.prefix || item?.order?.reason?.reason) {
          reason = `${item?.order?.reason?.prefix} - ${item?.order?.reason?.reason}`;
        }
      } else {
        if (item?.order?.reasonCode || item?.order?.reasonNote) {
          reason = `${item?.order?.reasonCode} - ${item?.order?.reasonNote}`;
        }
      }
      try {
        rows[0][0] = index + 1;
        rows[0][1] = item?.assignee ? userLookup[item?.assignee]?.name : null;
        rows[0][2] = item?.order?.lastCarrier?.waybillNumber;
        rows[0][3] = item?.order?.displayId;
        rows[0][4] = item?.displayId;
        rows[0][5] = RAISE_TICKET_TYPE[item?.type];
        rows[0][6] = item?.order?.clientId ? userLookup[item?.order?.clientId]?.name : null;
        rows[0][7] = ORDER_FFM_STATUS[item?.order?.status];
        rows[0][8] = item?.order?.lastCarrier?.carrier?.name;
        rows[0][9] = RAISE_TICKET_STATUS[item?.status];
        rows[0][10] = moment(item?.createdAt)
          .tz('Asia/Ho_Chi_Minh')
          .format('DD/MM/YYYY');
        rows[0][11] = moment(item?.createdAt)
          .tz('Asia/Ho_Chi_Minh')
          .format('hh:mm A');
        rows[0][12] = RAISE_TICKET_NOTE[item?.lastNote?.type];
        rows[0][13] = item?.lastNote?.content;
        rows[0][14] = reason;
        rows[0][15] = moment(item?.updatedAt)
          .tz('Asia/Ho_Chi_Minh')
          .format('DD/MM/YYYY hh:mm A');
        rows[0][16] = item.notes.length;
        rows[0][17] = item?.creatorId ? userLookup[item?.creatorId]?.name : null;
        rows[0][18] = item?.order?.seller;
        rows[0][19] = item?.tags?.map(x => x?.content).join(', ');
        rows[0][20] = item?.lastNote
          ? moment(item?.lastNote?.createdAt)
              .tz('Asia/Ho_Chi_Minh')
              .format('DD/MM/YYYY')
          : '';

        data.push(...rows);
      } catch (error) {
        console.log(`error at`, index, result);
        console.log(`error reason`, error);
      }
    }

    const sheetOptions = {
      '!cols': new Array(columns?.length).fill({ wch: 20 }),
    };
    const buffer = xlsx.build([{ name: 'Raise Ticket export', data, options: {} }], {
      sheetOptions,
    }); // Returns a buffer
    return buffer;
  }

  async addTagBulkRaiseTicket(
    data: UpdateTagBulkRaiseTicketDto,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<Record<string, any>> {
    const { companyId, id } = request?.user;
    const { ids } = data;
    const countryIds = headers['country-ids']?.split(',');

    const result: Record<string, any> = {
      success: [],
      error: [],
    };

    const mQuery = this.raiseTicketRepository.createQueryBuilder('rt');
    mQuery.andWhere('rt.id IN (:...ids)', { ids });
    mQuery.leftJoinAndSelect('rt.tags', 'tags');
    mQuery.andWhere({ companyId });

    const raiseTickets = await mQuery.getMany();

    const raiseTicketsUpdate = [];
    let ticketTag = [];
    if (data?.tags?.length > 0) {
      ticketTag = await this.tagService.getTagByIds(
        uniq(data?.tags?.map((tag: TicketTagDto) => tag?.id)),
        request?.user?.companyId,
        countryIds[0],
      );
    }
    if (isEmpty(ticketTag)) throw new BadRequestException('Không tồn tại tag đã chọn!');
    const oldRaiseTickets = cloneDeep(raiseTickets);
    if (data?.type == TagAction.addTag) {
      if (data?.isKeepOldTag) {
        for (const item of raiseTickets) {
          item.tags =
            item.tags && item.tags.length > 0 ? uniq([...item.tags, ...ticketTag]) : ticketTag;
          item.lastUpdatedBy = id;
          raiseTicketsUpdate.push(item);
        }
      } else {
        for (const item of raiseTickets) {
          item.tags = ticketTag;
          item.lastUpdatedBy = id;
          raiseTicketsUpdate.push(item);
        }
      }
      const res = await this.raiseTicketRepository.save(raiseTicketsUpdate).catch(err => {
        if (err?.driverError) {
          throw new BadRequestException(err);
        }
        return err;
      });

      for (const item of oldRaiseTickets) {
        await this.amqpConnection.publish('ffm-order', 'log-ticket-tags', {
          oldTicket: item,
          user: request?.user,
        });
      }
      result.success = res ?? [];
    }

    const ticketUpdateIds = [];
    if (data?.type == TagAction.deleteTag) {
      for (const item of raiseTickets) {
        if (!isEmpty(item.tags)) {
          const tag = intersectionBy(item.tags, ticketTag, 'id');

          if (isEmpty(tag)) {
            result.error.push({ displayId: item?.displayId, code: 'SO_0057' });
          } else {
            ticketUpdateIds.push(item?.id);
            await this.amqpConnection.publish('ffm-order', 'log-ticket-tags', {
              oldTicket: item,
              user: request?.user,
            });
            result.success.push({ id: item?.id, displayId: item?.displayId });
          }
        } else {
          result.error.push({ displayId: item?.displayId, code: 'SO_0057' });
        }
      }
      await this.ticketTagRepository
        .delete({
          id_ticket: In(ids),
          id_tag: In(uniq(data?.tags?.map((tag: TicketTagDto) => tag?.id))),
        })
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });
    }

    // for (const item of oldRaiseTickets) {
    //   if (ticketUpdateIds.includes(item?.id)) {
    //     this.amqpConnection.publish('ffm-order', 'log-ticket-tags', {
    //       oldTicket: item,
    //       user: request?.user,
    //     });
    //   }
    // }

    return result;
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'log-ticket-tags',
    queue: 'ffm-queue-log-ticket-tags',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async logTicketTag(payload) {
    console.log('log-ticket-tags', payload);
    if (!payload?.oldTicket || !payload?.user) {
      console.log('Not found ticket | user');
      return new Nack();
    }
    const { oldTicket, user } = payload;
    let { ticket } = payload;

    if (isUndefined(ticket)) {
      ticket = await this.raiseTicketRepository
        .createQueryBuilder('rt')
        .where('rt.id = :id', { id: oldTicket?.id })
        .andWhere('rt.company_id = :companyId', { companyId: user?.companyId })
        .andWhere('rt.deleted_at IS NULL')
        .leftJoinAndSelect('rt.tags', 'tags')
        .getOne();
    }

    if (oldTicket?.tags != ticket?.tags) {
      const beforeChanges = oldTicket.tags.map(x => x?.content) ?? [];
      const afterChanges = ticket?.tags.map(x => x?.content) ?? [];
      const changes =
        beforeChanges.length > afterChanges.length
          ? beforeChanges.filter(item => !afterChanges.includes(item))
          : afterChanges.filter(item => !beforeChanges.includes(item));
      if (changes.length > 0) {
        const log = {
          action: beforeChanges.length > afterChanges.length ? 'DELETE' : 'INSERT',
          event: beforeChanges.length > afterChanges.length ? 'Remove' : 'Add',
          type: 'Tag',
          changes,
          afterChanges,
          beforeChanges,
          tableName: 'raise_ticket_tag',
          recordId: ticket?.id.toString(),
          creatorId: user?.id,
        };
        return await this.logRepository.save(log).catch(err => {
          if (err?.driverError) console.log(err?.driverError?.detail);
          return err;
        });
      }
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'update-ticket-tags',
    queue: 'ffm-queue-ticket-tags',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async syncOrderTags(payload) {
    console.log('update-ticket-tags', payload);
    if (!payload?.id || !payload?.user || !payload?.tagIds) {
      console.log('integration', 'Not found id | user | tag id');
      return new Nack();
    }
    const { id, user, tagIds } = payload;

    let tags = [];

    const ticket = await this.raiseTicketRepository
      .createQueryBuilder('rt')
      .withDeleted()
      .where('rt.id = :id', { id })
      .andWhere('rt.company_id = :companyId', { companyId: user?.companyId })
      .andWhere('rt.deleted_at IS NULL')
      .leftJoinAndSelect('rt.tags', 'tags')
      .getOne();

    const oldTicket = cloneDeep(ticket);

    if (ticket?.tags) {
      tags = oldTicket?.tags.filter(x => !tagIds.includes(x?.id)) ?? [];
      if (!!tags) {
        await this.amqpConnection.publish('ffm-order', 'log-ticket-tags', {
          oldTicket,
          user,
        });
      }
      ticket.tags = tags;

      await this.ticketTagRepository.delete({ id_ticket: id, id_tag: In(tagIds) }).catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
    }
    return ticket;
  }

  async deleteBulkRaiseTicket(
    data: DeleteBulkRaiseTicketDto,
    request?: Record<string, any>,
  ): Promise<Record<string, any>> {
    const { companyId, id } = request?.user;
    const { ids } = data;
    const result: Record<string, any> = {
      success: [],
      error: [],
    };

    const mQuery = this.raiseTicketRepository.createQueryBuilder('rt');
    mQuery.andWhere('rt.id IN (:...ids)', { ids });
    mQuery.leftJoinAndSelect('rt.notes', 'notes');
    mQuery.andWhere({ companyId });

    const raiseTickets = await mQuery.getMany(); // list ticket cần assignee
    const raiseTicketsDelete = [];

    for (const item of raiseTickets) {
      if (!isEmpty(item.notes)) {
        result.error.push({
          displayId: item?.displayId,
          message: `Cannot delete ticket because ticket has been processed.`,
          code: 'TIX_0013',
        });
      } else {
        item.lastUpdatedBy = id;
        item.deletedAt = new Date();

        raiseTicketsDelete.push(item);
      }
    }

    const res = await this.raiseTicketRepository.save(raiseTicketsDelete).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    result.success = res ?? [];

    return result;
  }

  async createRaiseTicketAppointment(
    data: CreateRaiseTicketAppointmentDto,
    request?: Record<string, any>,
    headers?: Record<string, any>,
  ): Promise<TicketAppointment> {
    const { companyId, id } = request?.user;
    const countryId = headers['country-ids'][0];
    const { appointmentSchedule, content, ticketId } = data;

    const raiseTicketAppointment: any = plainToInstance(TicketAppointment, {
      appointmentSchedule,
      content,
      companyId,
      countryId,
      lastUpdatedBy: id,
      creatorId: id,
      ticketId,
      status: TicketAppointmentStatusEnum.Upcoming,
    });
    const raiseTicket = await this.raiseTicketRepository
      .createQueryBuilder('rt')
      .andWhere({ companyId })
      .andWhere({ id: data?.ticketId })
      .getOne();

    const ptManager = await this.checkRolePtManager(id, Permission.ptManager);

    if (!ptManager && id != raiseTicket?.assignee) {
      throw new BadRequestException({
        code: 'TIX_0015',
        message: `Không có quyền cập nhật thông tin ticket.`,
      });
    }
    const result = await this.taRepository.save(raiseTicketAppointment).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });
    return result;
  }

  async updateRaiseTicketAppointment(
    data: UpdateRaiseTicketAppointmentDto,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<TicketAppointment> {
    const { companyId, id: uid } = request?.user;
    const countryIds = headers['country-ids']?.split(',');

    const ptManager = await this.checkRolePtManager(uid, Permission.ptManager);

    const { id, isDelete, status } = data;
    const result = await this.taRepository
      .createQueryBuilder('ta')
      .leftJoinAndSelect('ta.ticket', 'ticket')
      .where('ta.id = :id', { id })
      .andWhere({ companyId })
      .getOne();

    if (!ptManager && uid != result?.ticket?.assignee) {
      throw new BadRequestException({
        code: 'TIX_0015',
        message: `Không có quyền cập nhật thông tin ticket.`,
      });
    }

    if (result.status == TicketAppointmentStatusEnum.Upcoming) {
      if (isDelete) {
        await this.taRepository.delete({ id });
      } else {
        if (status != result?.status) {
          result.status = status;
          result.lastUpdatedBy = uid;
        }
        await this.taRepository.save(result).catch(err => {
          console.log(err?.driverError);
          return err;
        });
      }
    } else {
      throw new BadRequestException('Không thể thao tác lịch hẹn ở trạng thái này!');
    }

    return result;
  }

  async scheduleAutoProcessTicketAppointment() {
    const jobName = 'auto-process-ticket-appointment';
    try {
      const repeatable = await this.raiseTicketQueue.getRepeatableJobs();
      for (const job1 of repeatable) {
        if (job1.id !== jobName) {
          continue;
        }
        await this.raiseTicketQueue.removeRepeatableByKey(job1.key);
      }
      const queue = await this.raiseTicketQueue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            cron: '0 * * * *',
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: false,
        },
      );
      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm'),
      );
    } catch (e) {
      console.log('error', e);
    }
  }

  async autoProcessTicketAppointment() {
    const ticketAppointments = await this.taRepository
      .createQueryBuilder()
      .where('appointmentSchedule < :date', { date: new Date() })
      .andWhere('status = :status', { status: TicketAppointmentStatusEnum.Upcoming })
      .update()
      .set({
        status: TicketAppointmentStatusEnum.Missed,
        lastUpdatedBy: -99,
      })
      .returning(['id'])
      .execute();

    return ticketAppointments.raw.map(i => i.id);
  }

  async removeNeedRetryTicketWarningByTicket(orderId) {
    const checkNeedRetryTicket = await this.owRepository.find({
      orderId,
      warningType: WarningType.NeedRetryTicket,
    });
    if (!isEmpty(checkNeedRetryTicket)) {
      const log = plainToInstance(Logs, {
        action: 'UPDATE',
        event: 'Remove',
        type: 'Warning',
        changes: ['NeedRetryTicket'],
        afterChanges: ['NeedRetryTicket'],
        beforeChanges: ['NeedRetryTicket'],
        tableName: 'orders',
        recordId: orderId,
        creatorId: SystemIdEnum.system,
      });
      await Promise.all([
        this.owRepository.delete({
          id: In(checkNeedRetryTicket.map(x => x.id)),
          warningType: WarningType.NeedRetryTicket,
        }),
        this.logRepository.insert(log).catch(err => {
          if (err?.driverError) console.log(err?.driverError?.detail);
          return err;
        }),
      ]);
    }
  }
}
