import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateWard3plDto } from 'apps/ffm-order-api/src/dtos/address-3pl.dto';
import { District } from 'apps/ffm-order-api/src/entities/address_district_3pl.entity';
// import { AddressMapping3PL } from 'apps/ffm-order-api/src/entities/address_mapping_3pl.entity';
import { PostCode } from 'apps/ffm-order-api/src/entities/address_post_code_3pl.entity';
import { Province } from 'apps/ffm-order-api/src/entities/address_province_3pl.entity';
import { Ward } from 'apps/ffm-order-api/src/entities/address_ward_3pl.entity';
import { FilterAddress3PL } from 'apps/ffm-order-api/src/filters/address-3pl.filter';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { CarrierCode, CountryID } from 'core/enums/carrier-code.enum';
import ExcelUtils from 'core/utils/ExcelUtils';
import { In, Repository } from 'typeorm';
import { $enum } from 'ts-enum-util';
import { Carrier } from 'apps/ffm-order-api/src/entities/carrier.entity';
import ExcelUtilsV2 from 'core/utils/ExcelUtilsV2';

@Injectable()
export class Address3PLService {
  constructor(
    @InjectRepository(Province, orderConnection)
    private provinceRepository: Repository<Province>,
    @InjectRepository(District, orderConnection)
    private districtRepository: Repository<District>,
    @InjectRepository(Ward, orderConnection)
    private wardRepository: Repository<Ward>,
    @InjectRepository(PostCode, orderConnection)
    private postCodeRepository: Repository<PostCode>,
    // @InjectRepository(AddressMapping3PL, orderConnection)
    // private addressMapping3PLRepository: Repository<AddressMapping3PL>,
    @InjectRepository(Carrier, orderConnection)
    private carRepository: Repository<Carrier>,
  ) {}

  async getAddress3PLMapping(
    filters: FilterAddress3PL,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request.user;
    const countryIds = headers['country-ids']?.split(',');
    const { provinceId, districtId, wardId, postCode, carrierId } = filters;
    let result = {};
    if(carrierId){
      const [
        address3plProvince,
        address3plDistrict,
        address3plWard,
        address3plPostCode,
      ] = await Promise.all([
        this.provinceRepository.findOne({
          where: qb => {
            qb.andWhere({
              countryCode: In(countryIds),
              companyId,
              carrierId,
              ffmProvinceId: provinceId,
            });
          },
        }),
        this.districtRepository.findOne({
          where: qb => {
            qb.andWhere({
              countryCode: In(countryIds),
              companyId,
              carrierId,
              ffmDistrictId: districtId,
            });
          },
        }),
        this.wardRepository.findOne({
          where: qb => {
            qb.andWhere({
              countryCode: In(countryIds),
              companyId,
              carrierId,
              ffmWardId: wardId,
            });
          },
        }),
        this.postCodeRepository.findOne({
          where: qb => {
            qb.andWhere({
              countryCode: In(countryIds),
              companyId,
              carrierId,
              ffmPostCodeId: postCode,
            });
          },
        }),
      ]);
      result = { address3plProvince, address3plDistrict, address3plWard, address3plPostCode };
    }
    return result;
  }

  async getProvinces(
    filters: FilterAddress3PL,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<[Province[], number]> {
    const { companyId } = request.user;
    return await this.provinceRepository
      .createQueryBuilder('provinces')
      .where('provinces.company_id = :companyId', { companyId })
      .andWhere('provinces.carrier_id = :carrierId', { carrierId: filters?.carrierId })
      .andWhere('provinces.country_code = :countryId', { countryId: headers?.['country-ids'] })
      .getManyAndCount();
  }

  async getDistricts(
    filters: FilterAddress3PL,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<[District[], number]> {
    const { companyId } = request.user;
    return await this.districtRepository
      .createQueryBuilder('districts')
      .where('districts.company_id = :companyId', { companyId })
      .andWhere('districts.carrier_id = :carrierId', { carrierId: filters?.carrierId })
      .andWhere('districts.province_id = :provinceId', { provinceId: filters?.provinceId })
      .andWhere('districts.country_code = :countryId', { countryId: headers?.['country-ids'] })
      .getManyAndCount();
  }

  async getWards(
    filters: FilterAddress3PL,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<[Ward[], number]> {
    const { companyId } = request.user;
    return await this.wardRepository
      .createQueryBuilder('wards')
      .where('wards.company_id = :companyId', { companyId })
      .andWhere('wards.carrier_id = :carrierId', { carrierId: filters?.carrierId })
      .andWhere('wards.district_id = :districtId', { districtId: filters?.districtId })
      .andWhere('wards.country_code = :countryId', { countryId: headers?.['country-ids'] })
      .getManyAndCount();
  }

  async getPostCodes(
    filters: FilterAddress3PL,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<[PostCode[], number]> {
    const { companyId } = request.user;
    return await this.postCodeRepository
      .createQueryBuilder('pc')
      .where('pc.company_id = :companyId', { companyId })
      .andWhere('pc.carrier_id = :carrierId', { carrierId: filters?.carrierId })
      .andWhere('pc.district_id = :districtId', { districtId: filters?.districtId })
      .andWhere('pc.country_code = :countryId', { countryId: headers?.['country-ids'] })
      .getManyAndCount();
  }

  // async getOneAddress(request: Record<string, any>, id: number): Promise<AddressMapping3PL> {
  //   const { companyId } = request.user;
  //   return await this.addressMapping3PLRepository
  //     .createQueryBuilder('add')
  //     .where('add.company_id = :companyId', { companyId })
  //     .andWhere('add.id = :id', { id })
  //     .getOneOrFail();
  // }

  async insertAddress(buffer: Buffer, request: Record<string, any>): Promise<Record<string, any>> {
    const companyId = request?.user?.companyId;

    const data = await ExcelUtilsV2.read(buffer, 0, 'Provinsi');
    const result = { success: {}, error: 0, detailError: [] };

    const carriers = await this.carRepository.createQueryBuilder().getMany();
    const districts = [];
    const districtsCheckList = [];
    const wards = [];
    const wardsCheckList = [];
    const postCodes = [];
    const postCodesCheckList = [];
    const provinces = [];
    const provincesCheckList = [];

    function convertVietnameseToEnglish(name) {
      const vietnameseChars = 'àáảãạâầấẩẫậăắằẳẵặèéẻẽẹêềếểễệđìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵ';
      const englishChars = 'aaaaaaaaaaaaaaaaaeeeeeeeeeeediiiiiooooooooooooooooouuuuuuuuuuuyyyyy';

      return name
        .split('')
        .map(char => {
          const index = vietnameseChars.indexOf(char);
          return index !== -1 ? englishChars[index] : char;
        })
        .join('');
    }
    for (const item of data) {
      if (item?.Provinsi) {
        if (
          provincesCheckList.includes({
            name: item?.Provinsi,
            country: item?.Country,
            carrier: item?.Carrier,
          })
        )
          continue;
        const province = plainToInstance(Province, {
          name: item?.Provinsi,
          nameEn: convertVietnameseToEnglish(item?.Provinsi).toLowerCase(),
          countryCode: CountryID[item?.Country],
          companyId,
          carrierId: carriers.find(
            x => x.code == $enum(CarrierCode).getKeyOrDefault(item?.Carrier, null),
          )?.id,
        });
        provinces.push(province);
        provincesCheckList.push({
          name: item?.Provinsi,
          country: item?.Country,
          carrier: item?.Carrier,
        });
      }
    }

    const resultProvinces = await this.provinceRepository.save(provinces).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    for (const item of data) {
      if (item?.Kota) {
        if (
          districtsCheckList.includes({
            name: item?.Kota,
            country: item?.Country,
            carrier: item?.Carrier,
          })
        )
          continue;
        const district = plainToInstance(District, {
          name: item?.Kota,
          nameEn: convertVietnameseToEnglish(item?.Kota).toLowerCase(),
          provinceId: resultProvinces.find(x => x.name == item?.Provinsi)?.id,
          countryCode: CountryID[item?.Country],
          companyId,
          carrierId: carriers.find(
            x => x.code == $enum(CarrierCode).getKeyOrDefault(item?.Carrier, null),
          )?.id,
        });
        districts.push(district);
        districtsCheckList.push({
          name: item?.Kota,
          country: item?.Country,
          carrier: item?.Carrier,
        });
      }
      // break;
    }

    const resultDistricts = await this.districtRepository.save(districts).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    for (const item of data) {
      if (item?.Kecamatan) {
        if (
          wardsCheckList.includes({
            name: item?.Kecamatan,
            country: item?.Country,
            carrier: item?.Carrier,
          })
        )
          continue;
        const ward = plainToInstance(Ward, {
          name: item?.Kecamatan,
          nameEn: convertVietnameseToEnglish(item?.Kecamatan).toLowerCase(),
          districtId: resultDistricts.find(x => x.name == item?.Kota)?.id,
          provinceId: resultProvinces.find(x => x.name == item?.Provinsi)?.id,
          countryCode: CountryID[item?.Country],
          companyId,
          carrierId: carriers.find(
            x => x.code == $enum(CarrierCode).getKeyOrDefault(item?.Carrier, null),
          )?.id,
        });
        wards.push(ward);
        wardsCheckList.push({
          name: item?.Kecamatan,
          country: item?.Country,
          carrier: item?.Carrier,
        });
      }
      // break;
    }

    const resultWards = await this.wardRepository.save(wards).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });

    for (const item of data) {
      if (item?.PostCode) {
        if (
          postCodesCheckList.includes({
            name: item?.PostCode,
            country: item?.Country,
            carrier: item?.Carrier,
          })
        )
          continue;
        const postCode = plainToInstance(PostCode, {
          name: item?.PostCode,
          nameEn: convertVietnameseToEnglish(item?.PostCode).toLowerCase(),
          districtId: resultDistricts.find(x => x.name == item?.Kota)?.id,
          countryCode: CountryID[item?.Country],
          companyId,
          carrierId: carriers.find(
            x => x.code == $enum(CarrierCode).getKeyOrDefault(item?.Carrier, null),
          )?.id,
        });
        postCodes.push(postCode);
        postCodesCheckList.push({
          name: item?.PostCode,
          country: item?.Country,
          carrier: item?.Carrier,
        });
      }
      // break;
    }

    const resultPostcodes = await this.postCodeRepository.save(postCodes).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });
    result.success = {
      resultProvinces: resultProvinces.length,
      resultDistricts: resultDistricts.length,
      resultWards: resultWards.length,
      resultPostcodes: resultPostcodes.length,
    };

    return result;
  }
}
