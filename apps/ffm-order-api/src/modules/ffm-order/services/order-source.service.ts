/* eslint-disable @typescript-eslint/no-floating-promises */
import {
  AmqpConnection,
  Nack,
  RabbitRPC,
  defaultNackErrorHandler,
} from '@golevelup/nestjs-rabbitmq';
import { InjectQueue } from '@nestjs/bull';
import { BadRequestException, Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateOrderSourceDto, UpdateOrderSourceDto } from 'apps/ffm-order-api/src/dtos/order-source/order-source.dto';
import { MarketplaceIntegration } from 'apps/ffm-order-api/src/entities/marketplace-integration.entity';
import {
  FilterOrderSource,
  FilterSyncOrderFailed,
  OrderSourceSortBy,
  SortOrder,
} from 'apps/ffm-order-api/src/filters/order-source.filter';
import axios from 'axios';
import { Queue } from 'bull';
import { plainToClass, plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { find, isEmpty, reduce } from 'lodash';
import { Brackets, In, Repository } from 'typeorm';
import * as moment from 'moment-timezone';
import { log } from 'console';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { OrderService } from './order.service';
import { OrderSyncFailedMPI } from 'apps/ffm-order-api/src/entities/order-sync-failed-mki.entity';
import {
  StatusMIEnum,
  TypeMIEnum,
  TypeMIFailEnum,
} from 'apps/ffm-order-api/src/enums/marketplace-integration.enum';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { ExternalOrderType } from 'apps/ffm-order-api/src/enums/order-status.enum';
import { OrderProduct } from 'apps/ffm-order-api/src/entities/order-product.entity';
import { ProductVariation } from 'apps/ffm-order-api/src/read-entities/ffm-catalog/product-variation.entity';
import { ProductDto } from 'apps/ffm-order-api/src/dtos/create-order.dto';
import { Customer } from 'apps/ffm-order-api/src/entities/customer.entity';
import { CountryID } from 'core/enums/carrier-code.enum';
import { objToEnum } from 'core/utils';
import { SyncOrderSourceHistory } from 'apps/ffm-order-api/src/entities/sync-order-source-history.entity';
import xlsx from 'node-xlsx';

@Injectable()
export class OrderSourceService {
  constructor(
    @InjectRepository(MarketplaceIntegration, orderConnection)
    private mkiRepo: Repository<MarketplaceIntegration>,
    @InjectRepository(OrderSyncFailedMPI, orderConnection)
    private osfRepository: Repository<OrderSyncFailedMPI>,
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    @InjectRepository(SyncOrderSourceHistory, orderConnection)
    private soshRepository: Repository<SyncOrderSourceHistory>,
    @InjectRepository(Customer, orderConnection)
    private cusRepository: Repository<Customer>,
    private amqpConnection: AmqpConnection,
    private orderService: OrderService,
    @InjectQueue('order-source')
    private orderSourceQueue: Queue,
  ) {}

  // onModuleInit() {
  //   this.scheduleAutoProcessSyncOrder();
  // }

  async getOrderSource(
    pagination: PaginationOptions,
    filters: FilterOrderSource,
    headers: Record<string, any>,
    request: Record<string, any>,
  ): Promise<Record<string, any>> {
    const { companyId } = request.user;
    const { status, clientIds, countryIds, query, type, platform, sortBy, sortType } = filters;
    // throw new BadRequestException('xxxxxxxxxxxx')
    const mQuery = this.mkiRepo
      .createQueryBuilder('mki')
      .andWhere('mki.companyId =:companyId', { companyId });
    if (!isEmpty(countryIds)) mQuery.andWhere('mki.countryId IN (:...countryIds)', { countryIds });
    if (pagination) {
      mQuery.take(pagination.limit).skip(pagination.skip);
    }
    if (query && query.length > 0) {
      const queryTerms = query.filter(term => term && term.trim().length > 0);

      if (queryTerms.length === 1) {
        // Tối ưu cho single term: sử dụng ILIKE với OR
        const term = queryTerms[0];
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('mki.shopId ILIKE :term', { term: `%${term}%` })
              .orWhere('mki.shopName ILIKE :term', { term: `%${term}%` })
              .orWhere('mki.shopUrl ILIKE :term', { term: `%${term}%` });
          }),
        );
      } else {
        // Tối ưu cho multiple terms: sử dụng concatenated search
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where(
              `CONCAT(COALESCE(mki.shopId, ''), ' ', COALESCE(mki.shopName, ''), ' ', COALESCE(mki.shopUrl, '')) ILIKE ANY(:queryPatterns)`,
              { queryPatterns: queryTerms.map(term => `%${term}%`) }
            );
          }),
        );
      }
    }
    if (platform) {
      mQuery.andWhere('mki.platform IN (:...platform)', { platform });
    }
    if (status) {
      mQuery.andWhere('mki.status IN (:...status)', { status });
    }
    if (clientIds) {
      mQuery.andWhere('mki.clientId IN (:...clientIds)', { clientIds });
    }

    if (!isEmpty(type)) {
      mQuery.andWhere('mki.type IN (:...type)', { type });
    }

    // Dynamic sorting based on sortBy and sortType
    if (sortBy && sortType) {
      let orderByField: string;
      switch (sortBy) {
        case OrderSourceSortBy.shopName:
          orderByField = 'mki.shopName';
          break;
        case OrderSourceSortBy.clientId:
          orderByField = 'mki.clientId';
          break;
        case OrderSourceSortBy.updatedAt:
          orderByField = 'mki.updatedAt';
          break;
        case OrderSourceSortBy.createdAt:
          orderByField = 'mki.createdAt';
          break;
        default:
          orderByField = 'mki.updatedAt';
      }
      mQuery.orderBy(orderByField, sortType);
    } else {
      // Default sorting
      mQuery.orderBy('mki.updatedAt', 'DESC');
    }
    
    // console.log("🚬 ~ OrderSourceService ~ mQuery.getQueryAndParameters():", mQuery.getQueryAndParameters())

    return mQuery.getManyAndCount();
  }

  async getCountOrderSource(
    headers: Record<string, any>,
    request: Record<string, any>,
  ): Promise<Record<string, any>> {
    const { companyId } = request.user;
    const mQuery = this.mkiRepo
      .createQueryBuilder('mki')
      .select('mki.type', 'type')
      .addSelect('count(*)', 'total')
      .andWhere('mki.company_id =:companyId', { companyId })
      .groupBy('mki.type');

    return mQuery.getRawMany();
  }

  async getOrderSourceDetail(
    id: number,
    headers: Record<string, any>,
    request: Record<string, any>,
  ): Promise<MarketplaceIntegration> {
    const { companyId } = request.user;
    const mQuery = this.mkiRepo
      .createQueryBuilder('mki')
      .andWhere('mki.id =:id', { id })
      .andWhere('mki.company_id =:companyId', { companyId });

    return mQuery.getOne();
  }

  async getOrderSourceSyncFailed(
    filters: FilterSyncOrderFailed,
    headers: Record<string, any>,
    request: Record<string, any>,
  ): Promise<[OrderSyncFailedMPI[], number]> {
    const { companyId } = request.user;
    const countryIds = headers['country-ids']?.split(',');
    const mQuery = this.osfRepository
      .createQueryBuilder('osf')
      .andWhere('osf.company_id =:companyId', { companyId })
      .andWhere('osf.country_id IN (:...countryIds)', { countryIds })
      .leftJoinAndMapOne(
        'osf.order',
        Order,
        'o',
        `osf.external_id::varchar = o.external_id AND o.country_id = osf.country_id`,
      );

    if (!isEmpty(filters?.type)) {
      mQuery.andWhere('osf.type IN (:...type)', { type: filters?.type });
    }
    return mQuery.getManyAndCount();
  }

  async getOrderSourceDetailHistory(
    id: number,
    headers: Record<string, any>,
    request: Record<string, any>,
  ): Promise<SyncOrderSourceHistory[]> {
    const mQuery = this.soshRepository
      .createQueryBuilder('sosh')
      .andWhere('sosh.record_id =:id', { id });

    return mQuery.getMany();
  }

  formatDate(date: Date) {
    // Lấy thông tin về năm, tháng, ngày, giờ, phút và giây
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2); // Tháng được lấy từ 0-11, nên cần +1 và đảm bảo có hai chữ số
    const day = ('0' + date.getDate()).slice(-2); // Đảm bảo có hai chữ số cho ngày
    const hours = ('0' + date.getHours()).slice(-2); // Đảm bảo có hai chữ số cho giờ
    const minutes = ('0' + date.getMinutes()).slice(-2); // Đảm bảo có hai chữ số cho phút
    const seconds = ('0' + date.getSeconds()).slice(-2); // Đảm bảo có hai chữ số cho giây

    // Độ lệch múi giờ
    const timeZoneOffset = 7; // Ở đây là -08:00

    // Tạo chuỗi định dạng
    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${
      timeZoneOffset < 0 ? '-' : '+'
    }${Math.abs(timeZoneOffset)
      .toString()
      .padStart(2, '0')}:00`;

    return formattedDate;
  }

  async updateOrderSource(
    body: UpdateOrderSourceDto,
    headers: Record<string, any>,
    req: Record<string, any>,
    id: number,
  ): Promise<MarketplaceIntegration> {
    const { companyId } = req?.user;
    const mki = await this.mkiRepo.findOne({
      where: { id, companyId }
    });
    if (!mki) throw new BadRequestException(`Don't find this order resource!`);

    const result = await this.mkiRepo.save({
      ...mki,
      status: body.status ?? mki?.status,
      clientId: body.clientId ?? mki?.clientId,
      platform: mki.platform,
      lastUpdatedBy: req?.user?.id
    });
    return result;
  }

  async createOrderSource(
    body: CreateOrderSourceDto,
    headers: Record<string, any>,
    req: Record<string, any>,
  ): Promise<MarketplaceIntegration> {
    const { id, companyId } = req?.user;
    if(body.type == TypeMIEnum.onepage){
      const response = await axios
        .request({
          method: 'GET',
          url: body.shopUrl,
          params: {
            processed_at_min: this.formatDate(new Date()),
            processed_at_max: this.formatDate(new Date()),
            // limit: 100,
            // offset: 0,
          },
          headers: { Authorization: `Bearer ${body?.apiKey}` },
          timeout: 6000,
        })
        .catch(err => {
          throw new BadRequestException({
            code: 'OS_0001',
            message: 'Cannot connect. Please check URL or Access Token and try again.',
          });
        });

      if (isEmpty(response)) {
        throw new BadRequestException({
          code: 'OS_0001',
          message: 'Cannot connect. Please check URL or Access Token and try again.',
        });
      }
    }
    
    const mki = plainToInstance(MarketplaceIntegration, {
      ...body,
      shopUrl: body?.shopUrl ?? body?.shopName,
      apiKey: body?.apiKey ?? body?.shopId,
      companyId,
      creatorId: id,
      lastUpdatedBy: id,
      connectedAt: new Date(),
    });
    console.log("🚀 ~ OrderSourceService ~ mki:", mki)
    return this.mkiRepo.save(mki);
  }

  async scheduleAutoProcessSyncOrder() {
    const jobName = 'auto-process-sync-order';
    try {
      const repeatable = await this.orderSourceQueue.getRepeatableJobs();
      for (const job1 of repeatable) {
        if (job1.id !== jobName) {
          continue;
        }
        await this.orderSourceQueue.removeRepeatableByKey(job1.key);
      }
      const queue = await this.orderSourceQueue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            cron: '0 */6 * * *',
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: false,
        },
      );
      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm'),
      );
    } catch (e) {
      console.log('error', e);
    }
  }
  async autoProcessSyncOrder() {
    log('sonsonson run job này!!!!!');
    const mkis = await this.mkiRepo
      .createQueryBuilder('mki')
      .andWhere('mki.isSyncOrder = true')
      .andWhere('mki.status = :status', { status: StatusMIEnum.connected })
      .andWhere('mki.type = :type', { type: TypeMIEnum.onepage })
      .getMany();
    if (!isEmpty(mkis)) {
      const mkiIds = [];
      for (const mki of mkis) {
        await this.amqpConnection.publish('ffm-order', 'auto-sync-order-from-one-page', {
          ...mki,
          limit: 100,
          offset: 0,
        });
        mkiIds.push(mki.id);
      }
      await this.mkiRepo.update({ id: In(mkiIds) }, { lastSync: new Date() });
    }
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'auto-sync-order-from-one-page',
    queue: 'ffm-queue-auto-sync-order-from-one-page',
    errorHandler: defaultNackErrorHandler,
  })
  async syncOrderFromOnePage(payload) {
    console.log('payload syncOrderFromOnePage', payload);

    const processedAtMin =
      payload?.lastSync && payload?.lastSync > payload?.connectedAt
        ? payload?.lastSync
        : payload?.connectedAt;
    const response = await axios
      .request({
        method: 'GET',
        url: payload.shopUrl,
        params: {
          // processed_at_min: this.formatDate(new Date('2021-12-09T09:14:58.760389+00:00')),
          processed_at_min: this.formatDate(new Date(processedAtMin)),
          // processed_at_max: this.formatDate(new Date('2022-03-10T10:14:58.760389+00:00')),
          processed_at_max: this.formatDate(new Date()),
          limit: payload?.limit,
          offset: payload?.offset,
        },
        headers: { Authorization: `Bearer ${payload?.apiKey}` },
        timeout: 60000,
      })
      .catch(err => {
        throw new BadRequestException({
          code: 'OS_0001',
          message: 'Cannot connect. Please check URL or Access Token and try again.',
        });
      });

    if (!isEmpty(response?.data?.orders)) {
      for (const item of response?.data?.orders) {
        await this.amqpConnection.publish('ffm-order', 'create-order-from-one-page', {
          ...item,
          clientId: payload?.clientId,
          countryId: payload?.countryId,
          companyId: payload?.companyId,
          mkiId: payload?.id,
        });
      }
    }

    if (response?.data?.count && response?.data?.count > payload?.limit + payload?.offset) {
      await this.amqpConnection.publish('ffm-order', 'auto-sync-order-from-one-page', {
        ...payload,
        limit: 100,
        offset: payload?.offset + payload?.limit,
      });
    }
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'create-order-from-one-page',
    queue: 'ffm-queue-create-order-from-one-page',
    errorHandler: defaultNackErrorHandler,
  })
  async createOrderFromOnePage(payload) {
    console.log('payload createOrderFromOnePage', payload);

    const customerQuery = this.cusRepository
      .createQueryBuilder('cus')
      .where('cus.companyId = :companyId', { companyId: payload?.companyId });
    if (payload?.shipping_address?.phone) {
      customerQuery.andWhere('cus.recipientPhone = :recipientPhone', {
        recipientPhone: (payload?.shipping_address?.phone).replace(/\s/g, '').replace(/^\(\+\d+\)/, '0'),
      });
    } else {
      customerQuery.andWhere(`cus.recipientPhone is null`);
    }
    if (payload?.shipping_address?.email) {
      customerQuery.andWhere('cus.recipientEmail = :recipientEmail', {
        recipientEmail: payload?.shipping_address?.email,
      });
    } else {
      customerQuery.andWhere(`cus.recipientEmail is null`);
    }

    const [checkOrder, checkOrderSyncFailed, checkCustomer] = await Promise.all([
      this.odRepository.findOne({
        externalId: payload?.id,
        externalCode: ExternalOrderType.onepage,
        companyId: payload?.companyId,
        countryId: payload?.countryId,
        clientId: payload?.clientId
      }),
      this.osfRepository.findOne({
        clientId: payload?.clientId,
        countryId: payload?.countryId,
        companyId: payload?.companyId,
        externalId: payload?.id,
        type: TypeMIEnum.onepage,
      }),
      customerQuery.getOne(),
    ]);

    const customer = checkCustomer ?? new Customer();

    if (checkOrder) {
      console.log('Order synced!');
      return new Nack();
    }
    if (checkOrderSyncFailed) {
      console.log('Order sync failed!');
      return new Nack();
    }

    //  --------------------------------------------------------

    const wh: any = await this.amqpConnection.request({
      exchange: 'ffm-catalog-service-warehouses',
      routingKey: 'get-ffm-warehouse-client-allocation',
      payload: {
        clientId: payload?.clientId,
        countryId: payload?.countryId,
        companyId: payload?.companyId,
      },
      timeout: 10000,
    });

    const localtion: any = await this.amqpConnection.request({
      exchange: 'order-service',
      routingKey: 'get-location-by-name',
      payload: {
        province: payload?.shipping_address?.province,
        // district: payload?.shipping_address?.city,
        ward: payload?.shipping_address?.city,
      },
      timeout: 10000,
    });

    const osf = plainToInstance(OrderSyncFailedMPI, {
      clientId: payload?.clientId,
      countryId: payload?.countryId,
      companyId: payload?.companyId,
      externalId: payload?.id,
      type: TypeMIEnum.onepage,
      detail: payload,
      typeFailed: [],
    });
    if (!payload?.shipping_address?.phone && !payload?.shipping_address?.email) {
      await this.osfRepository.insert({
        clientId: payload?.clientId,
        countryId: payload?.countryId,
        companyId: payload?.companyId,
        externalId: payload?.id,
        type: TypeMIEnum.onepage,
        detail: payload,
        typeFailed: [TypeMIFailEnum.ContactNotExists],
      });
      return new Nack();
    }

    const data = {
      customer: {
        recipientName: `${payload?.shipping_address?.first_name} ${payload?.shipping_address?.last_name}`,
        recipientPhone: (payload?.shipping_address?.phone).replace(/\s/g, '').replace(/^\(\+\d+\)/, '0') ?? null,
        recipientEmail: payload?.shipping_address?.email ?? null,
        recipientAddress: payload?.shipping_address?.address1,
        recipientWard: localtion?.data?.wards_name,
        recipientWardId: localtion?.data?.wards_id,
        recipientProvince: localtion?.data?.provinces_name,
        recipientProvinceId: localtion?.data?.provinces_id,
        recipientPostCode: payload?.shipping_address?.postal_code,
        recipientCountry: payload?.shipping_address?.country_code,
        recipientCountryId: payload?.countryId,
        id: null,
      },
      countryId: payload?.countryId,
      companyId: payload?.companyId,
      countryCode: objToEnum(CountryID)[payload?.countryId],
      creatorId: -99,
      warehouseId: wh?.data?.warehouseId,
      lastUpdatedBy: -99,
      status: OrderFFMStatus.New,
      clientId: payload?.clientId,
      externalId: payload?.id,
      externalCode: ExternalOrderType.onepage,
      products: [],
      totalPrice: payload?.total_price,
      subTotal: payload?.subtotal_price,
      internalNote: '',
    };
    if (customer && customer?.id) {
      data.customer = {
        ...data?.customer,
        id: customer?.id,
      };
    }
    const internalNote = [];
    for (const item of payload?.line_items) {
      data.products.push({
        sku: item?.sku,
        quantity: item?.quantity,
        price: item?.price,
      });

      internalNote.push(`Name: ${item?.name}, quantity: ${item?.quantity}`);
    }

    if (!wh?.data || localtion?.data == 'false') {
      data.status = OrderFFMStatus.Draft;
      data.internalNote = `${internalNote.join('; ')} province: ${
        payload?.shipping_address?.province
      },  ward: ${payload?.shipping_address?.city}`;
      osf.typeFailed.push(TypeMIFailEnum.LocaltionNotExists);
    }

    let products: any = [];
    if (customer && customer?.id) {
      data.customer = {
        ...data?.customer,
        id: customer?.id,
      };
    }

    const order = await this.orderService.plainToOrder(
      data,
      -99,
      payload?.companyId,
      data?.countryCode,
      data?.countryId,
    );
    try {
      const res = await this.amqpConnection.request({
        exchange: 'ffm-catalog-service',
        routingKey: 'ffm-order-find-product',
        payload: {
          sku: data?.products?.map((item: any) => item?.sku),
          user: { companyId: payload?.companyId },
          relations: ['product', 'properties', 'properties.attributes'],
          countryId: data?.countryId,
        },
        timeout: 10000,
      });
      let tempProducts: any = [];
      tempProducts = res?.data;
      if (
        isEmpty(tempProducts) ||
        (!isEmpty(tempProducts) && tempProducts?.length != payload?.line_items.length)
      ) {
        order.status = OrderFFMStatus.Draft;
        order.internalNote = internalNote.join('; ');
        osf.typeFailed.push(TypeMIFailEnum.ProductsNotExists);
        order.products = [];
      } else {
        products = res?.data;
        const prods = data?.products.map(item => {
          const prod = find(products, function(o) {
            return o?.originSku == item?.sku;
          });
          return plainToInstance(OrderProduct, {
            ...item,
            productId: prod?.id,
            productName: prod?.product?.name,
            productDetail: prod ?? {},
            creatorId: data?.clientId,
          });
        });
        const checkDangerous = products.find(x => x.isDangerous);
        if (checkDangerous) order.isDangerous = true;

        order.products = prods;
      }
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    await this.odRepository.save(order).catch(err => {
      throw new BadRequestException(err?.driverError?.detail ?? 'Save order failed!');
    });

    if (!isEmpty(osf.typeFailed)) {
      await this.osfRepository.insert(osf);
    }
    //  --------------------------------------------------------
  }

  async exportExcel(
    filters: FilterSyncOrderFailed,
    request?: Record<string, any>,
    headers?: Record<string, string>,
  ) {
    // const { orderId, ids } = filters;
    const { companyId } = request.user;

    const mQuery = this.osfRepository
      .createQueryBuilder('osf')
      .addSelect('o.display_id', 'orderDisplay')
      .andWhere('osf.company_id =:companyId', { companyId })
      .leftJoinAndMapOne('osf.order', Order, 'o', `osf.external_id::varchar = o.external_id`);

    if (!isEmpty(filters?.type)) {
      mQuery.andWhere('osf.type IN (:...types)', { types: filters?.type });
    }
    const result = await mQuery.getMany();

    const columns = [
      'Order ID',
      'Recipient Name',
      'Email',
      'Recipient Address',
      'Country',
      'Province',
      'Ward',
      'Postcode',
      'Item Code',
      'Product Title',
      'SKU',
      'SKU Title',
      'Quantity',
      'Price',
      'SO Code',
    ];
    const data: unknown[][] = [columns];

    for (let index = 0; index < result.length; index++) {
      const item = result[index]?.detail;
      const products = item?.line_items;
      const rows = [...Array(1)].map(() => Array(columns.length).fill(null));
      const itemCode = products?.map(x => x?.shop_lineitem_code);
      const productTitle = products?.map(x => x?.title);
      const sku = products?.map(x => x?.sku);
      const skuTitle = products?.map(x => x?.variant_title);

      const quantity = reduce(
        products,
        (p: any, n: any) => {
          p += n?.quantity;
          return p;
        },
        0,
      );
      const price = reduce(
        products,
        (p: any, n: any) => {
          p += n?.quantity * n?.price;
          return p;
        },
        0,
      );

      try {
        rows[0][0] = item?.id;
        rows[0][1] = item?.shipping_address?.first_name;
        rows[0][2] = item?.shipping_address?.email;
        rows[0][3] = item?.shipping_address?.address1;
        rows[0][4] = item?.shipping_address?.country;
        rows[0][5] = item?.shipping_address?.province;
        rows[0][6] = item?.shipping_address?.city;
        rows[0][7] = item?.shipping_address?.postal_code;
        rows[0][8] = itemCode.join(', ');
        rows[0][9] = productTitle.join(', ');
        rows[0][10] = sku.join(', ');
        rows[0][11] = skuTitle.join(', ');
        rows[0][12] = quantity;
        rows[0][13] = price;
        rows[0][14] = result[index]?.order?.displayId;

        data.push(...rows);
      } catch (error) {
        console.log(`error at`, index, result);
        console.log(`error reason`, error);
      }
    }

    const sheetOptions = {
      '!cols': new Array(columns?.length).fill({ wch: 20 }),
    };
    const buffer = xlsx.build([{ name: 'Order Sync Failed', data, options: {} }], {
      sheetOptions,
    }); // Returns a buffer
    return buffer;
  }

  async deleteOrderSource(
    id: number,
    headers: Record<string, any>,
    req: Record<string, any>,
  ): Promise<{ success: boolean; message: string }> {
    const { companyId } = req.user;

    // Kiểm tra bản ghi tồn tại với điều kiện id và companyId
    const orderSource = await this.mkiRepo.findOne({
      where: {
        id,
        companyId
      }
    });

    if (!orderSource) {
      throw new BadRequestException('Order source not found or you do not have permission to delete this record');
    }

    // Remove clientId and save the record
    orderSource.clientId = null;
    orderSource.lastUpdatedBy = req.user.id;
    await this.mkiRepo.save(orderSource);

    return {
      success: true,
      message: 'Order source updated successfully - clientId removed'
    };
  }
}
