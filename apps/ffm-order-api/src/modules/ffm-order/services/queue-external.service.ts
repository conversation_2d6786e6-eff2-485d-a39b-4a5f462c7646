/* eslint-disable @typescript-eslint/no-floating-promises */
import {
  AmqpConnection,
  defaultNack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  RabbitRPC,
} from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ORDER_STATUS_CAN_UPDATE_TICKET_STATUS } from 'apps/ffm-order-api/src/constants/raise-ticket.constants';
import { FfmToAgOrderInfoDto } from 'apps/ffm-order-api/src/dtos/ag-order/ffm-to-ag-order-info.dto';
import { District as District3PL } from 'apps/ffm-order-api/src/entities/address_district_3pl.entity';
import { Province as Province3PL } from 'apps/ffm-order-api/src/entities/address_province_3pl.entity';
import { Ward as Ward3PL } from 'apps/ffm-order-api/src/entities/address_ward_3pl.entity';
import { HandOver } from 'apps/ffm-order-api/src/entities/hand-over.entity';
import { ODZConfigurationItem } from 'apps/ffm-order-api/src/entities/odz-configuration-item.entity';
import { ODZConfiguration } from 'apps/ffm-order-api/src/entities/odz-configuration.entity';
import { OrderCarrierHistories } from 'apps/ffm-order-api/src/entities/order-carrier-history.entity';
import { OrderProductComboVariant } from 'apps/ffm-order-api/src/entities/order-product-combo-variant.entity';
import { OrderStatusHistories } from 'apps/ffm-order-api/src/entities/order-status-history.entity';
import { OrderSyncFailedMPI } from 'apps/ffm-order-api/src/entities/order-sync-failed-mki.entity';
import { OrderTag } from 'apps/ffm-order-api/src/entities/order-tag.entity';
import { OrderWarning } from 'apps/ffm-order-api/src/entities/order_warning.entity';
import { ReImport } from 'apps/ffm-order-api/src/entities/re-import.entity';
import { Region } from 'apps/ffm-order-api/src/entities/regions.entity';
import { SystemSetting } from 'apps/ffm-order-api/src/entities/system-setting.entity';
import { UserPancakeShop } from 'apps/ffm-order-api/src/entities/user-pancake-shop.entity';
import { TypeOfTagEnum } from 'apps/ffm-order-api/src/enums/tag.enum';
import axios, { AxiosError } from 'axios';
import { plainToInstance } from 'class-transformer';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import {
  catalogFfmConnection,
  catalogFfmWriteConnection,
  identityConnection,
  orderAgConnection,
  orderConnection,
} from 'core/constants/database-connection.constant';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { SystemIdEnum } from 'core/enums/user-type.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import SignatureUtils from 'core/utils/SignatureUtils';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { clone, concat, filter, find, isEmpty, last, orderBy, reduce, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import { $enum } from 'ts-enum-util';
import { Brackets, In, Repository, SelectQueryBuilder } from 'typeorm';
import {
  BEFORE_ORDER_STATUS_3PL_PICKED_UP,
  NOT_ALLOW_UPDATE_STATUS_BY_3PL_OUT_FOR_DELIVERY,
  PANCAKE_STATUS_ORDER,
  PANCAKE_TAG_MAPPING_ORDER,
  PANCAKE_TAG_ORDER_POS,
  PancakePFGId,
  SYSTEM_LOCK_ACTION_STRING,
} from '../../../constants/order.constants';
import { Carrier } from '../../../entities/carrier.entity';
import { Customer } from '../../../entities/customer.entity';
import { Logs } from '../../../entities/logs.entity';
import { Note } from '../../../entities/note.entity';
import { OrderCarrier } from '../../../entities/order-carrier.entity';
import { OrderProduct } from '../../../entities/order-product.entity';
import { Order } from '../../../entities/order.entity';
import { Tag } from '../../../entities/tags.entity';
import {
  CarrieStatusEnum,
  ExternalOrderType,
  SyncInfoEnum,
  WarningType,
} from '../../../enums/order-status.enum';
import {
  TypeRevertStatus,
  TypeUpdateOrderStatus,
} from '../../../enums/type-update-order-status.enum';
import { District } from '../../../read-entities/ag-order/district.entity';
import { Province } from '../../../read-entities/ag-order/province.entity';
import { Ward } from '../../../read-entities/ag-order/ward.entity';
import { ProductComboVariant } from '../../../read-entities/ffm-catalog/product-combo-variant.entity';
import { StockInventory } from '../../../read-entities/ffm-catalog/stock-inventory.entity';
import { SlotWarehouses } from '../../../read-entities/ffm-catalog/warehouses.entity';
import { Country } from '../../../read-entities/identity/country.entity';
import { FulfillmentApiKey } from '../../../read-entities/identity/fulfillment-api-key.entity';
import { FulfillmentPartnerClient } from '../../../read-entities/identity/fulfillment-partner-client.entity';
import { User } from '../../../read-entities/identity/user.entity';
import { IDataVNP } from '../utils/tracking-parser';
import { WWPROXY } from '../utils/wwproxy';
import { ODZService } from './odz.service';
import { TagService } from './tags.service';
import { OrderCarriersService } from './order-partners.service';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import {
  InventoryType,
  ProductStatus,
  TypeInventory,
  TypePurpose,
} from 'apps/ffm-order-api/src/enums/stock-inventory.enum';
import { InventoryManagement } from 'apps/ffm-order-api/src/ffm-catalog-entities/inventory-management.entity';
import { InventoryLogs } from 'apps/ffm-order-api/src/ffm-catalog-entities/inventory-logs.entity';
import { InventoryLineItem } from 'apps/ffm-order-api/src/ffm-catalog-entities/inventory-line-item.entity';
import { StockInventoryInOrder } from './stock-inventory.service';

// const excel = require('node-excel-export');
// eslint-disable-next-line @typescript-eslint/no-var-requires
export interface IWebhookVNPDto {
  data: IDataVNP;
}

export interface ICalcInventory {
  status: 200 | 500;
  message: string;
  constraint?: string;
}
export interface IWarehouse {
  id: string;
  name: string;
  phoneNumber: string;
  address?: any;
  fullAddress: string;
  addressSplit?: string[];
  provinceId: string;
  districtId: string;
  communeId: string;
  status: string;
}

export interface IUpdateStatus3PL {
  id: number;
  isUpdateStatus3PL?: boolean;
  reason?: {
    reasonCode: string;
    reasonNote: string;
  };
  lastUpdateStatus: string;
  typeUpdateStatus: TypeUpdateOrderStatus;
  user: {
    id: number | string;
    companyId: number | string;
  };
  status: OrderFFMStatus;
}
export interface IOrderInventory {
  id: number;
  user: Record<string, any>;
  oldStatus: OrderFFMStatus;
  newStatus: OrderFFMStatus;
  lastUpdateStatus: Date;
  typeUpdateStatus?: any;
  reason?: string;
  revert?: boolean;
  callBackStatus?: boolean;
  isRequestQueue?: boolean;
  rollBackStatus?: boolean;
  revertType?: TypeRevertStatus;
  rmqErrorAttempts?: number;
  updatedAt?: number;
  isSyncToPartner?: boolean;
  saveNewStatus?: boolean;
  isReimport?: boolean;
  isPriorityStockInventory?: boolean;
}

export interface IOrderProduct {
  quantity: number;
  id: number;
  productId: number;
}

@Injectable()
export class FFMQueueExternalService {
  constructor(
    @InjectRepository(OrderProductComboVariant, orderConnection)
    private odProductComboVariantRepo: Repository<OrderProductComboVariant>,
    @InjectRepository(Region, orderConnection)
    private regionRepo: Repository<Region>,
    @InjectRepository(UserPancakeShop, orderConnection)
    private upsRepo: Repository<UserPancakeShop>,
    @InjectRepository(Carrier, orderConnection)
    private carRepository: Repository<Carrier>,
    @InjectRepository(FulfillmentPartnerClient, identityConnection)
    private ffmPartnerRepository: Repository<FulfillmentPartnerClient>,
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    @InjectRepository(OrderTag, orderConnection)
    private otRepository: Repository<OrderTag>,
    @InjectRepository(OrderCarrier, orderConnection)
    private ocRepository: Repository<OrderCarrier>,
    @InjectRepository(OrderProduct, orderConnection)
    private opRepository: Repository<OrderProduct>,
    @InjectRepository(Customer, orderConnection)
    private cusRepository: Repository<Customer>,
    @InjectRepository(Country, catalogFfmConnection)
    private countryRepository: Repository<Country>,
    @InjectRepository(SlotWarehouses, catalogFfmConnection)
    private whRepository: Repository<SlotWarehouses>,
    @InjectRepository(Note, orderConnection)
    private noteRepository: Repository<Note>,
    @InjectRepository(StockInventory, catalogFfmConnection)
    private stRepository: Repository<StockInventory>,
    @InjectRepository(Logs, orderConnection)
    private logRepository: Repository<Logs>,
    @InjectRepository(User, identityConnection)
    private uRepository: Repository<User>,
    @InjectRepository(FulfillmentApiKey, identityConnection)
    private apiKeyRepository: Repository<FulfillmentApiKey>,
    @InjectRepository(Province, orderAgConnection)
    private provinceRepository: Repository<Province>,
    @InjectRepository(District, orderAgConnection)
    private districtRepository: Repository<District>,
    @InjectRepository(Ward, orderAgConnection)
    private wardRepository: Repository<Ward>,
    @InjectRepository(ODZConfiguration, orderConnection)
    private odzConfigRepository: Repository<ODZConfiguration>,
    @InjectRepository(HandOver, orderConnection)
    private hoRepository: Repository<HandOver>,
    @InjectRepository(Province3PL, orderConnection)
    private province3plRepository: Repository<Province3PL>,
    @InjectRepository(District3PL, orderConnection)
    private district3plRepository: Repository<District3PL>,
    @InjectRepository(Ward3PL, orderConnection)
    private ward3plRepository: Repository<Ward3PL>,
    @InjectRepository(OrderCarrierHistories, orderConnection)
    private ochRepository: Repository<OrderCarrierHistories>,
    @InjectRepository(OrderSyncFailedMPI, orderConnection)
    private osfRepository: Repository<OrderSyncFailedMPI>,
    @InjectRepository(OrderWarning, orderConnection)
    private owRepository: Repository<OrderWarning>,
    @InjectRepository(ReImport, orderConnection)
    private reImportRepository: Repository<ReImport>,
    @InjectRepository(ODZConfigurationItem, orderConnection)
    private odzConfigurationItemRepository: Repository<ODZConfigurationItem>,
    @InjectRepository(OrderStatusHistories, orderConnection)
    private oshRepository: Repository<OrderStatusHistories>,
    @InjectRepository(SystemSetting, orderConnection)
    private systemSettingRepository: Repository<SystemSetting>,
    @InjectRepository(InventoryManagement, catalogFfmWriteConnection)
    private imRepository: Repository<InventoryManagement>,
    @InjectRepository(InventoryLineItem, catalogFfmWriteConnection)
    private iltRepository: Repository<InventoryLineItem>,
    @InjectRepository(InventoryLogs, catalogFfmWriteConnection)
    private logsRepository: Repository<InventoryLogs>,
    private readonly amqpConnection: AmqpConnection,
    private tagService: TagService,
    private carrierService: OrderCarriersService,
    private stockInventoryInOrder: StockInventoryInOrder,
    private odzService: ODZService,
    private readonly redisService: RedisCacheService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'count-product-in-order-2-1',
    queue: 'queue-external-count-product-in-order-2-1',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async countProductInOrder({ ids, companyId, warehouseIds }) {
    const mQuery = this.odRepository
      .createQueryBuilder('o')
      .select('o.status', 'status')
      .addSelect('SUM(products.quantity)', 'quantity')
      .addSelect('products.productId', 'id')
      .addSelect('o.warehouse_id', 'warehouse_id');

    mQuery
      .andWhere({
        companyId,
        warehouseId: In(warehouseIds),
        status: In([
          OrderFFMStatus.AwaitingCollection,
          OrderFFMStatus.Confirmed,
          OrderFFMStatus.Reconfirm,
          OrderFFMStatus.Collecting,
          OrderFFMStatus.Awaiting3PLPickup,
          OrderFFMStatus.Returned,
        ]),
      })
      .leftJoin('o.products', 'products')
      .andWhere('products.productId IN (:...ids)', { ids });

    mQuery
      .addGroupBy('o.status')
      .addGroupBy('products.productId')
      .addGroupBy('o.warehouse_id');

    return mQuery.getRawMany();
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'get-one-order-2-1',
    queue: 'queue-external-get-one-order-2-1',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async getOneOrder({ id, user, variants }) {
    const { companyId } = user;

    const mQuery = this.odRepository.createQueryBuilder('o');
    mQuery
      .andWhere({
        companyId,
      })
      .leftJoinAndSelect('o.products', 'products')
      .leftJoinAndSelect('o.carriers', 'carriers')
      .leftJoinAndSelect('carriers.carrier', 'carrier');

    if (!!variants && variants?.length > 0) {
      mQuery.andWhere('products.productId IN (:...variants)', { variants });
      mQuery.andWhere('o.status != :status', { status: OrderFFMStatus.Canceled });
    }
    if (!!id) {
      mQuery.andWhere({
        id,
      });
    }
    const order = await mQuery.getOne();
    return !!order ? order : false;
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'inbound-reserved-order-2-1',
    queue: 'queue-external-inbound-reserved-order-2-1',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async inboundReservedOrder({ ids, companyId, warehouseIds, comboIds }) {
    const lstStatus = [
      OrderFFMStatus.AwaitingCollection,
      OrderFFMStatus.Confirmed,
      OrderFFMStatus.Reconfirm,
      OrderFFMStatus.Collecting,
      OrderFFMStatus.Awaiting3PLPickup,
      // OrderFFMStatus.Returned,
    ];

    const mQuery = this.odRepository
      .createQueryBuilder('o')
      .select('o.status', 'status')
      .addSelect('SUM(products.quantity)', 'quantity')
      .addSelect('products.productId', 'id')
      .addSelect('o.warehouse_id', 'warehouse_id');

    mQuery
      .andWhere({
        companyId,
        warehouseId: In(warehouseIds),
      })
      .leftJoin('o.products', 'products')
      .andWhere('products.productId IN (:...ids)', { ids });

    mQuery
      .addGroupBy('o.status')
      .addGroupBy('products.productId')
      .addGroupBy('o.warehouse_id');

    const cancelQuery = mQuery.clone();
    mQuery.andWhere({
      status: In(lstStatus),
    });

    const comboQuery = this.odRepository
      .createQueryBuilder('o')
      .select('o.status', 'status')
      .addSelect('products.productDetail', 'product')
      .addSelect('products.quantity', 'quantity')
      .addSelect('o.warehouseId', 'warehouse_id')
      .leftJoin('o.products', 'products')
      .andWhere(`products.productId IN (:...comboIds)`, {
        comboIds,
      });
    const cancelInboundQuery = comboQuery.clone();
    const cancelProductSimple = await this.getInboundQueryCancel(cancelQuery);
    const cancelProductCombo = await this.getInboundQueryCancel(cancelInboundQuery);

    const awReimportQuery = this.odRepository
      .createQueryBuilder('o')
      .select('SUM(products.quantity)', 'quantity')
      .addSelect('products.productId', 'id')
      .addSelect('o.return_warehouse_id', 'warehouse_id')
      .andWhere({
        companyId,
      })
      .andWhere(`o.return_warehouse_id IN (:...warehouseIds)`, {
        warehouseIds,
      })
      .leftJoin('o.products', 'products')
      .groupBy('products.productId')
      .addGroupBy('o.return_warehouse_id')
      // .andWhere('products.productId IN (:...ids)', { ids })
      .andWhere({
        status: OrderFFMStatus.Returned,
      })
      .leftJoin(ReImport, 'ri', `(o.id = ri.order_id AND ri.deleted_at IS NULL)`)
      .andWhere('ri.id IS NULL');

    const [
      dataInbound,
      dataCancel,
      dataCombo,
      dataComboCancel,
      dataAwReimport,
    ] = await Promise.all([
      mQuery.getRawMany(),
      cancelProductSimple.getRawMany(),
      comboIds?.length > 0
        ? comboQuery.andWhere('o.status IN (:...lstStatus)', { lstStatus }).getRawMany()
        : [],
      comboIds?.length > 0 ? cancelProductCombo.getRawMany() : [],
      awReimportQuery.getRawMany(),
    ]);
    const dataExportCombo = [];
    const dataConcatCombo = concat(dataCombo, dataComboCancel);
    if (dataConcatCombo?.length > 0) {
      dataConcatCombo.forEach((o: any) => {
        o?.product?.product?.combo?.forEach((it: any) => {
          dataExportCombo.push({
            id: it?.variantId,
            warehouse_id: o?.warehouse_id,
            status: o?.status,
            quantity: it?.qty * o?.quantity,
          });
        });
      });
    }
    // console.log(mQuery?.getQueryAndParameters());
    console.log(
      '🐔  ~ inboundReservedOrder ~ concat(dataInbound, dataCancel, dataExportCombo):',
      concat(dataInbound, dataCancel, dataExportCombo)?.length
    );
    return { products: concat(dataInbound, dataCancel, dataExportCombo), dataAwReimport };
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'inbound-reserved-order-report',
    queue: 'queue-external-inbound-reserved-order-report',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async inboundReservedOrderReport() {
    const lstStatus = [
      OrderFFMStatus.AwaitingCollection,
      OrderFFMStatus.Confirmed,
      OrderFFMStatus.Reconfirm,
      OrderFFMStatus.Collecting,
      OrderFFMStatus.Awaiting3PLPickup,
    ];

    const mQuery = this.odRepository
      .createQueryBuilder('o')
      .select('o.status')
      .addSelect('o.id')
      .addSelect('products.productId')
      .addSelect('products.quantity')
      .addSelect('combo.productId')
      .addSelect('combo.variantId')
      .addSelect('combo.qty')
      .addSelect('o.warehouseId');

    mQuery
      .andWhere({
        companyId: In(['3', '55']),
      })
      .leftJoin('o.products', 'products')
      .leftJoin('o.orderProductCombo', 'combo');

    // mQuery
    //   .addGroupBy('o.status')
    //   .addGroupBy('products.productId')
    //   .addGroupBy('o.warehouseId');

    const cancelQuery = mQuery.clone();
    const awReimportQuery = mQuery.clone();
    mQuery.andWhere({
      status: In(lstStatus),
    });
    const cancelProduct = await this.getInboundQueryCancel(cancelQuery);

    awReimportQuery
      .andWhere({
        status: OrderFFMStatus.Returned,
      })
      .andWhere({
        companyId: In(['3', '55']),
      })
      .leftJoin(ReImport, 'ri', `(o.id = ri.order_id AND ri.deleted_at IS NULL)`)
      .andWhere('ri.id IS NULL');

    const [dataInbound, dataCancel, dataAwReimport] = await Promise.all([
      mQuery.getMany(),
      cancelProduct.getMany(),
      awReimportQuery.getMany(),
    ]);

    console.log(
      '🐔  ~ inboundReservedOrder ~ concat(dataInbound, dataCancel, dataAwReimport):',
      concat(dataInbound, dataCancel, dataAwReimport)?.length
    );
    return { products: concat(dataInbound, dataCancel), dataAwReimport };
  }

  async getInboundQueryCancel(cancelQuery: SelectQueryBuilder<Order>) {
    cancelQuery.leftJoin(
      OrderStatusHistories,
      'osh',
      'o.id = osh.order_id AND osh.order_id is NOT NULL',
    );
    cancelQuery.andWhere({
      status: OrderFFMStatus.Canceled,
    });
    cancelQuery.andWhere('osh.status = :statusCancel', { statusCancel: OrderFFMStatus.Canceled });
    cancelQuery.andWhere('osh.beforeStatus IN (:...beforeStatus)', {
      beforeStatus: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting],
    });
    cancelQuery.leftJoin(ReImport, 'reimport', '(o.id = reimport.order_id)');
    cancelQuery.andWhere('reimport.id is null');
    return cancelQuery;
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'get-return-order-2-1',
    queue: 'queue-external-get-return-order-2-1',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async getReturnOrder({ id, user }) {
    const { companyId } = user;

    const mQuery = this.reImportRepository.createQueryBuilder('r');
    mQuery
      .andWhere({
        companyId,
      })
      .leftJoinAndSelect('r.products', 'products');

    if (!!id) {
      mQuery.andWhere({
        id,
      });
    }
    const returnOrder = await mQuery.getOne();
    return !!returnOrder ? returnOrder : false;
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'stock-inventory-changed-2-1',
    queue: 'queue-external-stock-inventory-changed-2-1',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async priorityStockInventory(payload) {
    const { id, user, warehouseId, comboIds } = payload;
    const updatedAt = payload?.updatedAt ? payload?.updatedAt : moment()?.valueOf();
    console.log('payload', 'purchase-order-stock', payload);

    const { quantity } = payload;
    if (!payload?.id || !payload?.user || !payload?.warehouseId) {
      console.log('reconfirm', 'Not found id | user');
      return new Nack();
    }

    console.log('payload', 'purchase-order-stock re-confirm-order', payload);

    const subQb = this.opRepository
      .createQueryBuilder('opd')
      .select('o.id', 'id')
      .leftJoin('opd.order', 'o')
      .andWhere('o.companyId = :companyId', { companyId: user?.companyId })
      .andWhere('o.status = :status', { status: OrderFFMStatus.AwaitingStock })
      .andWhere('o.warehouse_id = :warehouseId', { warehouseId: Number(warehouseId) })
      .andWhere('opd.quantity <= :quantity', { quantity })
      .andWhere('opd.productId = :productId', { productId: id })
      .groupBy('o.id');

    const mQuery = this.odRepository.createQueryBuilder('o');
    mQuery.andWhere({
      companyId: user?.companyId,
      status: OrderFFMStatus.AwaitingStock,
    });
    mQuery
      .select('o.id', 'id')
      .addSelect('o.createdAt', 'created')
      .addSelect('o.lastUpdateStatus', 'lastupdate')
      // .addSelect('tags.id', 'tag')
      .addSelect('o.countryId', 'country')
      .addSelect('SUM (DISTINCT CASE WHEN "tags"."id" = -1 THEN "tags"."id" ELSE 0 END )', 'tag')
      .leftJoin('o.products', 'products')
      .leftJoin('o.tags', 'tags', 'tags.id = -1') //lấy tags ưu tiên chia tồn
      .orderBy('tag')
      .addOrderBy('created')
      .groupBy('o.id')
      .addGroupBy('o.createdAt');
    // .addGroupBy('tag');

    const comboQuery = mQuery.clone();

    mQuery
      .addSelect('SUM(products.quantity)', 'total')
      .addOrderBy('total')
      .addSelect(
        `SUM(DISTINCT CASE WHEN products.productId = ${id} THEN products.quantity ELSE 0 END)`,
        'quantity',
      )
      .innerJoin(`(${subQb.getQuery()})`, 'od', 'o.id = od.id', subQb.getParameters())
      .leftJoin('o.orderProductCombo', 'orderProductCombo')
      .andWhere(
        '(orderProductCombo.variant_id is null or orderProductCombo.variant_id <> :productId)',
        { productId: id },
      );

    const comboSubQuery = this.odProductComboVariantRepo
      .createQueryBuilder('opcv')
      .select('o.id', 'id')
      .addSelect('SUM(opcv.qty)', 'qty')
      .addSelect('opcv.variantId', 'variant')
      .leftJoin('opcv.order', 'o')
      .leftJoin('opcv.orderProduct', 'op')
      .andWhere('op.order_id IS NOT NULL')
      .andWhere('o.status = :status', { status: OrderFFMStatus.AwaitingStock })
      .andWhere('o.warehouse_id = :warehouseId', { warehouseId: Number(warehouseId) })
      .andWhere('opcv.companyId = :companyId', { companyId: user?.companyId })
      .andWhere('opcv.qty <= :quantity', { quantity })
      .andWhere('opcv.variantId = :variantId', { variantId: id })
      .groupBy('o.id')
      .addGroupBy('opcv.variantId');

    comboQuery
      .addSelect(
        `SUM(DISTINCT CASE WHEN products.productId = ${id} THEN products.quantity WHEN od.variant = ${id} THEN od.qty ELSE 0 END)`,
        'quantity',
      )
      .innerJoin(
        `(${comboSubQuery.getQuery()})`,
        'od',
        'o.id = od.id',
        comboSubQuery.getParameters(),
      );

    const [data, dataCombo] = await Promise.all([
      mQuery.getRawMany(),
      comboIds?.length > 0 ? comboQuery.getRawMany() : [],
    ]);

    // const lookups = concat(data, dataCombo);
    const lookups = orderBy(concat(data, dataCombo), ['tag', 'created'], ['asc', 'asc']);
    // const lookups = concat(data, dataCombo);
    let qty = clone(quantity);
    console.log(
      '🐔  ~ FFMQueueExternalService ~ importStockInventory ~ result:',
      lookups?.[0],
      last(lookups),
      'last data',
      last(data),
      'lookups',
      lookups?.length,
      'data',
      data?.length,
      qty,
    );
    const pipeline = this.redis.pipeline();
    for (const item of lookups) {
      const key = `${item?.id}${SYSTEM_LOCK_ACTION_STRING}`;
      pipeline.get(key);
    }
    const checkListOrder = await pipeline.exec();
    console.log(
      '🚬 ~ FFMQueueExternalService ~ priorityStockInventory ~ checkListOrder:',
      checkListOrder?.length,
    );
    const lookUpBlockOd = [];

    if (checkListOrder?.length > 0)
      for (const item of checkListOrder) {
        if (item?.[1] && item?.[1] != 'null') {
          const [orderId, checkLock] = item?.[1]?.split('_');
          if (orderId) lookUpBlockOd[orderId] = orderId;
        }
      }

    const lastUpdateStatus = new Date();
    for (const item of lookups) {
      if (qty > 0) {
        if (lookUpBlockOd?.[`${item?.id}`]) {
          console.log(
            '🚬 ~ FFMQueueExternalService ~ priorityStockInventory ~ SYSTEM_LOCK_ACTION:',
            item?.id,
          );
          continue;
        }
        console.log(
          '🐔  ~ FFMQueueExternalService ~ importStockInventory ~ calcInventory: Process',
          item?.id,
        );
        if (qty < item?.quantity) continue;
        try {
          const calcInventory = await this.stockInventoryInOrder.changeStockInventoryByOrder({
            id: item?.id,
            user: {
              id: SystemIdEnum.system,
              companyId: user?.companyId,
            },
            oldStatus: OrderFFMStatus.AwaitingStock,
            newStatus: OrderFFMStatus.Reconfirm,
            lastUpdateStatus,
            updatedAt: item?.lastupdate ? moment(item?.lastupdate)?.valueOf() : updatedAt,
            rollBackStatus: false,
            isSyncToPartner: false,
            isPriorityStockInventory: true,
          });
          if (
            calcInventory.status == 200 ||
            calcInventory.constraint == 'UQ_STOCK_TYPE_ORIGIN' ||
            calcInventory.message == 'UQ_STOCK_TYPE_ORIGIN'
          ) {
            // await Promise.all([
            //   this.odRepository
            //     .update(
            //       { id: item?.id, status: In([OrderFFMStatus.New, OrderFFMStatus.AwaitingStock]) },
            //       {
            //         status: OrderFFMStatus.Reconfirm,
            //         lastUpdatedBy: SystemIdEnum.system,
            //       },
            //     )
            //     .catch(err => {
            //       console.log('reconfirm error', err?.driverError);
            //     }),
            //   this.otRepository.delete({ id_order: item?.id, id_tag: -1 }),
            // ]);
            if (calcInventory.status == 200) qty = qty - item?.quantity;
            await this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
              id: item?.id,
              user: {
                id: SystemIdEnum.system,
                companyId: user?.companyId,
              },
              updatedAt: lastUpdateStatus,
            });
            console.log(
              '🐔  ~ FFMQueueExternalService ~ importStockInventory ~ calcInventory: Success',
              item?.id,
              qty,
            );
          } else {
            const checkOrder = await this.odRepository
              .createQueryBuilder('o')
              .where(`o.id = :oid`, { oid: item?.id })
              .getOne();
            if ([OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed].includes(checkOrder?.status)) {
              qty = qty - item?.quantity;
            }
            console.log(
              '🐔  ~ FFMQueueExternalService ~ importStockInventory ~ calcInventory: Failed',
              item?.id,
              qty,
            );
            if (
              calcInventory.status == 500 &&
              calcInventory.message == 'CHK_cb6880fb40a80b2e6e52b8f914' &&
              calcInventory.variantIds.includes(payload?.id)
            ) {
              console.log('No physical inventory left');
              break;
            }
          }
        } catch (error) {
          console.log('🐔  ~ FFMQueueExternalService ~ order-changed-stock-v2: Failed', error);
        }
      } else break;
    }

    return new Nack();
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'stock-update-status-order-2-1',
    queue: 'queue-external-stock-update-status-order-2-1',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async stockUpdateStatusOrder(payload: IUpdateStatus3PL) {
    console.log('update status when calc inventory', payload);

    if (!payload?.id || !payload?.status || !payload?.user) {
      console.log('stock-update-order', 'Not found id | status | user');
      return new Nack();
    }

    const { id, status, user, typeUpdateStatus, lastUpdateStatus, reason } = payload;

    const [order, lastOch] = await Promise.all([
      this.odRepository.findOne({
        where: qb => {
          qb.andWhere({
            id,
            companyId: user?.companyId,
          });
        },
        relations: ['products', 'carriers', 'carriers.carrier', 'tags'],
      }),
      this.ochRepository
        .createQueryBuilder('och')
        .where('och.order_id = :id', { id })
        .andWhere('och.company_id = :companyId', { companyId: user?.companyId })
        .orderBy('updated_at', 'DESC')
        .limit(1)
        .getOne(),
    ]);

    if (!order?.id) {
      console.log('stock-update-order', 'Not found order');
      return new Nack();
    }
    // Những đơn tạo mới với trạng thái là confirm nhưng kèm cả waybill sẽ tự động sang awaiting collection mà ko cần phải update lại
    if (order?.status == OrderFFMStatus.AwaitingCollection && status == OrderFFMStatus.Confirmed)
      return new Nack();

    if (
      lastUpdateStatus &&
      status != order?.status &&
      order?.lastUpdateStatus &&
      moment(lastUpdateStatus)?.valueOf() < moment(order?.lastUpdateStatus)?.valueOf()
    ) {
      if (
        [
          OrderFFMStatus.FailedDelivery,
          OrderFFMStatus.Delivered,
          OrderFFMStatus.AwaitingReturn,
          OrderFFMStatus.LostBy3PL,
          OrderFFMStatus.Returned,
        ]?.includes(Number(status)) &&
        [
          OrderFFMStatus.PickedUp3PL,
          OrderFFMStatus.InTransit,
          OrderFFMStatus.Stocked3PL,
          OrderFFMStatus.InDelivery,
        ]?.includes(Number(order?.status))
      ) {
        console.log('stock-update-order', '3pl update late', payload, order?.status);
      } else {
        //cover case queue update status order mất nhiều thời gian xử lý queue hơn thời gian giữa 2 trạng thái 3pl trả về
        //cho phép update khi trạng thái order trùng với trạng thái mới nhất 3pl trả về
        if (
          lastOch &&
          lastOch.status == status &&
          order?.typeUpdateStatus == TypeUpdateOrderStatus.By3PL
        ) {
          console.log(
            'stock-update-order',
            'last status diffirent last och status',
            payload,
            order?.typeUpdateStatus,
            lastOch,
          );
        } else {
          console.log('stock-update-order', 'lastUpdateStatus late', payload);
          return new Nack();
        }
      }
    }

    const _update: any = !!typeUpdateStatus
      ? {
          status,
          lastUpdatedBy: user?.id,
          typeUpdateStatus,
          lastUpdateStatus,
          reasonCode: !!reason?.reasonCode ? reason?.reasonCode : order?.reasonCode,
          reasonNote: !!reason?.reasonNote ? reason?.reasonNote : order?.reasonNote,
          lastUpdateReason: !!reason?.reasonCode ? new Date() : order?.lastUpdateReason,
        }
      : {
          status,
          lastUpdatedBy: user?.id,
        };

    let isSyncTag = false;
    if (order?.lastCarrier?.extraData?.courier_name) {
      _update.tags = order?.tags;
      const tagInFFM = await this.tagService.getTagByContent(
        [order?.lastCarrier?.extraData?.courier_name],
        Number(user?.companyId),
        order?.countryId,
      );
      if (tagInFFM?.[0]) {
        const tagLookup = find(order?.tags, (it: Tag) => it.id == tagInFFM?.[0]?.id);
        if (!tagLookup) _update.tags.push(tagInFFM?.[0]);
      } else {
        const newTag = new Tag();
        newTag.content = order?.lastCarrier?.extraData?.courier_name;
        newTag.companyId = Number(user?.companyId);
        newTag.color = '#D4E0FF';
        _update.tags.push(newTag);
      }
      isSyncTag = true;
    }

    const updateData = {
      ...order,
      ..._update,
    };

    //tag 'Stock Priority' create by system, id default = -1
    updateData.tags = [OrderFFMStatus.New, OrderFFMStatus.AwaitingStock].includes(
      updateData?.status,
    )
      ? updateData?.tags
      : !isEmpty(updateData?.tags)
      ? updateData?.tags.filter(x => x.id != -1)
      : [];

    await this.odRepository.save(updateData).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    if (
      order?.id &&
      [OrderFFMStatus.ReturnedStocked, OrderFFMStatus.ReturnedCompleted].includes(status)
    ) {
      await this.amqpConnection.publish('ffm-order', 'ffm-queue-auto-generate-remittance', {
        orderId: order?.id,
      });
    }

    console.log('update status when calc inventory', 'success', payload);

    if (!!order?.externalId)
      await this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
        id: order?.id,
        user,
        updatedAt: lastUpdateStatus,
        isSyncTag,
      });
    if (ORDER_STATUS_CAN_UPDATE_TICKET_STATUS?.includes(_update?.status))
      this.updateTicketStatus(order?.id, user, _update?.status);
    if (_update?.status == OrderFFMStatus.ReturnedStocked)
      this.updateOrderAwaitingStock(order, user);

    return new Nack();
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 'stock-update-status-order-2-2',
    queue: 'queue-external-stock-update-status-order-2-2',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async stockUpdateStatusOrderV2(payload: IUpdateStatus3PL) {
    console.log('update status when calc inventory', payload);

    if (!payload?.id || !payload?.status || !payload?.user) {
      console.log('stock-update-order', 'Not found id | status | user');
      return new Nack();
    }

    const { id, status, user, typeUpdateStatus, lastUpdateStatus, reason } = payload;

    const order = await this.odRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
          companyId: user?.companyId,
        });
      },
      relations: ['products', 'carriers', 'carriers.carrier', 'tags'],
    });

    console.log('stock-update-order', order?.id, lastUpdateStatus);

    if (!order?.id) {
      console.log('stock-update-order', 'Not found order');
      return new Nack();
    }
    // Nếu không gửi từ 3pl thì check lastUpdateStatus
    // Còn không sẽ check trạng thái cuối khi 3pl gửi về
    let isSyncTag = false;
    const newStatus =
      !isEmpty(order?.lastCarrier) &&
      order?.lastCarrier?.status == 'activated' &&
      order?.lastCarrier?.waybillNumber &&
      status == OrderFFMStatus.Confirmed
        ? OrderFFMStatus.AwaitingCollection
        : status;
    if (
      (order?.lastUpdateStatus &&
        moment(lastUpdateStatus)?.valueOf() < moment(order?.lastUpdateStatus)?.valueOf() &&
        typeUpdateStatus != TypeUpdateOrderStatus.By3PL) ||
      ((NOT_ALLOW_UPDATE_STATUS_BY_3PL_OUT_FOR_DELIVERY?.includes(order?.status) ||
        BEFORE_ORDER_STATUS_3PL_PICKED_UP?.includes(order?.status)) &&
        typeUpdateStatus == TypeUpdateOrderStatus.By3PL)
    ) {
      console.log('stock-update-order', 'lastUpdateStatus late', payload, order?.lastUpdateStatus);

      const log = plainToInstance(Logs, {
        action: 'STATUS',
        event: 'Change',
        type: `Status (by ${order?.lastCarrier?.carrier?.name ?? ''})`,
        afterChanges: [
          'reason_code',
          reason?.reasonCode ?? '',
          'reason_note',
          reason?.reasonNote ?? '',
        ],
        changes: [`${newStatus}`],
        beforeChanges: [`${order.status}`],
        tableName: 'orders',
        recordId: order?.id,
        creatorId: SystemIdEnum?.systemCraw3pl,
      });
      this.logRepository.insert(log).catch(err => {
        if (err?.driverError) console.log(err?.driverError?.detail);
        return err;
      });
      return new Nack();
    } else {
      const updateData: any = !!typeUpdateStatus
        ? {
            status: newStatus,
            lastUpdatedBy: user?.id,
            typeUpdateStatus,
            lastUpdateStatus,
            reasonCode: !!reason?.reasonCode ? reason?.reasonCode : order?.reasonCode,
            reasonNote: !!reason?.reasonNote ? reason?.reasonNote : order?.reasonNote,
            lastUpdateReason: !!reason?.reasonCode ? new Date() : order?.lastUpdateReason,
          }
        : {
            status: newStatus,
            lastUpdatedBy: user?.id,
          };

      let tags = [];
      if (order?.lastCarrier?.extraData?.courier_name) {
        tags = order?.tags;
        const tagInFFM = await this.tagService.getTagByContent(
          [order?.lastCarrier?.extraData?.courier_name],
          Number(user?.companyId),
          order?.countryId,
        );
        if (tagInFFM?.[0]) {
          const tagLookup = find(order?.tags, (it: Tag) => it.id == tagInFFM?.[0]?.id);
          if (!tagLookup) tags.push(tagInFFM?.[0]);
        } else {
          const newTag = new Tag();
          newTag.content = order?.lastCarrier?.extraData?.courier_name;
          newTag.companyId = Number(user?.companyId);
          newTag.modules = [TypeOfTagEnum.Order];
          newTag.countryId = order.countryId;
          newTag.color = '#D4E0FF';
          tags.push(newTag);
        }
        isSyncTag = true;
      }

      const responseUpdate = await this.odRepository
        .update({ id: order?.id }, updateData)
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });

      if (responseUpdate?.affected == 0 || !responseUpdate?.affected) {
        throw new BadRequestException('save order affected = 0');
      }

      // if (status == OrderFFMStatus.Returned)
      //   await this.amqpConnection.publish('stock-inventory-service', 'order-changed-stock-v2', {
      //     id: order?.id,
      //     user: {
      //       id: order?.creatorId,
      //       companyId: order?.companyId,
      //     },
      //     oldStatus: order?.status,
      //     newStatus: OrderFFMStatus.Returned,
      //     lastUpdateStatus: new Date(),
      //     updatedAt: moment()?.valueOf(),
      //     isSyncToPartner: true,
      //   });

      if (tags?.length > 0)
        await this.odRepository
          .save({
            ...order,
            ...updateData,
            tags,
          })
          .catch(err => {
            if (err?.driverError) console.log(err?.driverError?.detail);
            return err;
          });
    }

    if (
      order?.id &&
      [OrderFFMStatus.ReturnedStocked, OrderFFMStatus.ReturnedCompleted].includes(newStatus)
    ) {
      this.amqpConnection.publish('ffm-order', 'ffm-queue-auto-generate-remittance', {
        orderId: order?.id,
      });
    }

    console.log('update status when calc inventory', 'success', payload);

    if (!!order?.externalId)
      this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
        id: order?.id,
        user,
        updatedAt: lastUpdateStatus,
        isSyncTag,
      });
    if (ORDER_STATUS_CAN_UPDATE_TICKET_STATUS?.includes(newStatus))
      this.updateTicketStatus(order?.id, user, newStatus);
    // if (status == OrderFFMStatus.ReturnedStocked) this.updateOrderAwaitingStock(order, user);
    return new Nack();
  }

  async updateTicketStatus(id: number, user: any, status: OrderFFMStatus) {
    if (!!id) {
      await this.amqpConnection.publish('ffm-order', 'order-update-ticket-status', {
        id,
        user,
        status,
      });
    }
  }

  async updateOrderAwaitingStock(order: Order, user: any) {
    const lstVarId: number[] = [];

    // export combo => single product
    order?.products?.forEach((item: OrderProduct) => {
      if (item?.productDetail?.product?.isCombo) {
        item?.productDetail?.product?.combo?.forEach((pcv: ProductComboVariant) => {
          lstVarId.push(pcv?.variantId);
        });
      } else {
        lstVarId.push(item?.productId);
      }
    });

    if (lstVarId?.length > 0) {
      const variants = await this.stRepository.find({
        where: {
          variantId: In(lstVarId),
          warehouseId: order?.warehouseId,
          bizId: user?.companyId,
        },
      });

      for (const e of variants) {
        if (e?.stockedPhySellable > 0)
          await this.amqpConnection.publish('ffm-order', 'stock-inventory-changed', {
            id: e?.variantId,
            user,
            warehouseId: Number(order?.warehouseId),
            quantity: e?.stockedPhySellable,
          });
      }

      return true;
    }
  }

  @RabbitRPC({
    exchange: 'ffm-order-external',
    routingKey: 're-import-order-cancel-2-1',
    queue: 'queue-external-re-import-order-cancel-2-1',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async getListReturnOrder({ ids, user }) {
    const { companyId } = user;

    const mQuery = this.reImportRepository.createQueryBuilder('r');
    mQuery.andWhere({
      companyId,
    });

    if (!!ids) {
      mQuery.andWhere({
        id: In(ids),
      });
    }
    mQuery.select(['r.id', 'r.orderId', 'r.orderDisplayId']);

    return mQuery.getMany();
  }

  @RabbitRPC({
    exchange: 'ffm-sync-order',
    routingKey: 'sync-order-2-1',
    queue: 'queue-sync-order-2-1',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async syncAgOrder(payload) {
    console.log('payload', payload);

    if (!payload?.id || !payload?.user) {
      console.log('integration', 'Not found id | user');
      return new Nack();
    }
    const { id, user, updatedAt, isSyncInfo, isSyncTag, notSyncStatus } = payload;
    const order = await this.odRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
          companyId: user?.companyId,
        });
      },
      relations: ['carriers', 'reason', 'tags', 'carriers.carrier', 'products'],
    });

    if (!order?.id || !order?.externalId) {
      console.log('integration', 'Not found order | externalId');
      return new Nack();
    }

    //..................
    if (order?.lastCarrier?.status == CarrieStatusEnum.activated) {
      if (
        ![
          OrderFFMStatus.Delivered,
          OrderFFMStatus.Returned,
          OrderFFMStatus.ReturnedStocked,
          OrderFFMStatus.ReturnedCompleted,
          OrderFFMStatus.DeliveredCompleted,
          OrderFFMStatus.ReturnedDamagedBy3PL,
          OrderFFMStatus.DamagedByWH,
          OrderFFMStatus.LostByWH,
          OrderFFMStatus.LostBy3PL,
          OrderFFMStatus.DamagedCompleted,
          OrderFFMStatus.LostCompleted,
        ].includes(order?.status)
      ) {
        const owQuery = this.owRepository
          .createQueryBuilder('ow')
          .where('ow.order_id = :orderId', { orderId: payload?.orderId })
          .andWhere('ow.warning_type = :warningType', { warningType: WarningType.ODZ })
          .andWhere('ow.carrier_id <> :carrierId', { carrierId: order?.lastCarrier?.carrierId });

        const owOdz = await owQuery.getMany();

        await Promise.all([
          !isEmpty(owOdz) &&
            this.owRepository.delete({
              id: In(owOdz.map(x => x.id)),
              warningType: WarningType.ODZ,
              // isOdz3pl: false,
            }),
          this.owRepository.delete({
            orderId: order?.id,
            warningType: WarningType.CancelWaybill,
          }),
        ]);
      }
    } else if (
      order?.lastCarrier?.status == CarrieStatusEnum.canceled ||
      isEmpty(order?.carriers)
    ) {
      if (
        ![
          OrderFFMStatus.New,
          OrderFFMStatus.Draft,
          OrderFFMStatus.Confirmed,
          OrderFFMStatus.Reconfirm,
          OrderFFMStatus.AwaitingStock,
          // OrderFFMStatus.AwaitingCollection,
        ].includes(order?.status)
      ) {
        await this.amqpConnection.publish('ffm-order', 'warning-order', {
          orderId: order?.id,
        });
      }
    }

    //..................

    if (order?.externalCode == ExternalOrderType.ag) {
      const apiKey = await this.apiKeyRepository.findOne({
        where: { clientId: order?.clientId },
      });
      if (!apiKey?.apiKey) {
        console.log('integration-ag', 'Not found order | api key');
        return new Nack();
      }

      let actor = 'FFM';
      if (![SystemIdEnum?.system, SystemIdEnum?.ag]?.includes(user?.id)) {
        const { data: UData } = await this.amqpConnection.request({
          exchange: 'identity-service-roles',
          routingKey: 'get-users-by-ids',
          payload: { ids: [user?.id] },
          timeout: 10000,
        });

        const users = UData as User[];
        actor = `${users[0]?.displayId ?? users[0]?.id}-${users[0]?.name}`;
      }

      // const products = [];
      const products: Record<string, any> = reduce(
        order?.products,
        (prev, item) => {
          prev.push({
            productSKU: item?.productDetail?.originSku,
            productName: item?.productDetail?.name,
            quantity: item?.quantity,
            weight: item?.weight,
          });
          return prev;
        },
        [],
      );
      await Promise.all([
        order?.tags &&
          isSyncTag == true &&
          this.amqpConnection.publish('order-service', 'sync-tags-from-ffm', {
            displayId: order?.displayId,
            externalId: order?.externalId,
            tags: order?.tags,
            actor: actor ?? '',
            updatedAt: moment(updatedAt)?.valueOf(),
          }),
        !notSyncStatus &&
          this.amqpConnection.publish('order-service', 'sync-status-from-ffm', {
            displayId: order?.displayId,
            externalId: order?.externalId,
            status: order?.status,
            reason: order?.reason ? `${order?.reason?.prefix}-${order?.reason?.reason}` : '',
            description: order?.reasonNote ?? '',
            actor: actor ?? '',
            updatedAt: moment(updatedAt)?.valueOf(),
            clientId: order.clientId,
          }),
        isSyncInfo == SyncInfoEnum.Full &&
          this.amqpConnection.publish(
            'order-service',
            'sync-info-order-from-ffm',
            plainToInstance(FfmToAgOrderInfoDto, {
              ...order,
              products,
              creator: user?.fullname,
            }) as FfmToAgOrderInfoDto,
          ),
        isSyncInfo == SyncInfoEnum.ALittle &&
          this.amqpConnection.publish(
            'order-service',
            'sync-info-order-from-ffm',
            plainToInstance(FfmToAgOrderInfoDto, {
              displayId: order?.displayId,
              updatedAt: order?.updatedAt,
              products,
              waybillNote: order?.waybillNote,
              creator: user?.fullname,
            }) as FfmToAgOrderInfoDto,
          ),
      ]);
    }

    if (order?.externalCode == ExternalOrderType.pancake && !!PANCAKE_STATUS_ORDER[order?.status]) {
      const shop = await this.upsRepo
        .createQueryBuilder('o')
        .andWhere('o.user_id = :clientId', { clientId: order?.clientId })
        .andWhere('o.country_id = :countryId', { countryId: order?.countryId })
        .andWhere('o.companyId = :companyId', { companyId: order?.companyId })
        .getOne();

      const wwproxy = new WWPROXY(this.redisService);

      // const proxy = await wwproxy.getCurrentProxyWithCheck();
      // console.log('🐔  ~ PancakeService ~ proxy:', proxy, order.displayId);

      try {
        const response = await this.carrierService.retryFunc(async () => {
          const proxy = await wwproxy.getCurrentProxyWithCheck();
          return await axios.request({
            method: 'GET',
            baseURL: process.env.PANCAKE_URL_API,
            url: `/shops/${shop?.shopId}/orders`,
            params: {
              search: order?.externalId,
              api_key: shop?.apiKey,
              page_size: 1000,
            },
            httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
            timeout: 30000,
          });
        });
        const tags = order?.tags.map(x => x.content).join(', ');
        const pancakeOrder = response?.data?.data?.[0];
        const extendUpdate = pancakeOrder?.partner?.extend_update;
        const pancakeNote = pancakeOrder?.note ?? '';

        const params: Record<string, any> = {
          status: PANCAKE_STATUS_ORDER[order?.status],
          partner: {
            order_number_vtp: order?.lastCarrier?.waybillNumber,
            partner_id: PancakePFGId,
            extend_code: order?.displayId,
          },
          tags: pancakeOrder?.tags ?? [],
          note: !isEmpty(order?.tags)
            ? `${
                pancakeNote?.includes('Tag:') ? pancakeNote.split('Tag:')[0].trim() : pancakeNote
              }\n Tag: ${tags}`
            : `${
                pancakeNote?.includes('Tag:') ? pancakeNote.split('Tag:')[0].trim() : pancakeNote
              }`,
        };

        if (PANCAKE_TAG_MAPPING_ORDER?.[order?.status]) {
          const filterTags = filter(params?.tags, function(o) {
            return !PANCAKE_TAG_ORDER_POS?.includes(o?.id);
          });
          filterTags.push(PANCAKE_TAG_MAPPING_ORDER?.[order?.status]);
          params.tags = filterTags;
        }

        if (!!shop) {
          try {
            // const res = await axios.request({
            //   method: 'PUT',
            //   baseURL: process.env.PANCAKE_URL_API,
            //   url: `/shops/${shop?.shopId}/orders/${order?.externalId}`,
            //   params: {
            //     api_key: shop?.apiKey,
            //   },
            //   data: params,
            //   httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
            //   timeout: 30000,
            // });
            const res = await this.carrierService.retryFunc(async () => {
              const proxy = await wwproxy.getCurrentProxyWithCheck();
              return await axios.request({
                method: 'PUT',
                baseURL: process.env.PANCAKE_URL_API,
                url: `/shops/${shop?.shopId}/orders/${order?.externalId}`,
                params: {
                  api_key: shop?.apiKey,
                },
                data: params,
                httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
                timeout: 30000,
              });
            });
            console.log(
              `sync order to Pancake ${order.displayId} => ${order.externalId}`
            );
          } catch (error) {
            const e = error as AxiosError;
            console.error(
              `sync order to Pancake error ${order.displayId} => ${order.externalId}`,
              e.response.data,
            );
            if (e.response?.status == 422) return new Nack();
            throw e.response?.data;
          }

          if (order?.status == OrderFFMStatus.FailedDelivery) {
            try {
              const res = await this.carrierService.retryFunc(async () => {
                const proxy = await wwproxy.getCurrentProxyWithCheck();
                return await axios.request({
                  method: 'POST',
                  baseURL: process.env.PANCAKE_URL_API,
                  url: `/pfg/update?api_token=$2b$12$C9lZxTZeejj8TrqXI5mYpuGvEBDvIz/BWoFjhSfoiUywoaSIanzQa`,
                  data: {
                    display_id: order?.displayId,
                    partner_order_id: order?.lastCarrier?.waybillNumber,
                    status_text: $enum(OrderFFMStatus).getKeyOrDefault(Number(order?.status), null),
                    extend_updates: [
                      {
                        key: order?.lastCarrier?.id,
                        note: `${
                          order?.reason ? `${order?.reason?.prefix}-${order?.reason?.reason}: ` : ''
                        } ${order?.reasonNote}`,
                        status: $enum(OrderFFMStatus).getKeyOrDefault(Number(order?.status), null),
                        tracking_id: order?.displayId,
                        update_at: order?.lastUpdateReason,
                      },
                    ],
                  },
                  httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
                  timeout: 30000,
                });
              });
              console.log(
                `FailedDelivery`,
                `sync order to Pancake ${order.displayId} => ${order.externalId}`
              );
            } catch (error) {
              const e = error as AxiosError;
              console.error(
                `sync order to Pancake error ${order.displayId} => ${order.externalId}`,
                e.response.data,
              );
              if (e.response?.status == 422) return new Nack();
              throw e.response?.data;
            }
          }
        }
      } catch (error) {
        console.log('🐔  ~ FFMQueueExternalService ~ syncAgOrder ~ error:', order.displayId, error);
      }
    }

    if (order?.clientId) {
      const orderCarrier = order?.carriers?.find(x => x?.status == 'activated');
      const orderData = {
        // id: order?.id,
        displayId: order?.displayId,
        status: $enum(OrderFFMStatus).getKeyOrDefault(order?.status, null),
        clientId: order?.clientId,
        totalPrice: order?.totalPrice,
        shippingFee: order?.shippingFee,
        subTotal: order?.subTotal,
        serviceInsurance: order?.serviceInsurance,
        surcharge: order?.surcharge,
        discount: order?.discount,
        discountPercentage: order?.discountPercentage,
        // createdAt: order?.createdAt,
        carrier: orderCarrier?.carrier?.name,
        waybillNumber: orderCarrier?.waybillNumber,
        externalId: order?.externalId,
      };
      // await this.amqpConnection.publish('ffm-order', 'web-hook-ag', {
      //   data: orderData,
      // });
      //-------------------
      const res: any = await this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'check-client-web-hook',
        payload: {
          clientId: orderData?.clientId,
        },
        timeout: 10000,
      });

      if (res?.data?.webHook) {
        const requestBody = JSON.stringify(orderData);

        const signature = SignatureUtils.gen(res?.data?.apiKey, requestBody);

        const headers = {
          Accept: 'application/json',
          'Content-Type': 'application/json;charset=UTF-8',
          'ag-signature': signature,
        };

        try {
          const result: any = await axios.request({
            url: res?.data?.webHook,
            method: 'POST',
            headers,
            data: requestBody,
          });
          console.log(`web-hook-ag`, result?.data, requestBody);
          if (!result?.data || result?.data?.success == false) console.log('Empty response');
        } catch (error) {
          console.log('Error web-hook-ag', error?.message ?? error);
        }
      }
      //-------------------
    }

    return new Nack();
  }
}
