import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Carrier } from 'apps/ffm-order-api/src/entities/carrier.entity';
import { ODZConfiguration } from 'apps/ffm-order-api/src/entities/odz-configuration.entity';
import { OrderCarrier } from 'apps/ffm-order-api/src/entities/order-carrier.entity';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { CarrieStatusEnum } from 'apps/ffm-order-api/src/enums/order-status.enum';
import { FilterODZ } from 'apps/ffm-order-api/src/filters/odz.filter';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import ExcelUtils from 'core/utils/ExcelUtils';
import e from 'express';
import { reduce, take } from 'lodash';
import { Not, Repository } from 'typeorm';
import { ODZService } from './odz.service';
import { OrderService } from './order.service';
import { CarrierCode, CarrierName } from 'core/enums/carrier-code.enum';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { SystemIdEnum } from 'core/enums/user-type.enum';
import { ODZ } from 'apps/ffm-order-api/src/entities/odz.entity';
import ExcelUtilsV2 from 'core/utils/ExcelUtilsV2';
import { FilterCarrier } from 'apps/ffm-order-api/src/filters/carrier-configuration.filter';
import { LeadTime } from 'apps/ffm-order-api/src/entities/lead-time.entity';

@Injectable()
export class CarrierService {
  constructor(
    @InjectRepository(Carrier, orderConnection)
    private carRepository: Repository<Carrier>,
    @InjectRepository(OrderCarrier, orderConnection)
    private orderCarRepository: Repository<OrderCarrier>,
    @InjectRepository(Order, orderConnection)
    private orderRepository: Repository<Order>,
    @InjectRepository(ODZConfiguration, orderConnection)
    private readonly amqpConnection: AmqpConnection,
  ) {}

  // fetch all carriers with basic information
  async fetchAll() {
    return this.carRepository
      .createQueryBuilder('c')
      .select(['c.id', 'c.name', 'c.avatar', 'c.code'])
      .getMany();
  }

  async getCarriers(filters: FilterCarrier, headers: Record<string, any>, request: Record<string, any>): Promise<Carrier[]> {
    const countryId = headers['country-ids'];
    const companyId = request?.user?.companyId;
    const query = this.carRepository.createQueryBuilder('carrier');
    if(filters?.leadTime){
      query.leftJoinAndSelect('carrier.leadTime', 'leadTime', 'country_id = :countryId AND company_id = :companyId', {countryId, companyId})
    } 

    const carriers = await query.getMany();
    return carriers;
  }

  async importExcel(body, buffer: Buffer, request): Promise<any> {
    const companyId = request?.user?.companyId;
    const mapCol = {
      soCode: 'S.O CODE',
      carrier: 'CARRIER',
      waybillNumberOld: 'WAYBILL NUMBER OLD',
      waybillNumberNew: 'WAYBILL NUMBER NEW',
    };

    const data = await ExcelUtilsV2.read(buffer, 0, 'NO');

    const result = { success: 0, error: 0, detailError: [] };

    const waybillNumberList = [];
    const soCodeList = [];
    const carrierList = [];
    const waybillNumberOldList = [];

    for (const item of data) {
      if (item[mapCol.soCode]) {
        const orderCarrier = new OrderCarrier();
        const waybillNumberNew = item[mapCol.waybillNumberNew];
        const soCode = item[mapCol.soCode];
        const carrierCode = item[mapCol.carrier];
        orderCarrier.waybillNumber = item[mapCol.waybillNumberOld];

        waybillNumberList.push({
          waybillNumberNew,
          soCode,
          carrierCode,
          waybillNumber: orderCarrier.waybillNumber,
        });
        soCodeList.push(soCode);
        waybillNumberOldList.push(orderCarrier.waybillNumber);
        if (!carrierList.includes(carrierCode)) carrierList.push(carrierCode);
      }
    }

    const [orderList, carrierListData] = await Promise.all([
      this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.carriers', 'carriers')
        .leftJoinAndSelect('carriers.carrier', 'carrier')
        .select([
          'order.id',
          'order.displayId',
          'carriers.creatorId',
          'carriers.id',
          'carriers.waybillNumber',
          'carriers.status',
          'carriers.carrierId',
          'carrier.name',
          'order.recipientCountryId',
          'order.recipientWardId',
          'order.recipientPostCode',
          'order.recipientDistrictId',
        ])
        .where('order.display_id IN (:...soCodeList)', { soCodeList })
        .andWhere('order.companyId = :companyId', { companyId })
        .andWhere('carriers.waybill_number IN (:...waybillNumberOldList)', { waybillNumberOldList })
        .getMany(),
      this.carRepository
        .createQueryBuilder('carrier')
        .select(['carrier.id', 'carrier.name'])
        .where('carrier.name IN (:...carrierList)', { carrierList })
        .getMany(),
    ]);
    // throw new Error("");

    const orderCarrierUpdateList = [];
    const orderCarrierInsertList = [];

    const carrierLookup: Record<string, any> = reduce(
      data,
      (prev, item) => {
        prev[item['WAYBILL NUMBER NEW']] = item;
        return prev;
      },
      {},
    );

    if (waybillNumberList.length > 0) {
      for (const item of waybillNumberList) {
        const existOrder = orderList.find(x => x.displayId === item.soCode);
        const existCarrier = carrierListData.find(x => x.code === item.carrierName);

        if (!existOrder) {
          result.error++;
          result.detailError.push('Đơn hàng ' + item.soCode + ' không tồn tại');
          continue;
        }

        if (!existCarrier) {
          result.error++;
          result.detailError.push('Đơn vị vận chuyển ' + item.carrierCode + ' không tồn tại');
          continue;
        }

        if (
          carrierLookup[item?.waybillNumberNew]?.CARRIER == CarrierName.bestexpress &&
          existOrder?.countryId == '60' &&
          existOrder?.lastCarrier?.carrier?.name == CarrierName.ninjavan
        ) {
          await this.amqpConnection.publish('ffm-order', 'auto-create-odz', {
            user: request?.user,
            odz: plainToInstance(ODZ, {
              companyId: request?.user?.companyId,
              carrierId: existOrder?.lastCarrier?.carrierId,
              countryId: Number(existOrder?.countryId),
              provinceId: existOrder?.recipientProvinceId,
              districtId: existOrder?.recipientDistrictId,
              wardId: existOrder?.recipientWardId,
              postCode: existOrder?.recipientPostCode,
              note: `Auto-add ODZ ${existOrder?.displayId}`,
              isSystemCreated: true,
              creatorId: SystemIdEnum.system,
              lastUpdatedBy: SystemIdEnum.system,
            }),
          });
        }

        if (existOrder && existCarrier) {
          const exist = existOrder.carriers?.find(x => x.waybillNumber == item.waybillNumber);
          if (exist) {
            orderCarrierUpdateList.push({ ...exist, status: CarrieStatusEnum.canceled });

            orderCarrierInsertList.push(
              plainToInstance(OrderCarrier, {
                status: CarrieStatusEnum.activated,
                carrierId: existCarrier.id,
                waybillNumber: item.waybillNumberNew,
                creatorId: request.user.id,
                lastUpdatedBy: request.user.id,
                orderId: existOrder.id,
              }),
            );
          }
        }
      }
    }

    // throw new Error("");

    await this.orderCarRepository.save(orderCarrierUpdateList).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    const dataUpdate = await this.orderCarRepository.save(orderCarrierInsertList).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    result.success = dataUpdate.length;
    return result;
  }
}
