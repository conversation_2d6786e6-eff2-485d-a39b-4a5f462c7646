import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Carrier } from 'apps/ffm-order-api/src/entities/carrier.entity';
import { FilterCollection } from 'apps/ffm-order-api/src/entities/filter-collection.entity';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { RaiseTicketNote } from 'apps/ffm-order-api/src/entities/raise-ticket-note.entity';
import { RaiseTicket } from 'apps/ffm-order-api/src/entities/raise-ticket.entity';
import { FilterDashboardPtPerformance } from 'apps/ffm-order-api/src/filters/dashboard.filter';
import { OrderCarrier } from 'apps/order-api/src/entities/order-carrier.entity';
import { orderConnection } from 'core/constants/database-connection.constant';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { Brackets, Repository, SelectQueryBuilder, getConnection } from 'typeorm';
import * as moment from 'moment-timezone';
import { OrderStatusHistories } from 'apps/ffm-order-api/src/entities/order-status-history.entity';
import {
  RaiseTicketStatusEnum,
  RaiseTicketTypeEnum,
} from 'apps/ffm-order-api/src/enums/raise-ticket.enum';
import { isEmpty, isNil, reduce, uniq } from 'lodash';
import { Permission } from 'core/enums/permission-ffm.enum';
import { UserStatus } from 'core/enums/user-status.enum';
import { User } from 'core/entities/identity/user.entity';
import xlsx, { WorkSheet } from 'node-xlsx';

import { $enum } from 'ts-enum-util';
@Injectable()
export class DashboardPtPerformanceService {
  constructor(
    @InjectRepository(RaiseTicket, orderConnection)
    private ticketRepository: Repository<RaiseTicket>,
    @InjectRepository(Carrier, orderConnection)
    private carrierRepository: Repository<Carrier>,
    @InjectRepository(RaiseTicketNote, orderConnection)
    private noteRepository: Repository<RaiseTicketNote>,
    @InjectRepository(OrderCarrier, orderConnection)
    private orderCarrierRepository: Repository<OrderCarrier>,
    @InjectRepository(Order, orderConnection)
    private orderRepository: Repository<Order>,
    @InjectRepository(FilterCollection, orderConnection)
    private filterRepo: Repository<FilterCollection>,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  getSubQueryBuilder(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): SelectQueryBuilder<unknown> {
    const { companyId } = request?.user;
    const countryIds = headers['country-ids']?.split(',');
    const { from, to } = filters;
    const qb = this.orderRepository
      .createQueryBuilder('o')
      .select('o.id', 'order_id')
      .leftJoin('o.tickets', 'rt')
      .leftJoin('rt.notes', 'notes')
      .innerJoin(
        OrderStatusHistories,
        'logs',
        `(o.id = logs.order_id AND logs.status IN (${OrderFFMStatus.Delivered}) AND logs.updated_at > notes.created_at)`,
      )
      .leftJoin(
        OrderStatusHistories,
        'subLogs',
        `(o.id = subLogs.order_id AND subLogs.status IN (${OrderFFMStatus.PickedUp3PL}))`,
      )
      .where(`rt.company_id = ${companyId}`)
      .andWhere('o.country_id IN (:...countryIds)', { countryIds });
    // .andWhere('logs.updated_at > notes.created_at');
    if (from)
      qb.andWhere('subLogs.updated_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      qb.andWhere('subLogs.updated_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });
    return qb;
  }

  getLastNoteQueryBuilder(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): SelectQueryBuilder<unknown> {
    const { companyId } = request?.user;
    const countryIds = headers['country-ids']?.split(',');
    const { from, to } = filters;
    const lastNoteQuery = this.noteRepository
      .createQueryBuilder('note')
      // .select(`order.id`)
      .select(`MAX ( "note"."id" )`, `note_id`)
      .leftJoin(`note.ticket`, `rt`)
      .leftJoin('rt.order', 'order')
      .where(`rt.company_id = ${companyId}`)
      .andWhere('order.country_id IN (:...countryIds)', { countryIds })
      .groupBy(`order.id`);
    if (from)
      lastNoteQuery.andWhere('order.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      lastNoteQuery.andWhere('order.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    return lastNoteQuery;
  }
  async getRescueRateByDate(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const countryIds = headers['country-ids']?.split(',');
    const { from, to, carrierIds, clientIds, productIds, ptIds, whIds } = filters;
    const handoverOrderStatus = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.Delivered,
      OrderFFMStatus.AwaitingReturn,
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
      OrderFFMStatus.DeliveredCompleted,
    ];

    const uncountedTicketStatus = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
    ];

    const returned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];

    const delivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];

    const ticketStatus = [
      RaiseTicketStatusEnum.Assigned,
      // RaiseTicketStatusEnum['Re-Open'],
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Resolved,
    ];
    const subSql = this.orderRepository
      .createQueryBuilder('o')
      .select(`max(osh.updated_at)`, 'updated_at')
      .addSelect('osh.order_id', 'order_id')
      .leftJoin('o.tickets', 'rt')
      .innerJoin(
        OrderStatusHistories,
        'osh',
        `(o.id = osh.order_id AND osh.status = ${OrderFFMStatus.PickedUp3PL})`,
      )
      .where(`o.company_id = ${companyId}`)
      .andWhere('o.country_id IN (:...countryIds)', { countryIds })
      // .where(`o.status In (:...handoverOrderStatus)`, { handoverOrderStatus })
      .andWhere(`o.status NOT IN (:...canceled)`, { canceled })
      .groupBy('osh.order_id');
    if (from)
      subSql.andWhere('osh.updated_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      subSql.andWhere('osh.updated_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (whIds) {
      subSql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    const qb = this.getSubQueryBuilder(filters, headers, request);
    const lastNoteQuery = this.getLastNoteQueryBuilder(filters, headers, request);
    const sql = this.orderRepository
      .createQueryBuilder('o')
      .select(
        `DATE("picked_up_3pl"."updated_at" AT TIME ZONE '-7')`,
        'OrderUpdateStatus3plPickedUp',
      )
      .addCommonTableExpression(() => qb, 'sub_cte')
      .addCommonTableExpression(() => lastNoteQuery, 'last_note')
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${handoverOrderStatus}) THEN o."id" ELSE NULL END )`,
        'HandoverOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${returned}) THEN o."id" ELSE NULL END )`,
        'ReturnOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${delivered}) THEN o."id" ELSE NULL END )`,
        'DeliveryOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${handoverOrderStatus}) AND rt.status IN (${ticketStatus}) AND rt.assignee IS NOT NULL THEN o."id" ELSE NULL END )`,
        'ProceedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) AND rt.assignee IS NOT NULL THEN rt."id" ELSE NULL END )`,
        'RaisedTickets',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${uncountedTicketStatus}) AND "rt"."status" IN (${ticketStatus}) AND rt.assignee IS NOT NULL THEN o."id" ELSE NULL END )`,
        'UncountedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${returned}) AND rt.type = ${RaiseTicketTypeEnum.RetryDelivery} AND "rt"."status" IN (${ticketStatus}) AND rt.assignee IS NOT NULL AND notes.id IS NULL THEN o."id" ELSE NULL END )`,
        'ReturnedUnprocessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${returned}) AND rt.type = ${RaiseTicketTypeEnum.RetryDelivery} AND "rt"."status" IN (${ticketStatus}) AND notes."id" IN (SELECT note_id FROM last_note) AND rt.assignee IS NOT NULL AND "logs"."updated_at" > "notes"."created_at" AND "logs".status = ${OrderFFMStatus.InReturn} THEN o."id" ELSE NULL END )`,
        'ReturnedProcessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${returned}) AND rt.type IN (${RaiseTicketTypeEnum.RetryDelivery}, ${RaiseTicketTypeEnum.RushDelivery}) AND "rt"."status" IN (${ticketStatus}) AND notes."id" IN (SELECT note_id FROM last_note) AND rt.assignee IS NOT NULL AND "logs"."updated_at" < "notes"."created_at" AND "logs".status = ${OrderFFMStatus.InReturn} THEN o."id" ELSE NULL END )`,
        'ReturnedLateProcessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${delivered}) AND "rt"."status" IN (${ticketStatus}) AND rt.assignee IS NOT NULL AND (notes.id IS NULL OR o.id NOT IN (SELECT order_id FROM sub_cte)) THEN o."id" ELSE NULL END )`,
        'DeliveredUnprocessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${delivered}) AND rt.type = ${RaiseTicketTypeEnum.NotifyDelivery} AND "rt"."status" IN (${ticketStatus}) AND rt.assignee IS NOT NULL AND notes.id IS NOT NULL AND o.id IN (SELECT order_id FROM sub_cte) THEN o."id" ELSE NULL END )`,
        'DeliveredNotifyDeliveryOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${delivered}) AND rt.type IN (${RaiseTicketTypeEnum.RetryDelivery}, ${RaiseTicketTypeEnum.RushDelivery}) AND "rt"."status" IN (${ticketStatus}) AND rt.assignee IS NOT NULL AND notes.id IS NOT NULL THEN o."id" ELSE NULL END )`,
        'DeliveredRescuedOrders',
      )
      .leftJoin('o.tickets', 'rt')
      .leftJoin('rt.notes', 'notes')
      .leftJoin(
        OrderStatusHistories,
        'logs',
        `(o.id = logs.order_id AND logs.status = ${OrderFFMStatus.InReturn})`,
      )
      .innerJoin(
        `(${subSql.getQuery()})`,
        'picked_up_3pl',
        'picked_up_3pl.order_id = o.id',
        subSql.getParameters(),
      )
      .where(`o.company_id = ${companyId}`)
      .andWhere('o.country_id IN (:...countryIds)', { countryIds })
      .andWhere('rt.type IN (:...type)', {
        type: [
          RaiseTicketTypeEnum.NotifyDelivery,
          RaiseTicketTypeEnum.RushDelivery,
          RaiseTicketTypeEnum.RetryDelivery,
        ],
      })
      .groupBy(`DATE("picked_up_3pl"."updated_at" AT TIME ZONE '-7')`)
      .orderBy(`DATE("picked_up_3pl"."updated_at" AT TIME ZONE '-7')`, 'DESC');

    if (carrierIds) {
      sql
        .leftJoin('o.carriers', 'carriers')
        .andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }
    if (clientIds) {
      sql.andWhere('o.client_id IN (:...clientIds)', { clientIds });
    }
    if (whIds) {
      sql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    if (productIds) {
      sql
        .leftJoin('o.products', 'products')
        .andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (ptIds) {
      sql.andWhere('rt.assignee IN (:...ptIds)', { ptIds });
    }
    return sql.getRawMany();
  }

  async getRescueRateByStaff(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const countryIds = headers['country-ids']?.split(',');
    const { from, to, carrierIds, clientIds, productIds, ptIds, whIds } = filters;
    const handoverOrderStatus = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.Delivered,
      OrderFFMStatus.AwaitingReturn,
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
      OrderFFMStatus.DeliveredCompleted,
    ];

    const uncountedTicketStatus = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
    ];

    const returned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];

    const delivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];

    const ticketStatus = [
      RaiseTicketStatusEnum.Assigned,
      // RaiseTicketStatusEnum['Re-Open'],
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Resolved,
    ];

    const payload: Record<string, any> = {
      filter: {
        role: [Permission.ptManager, Permission.ptView],
        countryIds,
        status: [UserStatus.active],
        companyIds: [companyId],
      },
    };

    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-filter',
      payload: payload,
      timeout: 10000,
    });
    const userIds = data.map(x => x.id);
    const users = data as User[];
    const userLookup: Record<string, User> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    const subSql = this.orderRepository
      .createQueryBuilder('o')
      .select(`max(osh.updated_at)`, 'updated_at')
      .addSelect('osh.order_id', 'order_id')
      .innerJoin(
        OrderStatusHistories,
        'osh',
        `(o.id = osh.order_id AND osh.status = ${OrderFFMStatus.PickedUp3PL})`,
      )
      .where(`o.company_id = ${companyId}`)
      .andWhere('o.country_id IN (:...countryIds)', { countryIds })
      // .where(`o.status In (:...handoverOrderStatus)`, { handoverOrderStatus })
      .andWhere(`o.status NOT IN (:...canceled)`, { canceled })
      .groupBy('osh.order_id');
    if (from)
      subSql.andWhere('osh.updated_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      subSql.andWhere('osh.updated_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (whIds) {
      subSql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    const qb = this.getSubQueryBuilder(filters, headers, request);
    const lastNoteQuery = this.getLastNoteQueryBuilder(filters, headers, request);
    const sql = this.ticketRepository
      .createQueryBuilder('rt')
      .select(`rt.assignee`, 'assignee')
      .addCommonTableExpression(() => qb, 'sub_cte')
      .addCommonTableExpression(() => lastNoteQuery, 'last_note')
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${handoverOrderStatus}) THEN o."id" ELSE NULL END )`,
        'ProceedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${handoverOrderStatus}) THEN rt."id" ELSE NULL END)`,
        'RaisedTickets',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${uncountedTicketStatus}) AND "rt"."status" IN (${ticketStatus}) THEN o."id" ELSE NULL END )`,
        'UncountedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${returned}) AND rt.type = ${RaiseTicketTypeEnum.RetryDelivery} AND notes.id IS NULL THEN o."id" ELSE NULL END )`,
        'ReturnedUnprocessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${returned}) AND rt.type = ${RaiseTicketTypeEnum.RetryDelivery} AND notes.id IN (SELECT note_id FROM last_note) AND "logs"."updated_at" > "notes"."created_at" AND "logs".status = ${OrderFFMStatus.InReturn} THEN o."id" ELSE NULL END )`,
        'ReturnedProcessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${returned}) AND rt.type IN (${RaiseTicketTypeEnum.RetryDelivery}, ${RaiseTicketTypeEnum.RushDelivery}) AND notes.id IN (SELECT note_id FROM last_note) AND "logs"."updated_at" < "notes"."created_at" AND "logs".status = ${OrderFFMStatus.InReturn} THEN o."id" ELSE NULL END )`,
        'ReturnedLateProcessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${delivered}) AND "rt"."status" IN (${ticketStatus}) AND (notes.id IS NULL OR o.id NOT IN (SELECT order_id FROM sub_cte)) THEN o."id" ELSE NULL END )`,
        'DeliveredUnprocessedOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${delivered}) AND rt.type = ${RaiseTicketTypeEnum.NotifyDelivery} AND notes.id IS NOT NULL AND o.id IN (SELECT order_id FROM sub_cte) THEN o."id" ELSE NULL END )`,
        'DeliveredNotifyDeliveryOrders',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "o"."status" IN (${delivered}) AND rt.type IN (${RaiseTicketTypeEnum.RetryDelivery}, ${RaiseTicketTypeEnum.RushDelivery}) AND notes.id IS NOT NULL THEN o."id" ELSE NULL END )`,
        'DeliveredRescuedOrders',
      )
      .leftJoin('rt.order', 'o')
      .leftJoin('rt.notes', 'notes')
      .leftJoin(
        OrderStatusHistories,
        'logs',
        `(o.id = logs.order_id AND logs.status = ${OrderFFMStatus.InReturn})`,
      )
      .innerJoin(
        `(${subSql.getQuery()})`,
        'picked_up_3pl',
        'picked_up_3pl.order_id = o.id',
        subSql.getParameters(),
      )
      .where('o.country_id IN (:...countryIds)', { countryIds })
      .andWhere(`rt.status != ${RaiseTicketStatusEnum.Closed}`)
      .andWhere(`o.company_id = ${companyId}`)
      .andWhere(`"rt"."status" IN (:...ticketStatus)`, { ticketStatus })
      .andWhere('rt.type IN (:...type)', {
        type: [
          RaiseTicketTypeEnum.NotifyDelivery,
          RaiseTicketTypeEnum.RushDelivery,
          RaiseTicketTypeEnum.RetryDelivery,
        ],
      })
      .andWhere(`rt.assignee IN (:...userIds)`, { userIds })
      .andWhere(`o.company_id = ${companyId}`)
      .groupBy('assignee');

    if (carrierIds) {
      sql
        .leftJoin('o.carriers', 'carriers')
        .andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }
    if (clientIds) {
      sql.andWhere('o.client_id IN (:...clientIds)', { clientIds });
    }
    if (whIds) {
      sql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    if (productIds) {
      sql
        .leftJoin('o.products', 'products')
        .andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (ptIds) {
      sql.andWhere('rt.assignee IN (:...ptIds)', { ptIds });
    }
    const result = await sql.getRawMany();

    for (const item of result) {
      item[`name`] = userLookup[item?.assignee]?.name;
    }

    return result.sort((a, b) => a?.name.localeCompare(b?.name));
  }

  async exportRescueRate(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const dataByDate = await this.getRescueRateByDate(filters, headers, request);
    const dataByStaff = await this.getRescueRateByStaff(filters, headers, request);
    const getDatesInRange = (startDate: Date, endDate: Date) => {
      const date = new Date(startDate.getTime());
      const dates = [];
      while (date <= endDate) {
        dates.push(moment(date).toDate());
        date.setDate(date.getDate() + 1);
      }
      return dates;
    };
    const customDataByDate = [];
    const dateArr = getDatesInRange(
      new Date(filters?.from.getTime() + 7 * 60 * 60 * 1000),
      new Date(filters?.to.getTime() + 7 * 60 * 60 * 1000),
    );

    for (let index = 0; index < dateArr.length; index++) {
      const element = dateArr[index];
      const data = dataByDate.find(x => x?.OrderUpdateStatus3plPickedUp.getTime() == element.getTime());
      if (data) customDataByDate.unshift(data);
      else
        customDataByDate.unshift({
          OrderUpdateStatus3plPickedUp: element,
          HandoverOrders: '0',
          ProceedOrders: '0',
          RaisedTickets: '0',
          UncountedOrders: '0',
          ReturnedUnprocessedOrders: '0',
          ReturnedProcessedOrders: '0',
          ReturnedLateProcessedOrders: '0',
          DeliveredUnprocessedOrders: '0',
          DeliveredNotifyDeliveryOrders: '0',
          DeliveredRescuedOrders: '0',
        });
    }

    let paramsByDate = [];
    let paramsByStaff = [];

    paramsByDate = reduce(
      customDataByDate,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            next?.OrderUpdateStatus3plPickedUp ?? '',
            next?.HandoverOrders ?? '',
            next?.ProceedOrders ?? '',
            next?.RaisedTickets ?? '',
            next?.UncountedOrders ?? '',
            next?.ReturnedUnprocessedOrders ?? '',
            next?.ReturnedProcessedOrders ?? '',
            next?.ReturnedLateProcessedOrders ?? '',
            next?.DeliveredUnprocessedOrders ?? '',
            next?.DeliveredNotifyDeliveryOrders ?? '',
            next?.DeliveredRescuedOrders ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'Date',
          'Handover Orders',
          'Proceed Orders',
          'Raised Tickers',
          'Uncounted Tickets',
          'Returned (Unprocessed)',
          'Returned(Processed)',
          'Returned (Late Processed)',
          'Delivered(Unprocessed)',
          'Delivered (Notified Delivery)',
          'Delivered(Rescued)',
        ],
      ],
    );
    paramsByStaff = reduce(
      dataByStaff,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            next?.name ?? '',
            next?.RaisedTickets ?? '',
            next?.UncountedOrders ?? '',
            next?.ReturnedUnprocessedOrders ?? '',
            next?.ReturnedProcessedOrders ?? '',
            next?.ReturnedLateProcessedOrders ?? '',
            next?.DeliveredUnprocessedOrders ?? '',
            next?.DeliveredNotifyDeliveryOrders ?? '',
            next?.DeliveredRescuedOrders ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'Date',
          'Raised Tickers',
          'Uncounted Tickets',
          'Returned (Unprocessed)',
          'Returned(Processed)',
          'Returned (Late Processed)',
          'Delivered(Unprocessed)',
          'Delivered (Notified Delivery)',
          'Delivered(Rescued)',
        ],
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push(
      {
        name: 'Dates',
        data: paramsByDate,
        options: {},
      },
      {
        name: 'Staff',
        data: paramsByStaff,
        options: {},
      },
    );
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }

  async addField(query) {
    for (const item of $enum(RaiseTicketTypeEnum).getKeys()) {
      query
        .addSelect(
          `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.Resolved}) AND rt."type" = ${RaiseTicketTypeEnum[item]} AND notes."id" IS NULL THEN rt."id" ELSE NULL END )`,
          `${item}Unprocess`,
        )
        .addSelect(
          `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.Assigned}) AND rt."type" = ${RaiseTicketTypeEnum[item]} THEN rt."id" ELSE NULL END )`,
          `${item}AwProcess`,
        )
        .addSelect(
          `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.InProcess}, ${RaiseTicketStatusEnum.FollowUp}) AND rt."type" = ${RaiseTicketTypeEnum[item]} THEN rt."id" ELSE NULL END )`,
          `${item}Processing`,
        )
        .addSelect(
          `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.Resolved}) AND rt."type" = ${RaiseTicketTypeEnum[item]} AND notes."id" IS NOT NULL THEN rt."id" ELSE NULL END )`,
          `${item}Resolved`,
        );
    }
    return query;
  }

  async getTicketTypeByDate(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const { from, to, carrierIds, clientIds, productIds, ptIds, whIds, isTotal } = filters;

    const ticketStatus = [
      RaiseTicketStatusEnum.New,
      RaiseTicketStatusEnum.Assigned,
      RaiseTicketStatusEnum['Re-Open'],
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Resolved,
    ];
    const ticketStatusAssigned = [
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Assigned,
      RaiseTicketStatusEnum.Resolved,
    ];
    let sql = this.ticketRepository.createQueryBuilder('rt');

    if (isTotal) {
      sql.select(`COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) AND o.status != ${OrderFFMStatus.Canceled} THEN o."id" ELSE NULL END )`, 'ProceedOrders');
    } else {
      sql
        .select(`DATE("rt"."created_at" AT TIME ZONE '-7')`, 'RtCreatedAt')
        .addSelect(`COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) AND o.status != ${OrderFFMStatus.Canceled} THEN o."id" ELSE NULL END )`, 'ProceedOrders')
        .groupBy(`DATE("rt"."created_at" AT TIME ZONE '-7')`)
        .orderBy(`DATE("rt"."created_at" AT TIME ZONE '-7')`, 'DESC');
    }

    sql
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) THEN rt."id" ELSE NULL END )`,
        'TotalTickets',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatusAssigned}) AND rt.assignee IS NOT NULL THEN rt."id" ELSE NULL END )`,
        'AssignedTickets',
      )

      .leftJoin('rt.order', 'o')
      .leftJoin('rt.notes', 'notes')
      .andWhere(`o.company_id = ${companyId}`);
    sql = await this.addField(sql);
    if (from)
      sql.andWhere('rt.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      sql.andWhere('rt.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds)) sql.andWhere('o.country_id IN (:...countryIds)', { countryIds });
    }

    if (carrierIds) {
      sql
        .leftJoin('o.carriers', 'carriers')
        .andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }
    if (clientIds) {
      sql.andWhere('o.client_id IN (:...clientIds)', { clientIds });
    }
    if (whIds) {
      sql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    if (productIds) {
      sql
        .leftJoin('o.products', 'products')
        .andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (ptIds) {
      sql.andWhere('rt.assignee IN (:...ptIds)', { ptIds });
    }
    return sql.getRawMany();
  }

  async getPTPerformanceOverview(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const { from, to, carrierIds, clientIds, productIds, ptIds, whIds } = filters;

    const ticketStatus = [
      RaiseTicketStatusEnum.New,
      RaiseTicketStatusEnum.Assigned,
      RaiseTicketStatusEnum['Re-Open'],
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Resolved,
    ];
    const assigntTicketStatus = [
      RaiseTicketStatusEnum.Assigned,
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Resolved,
    ];
    const sql = this.ticketRepository
      .createQueryBuilder('rt')
      .select(`COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) AND o.status != ${OrderFFMStatus.Canceled} THEN o."id" ELSE NULL END )`, 'ProceedOrders')
      .addSelect(`COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) THEN rt."id" ELSE NULL END )`, 'TotalTickets')
      .addSelect(
        `COUNT (DISTINCT CASE WHEN rt.status IN (${assigntTicketStatus}) and rt.assignee IS NOT NULL THEN rt.id ELSE NULL END)`,
        'AssignedTickets',
      )
      .addSelect(
        `COUNT (DISTINCT CASE WHEN rt.status = ${RaiseTicketStatusEnum.Resolved} and notes.id IS NULL THEN rt.id ELSE NULL END)`,
        'Unprocessed',
      )
      .addSelect(
        `COUNT (DISTINCT CASE WHEN rt.status = ${RaiseTicketStatusEnum.Assigned} THEN rt.id ELSE NULL END)`,
        'AwProcess',
      )
      .addSelect(
        `COUNT (DISTINCT CASE WHEN rt.status IN (${RaiseTicketStatusEnum.InProcess}, ${RaiseTicketStatusEnum.FollowUp}) THEN rt.id ELSE NULL END)`,
        'Processing',
      )
      .addSelect(
        `COUNT (DISTINCT CASE WHEN rt.status = ${RaiseTicketStatusEnum.Resolved} and notes.id IS NOT NULL THEN rt.id ELSE NULL END)`,
        'Resolved',
      )

      .leftJoin('rt.order', 'o')
      .leftJoin('rt.notes', 'notes')
      .andWhere(`o.company_id = ${companyId}`);
    if (from)
      sql.andWhere('rt.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      sql.andWhere('rt.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds)) sql.andWhere('o.country_id IN (:...countryIds)', { countryIds });
    }

    if (carrierIds) {
      sql
        .leftJoin('o.carriers', 'carriers')
        .andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }
    if (clientIds) {
      sql.andWhere('o.client_id IN (:...clientIds)', { clientIds });
    }
    if (whIds) {
      sql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    if (productIds) {
      sql
        .leftJoin('o.products', 'products')
        .andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (ptIds) {
      sql.andWhere('rt.assignee IN (:...ptIds)', { ptIds });
    }
    return sql.getRawMany();
  }

  async getTicketTypeByStaff(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const countryIds = headers['country-ids']?.split(',');
    const { from, to, carrierIds, clientIds, productIds, ptIds, whIds, isTotal } = filters;

    const ticketStatus = [
      RaiseTicketStatusEnum.New,
      RaiseTicketStatusEnum.Assigned,
      RaiseTicketStatusEnum['Re-Open'],
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Resolved,
    ];
    const ticketStatusAssigned = [
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Assigned,
      RaiseTicketStatusEnum.Resolved,
    ];

    const payload: Record<string, any> = {
      filter: {
        role: [Permission.ptView],
        countryIds,
        status: [UserStatus.active, UserStatus.deactivated],
        companyIds: [companyId],
      },
    };

    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-filter',
      payload: payload,
      timeout: 10000,
    });
    const userIds = data.map(x => x.id);
    const users = data as User[];
    const userLookup: Record<string, User> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    let sql = this.ticketRepository.createQueryBuilder('rt');

    if (isTotal) {
      sql.select(`COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) AND o.status != ${OrderFFMStatus.Canceled} THEN o."id" ELSE NULL END )`, 'ProceedOrders');
    } else {
      sql
        .select(`rt.assignee`, 'assignee')
        .addSelect(`COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) AND o.status != ${OrderFFMStatus.Canceled} THEN o."id" ELSE NULL END )`, 'ProceedOrders')
        .groupBy('assignee');
    }

    sql
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) THEN rt."id" ELSE NULL END )`,
        'TotalTickets',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatusAssigned}) and rt.assignee IS NOT NULL THEN rt."id" ELSE NULL END )`,
        'AssignedTickets',
      )
      .leftJoin('rt.order', 'o')
      .leftJoin('rt.notes', 'notes')
      .leftJoin(
        OrderStatusHistories,
        'logs',
        `(o.id = logs.order_id AND logs.status = ${OrderFFMStatus.InReturn})`,
      )
      .where(`o.company_id = ${companyId}`)
      .andWhere(
        new Brackets(qb => {
          qb.where(`rt.assignee IN (:...userIds)`, { userIds });
          if(isEmpty(clientIds) || isNil(clientIds)) qb.orWhere('rt.assignee IS NULL');
        }),
      );
    sql = await this.addField(sql);
    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds)) sql.andWhere('o.country_id IN (:...countryIds)', { countryIds });
    }
    //   .orderBy('RtCreatedAt', 'DESC');
    if (from)
      sql.andWhere('rt.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      sql.andWhere('rt.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (carrierIds) {
      sql
        .leftJoin('o.carriers', 'carriers')
        .andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }
    if (clientIds) {
      sql.andWhere('o.client_id IN (:...clientIds)', { clientIds });
    }
    if (whIds) {
      sql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    if (productIds) {
      sql
        .leftJoin('o.products', 'products')
        .andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (ptIds) {
      sql.andWhere('rt.assignee IN (:...ptIds)', { ptIds });
    }

    const result = await sql.getRawMany();
    if(isTotal) return result;
    for (const item of result) {
      item[`name`] = userLookup[item?.assignee]?.name ?? "< no assign >";
    }

    return [result.find(x => x.name == '< no assign >'), ...result.filter(x => x.name != '< no assign >')];
  }

  async getStaffAssignment(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const countryIds = headers['country-ids']?.split(',');
    const { from, to, carrierIds, clientIds, productIds, ptIds, whIds } = filters;

    const ticketStatus = [
      RaiseTicketStatusEnum.New,
      RaiseTicketStatusEnum.Assigned,
      RaiseTicketStatusEnum['Re-Open'],
      RaiseTicketStatusEnum.InProcess,
      RaiseTicketStatusEnum.FollowUp,
      RaiseTicketStatusEnum.Resolved,
    ];

    const payload: Record<string, any> = {
      filter: {
        role: [Permission.ptView],
        countryIds,
        status: [UserStatus.active, UserStatus.deactivated],
        companyIds: [companyId],
      },
    };

    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-filter',
      payload: payload, 
      timeout: 10000,
    });
    const userIds = data.map(x => x.id);
    const users = data as User[];
    const userLookup: Record<string, User> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    const sql = this.ticketRepository
      .createQueryBuilder('rt')
      .select(`rt.assignee`, 'assignee')
      .addSelect(`COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${ticketStatus}) THEN rt."id" ELSE NULL END )`, 'Tickets')
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.Assigned}) THEN rt."id" ELSE NULL END )`,
        'Assigned',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.InProcess}) THEN rt."id" ELSE NULL END )`,
        'InProcess',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.FollowUp}) THEN rt."id" ELSE NULL END )`,
        'FollowUp',
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN "rt"."status" IN (${RaiseTicketStatusEnum.Resolved}) THEN rt."id" ELSE NULL END )`,
        'Resolved',
      )
      .leftJoin('rt.order', 'o')
      .where(`o.company_id = ${companyId}`)
      .andWhere(`rt.assignee IN (:...userIds)`, { userIds })
      .groupBy('assignee');
    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds)) sql.andWhere('o.country_id IN (:...countryIds)', { countryIds });
    }
    //   .orderBy('RtCreatedAt', 'DESC');
    if (from)
      sql.andWhere('rt.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      sql.andWhere('rt.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (carrierIds) {
      sql
        .leftJoin('o.carriers', 'carriers')
        .andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }
    if (clientIds) {
      sql.andWhere('o.client_id IN (:...clientIds)', { clientIds });
    }
    if (whIds) {
      sql.andWhere('o.warehouse_id IN (:...whIds)', { whIds });
    }
    if (productIds) {
      sql
        .leftJoin('o.products', 'products')
        .andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (ptIds) {
      sql.andWhere('rt.assignee IN (:...ptIds)', { ptIds });
    }

    const result = await sql.getRawMany();

    for (const item of result) {
      item[`name`] = userLookup[item?.assignee]?.name;
    }
    return result;
  }

  async exportTicketType(
    filters: FilterDashboardPtPerformance,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const dataByDate = await this.getTicketTypeByDate(filters, headers, request);
    const dataByStaff = await this.getTicketTypeByStaff(filters, headers, request);
    const dataStaffAssignment = await this.getStaffAssignment(filters, headers, request);
    // const getDatesInRange = (startDate: Date, endDate: Date) => {
    //   const date = new Date(startDate.getTime());
    //   const dates = [];
    //   while (date <= endDate) {
    //     dates.push(moment(date).toDate());
    //     date.setDate(date.getDate() + 1);
    //   }
    //   return dates;
    // };
    const customDataByDate = dataByDate.sort((a, b) => b?.RtCreatedAt.getTime() - a?.RtCreatedAt.getTime());
    const customDataByStaff = dataByStaff.sort((a, b) => a?.name.localeCompare(b?.name));
    const customDataStaffAssignment = dataStaffAssignment.sort((a, b) => a?.name.localeCompare(b?.name));
    // const dateArr = getDatesInRange(
    //   new Date(filters?.from.getTime() + 7 * 60 * 60 * 1000),
    //   new Date(filters?.to.getTime() + 7 * 60 * 60 * 1000),
    // );

    // for (let index = 0; index < dateArr.length; index++) {
    //   const element = dateArr[index];
    //   const data = dataByDate.find(x => x?.RtCreatedAt.getTime() == element.getTime());
    //   if (data) customDataByDate.unshift(data);
    // }

    let paramsByDate = [];
    let paramsByStaff = [];
    let paramsStaffAssignment = [];

    const mergeConfig = [
      { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } }, // Merge cột A1 và A2
      { s: { r: 0, c: 1 }, e: { r: 1, c: 1 } }, // Merge cột B1 và B2
      { s: { r: 0, c: 2 }, e: { r: 1, c: 2 } }, // Merge cột C1 và C2
      { s: { r: 0, c: 3 }, e: { r: 1, c: 3 } }, // Merge cột D1 và D2
      { s: { r: 0, c: 4 }, e: { r: 0, c: 7 } }, // Merge cột D1, E1, F1
      { s: { r: 0, c: 8 }, e: { r: 0, c: 11 } }, // Merge cột G1, H1, I1
      { s: { r: 0, c: 12 }, e: { r: 0, c: 15 } }, // Merge cột J1, K1, L1
      { s: { r: 0, c: 16 }, e: { r: 0, c: 19 } },
    ];

    paramsByDate = reduce(
      customDataByDate,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            moment(next?.RtCreatedAt)
              .tz('Asia/Ho_Chi_Minh')
              .format('DD/MM/YYYY') ?? '',
            Number(next?.ProceedOrders) ?? '',
            Number(next?.TotalTickets) ?? '',
            Number(next?.AssignedTickets) ?? '',
            Number(next?.NotifyDeliveryUnprocess) ?? '',
            Number(next?.NotifyDeliveryAwProcess) ?? '',
            Number(next?.NotifyDeliveryProcessing) ?? '',
            Number(next?.NotifyDeliveryResolved) ?? '',
            Number(next?.RushDeliveryUnprocess) ?? '',
            Number(next?.RushDeliveryAwProcess) ?? '',
            Number(next?.RushDeliveryProcessing) ?? '',
            Number(next?.RushDeliveryResolved) ?? '',
            Number(next?.RetryDeliveryUnprocess) ?? '',
            Number(next?.RetryDeliveryAwProcess) ?? '',
            Number(next?.RetryDeliveryProcessing) ?? '',
            Number(next?.RetryDeliveryResolved) ?? '',
            Number(next?.ClaimUnprocess) ?? '',
            Number(next?.ClaimAwProcess) ?? '',
            Number(next?.ClaimProcessing) ?? '',
            Number(next?.ClaimResolved) ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'Date',
          'Processed Orders',
          'Total Tickets',
          'Assigned Tickets',
          'Notify',
          '',
          '',
          '',
          'Rush',
          '',
          '',
          '',
          'Retry',
          '',
          '',
          '',
          'Claim',
        ],
        [
          '',
          '',
          '',
          '',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
        ],
      ],
    );
    paramsByStaff = reduce(
      customDataByStaff,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            next?.name ?? '',
            Number(next?.ProceedOrders) ?? '',
            Number(next?.TotalTickets) ?? '',
            Number(next?.AssignedTickets) ?? '',
            Number(next?.NotifyDeliveryUnprocess) ?? '',
            Number(next?.NotifyDeliveryAwProcess) ?? '',
            Number(next?.NotifyDeliveryProcessing) ?? '',
            Number(next?.NotifyDeliveryResolved) ?? '',
            Number(next?.RushDeliveryUnprocess) ?? '',
            Number(next?.RushDeliveryAwProcess) ?? '',
            Number(next?.RushDeliveryProcessing) ?? '',
            Number(next?.RushDeliveryResolved) ?? '',
            Number(next?.RetryDeliveryUnprocess) ?? '',
            Number(next?.RetryDeliveryAwProcess) ?? '',
            Number(next?.RetryDeliveryProcessing) ?? '',
            Number(next?.RetryDeliveryResolved) ?? '',
            Number(next?.ClaimUnprocess) ?? '',
            Number(next?.ClaimAwProcess) ?? '',
            Number(next?.ClaimProcessing) ?? '',
            Number(next?.ClaimResolved) ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'Staff',
          'Processed Orders',
          'Total Tickets',
          'Assigned Tickets',
          'Notify',
          '',
          '',
          '',
          'Rush',
          '',
          '',
          '',
          'Retry',
          '',
          '',
          '',
          'Claim',
        ],
        [
          '',
          '',
          '',
          '',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
          'Unprocessed',
          'Aw Process',
          'Processing',
          'Resolved',
        ],
      ],
    );
    paramsStaffAssignment = reduce(
      customDataStaffAssignment,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            next?.name ?? '',
            Number(next?.Tickets) ?? '',
            Number(next?.Assigned) ?? '',
            Number(next?.InProcess) ?? '',
            Number(next?.FollowUp) ?? '',
            Number(next?.Resolved) ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'Staff',
          'Tickets',
          'Assigned',
          'In Process',
          'Follow up',
          'Resolved',
        ]
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push(
      {
        name: 'Dates',
        data: paramsByDate,
        options: { '!merges': mergeConfig },
      },
      {
        name: 'Staff (Performance)',
        data: paramsByStaff,
        options: { '!merges': mergeConfig },
      },
      {
        name: 'Staff Assignment',
        data: paramsStaffAssignment,
        options: {},
      },
    );
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }
}
