/* eslint-disable @typescript-eslint/no-floating-promises */
import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Users } from 'apps/ffm-catalog-api/src/read-entities/identity-entities/Users';
import { ID_COUNTRY_REQUIRE_INPUT_POST_CODE_SYNC } from 'apps/ffm-order-api/src/constants/odz.constants';
import { COLOR_DEFAULT_TAG, LAST_STATUS } from 'apps/ffm-order-api/src/constants/order.constants';
import {
  AddPancakeShopDto,
  CreateOrderPancakeDto,
  OrderPancakeDto,
  ProductPancakeDto,
  TagPancakeDto,
} from 'apps/ffm-order-api/src/dtos/pancake/pancake-shop.dto';
import { Customer } from 'apps/ffm-order-api/src/entities/customer.entity';
import { OrderProduct } from 'apps/ffm-order-api/src/entities/order-product.entity';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { Tag } from 'apps/ffm-order-api/src/entities/tags.entity';
import { UserPancakeShop } from 'apps/ffm-order-api/src/entities/user-pancake-shop.entity';
import { ExternalOrderType } from 'apps/ffm-order-api/src/enums/order-status.enum';
import { TypeOfTagEnum } from 'apps/ffm-order-api/src/enums/tag.enum';
import { UserPancakeShopFilter } from 'apps/ffm-order-api/src/filters/user-pancake-shop.filter';
import axios from 'axios';
import { plainToInstance } from 'class-transformer';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { CountryCode } from 'core/constants/country_code';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { CountryID } from 'core/enums/carrier-code.enum';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { compact, filter, find, isEmpty, reduce, sum, sumBy, unionBy, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import { In, Repository } from 'typeorm';
import { WWPROXY } from '../utils/wwproxy';
import { ICalcInventory } from './order.service';
import { TagService } from './tags.service';
import { StockInventoryInOrder } from './stock-inventory.service';
import { ProductsService } from './products.service';

export interface ICountry {
  name: string;
  dial_code: string;
  code: string;
  countryName?: string;
}

export interface IWarehouse {
  id: string;
  name: string;
  phoneNumber: string;
  address?: any;
  fullAddress: string;
  addressSplit?: string[];
  provinceId: string;
  districtId: string;
  communeId: string;
  status: string;
  returnWarehouseId: number;
}

@Injectable()
export class PancakeService {
  constructor(
    @InjectRepository(UserPancakeShop, orderConnection)
    private upsRepo: Repository<UserPancakeShop>,
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    @InjectRepository(Customer, orderConnection)
    private cusRepository: Repository<Customer>,
    private amqpConnection: AmqpConnection,
    private tagService: TagService,
    private productsService: ProductsService,
    private readonly stockInventoryInOrder: StockInventoryInOrder,
    private readonly redisService: RedisCacheService,
  ) {}

  // async scanThaiPostcode() {
  //   const orders = await this.odRepository.find({countryId: '66', recipientPostCode: 0});
  //   const shopLookup: Record<string, UserPancakeShop> = {}
  //   const shopOrders = groupBy(orders, 'clientId');
  //   const shops = await this.upsRepo.find({
  //     countryId: '66',
  //     userId: In(Object.keys(shopOrders).map(i => Number(i)))
  //   });
  //   console.log('shops', shops, Object.keys(shopOrders));
  //   const updateOrders = [];
  //   for (const shop of shops) {
  //     const response = await axios.request({
  //       method: 'GET',
  //       baseURL: process.env.PANCAKE_URL_API,
  //       url: `/shops/${shop.shopId}/orders`,
  //       params: {
  //         search: shopOrders[shop.userId].map(i => i.displayId)?.join(' '),
  //         api_key: shop.apiKey,
  //         page_size: 1000,
  //       },
  //       timeout: 60000,
  //     });
  //     console.log('shop', response.data.data.length, shopOrders[shop.userId].length)
  //     const orderLookup: Record<string, PancakeOrderDto> = reduce(response.data.data, (prev, item: PancakeOrderDto) => {
  //       prev[item.id] = item;
  //       return prev;
  //     }, {});
  //     for (const order of shopOrders[shop.userId]) {
  //       const item = orderLookup[order.externalId];
  //       if (!item) {
  //         continue;
  //       }
  //       const fullAddressSplit = item?.shipping_address?.full_address?.split(',') || [];
  //       const maybePostcode = fullAddressSplit[fullAddressSplit.length - 1]?.trim();
  //       if (`${maybePostcode}` === `${Number(maybePostcode)}`) {
  //         order.recipientPostCode = Number(maybePostcode);
  //         updateOrders.push(order);
  //       }
  //       console.log(order.displayId, '=>', item.id, '=>', order.recipientPostCode);
  //     }
  //   }
  //   await this.odRepository.save(updateOrders);
  // }

  async getPancake(
    clientId: number,
    url,
    query = {},
    headers: Record<string, string>,
    req: Record<string, any>,
  ) {
    if (!headers['country-ids']) throw new BadRequestException('Country IDs must be provided');
    const shop = await this.upsRepo
      .createQueryBuilder('o')
      .andWhere('o.user_id = :clientId', { clientId })
      .andWhere('o.country_id = :countryId', { countryId: headers['country-ids'] })
      .andWhere('o.companyId = :companyId', { companyId: req?.user?.companyId })
      .getOne();
    if (!shop) throw new NotFoundException();

    const wwproxy = new WWPROXY(this.redisService);

    const proxy = await wwproxy.getCurrentProxyWithCheck();
    console.log('🐔  ~ PancakeService ~ proxy:', proxy);

    const response = await axios.request({
      method: 'GET',
      baseURL: process.env.PANCAKE_URL_API,
      url: `/shops/${shop.shopId}${url ? '/' + url : ''}`,
      params: {
        ...query,
        api_key: shop.apiKey,
      },
      httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
      timeout: 60000,
    });
    return response.data;
  }

  async getShopTags(clientId: number, headers: Record<string, string>, req: Record<string, any>) {
    if (!headers['country-ids']) throw new BadRequestException('Country IDs must be provided');
    const shop = await this.upsRepo
      .createQueryBuilder('o')
      .andWhere('o.user_id = :clientId', { clientId })
      .andWhere('o.country_id = :countryId', { countryId: headers['country-ids'] })
      .andWhere('o.companyId = :companyId', { companyId: req?.user?.companyId })
      .getOne();
    if (!shop) throw new NotFoundException();

    const wwproxy = new WWPROXY(this.redisService);

    const proxy = await wwproxy.getCurrentProxyWithCheck();
    console.log('🐔  ~ PancakeService ~ proxy:', proxy);

    const response = await axios.request({
      method: 'GET',
      baseURL: process.env.PANCAKE_URL_API,
      url: `/shops/${shop.shopId}/orders/tags`,
      params: {
        api_key: shop.apiKey,
      },
      httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
      timeout: 60000,
    });
    return response.data;
  }

  async createPancakeOrder(body: CreateOrderPancakeDto, req: Record<string, any>) {
    const result = {
      success: [],
      error: [],
    };

    const { userId, countryId, ids } = body;

    const [shop, orders] = await Promise.all([
      this.upsRepo
        .createQueryBuilder('o')
        .andWhere('o.user_id = :clientId', { clientId: userId })
        .andWhere('o.country_id = :countryId', { countryId })
        .andWhere('o.companyId = :companyId', { companyId: req?.user?.companyId })
        .getOne(),
      this.odRepository
        .createQueryBuilder('od')
        .andWhere('od.companyId = :companyId', { companyId: req?.user?.companyId })
        .andWhere('od.clientId = :clientId', { clientId: userId })
        .andWhere('od.country_id = :countryId', { countryId })
        .andWhere('od.externalCode = :externalCode', { externalCode: ExternalOrderType.pancake })
        .andWhere('od.externalId IN (:...ids)', { ids })
        .getMany(),
    ]);

    if (!shop) throw new NotFoundException();
    const wwproxy = new WWPROXY(this.redisService);

    const proxy = await wwproxy.getCurrentProxyWithCheck();
    console.log('🐔  ~ PancakeService ~ proxy:', proxy);

    const response = await axios.request({
      method: 'GET',
      baseURL: process.env.PANCAKE_URL_API,
      url: `/shops/${shop.shopId}/orders`,
      params: {
        search: ids?.join(' '),
        api_key: shop.apiKey,
        page_size: 1000,
      },
      httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
      timeout: 60000,
    });
    if (!response.data?.data) return result;

    // let client: Users;
    // try {
    //   const { data } = await this.amqpConnection.request({
    //     exchange: 'identity-service-users',
    //     routingKey: 'find-user',
    //     payload: {
    //       filters: { ids: [userId] },
    //       header: {},
    //       request: { user: { companyId: req?.user?.companyId } },
    //     },
    //     timeout: 10000,
    //   });
    //   client = data[0] as Users;

    // } catch (error) {
    //   console.log(error);
    //   throw new BadRequestException();
    // }

    let warehouse: IWarehouse;
    try {
      const { data: wh }: { data: IWarehouse } = await this.amqpConnection.request({
        exchange: 'ffm-catalog-service-warehouses',
        routingKey: 'get-main-ffm-warehouse',
        payload: { companyId: req?.user?.companyId, countryId, clientId: Number(userId) },
        timeout: 10000,
      });
      warehouse = wh;
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Not found warehouse');
    }
    if (!warehouse?.id) throw new BadRequestException('Warehouse invalid');

    let products: any = [],
      tagInFFM: Tag[];
    const sku = [],
      phones = [];
    let tagNames: string[] = [];
    let isDangerous = false;

    response.data?.data?.forEach((item: OrderPancakeDto) => {
      item?.items.forEach((prod: ProductPancakeDto) => {
        if (!!prod?.variation_info?.display_id) sku.push(prod?.variation_info?.display_id);
      });
      if (item?.shipping_address?.phone_number) phones.push(item?.shipping_address?.phone_number);
      item?.tags?.map((tag: TagPancakeDto) => tagNames.push(tag.name));
    });

    const country: ICountry = find(CountryCode, { dial_code: `+${countryId}` });

    const customers = await this.cusRepository.find({
      where: { companyId: req?.user?.companyId, recipientPhone: In(phones) },
    });
    const customerLookup: Record<string, Customer> = reduce(
      customers,
      (prev, item) => {
        prev[item.recipientPhone] = item;
        return prev;
      },
      {},
    );

    tagNames = compact(tagNames);
    if (tagNames.length > 0) {
      tagInFFM = await this.tagService.getTagByContent(
        uniq(tagNames),
        req?.user?.companyId,
        countryId,
      );
    }

    try {
      // const res = await this.amqpConnection.request({
      //   exchange: 'ffm-catalog-service',
      //   routingKey: 'ffm-order-find-product',
      //   payload: {
      //     sku,
      //     clientId: userId,
      //     relations: ['product', 'properties', 'properties.attributes'],
      //     countryId,
      //   },
      //   timeout: 10000,
      // });
      // products = res?.data ?? [];
      products =
        (await this.productsService.findProduct({
          sku,
          clientId: userId,
          countryId,
        })) ?? [];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    const checkDangerous = products.find(x => x.isDangerous);
    if (checkDangerous) isDangerous = true;

    const params: Order[] = [];

    response.data?.data?.forEach((item: OrderPancakeDto) => {
      if (
        item?.shipping_address?.phone_number &&
        item?.shipping_address?.phone_number !=
          item?.shipping_address?.phone_number.replace(/\D/g, '')
      ) {
        result.error.push({
          id: item?.id,
          message: "Recipient's mobile phone is invalid.",
        });
        return;
      }
      if (ids?.map((it: any) => it?.toString()).includes(item?.id?.toString())) {
        const validOd = find(orders, {
          externalId: item?.id.toString(),
          externalCode: ExternalOrderType.pancake,
        });
        if (!!validOd) {
          if (
            ![
              OrderFFMStatus.Canceled,
              OrderFFMStatus.Draft,
              OrderFFMStatus.New,
              OrderFFMStatus.AwaitingStock,
              OrderFFMStatus.Reconfirm,
              OrderFFMStatus.Confirmed,
            ].includes(validOd.status)
          ) {
            result.error.push({
              id: item?.id,
              message:
                'Order cannot be synced as it is in preparation. Order in Confirmed or Reconfirm status are allowed.',
            });
            return;
          }

          const order = validOd;
          order.tags = unionBy(
            order.tags,
            filter(item.tags, (o: any) => {
              return !!o?.name;
            })?.map(tag => {
              const tagLookup = find(tagInFFM, it => it.content == tag.name);
              if (tagLookup) return tagLookup;
              const newTag = new Tag();
              newTag.content = tag.name;
              newTag.companyId = req?.user?.companyId;
              newTag.countryId = countryId;
              newTag.color = COLOR_DEFAULT_TAG;
              newTag.modules = [TypeOfTagEnum.Order];
              return newTag;
            }),
            'content',
          );
          console.log('order.tags', order.tags, item.tags);
          let customer = customerLookup[item?.shipping_address?.phone_number];
          const countryOrder: ICountry = find(CountryCode, {
            dial_code: `+${item?.shipping_address?.country_code}`,
          });
          if (!customer) customer = new Customer();
          customer = {
            ...customer,
            companyId: req?.user?.companyId,
            creatorId: req?.user?.id,
            recipientName: item?.shipping_address?.full_name,
            recipientPhone: item?.shipping_address?.phone_number,
            recipientAddress: item?.shipping_address?.address,
            recipientWard: item?.shipping_address?.commnue_name,
            recipientWardId: item?.shipping_address?.commune_id,
            recipientDistrict: item?.shipping_address?.district_name,
            recipientDistrictId: item?.shipping_address?.district_id,
            recipientProvince: item?.shipping_address?.province_name,
            recipientProvinceId: item?.shipping_address?.province_id,
            recipientCountry: countryOrder?.name,
            recipientCountryId: item?.shipping_address?.country_code,
          };
          order.customer = customer;
          order.recipientName = item?.shipping_address?.full_name;
          order.recipientPhone = item?.shipping_address?.phone_number;
          order.recipientAddress = item?.shipping_address?.address;
          order.recipientWard = item?.shipping_address?.commnue_name;
          order.recipientWardId = item?.shipping_address?.commune_id;
          order.recipientDistrict = item?.shipping_address?.district_name;
          order.recipientDistrictId = item?.shipping_address?.district_id;
          order.recipientProvince = item?.shipping_address?.province_name;
          order.recipientProvinceId = item?.shipping_address?.province_id;
          order.recipientPostCode = (Number(item?.shipping_address?.country_code) == CountryID.MY
            ? item?.shipping_address?.commune_id
            : item?.shipping_address?.post_code
          )
            ?.replace(/​/g, '')
            .trim();
          order.recipientCountry = countryOrder?.name;
          order.recipientCountryId = item?.shipping_address?.country_code;
          order.seller = item?.assigning_seller?.name;
          order.isDangerous = isDangerous;
          order.internalNote = item?.note ? `${item?.note ?? ''}` : null;
          order.waybillNote = item?.note_print
            ? `Other-${item?.note_print?.substring(0, 200)}`
            : '';
          order.customerEDD = item?.estimate_delivery_date;

          if (!order.recipientPostCode) {
            const fullAddressSplit = item?.shipping_address?.full_address?.split(',') || [];
            const maybePostcode = fullAddressSplit[fullAddressSplit.length - 1]?.trim();
            if (`${maybePostcode}` === `${Number(maybePostcode)}`) {
              order.recipientPostCode = maybePostcode.trim();
            }
            if (
              [CountryID.MY, CountryID.TH]?.includes(Number(item?.shipping_address?.country_code))
            ) {
              const maybePostcode = (Number(item?.shipping_address?.country_code) == CountryID.MY
                ? item?.shipping_address?.commune_id
                : item?.shipping_address?.post_code
              )
                ?.replace(/​/g, '')
                .trim();
              if (`${Number(maybePostcode)}` == `${maybePostcode}`) {
                order.recipientPostCode = maybePostcode.trim();
              } else order.recipientPostCode = order.recipientWardId;
            }
          }
          if (
            ID_COUNTRY_REQUIRE_INPUT_POST_CODE_SYNC?.includes(
              Number(item?.shipping_address?.country_code),
            )
          ) {
            if (!order?.recipientPostCode || order?.recipientPostCode.toString().length != 5) {
              result.error.push({
                id: item?.id,
                message: 'Postcode is empty or invalid format.',
              });
              return;
            }
          }
          order.clientId = userId;
          order.companyId = req?.user?.companyId;
          order.countryCode = country?.code;
          order.countryId = countryId;
          order.lastUpdatedBy = req?.user?.id;

          let validAddress = true;
          if (
            !order.recipientProvinceId
            // ||
            // (!order.recipientDistrictId &&
            //   !ID_COUNTRY_REQUIRE_INPUT_DISTRICT?.includes(
            //     Number(item?.shipping_address?.country_code),
            //   ))
          ) {
            validAddress = false;
          }
          // if (
          //   ID_COUNTRY_REQUIRE_INPUT_POST_CODE_SYNC?.includes(
          //     Number(item?.shipping_address?.country_code),
          //   ) &&
          //   (!order.recipientPostCode || order.recipientPostCode == '0')
          // ) {
          //   validAddress = false;
          // }

          // if (
          //   concat([CountryID.TH], ID_COUNTRY_REQUIRE_INPUT_WARD)?.includes(
          //     Number(item?.shipping_address?.country_code),
          //   ) &&
          //   !order?.recipientWardId
          // ) {
          //   validAddress = false;
          // }

          if (validAddress) {
            params.push(order);
          } else {
            result.error.push({
              id: item?.id,
              message: 'Lack of recipient’s address information',
            });
            return;
          }
        } else {
          const lstProd: OrderProduct[] = [];

          item?.items.forEach((prod: ProductPancakeDto) => {
            const validProd = find(products, { originSku: prod?.variation_info?.display_id });
            if (!!validProd) {
              if (Number(prod?.quantity) < 0)
                throw new BadRequestException('Không thể đồng bộ đơn có số lượng sản phẩm âm!');
              lstProd.push({
                ...new OrderProduct(),
                productId: validProd?.id,
                productName: validProd?.product?.name,
                productDetail: validProd,
                weight: Number(validProd?.weight ?? 0) * Number(prod?.quantity ?? 0),
                quantity: prod?.quantity,
                price: prod?.variation_info?.retail_price
                  ? [`${CountryID.TH}`, `${CountryID.MY}`]?.includes(
                      item?.shipping_address?.country_code,
                    )
                    ? Number(prod?.variation_info?.retail_price) / 100
                    : prod?.variation_info?.retail_price
                  : 0,
              });
            }
          });

          if (lstProd?.length != item?.items?.length) {
            result.error.push({
              id: item?.id,
              message: 'Product invalid',
            });
          } else {
            const order = new Order();
            order.tags = filter(item.tags, (o: any) => {
              return !!o?.name;
            })?.map(tag => {
              const tagLookup = find(tagInFFM, it => it.content == tag.name);
              if (tagLookup) return tagLookup;
              const newTag = new Tag();
              newTag.content = tag.name;
              newTag.companyId = req?.user?.companyId;
              newTag.countryId = countryId;
              newTag.color = COLOR_DEFAULT_TAG;
              newTag.modules = [TypeOfTagEnum.Order];
              return newTag;
            });

            order.products = lstProd;
            let customer = customerLookup[item?.shipping_address?.phone_number];
            const countryOrder: ICountry = find(CountryCode, {
              dial_code: `+${item?.shipping_address?.country_code}`,
            });
            if (!customer) customer = new Customer();
            customer = {
              ...customer,
              companyId: req?.user?.companyId,
              creatorId: req?.user?.id,
              recipientName: item?.shipping_address?.full_name,
              recipientPhone: item?.shipping_address?.phone_number,
              recipientAddress: item?.shipping_address?.address,
              recipientWard: item?.shipping_address?.commnue_name,
              recipientWardId: item?.shipping_address?.commune_id,
              recipientDistrict: item?.shipping_address?.district_name,
              recipientDistrictId: item?.shipping_address?.district_id,
              recipientProvince: item?.shipping_address?.province_name,
              recipientProvinceId: item?.shipping_address?.province_id,
              recipientCountry: countryOrder?.name,
              recipientCountryId: item?.shipping_address?.country_code,
            };
            order.customer = customer;
            order.recipientName = item?.shipping_address?.full_name;
            order.recipientPhone = item?.shipping_address?.phone_number;
            order.recipientAddress = item?.shipping_address?.address;
            order.recipientWard = item?.shipping_address?.commnue_name;
            order.recipientWardId = item?.shipping_address?.commune_id;
            order.recipientDistrict = item?.shipping_address?.district_name;
            order.recipientDistrictId = item?.shipping_address?.district_id;
            order.recipientProvince = item?.shipping_address?.province_name;
            order.recipientProvinceId = item?.shipping_address?.province_id;
            order.recipientPostCode = `${(Number(item?.shipping_address?.country_code) ==
            CountryID.MY
              ? item?.shipping_address?.commune_id
              : item?.shipping_address?.post_code) || ''}`
              .replace(/​/g, '')
              .trim();
            order.recipientCountry = countryOrder?.name;
            order.recipientCountryId = item?.shipping_address?.country_code;
            order.internalNote = item?.note ? `${item?.note ?? ''}` : null;
            order.waybillNote = item?.note_print
              ? `Other-${item?.note_print?.substring(0, 200)}`
              : '';
            order.customerEDD = item?.estimate_delivery_date;

            if (isEmpty(order.recipientPostCode)) {
              const fullAddressSplit = item?.shipping_address?.full_address?.split(',') || [];
              const maybePostcode = fullAddressSplit[fullAddressSplit.length - 1]?.trim();
              if (`${maybePostcode}` === `${Number(maybePostcode)}`) {
                order.recipientPostCode = maybePostcode;
              }
              if (
                ID_COUNTRY_REQUIRE_INPUT_POST_CODE_SYNC?.includes(
                  Number(item?.shipping_address?.country_code),
                )
              ) {
                const maybePostcode = `${(Number(item?.shipping_address?.country_code) ==
                CountryID.MY
                  ? item?.shipping_address?.commune_id
                  : item?.shipping_address?.post_code) || ''}`
                  .replace(/​/g, '')
                  .trim();
                if (`${Number(maybePostcode)}` == `${maybePostcode}`) {
                  order.recipientPostCode = maybePostcode;
                } else if (
                  `${Number(order.recipientWardId)}` == `${order.recipientWardId}` ||
                  `0${Number(order.recipientWardId)}` == `${order.recipientWardId}`
                )
                  order.recipientPostCode = order.recipientWardId;
              }
            }
            if (typeof order.recipientPostCode === 'string' && isEmpty(order.recipientPostCode)) {
              delete order.recipientPostCode;
            }
            // if (
            //   ID_COUNTRY_REQUIRE_INPUT_POST_CODE_SYNC?.includes(
            //     Number(item?.shipping_address?.country_code),
            //   ) &&
            //   !order.recipientPostCode
            // ) {
            //   result.error.push({
            //     id: item?.id,
            //     message: "Recipient's postcode is empty",
            //   });
            // }

            if (
              ID_COUNTRY_REQUIRE_INPUT_POST_CODE_SYNC?.includes(
                Number(item?.shipping_address?.country_code),
              )
            ) {
              if (!order?.recipientPostCode || order?.recipientPostCode.toString().length != 5) {
                result.error.push({
                  id: item?.id,
                  message: 'Postcode is empty or invalid format.',
                });
                return;
              }
            }
            order.totalWeight = sumBy(lstProd, 'weight');
            order.subTotal = item?.total_price;
            order.totalPrice = Number(item?.cod) - Number(item?.exchange_payment ?? 0);
            order.discount =
              item?.total_discount + sum(item?.items?.map(i => i.discount_each_product));
            order.paid = Number(item?.prepaid ?? 0) + Number(item?.exchange_payment ?? 0);
            order.surcharge = item?.surcharge ?? 0;
            order.shippingFee = item?.is_free_shipping ? 0 : item?.shipping_fee;

            if (['66', '60']?.includes(item?.shipping_address?.country_code)) {
              order.subTotal = Number(order.subTotal) / 100;
              order.totalPrice = Number(order.totalPrice) / 100;
              order.discount = Number(order.discount) / 100;
              order.paid = Number(order.paid) / 100;
              order.surcharge = Number(order.surcharge) / 100;
              order.shippingFee = Number(order.shippingFee) / 100;
            }

            order.clientId = userId;
            order.companyId = req?.user?.companyId;
            order.warehouseId = Number(warehouse?.id);
            order.returnWarehouseId = Number(warehouse?.returnWarehouseId);
            order.countryCode = country?.code;
            order.countryId = countryId;
            order.creatorId = req?.user?.id;
            order.externalCode = ExternalOrderType.pancake;
            order.externalId = item?.id.toString();
            order.status = OrderFFMStatus.AwaitingStock;
            order.lastUpdatedBy = req?.user?.id;
            order.seller = item?.assigning_seller?.name;

            let validAddress = true;
            if (
              !order.recipientProvinceId
              // ||
              // (!order.recipientDistrictId &&
              //   !ID_COUNTRY_REQUIRE_INPUT_DISTRICT?.includes(
              //     Number(item?.shipping_address?.country_code),
              //   ))
            ) {
              validAddress = false;
            }
            // if (
            //   ID_COUNTRY_REQUIRE_INPUT_POST_CODE_SYNC?.includes(
            //     Number(item?.shipping_address?.country_code),
            //   ) &&
            //   (!order.recipientPostCode || order.recipientPostCode == '0')
            // ) {
            //   validAddress = false;
            // }

            // if (
            //   concat([66], ID_COUNTRY_REQUIRE_INPUT_WARD)?.includes(
            //     Number(item?.shipping_address?.country_code),
            //   ) &&
            //   !order?.recipientWardId
            // ) {
            //   validAddress = false;
            // }

            if (validAddress) {
              params.push(order);
            } else {
              result.error.push({
                id: item?.id,
                message: 'Lack of recipient’s address information',
              });
              return;
            }
          }
        }
      }
    });

    if (params?.length > 0) {
      for (const order of params) {
        if (order.countryId != order.recipientCountryId) {
          let returnWarehouse;
          try {
            const { data: wh }: { data: IWarehouse } = await this.amqpConnection.request({
              exchange: 'ffm-catalog-service-warehouses',
              routingKey: 'get-ffm-return-default-warehouse',
              payload: { countryId: order.recipientCountryId, companyId: req?.user?.companyId },
              timeout: 10000,
            });
            returnWarehouse = wh;
          } catch (error) {
            console.log(error);
            throw new BadRequestException('Not found return warehouse');
          }
          if (returnWarehouse?.id) {
            order.returnWarehouseId = returnWarehouse?.id;
          }
        }
        const od = await this.odRepository.save(order).catch(err => {
          result.error.push({
            id: order?.externalId,
            message: err ?? 'An error occurred when you try to save',
          });
          return err;
        });
        console.log('🚀🚀🚀 ~ PancakeService ~ od ~ od:', od);
        if (!!od?.id) {
          const code = await this.odRepository.query(
            `SELECT id_encode( ${od?.id}, 'orders', 6,'1234567890QERTYUPLKJHGFDSAZXCVBNM' ) as id`,
          );
          result.success.push({
            id: order?.externalId,
          });

          if (![OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm]?.includes(od.status)) {
            const calcInventory = await this.stockInventoryInOrder.changeStockInventoryByOrder({
              id: order?.id,
              user: req?.user,
              oldStatus: OrderFFMStatus.AwaitingStock,
              newStatus: OrderFFMStatus.Confirmed,
              lastUpdateStatus: new Date(),
              updatedAt: moment()?.valueOf(),
              rollBackStatus: false,
              isRequestQueue: true,
            });
            if (calcInventory.status == 200) {
              await this.odRepository
                .update({ id: order?.id }, { status: OrderFFMStatus.Confirmed })
                .catch(err => {
                  console.log(err?.driverError?.detail);
                });
            }
            let variantIds = [];
            for (const item of od?.products) {
              if (item?.productDetail?.product?.isCombo == true) {
                variantIds = variantIds.concat(
                  item?.productDetail?.product?.combo?.map(x => x?.variantId),
                );
              } else {
                variantIds.push(item?.productId);
              }
            }
            await Promise.all([
              this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
                id: order?.id,
                user: req?.user,
                updatedAt: moment()?.valueOf(),
              }),
              this.amqpConnection.publish('stock-inventory-service', 'create-stock-inventory', {
                variantIds,
                warehouseId: od?.returnWarehouseId,
                companyId: od?.companyId,
              }),
            ]);
          }
        }
        if (!LAST_STATUS.includes(order?.status)) {
          await this.amqpConnection.publish('ffm-order', 'duplicate-order', {
            countryId: order?.countryId,
            companyId: order?.companyId,
            recipientPhone: order?.recipientPhone,
            productIds: order?.products?.map(x => x.productId),
            orderId: order.id,
          });
        }
      }
    }

    return result;
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'pancake-order',
    queue: 'ffm-queue-pancake-order',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async pancakeOrder(payload) {
    if (
      !payload?.id ||
      !payload?.user ||
      !payload?.shop ||
      !payload?.so ||
      !payload?.oId ||
      !payload?.countryId
    ) {
      console.log('ffm-pancake', 'Not found id | user');
      return new Nack();
    }
    const { id, user, shop, so, oId } = payload;
    const { data: calcInventory } = await this.amqpConnection.request<{
      data: ICalcInventory;
    }>({
      exchange: 'stock-inventory-service',
      routingKey: 'order-changed-stock-v2',
      payload: {
        id: oId,
        user,
        oldStatus: OrderFFMStatus.AwaitingStock,
        newStatus: OrderFFMStatus.Confirmed,
        lastUpdateStatus: new Date(),
        updatedAt: moment()?.valueOf(),
        rollBackStatus: false,
        isRequestQueue: true,
      },
      timeout: 10000,
    });
    if (calcInventory.status == 200) {
      await this.odRepository
        .update({ id: oId }, { status: OrderFFMStatus.Confirmed })
        .catch(err => {
          console.log(err?.driverError?.detail);
        });
    }
    await this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
      id: oId,
      user,
      updatedAt: moment()?.valueOf(),
    });
    return new Nack();
  }

  async addShop(body: AddPancakeShopDto, req: Record<string, any>) {
    const wwproxy = new WWPROXY(this.redisService);

    const proxy = await wwproxy.getCurrentProxyWithCheck();
    console.log('🐔  ~ PancakeService ~ proxy:', proxy);

    const response = await axios
      .request({
        method: 'GET',
        baseURL: process.env.PANCAKE_URL_API,
        url: '/shops/' + body.shopId,
        params: { api_key: body.apiKey },
        httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
        timeout: 60000,
      })
      .catch(err => {
        throw err;
      });

    if (!response.data?.success) {
      throw new BadRequestException(
        response.data,
        response.data.message || 'Shop id or api key are not valid',
      );
    }

    return this.upsRepo.save(
      plainToInstance(UserPancakeShop, {
        ...body,
        companyId: req?.user?.companyId,
        shopName: response.data.shop.name,
      }),
    );
  }

  async getConnectedShops(
    filter: UserPancakeShopFilter,
    pagination?: PaginationOptions,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<UserPancakeShop[]> {
    const qb = this.upsRepo
      .createQueryBuilder('o')
      .andWhere('o.companyId = :companyId', { companyId: request?.user?.companyId });

    const { userIds, countryIds } = filter;
    if (pagination) qb.take(pagination.limit).skip(pagination.skip);

    if (userIds) qb.andWhere('o.user_id IN (:...userIds)', { userIds });
    if (countryIds) qb.andWhere('o.country_id IN (:...countryIds)', { countryIds });
    const shops = await qb.getMany();

    let users: Users[];
    try {
      const clientIds = shops.map(it => it.userId);
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user',
        payload: {
          filters: { ids: clientIds },
          header: {},
          request: { user: { companyId: request?.user?.companyId } },
        },
        timeout: 10000,
      });
      users = data as Users[];
      console.log(`users`, users);
      const usersLookup = users.reduce((prev, item) => {
        prev[item.id] = item;
        return prev;
      }, {});

      console.log(`usersLookup`, usersLookup);

      return shops.map(it => {
        const item = plainToInstance(UserPancakeShop, { ...it });
        item.user = usersLookup[item.userId];
        return item;
      });
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }
  }
}
