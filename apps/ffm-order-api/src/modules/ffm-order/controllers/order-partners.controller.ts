import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Headers,
  Param,
  ParseArrayPipe,
  Post,
  Put,
  Query,
  Req,
  HttpCode,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { OrderCarriersService } from '../services/order-partners.service';
import { CancelAnywayDto, CorrectionOrderDto, CreateOrderPartnerDto } from '../../../dtos/create-order-partner.dto';
import { cachedRawBodyRequestKey } from 'core/interceptors/raw-requests.interceptors';
import { TypeUpdate3PL } from 'apps/ffm-order-api/src/enums/type-update-order-status.enum';
import { RawResponse } from 'core/raw/raw-response';
import * as crypto from 'crypto';

@Controller('order-partners')
@ApiTags('order-partners')
export class OrderPartnersController {
  constructor(private carrierService: OrderCarriersService) {}

  @Get(':id/extra-data')
  async getLabel(@Req() req: Record<string, any>, @Param('id') id): Promise<any> {
    return this.carrierService.getLabel(id);
  }

  @Get()
  async getTracking(
    @Req() req: Record<string, any>,
    @Query('waybill', new ParseArrayPipe({ separator: ',' })) waybills,
  ): Promise<any> {
    return this.carrierService.getWaybill(waybills);
  }

  @Post()
  @Auth()
  async create(@Req() req: Record<string, any>, @Body() data: CreateOrderPartnerDto): Promise<any> {
    if (!req?.user?.companyId) {
      throw new ForbiddenException('');
    }
    return this.carrierService.createOrder(data, req.user);
  }

  @Post('njv/webhook')
  // @Auth()
  async njvWebhook(
    @Body() data: Record<string, any>,
    @Headers() header: Record<string, any>,
  ): Promise<any> {
    await this.carrierService.handleNjvWebhook(data, header);
    return { message: 'sucess' };
  }

  @Post('nimbus/webhook')
  // @Auth()
  async nimbusWebhook(
    @Body() data,
    @Headers() header: Record<string, any>,
    @Req() request,
  ): Promise<any> {
    const nimbusSign = header['x-hmac-sha256'];
    const textBody = request[cachedRawBodyRequestKey]?.toString('utf-8');
    const ourSign = crypto
      .createHmac('sha256', process.env.NIMBUS_KEY)
      .update(textBody)
      .digest('base64');
    if (ourSign != nimbusSign) {
      throw new ForbiddenException('Key not valid');
    }
    await this.carrierService.handleNimbusWebhook(data);
    return { message: 'succeed' };
  }

  @Post('jnt-th/webhook')
  // @Auth()
  @HttpCode(200)
  async jntThaiWebhook(
    @Body() data: Record<string, any>,
    @Headers() header: Record<string, any>,
    @Req() request,
  ): Promise<any> {
    const textBody = request[cachedRawBodyRequestKey]?.toString('utf-8');
    const raw = textBody.split('"logistics_interface":')[1]?.slice(0, -1);
    const { data_digest, logistics_interface } = data;
    const logisticData = JSON.parse(logistics_interface);
    const { billcode, txlogisticid } = logisticData;
    return this.carrierService.handleJntThaiWebhook(logisticData, data_digest, logistics_interface);
  }

  @Post('jnt-ph/webhook')
  // @Auth()
  async jntPhilWebhook(
    @Body() data: Record<string, any>,
    @Headers() header: Record<string, any>,
    @Req() request
  ): Promise<any> {
    const textBody = request[cachedRawBodyRequestKey]?.toString('utf-8');
    const raw = textBody.split('"logistics_interface":')[1]?.slice(0, -1)
    const {data_digest, logistics_interface} = data;
    const logisticData = JSON.parse(logistics_interface);
    const {billcode, txlogisticid} = logisticData;
    await this.carrierService.handleJntThaiWebhook(logisticData, data_digest, logistics_interface);
    return new RawResponse({
      logisticproviderid: 'JT',
      responseitems: {
        billcode,
        txlogisticid,
        reason: '',
        success: 'true',
      },
    })
  }

  @Post('njv/self-webhook')
  // @Auth()
  async njvSelfWebhook(
    @Body() data: Record<string, any>,
    @Headers('key') key: string,
  ): Promise<any> {
    if (key !== process.env.NJV_SELF_KEY) {
      return { message: 'failed' };
    }
    await this.carrierService.handleNjvSelfWebhook(data);
    return { message: 'succeed' };
  }

  @Delete(':id')
  @Auth()
  async cancel(@Req() req: Record<string, any>, @Param('id') id: number): Promise<any> {
    if (!req?.user?.companyId) {
      throw new ForbiddenException('');
    }
    return this.carrierService.cancelOrder(id, req.user, { type: TypeUpdate3PL.CancelDelivery });
  }

  @Put('cancel/:id')
  @Auth()
  async cancel3PL(
    @Req() req: Record<string, any>,
    @Body() data: Record<string, any>,
    @Param('id') id: number,
  ): Promise<any> {
    if (!req?.user?.companyId) {
      throw new ForbiddenException('');
    }
    return this.carrierService.cancelOrder(id, req.user, {
      ...data,
      type: TypeUpdate3PL.BulkCancelDelivery,
    });
  }

  @Put('cancel-anyway/:id')
  @Auth()
  async cancelAnyWay3PL(
    @Req() req: Record<string, any>,
    @Body() data: CancelAnywayDto,
    @Param('id') id: number,
  ): Promise<any> {
    if (!req?.user?.companyId) {
      throw new ForbiddenException('');
    }
    return this.carrierService.cancelAnyway(id, req.user, data);
  }

  @Put(':id/tracking')
  @Auth()
  async updateTracking(@Req() req: Record<string, any>, @Param('id') id: number): Promise<any> {
    if (!req?.user?.companyId) {
      throw new ForbiddenException('');
    }
    return this.carrierService.updateTracking(id);
  }

  @Put(':id')
  @Auth()
  async update(@Req() req: Record<string, any>, @Param('id') id: number, @Body() data: CorrectionOrderDto): Promise<any> {
    if (!req?.user?.companyId) {
      throw new ForbiddenException('');
    }
    return this.carrierService.updateOrder(id, req.user, data);
  }
}
