import {
  Body,
  Controller,
  Get,
  Headers,
  Post,
  Query,
  Req,
  Request,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';
import { Carrier } from 'apps/ffm-order-api/src/entities/carrier.entity';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { CarrierService } from '../services/carriers.service';
import { FilterCarrier } from 'apps/ffm-order-api/src/filters/carrier-configuration.filter';
import axios from 'axios';
import { aesEncrypt } from '../utils/miaoshou';
// import { OrdersService } from '../services/orders.service';

@Controller('carriers')
@ApiTags('carriers')
export class CarrierController {
  constructor(private carrierService: CarrierService) {}

  @Get('public')
  @ApiExcludeEndpoint()
  async fetchAll() {
    return this.carrierService.fetchAll();
  }

  @Get()
  @Auth()
  async list(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterCarrier,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ): Promise<Carrier[]> {
    return this.carrierService.getCarriers(filters, headers, req);
  }

  @Post('/import')
  @Auth()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        name: {
          type: 'string',
          format: 'string',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importExcel(
    @Request() request,
    @Query() filters,
    @Body() body: [],
    @UploadedFile('file') file,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.carrierService.importExcel(body, buffer, request);
  }
}
