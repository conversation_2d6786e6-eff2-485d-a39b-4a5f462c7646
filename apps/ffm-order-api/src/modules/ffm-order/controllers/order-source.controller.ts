import { Body, Controller, Post, Req, Headers, Get, Query, Param, Put, Response, Delete, ParseIntPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CreateOrderPancakeDto } from 'apps/ffm-order-api/src/dtos/pancake/pancake-shop.dto';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { OrderSourceService } from '../services/order-source.service';
import { MarketplaceIntegration } from 'apps/ffm-order-api/src/entities/marketplace-integration.entity';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { FilterOrderSource, FilterSyncOrderFailed } from 'apps/ffm-order-api/src/filters/order-source.filter';
import { SyncOrderSourceHistory } from 'apps/ffm-order-api/src/entities/sync-order-source-history.entity';
import { OrderSyncFailedMPI } from 'apps/ffm-order-api/src/entities/order-sync-failed-mki.entity';
import { Permission } from 'core/enums/permission-ffm.enum';
import { CreateOrderSourceDto, UpdateOrderSourceDto } from 'apps/ffm-order-api/src/dtos/order-source/order-source.dto';

@Controller('order-source')
@ApiTags('order-source')
export class OrderSourceController {
  constructor(private orderSourceService: OrderSourceService) {}

  @Get()
  @Auth(Permission.orderSource)
  async getOrderSource(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterOrderSource,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.orderSourceService.getOrderSource(pagination, filters, headers, req);
  }

  @Get('count-market-place')
  @Auth(Permission.orderSource)
  async getCountOrderSource(
    // @Pagination() pagination: PaginationOptions,
    // @Query() filters: FilterOrderSource,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.orderSourceService.getCountOrderSource(headers, req);
  }

  @Get('sync-failed')
  @Auth(Permission.orderSource)
  async getOrderSourceSyncFailed(
    @Query() filters: FilterSyncOrderFailed,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<[OrderSyncFailedMPI[], number]> {
    return this.orderSourceService.getOrderSourceSyncFailed(filters, headers, req);
  }

  @Get('history/:id')
  @Auth(Permission.orderSource)
  async getOrderSourceDetailHistory(
    @Param('id') id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<SyncOrderSourceHistory[]> {
    return this.orderSourceService.getOrderSourceDetailHistory(id, headers, req);
  }

  @Get(':id')
  @Auth(Permission.orderSource)
  async getOrderSourceDetail(
    @Param('id') id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<MarketplaceIntegration> {
    return this.orderSourceService.getOrderSourceDetail(id, headers, req);
  }

  @Put(':id')
  @Auth(Permission.orderSource)
  async updateOrderSource(
    @Body() data: UpdateOrderSourceDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Param('id') id,
  ): Promise<MarketplaceIntegration> {
    return this.orderSourceService.updateOrderSource(data, headers, req, id);
  }

  @Post('')
  @Auth(Permission.orderSource)
  async createOrderSource(
    @Body() body: CreateOrderSourceDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<MarketplaceIntegration> {
    return await this.orderSourceService.createOrderSource(body, headers, req);
  }

  @Post('export')
  @Auth(Permission.orderSource)
  async exportSyncFailedData(
    @Response() response,
    @Body() query: FilterSyncOrderFailed,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    const buffer = await this.orderSourceService.exportExcel(query, req, headers);
    response.attachment(`OrderSyncFailed.xlsx`);
    return response.send(buffer);
  }

  @Delete(':id')
  @Auth(Permission.orderSource)
  async deleteOrderSource(
    @Param('id', ParseIntPipe) id: number,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<{ success: boolean; message: string }> {
    return this.orderSourceService.deleteOrderSource(id, headers, req);
  }

}
