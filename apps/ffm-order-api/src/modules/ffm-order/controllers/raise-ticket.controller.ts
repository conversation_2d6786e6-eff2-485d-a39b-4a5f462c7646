import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  Post,
  Put,
  Query,
  Req,
  Response,
} from '@nestjs/common';
import { RaiseTicketService } from '../services/raise-ticket.service';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { Permission } from 'core/enums/permission-ffm.enum';
import {
  BulkRaiseTicketDto,
  CloseRaiseTicketDto,
  CreateRaiseTicketAppointmentDto,
  CreateRaiseTicketDto,
  CreateRaiseTicketNoteDto,
  DeleteBulkRaiseTicketDto,
  UpdateBulkRaiseTicketDto,
  UpdateRaiseTicketAppointmentDto,
  UpdateRaiseTicketDto,
  UpdateTagBulkRaiseTicketDto,
} from 'apps/ffm-order-api/src/dtos/create-raise-ticket.dto';
import { RaiseTicket } from 'apps/ffm-order-api/src/entities/raise-ticket.entity';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { FilterRaiseTicket } from 'apps/ffm-order-api/src/filters/raise-ticket.filter';
import { RaiseTicketNote } from 'apps/ffm-order-api/src/entities/raise-ticket-note.entity';
import * as moment from 'moment-timezone';
import { TicketAppointment } from 'apps/ffm-order-api/src/entities/ticket-appointment.entity';

@Controller('raise-ticket')
@ApiTags('raise-ticket')
export class RaiseTicketController {
  constructor(private raiseTicketService: RaiseTicketService) {}

  @Get('search')
  @Auth(Permission.ptManager, Permission.ptView)
  async searchRaiseTicket(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterRaiseTicket,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<RaiseTicket[]> {
    return this.raiseTicketService.searchRaiseTicket(pagination, filters, headers, req);
  }

  @Get()
  @Auth(Permission.ptManager, Permission.ptView)
  async getRaiseTicket(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterRaiseTicket,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<[RaiseTicket[], number]> {
    return this.raiseTicketService.listRaiseTicket(pagination, filters, headers, req);
  }

  @Post('large')
  @Auth()
  async getLargeRaiseTicket(
    @Body() data: FilterRaiseTicket,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<[RaiseTicket[], number]> {
    return this.raiseTicketService.listRaiseTicket(
      { limit: data?.limit ?? 20, skip: data?.page >= 0 ? data?.page * (data?.limit ?? 20) : 0 },
      data,
      headers,
      req,
    );
  }

  @Get('count')
  @Auth()
  async countRaiseTicket(
    @Query() filters: FilterRaiseTicket,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.raiseTicketService.countRaiseTicket(filters, headers, req);
  }

  @Post('export')
  @Auth(Permission.bulkExportTicket)
  async exportData(
    @Response() response,
    @Body() query: FilterRaiseTicket,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    const buffer = await this.raiseTicketService.exportExcel(query, req, headers);
    response.attachment(`Ticket-${moment().format('DMYYYY-Hm')}.xls`);
    return response.send(buffer);
  }

  @Post('count')
  @Auth()
  async countLargeRaiseTicket(
    @Body() data: FilterRaiseTicket,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    return this.raiseTicketService.countRaiseTicket(data, headers, req);
  }

  @Post('appointments')
  @Auth()
  async getTicketAppointmentsData(
    @Body() data: FilterRaiseTicket,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    return this.raiseTicketService.getTicketAppointmentsData(
      {
        limit: data?.limit ?? 20,
        skip: data?.page > 0 ? (data?.page - 1) * (data?.limit ?? 20) : 0,
      },
      data,
      headers,
      req,
    );
  }

  @Post()
  @Auth()
  async createRaiseTicket(
    @Body() data: CreateRaiseTicketDto,
    @Req() req: Record<string, any>,
  ): Promise<RaiseTicket> {
    return this.raiseTicketService.createRaiseTicket(data, req);
  }

  @Post('bulk')
  @Auth(Permission.bulkRaiseTicket)
  async bulkRaiseTicket(
    @Body() data: BulkRaiseTicketDto,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.raiseTicketService.bulkRaiseTicket(data, req);
  }

  @Get('/:id')
  @Auth()
  async viewRaiseTicket(
    @Param('id') id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<RaiseTicket> {
    return this.raiseTicketService.viewRaiseTicket(id, req, headers);
  }

  @Put('update-bulk/')
  @Auth(Permission.bulkChangeAssignee)
  async updateBulkRaiseTicket(
    @Body() data: UpdateBulkRaiseTicketDto,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.raiseTicketService.updateBulkRaiseTicket(data, req);
  }

  @Put('update-bulk/delete')
  @Auth(Permission.bulkChangeAssignee)
  async deleteBulkRaiseTicket(
    @Body() data: DeleteBulkRaiseTicketDto,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.raiseTicketService.deleteBulkRaiseTicket(data, req);
  }

  @Put('add-tags-bulk/')
  @Auth(Permission.bulkAddTags)
  // @Auth()
  async addTagBulkRaiseTicket(
    @Body() data: UpdateTagBulkRaiseTicketDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.raiseTicketService.addTagBulkRaiseTicket(data, headers, req);
  }

  @Put('update/appointment')
  @Auth()
  async updateRaiseTicketAppointment(
    @Body() data: UpdateRaiseTicketAppointmentDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<TicketAppointment> {
    return this.raiseTicketService.updateRaiseTicketAppointment(data, headers, req);
  }

  @Put('update/:id')
  @Auth()
  async updateRaiseTicket(
    @Body() data: UpdateRaiseTicketDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @Param('id') id,
  ): Promise<RaiseTicket> {
    return this.raiseTicketService.updateRaiseTicket(data, id, headers, req);
  }

  @Put('change-status-ticket/:id')
  @Auth()
  async changeStatusRaiseTicket(
    @Body() data: CloseRaiseTicketDto,
    @Req() req: Record<string, any>,
    @Param('id') id,
  ): Promise<RaiseTicket> {
    return this.raiseTicketService.changeStatusRaiseTicket(data, id, req);
  }

  @Post('notes')
  @Auth()
  async createRaiseTicketNotes(
    @Body() data: CreateRaiseTicketNoteDto,
    @Req() req: Record<string, any>,
  ): Promise<RaiseTicketNote> {
    return this.raiseTicketService.createRaiseTicketNotes(data, req);
  }

  @Get('/history/:id')
  @Auth()
  async historyRaiseTicket(@Param('id') id, @Req() req: Record<string, any>): Promise<any[]> {
    return this.raiseTicketService.historyRaiseTicket(id, req);
  }

  @Put('delete/:id')
  @Auth()
  async deleteRaiseTicket(
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @Param('id') id,
  ): Promise<RaiseTicket> {
    return this.raiseTicketService.deleteRaiseTicket(id, headers, req);
  }

  @Post('appointment')
  @Auth()
  async createRaiseTicketAppointment(
    @Body() data: CreateRaiseTicketAppointmentDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<TicketAppointment> {
    return this.raiseTicketService.createRaiseTicketAppointment(data, req, headers);
  }
}
