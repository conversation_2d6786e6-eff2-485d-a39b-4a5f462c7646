import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  Response,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import {
  CheckSyncStatusDto,
  CreateOrderDto,
  ExpeditedReconciliationDto,
  MultiDownloadDto,
  MultiStatusDto,
  MultiUpdateTagDto,
  MultiUpdateWarehouseDto,
  PrintWaybillDto,
  RevertOrder,
  ScanWaybill,
  SupportChangeOrder,
  SupportDuplicateOrder,
  UpdateCarrierDto,
  UpdateInternalNoteOrderDto,
  UpdateMultiInternalNoteDto,
  UpdateOrderDto,
  UpdateOrderHandOverDto,
  UpdateStatusDto,
  UpdateStatusOutForDeliveryDto,
  UploadOrderInformation,
  WebhookGHTKDto,
  WebhookSPXDto,
  WebhookVNPDto,
  WebhookVTPDto,
} from 'apps/ffm-order-api/src/dtos/create-order.dto';
import { CreateMultiNoteDto, CreateNoteDto } from 'apps/ffm-order-api/src/dtos/note.dto';
import {
  HvnetCancelStatusDto,
  HvnetSyncTagsDto,
  SyncManualDto,
  SyncOrderDto,
  SyncStatusOrderDto,
} from 'apps/ffm-order-api/src/dtos/sync-order.dto';
import { PrintWaybillGHTKDto } from 'apps/ffm-order-api/src/dtos/waybills.dto';
import { Note } from 'apps/ffm-order-api/src/entities/note.entity';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { OrderExportEnum } from 'apps/ffm-order-api/src/enums/order-search-recipient.enum';
import {
  ExternalOrderType,
  TypeTemplateDownloadOrder,
} from 'apps/ffm-order-api/src/enums/order-status.enum';
import {
  FilterDuplicateOrder,
  FilterInvalidOrder,
  FilterOrder,
  FilterStatisCustomerOrder,
} from 'apps/ffm-order-api/src/filters/order.filter';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { CarrierCode, CountryID } from 'core/enums/carrier-code.enum';
import { cachedRawBodyRequestKey } from 'core/interceptors/raw-requests.interceptors';
import { concat } from 'lodash';
import * as moment from 'moment-timezone';
import { Permission } from '../../../../../../core/enums/permission-ffm.enum';
import { OrderService } from '../services/order.service';
import { TemplateExportService } from '../services/template-export.service';
import axios, { AxiosError } from 'axios';
// import { OrdersService } from '../services/orders.service';
export interface ITemplateExport {
  title: string;
  value: string;
  fields?: string[];
  carrier?: CarrierCode;
  actor?: number;
  id?: number;
  isCustom?: boolean;
}

@Controller('orders')
@ApiTags('orders')
export class OrdersController {
  constructor(
    private ordersService: OrderService,
    private templateService: TemplateExportService,
  ) {}

  @Get()
  @Auth(Permission.order)
  async getOrders(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<[Order[], number]> {
    return this.ordersService.list(pagination, filters, headers, req);
  }

  @Get('master-data')
  async masterDataVNP(): Promise<any> {
    return this.ordersService.masterData();
  }

  @Get('export')
  @Auth(Permission.export)
  async getOrdersExport(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<[Order[], number]> {
    return this.ordersService.list(pagination, filters, headers, req);
  }

  @Get('search')
  @Auth(Permission.order)
  async searchOrder(
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @Query() filters: FilterOrder,
  ): Promise<any[]> {
    return this.ordersService.searchOrder(headers, req, filters);
  }

  @Post('search/many')
  @Auth(Permission.order)
  async searchManyOrder(
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @Body() data: FilterOrder,
  ): Promise<any[]> {
    return this.ordersService.searchOrder(headers, req, data);
  }

  @Get('test/vnpost')
  @Auth(Permission.order)
  async testVnpost(
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @Query() filters: FilterOrder,
  ): Promise<any> {
    const config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'https://connect-my.vnpost.vn/customer-partner/getAllProvince',
      headers: {
        Token:
          'CCBM0a0WqkVPAvIDw3ph+z7kyu3BwyEuoR4NBNIa4XX4uhwR9SzUQKe2GMhDYG6EilYLBTBD3jT4ZKG2m26wpSSUtX8sAk95LxpH99wtdyw=',
      },
    };

    return axios
      .request(config)
      .then(response => {
        console.log(JSON.stringify(response.data));
        return response.data;
      })
      .catch(error => {
        console.log(error);
        throw new Error('');
      });
  }

  @Get('config')
  @Auth()
  async config(
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<{
    templates: Record<string, ITemplateExport[]>;
  }> {
    const countryIds = headers['country-ids']?.split(',');

    const fields = [];
    Object.entries(OrderExportEnum).forEach(([key]) => {
      fields.push(key);
    });
    const data = await this.templateService.getTx(req, headers);
    const parseData = data?.map((item: any) => {
      return {
        title: item?.name,
        value: item?.id,
        id: item?.id,
        fields,
        actor: item?.creatorId,
        isCustom: true,
        data: item?.fields,
      };
    });

    const templates = {
      [`${CountryID.TH}`]: [
        {
          title: TypeTemplateDownloadOrder['THJ&T'],
          value: TypeTemplateDownloadOrder['THJ&T'],
          fields: [],
          carrier: CarrierCode.jtexpress,
        },
        {
          title: TypeTemplateDownloadOrder.THNinjaVan,
          value: TypeTemplateDownloadOrder.THNinjaVan,
          fields: [],
          carrier: CarrierCode.ninjavan,
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
        {
          title: TypeTemplateDownloadOrder.KerryExpress,
          value: TypeTemplateDownloadOrder.KerryExpress,
          fields: [],
          carrier: CarrierCode.kerryexpress,
        },
        {
          title: TypeTemplateDownloadOrder.THFlashExpress,
          value: TypeTemplateDownloadOrder.THFlashExpress,
          fields: [],
          carrier: CarrierCode.flashexpress,
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionDropshipThailand,
          value: TypeTemplateDownloadOrder.CollectionDropshipThailand,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.PurchaseDropship,
          value: TypeTemplateDownloadOrder.PurchaseDropship,
          fields: [],
        },
      ],
      [`${CountryID.PH}`]: [
        {
          title: TypeTemplateDownloadOrder['PHJ&T'],
          value: TypeTemplateDownloadOrder['PHJ&T'],
          fields: [],
          carrier: CarrierCode.jtexpress,
        },
        {
          title: TypeTemplateDownloadOrder.PHNinjaVan,
          value: TypeTemplateDownloadOrder.PHNinjaVan,
          fields: [],
          carrier: CarrierCode.ninjavan,
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
      ],
      [`${CountryID.MY}`]: [
        {
          title: TypeTemplateDownloadOrder.PosLaju,
          value: TypeTemplateDownloadOrder.PosLaju,
          fields: [],
          carrier: CarrierCode.poslaju,
        },
        {
          title: TypeTemplateDownloadOrder.MANinjaVan,
          value: TypeTemplateDownloadOrder.MANinjaVan,
          fields: [],
          carrier: CarrierCode.ninjavan,
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
        {
          title: TypeTemplateDownloadOrder.BestExpress,
          value: TypeTemplateDownloadOrder.BestExpress,
          fields: [],
          carrier: CarrierCode.bestexpress,
        },
        {
          title: TypeTemplateDownloadOrder.FlashExpress,
          value: TypeTemplateDownloadOrder.FlashExpress,
          fields: [],
          carrier: CarrierCode.flashexpress,
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.PurchaseDropship,
          value: TypeTemplateDownloadOrder.PurchaseDropship,
          fields: [],
        },
      ],
      [`${CountryID.ID}`]: [
        {
          title: TypeTemplateDownloadOrder['IDJ&T'],
          value: TypeTemplateDownloadOrder['IDJ&T'],
          fields: [],
          carrier: CarrierCode.jtexpress,
        },
        {
          title: TypeTemplateDownloadOrder.JNE,
          value: TypeTemplateDownloadOrder.JNE,
          fields: [],
          carrier: CarrierCode.jneexpress,
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
      ],
      [`${CountryID.IN}`]: [
        {
          title: TypeTemplateDownloadOrder.Nimbus,
          value: TypeTemplateDownloadOrder.Nimbus,
          fields: [],
          carrier: CarrierCode.nimbus,
        },
        {
          title: TypeTemplateDownloadOrder.NimbusManually,
          value: TypeTemplateDownloadOrder.NimbusManually,
          fields: [],
          carrier: CarrierCode.nimbus,
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
      ],
      [`${CountryID.MM}`]: [
        {
          title: TypeTemplateDownloadOrder.MMNinjaVan,
          value: TypeTemplateDownloadOrder.MMNinjaVan,
          fields: [],
          carrier: CarrierCode.ninjavan,
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
      ],
      [`${CountryID.LA}`]: [
        {
          title: TypeTemplateDownloadOrder.AnousithExpress,
          value: TypeTemplateDownloadOrder.AnousithExpress,
          fields: [],
          // carrier: CarrierCode.ninjavan
        },
        {
          title: TypeTemplateDownloadOrder.AnousithWaybill,
          value: TypeTemplateDownloadOrder.AnousithWaybill,
          carrier: CarrierCode.anousith,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.HALWaybill,
          value: TypeTemplateDownloadOrder.HALWaybill,
          carrier: CarrierCode.hal,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.HALExcel,
          value: TypeTemplateDownloadOrder.HAL,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Mixay,
          value: TypeTemplateDownloadOrder.Mixay,
          carrier: CarrierCode.mixayexpress,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.PurchaseChineseLao,
          value: TypeTemplateDownloadOrder.PurchaseChineseLao,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionDropshipLao,
          value: TypeTemplateDownloadOrder.CollectionDropshipLao,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
      ],
      [`${CountryID.US}`]: [
        {
          title: TypeTemplateDownloadOrder.TemplatePurchase,
          value: TypeTemplateDownloadOrder.TemplatePurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Wedo,
          value: TypeTemplateDownloadOrder.Wedo,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
      ],
      [`${CountryID.VN}`]: [
        {
          title: TypeTemplateDownloadOrder.PurchaseDropship,
          value: TypeTemplateDownloadOrder.PurchaseDropship,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.RegionBy3PL,
          value: TypeTemplateDownloadOrder.RegionBy3PL,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.VNWaybill,
          value: TypeTemplateDownloadOrder.VNWaybill,
          // carrier: CarrierCode.hal,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.CollectionDropshipBDG,
          value: TypeTemplateDownloadOrder.CollectionDropshipBDG,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
      ],
      other: [
        {
          title: TypeTemplateDownloadOrder.CollectionPurchase,
          value: TypeTemplateDownloadOrder.CollectionPurchase,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.Collection,
          value: TypeTemplateDownloadOrder.Collection,
          fields: [],
        },
        {
          title: TypeTemplateDownloadOrder.CollectionPDF,
          value: TypeTemplateDownloadOrder.CollectionPDF,
          fields: [],
          printer: true,
        },
        {
          title: TypeTemplateDownloadOrder.Custom,
          value: TypeTemplateDownloadOrder.Custom,
          fields,
        },
      ],
    };

    if (data?.length > 0)
      for (const [key, value] of Object.entries(templates)) {
        if (templates[`${key}`]) templates[`${key}`] = concat(value, parseData);
      }

    return {
      templates: {
        [countryIds[0]]: templates[`${countryIds[0]}`],
      },
    };
  }

  @Get('count')
  @Auth(Permission.order)
  async countOrders(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.countOrders(pagination, filters, headers, req);
  }

  @Get('list-ids')
  @Auth(Permission.order)
  async getListDataIds(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.getListDataIds(pagination, filters, headers, req);
  }

  @Get('statis-customer')
  @Auth()
  async getStatisCustomer(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: FilterStatisCustomerOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.getStatisCustomer(pagination, filters, headers, req);
  }

  @Post()
  @Auth(Permission.createOrder)
  async createOrder(@Body() data: CreateOrderDto, @Req() req: Record<string, any>): Promise<Order> {
    return this.ordersService.createOrder(data, req);
  }

  @Post('ag-sale')
  async syncAgSale(
    @Body() data: SyncOrderDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const body = req[cachedRawBodyRequestKey]?.toString('utf-8');
    return this.ordersService.syncOrder(
      data,
      body,
      headers?.['ag-signature'],
      ExternalOrderType.ag,
    );
  }

  @Post('external')
  async external(
    @Body() data: SyncOrderDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const body = req[cachedRawBodyRequestKey]?.toString('utf-8');
    return this.ordersService.syncOrder(
      data,
      body,
      headers?.['ag-signature'],
      ExternalOrderType.hvnet,
    );
  }

  @Post('ag-sale/update')
  async updateAgOrder(
    @Body() data: SyncOrderDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const body = req[cachedRawBodyRequestKey]?.toString('utf-8');
    return this.ordersService.updateAgOrder(data, body, headers?.['ag-signature']);
  }

  @Post('ag-sale/status')
  async syncStatusOrder(
    @Body() data: SyncStatusOrderDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const body = req[cachedRawBodyRequestKey]?.toString('utf-8');
    return this.ordersService.syncStatusOrder(data, body, headers?.['ag-signature']);
  }

  @Post('hvnet/cancel-status')
  async hvnetCancelStatus(
    @Body() data: HvnetCancelStatusDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const body = req[cachedRawBodyRequestKey]?.toString('utf-8');
    return this.ordersService.hvnetCancelStatus(data, body, headers?.['ag-signature']);
  }

  @Post('hvnet/sync-tags')
  @HttpCode(200)
  async hvnetSyncTags(
    @Body() data: HvnetSyncTagsDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    const body = req[cachedRawBodyRequestKey]?.toString('utf-8');
    return this.ordersService.hvnetSyncTags(data, body, headers?.['ag-signature']);
  }

  @Put('update-order-after-picked-up/:id')
  @Auth(Permission.editOder)
  async updateOrderAfterPickedUp(
    @Body() data: UpdateOrderDto,
    @Req() req: Record<string, any>,
    @Param('id') id,
  ): Promise<Order> {
    return this.ordersService.updateOrderAfterPickedUp(data, id, req);
  }

  @Put('update-order-after-picked-up/partner/:id')
  @Auth(Permission.editOder)
  async updateOrderAfterPickedUpPartner(
    @Req() req: Record<string, any>,
    @Param('id') id,
  ): Promise<Order> {
    return this.ordersService.updateOrderAfterPickedUpPartner(id, req);
  }

  @Get('/invalid-order-map-3pl-address')
  @Auth()
  async invalidOrderMap3plAddress(
    @Query() filters: FilterInvalidOrder,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.invalidOrderMap3plAddress(filters, req, headers);
  }

  @Get('export-invalid-order-map-3pl-address')
  @Auth()
  async exportInvalidOrderMap3plAddress(
    @Query() filters: FilterInvalidOrder,
    @Req() req: Record<string, any>,
    @Response() response,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    const buffer = await this.ordersService.exportInvalidOrderMap3plAddress(filters, req, headers);
    response.attachment(
      `invalidorder_map3pladdress_${moment().valueOf()}-${moment().format(
        'DD-MM-YYYY hh:mm:ss',
      )}.xlsx`,
    );
    return response.send(buffer);
  }

  @Get('duplicate-order')
  @Auth()
  async getDuplicateOrder(
    @Query() filters: FilterDuplicateOrder,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Order[]> {
    return this.ordersService.duplicateOrder(filters, req, headers);
  }

  @Post('check-sync-status')
  @Auth()
  async checkSyncStatusManual(
    @Body() data: CheckSyncStatusDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.checkSyncStatusManual(data, req, headers);
  }

  @Get('/:id')
  @Auth(Permission.createOrder, Permission.order)
  async getDetailOrder(
    @Param('id') id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.detail(id, req, headers);
  }

  @Delete('/:id')
  @Auth(Permission.createOrder, Permission.order)
  async removeOrder(
    @Param('id', ParseIntPipe) id,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.removeOrder(id, req, headers);
  }

  @Get('/history/:id')
  @Auth(Permission.createOrder, Permission.order)
  async historyOrder(
    @Param('id') id,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any[]> {
    return this.ordersService.historyOrder(id, req, headers);
  }

  @Get('/history/status/:id')
  @Auth(Permission.createOrder, Permission.order, Permission.ptView, Permission.ptManager)
  async historyStatusOrder(
    @Param('id') id,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.historyStatusOrder(id, req);
  }

  @Put('update-status-out-delivery/:id')
  @Auth(Permission.createOrder)
  async updateStatusOutForDelivery(
    @Body() data: UpdateStatusOutForDeliveryDto,
    @Req() req: Record<string, any>,
    @Param('id', ParseIntPipe) id,
    @Headers() headers: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.updateStatusOutForDelivery(data, id, req, headers);
  }

  @Put('update-status/:id')
  @Auth(Permission.createOrder)
  async updateStatusOrder(
    @Body() data: UpdateStatusDto,
    @Req() req: Record<string, any>,
    @Param('id', ParseIntPipe) id,
    @Headers() headers: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.updateStatus(data, id, req, headers);
  }

  @Put('update/note/:id')
  @Auth(Permission.createOrder)
  async updateInternalNoteOrder(
    @Body() data: UpdateInternalNoteOrderDto,
    @Req() req: Record<string, any>,
    @Param('id', ParseIntPipe) id,
    @Headers() headers: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.updateInternalNoteOrder(data, id, req, headers);
  }

  @Post('bulk/update-internal-note')
  @Auth(Permission.bulkUpdateOrderInternalNote)
  async bulkUpdateInternalNote(
    @Body() data: UpdateMultiInternalNoteDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.bulkUpdateInternalNote(data, req, headers);
  }

  @Put('update/:id')
  @Auth(Permission.createOrder)
  async updateOrder(
    @Body() data: UpdateOrderDto,
    @Req() req: Record<string, any>,
    @Param('id') id,
    @Headers() headers: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.updateOrderV2(data, id, req, headers);
  }

  @Put('update-whtag/:id')
  @Auth(Permission.createOrder && Permission.bulkWarehouseTag)
  async updateOrderWithWhTag(
    @Body() data: UpdateOrderDto,
    @Req() req: Record<string, any>,
    @Param('id') id,
    @Headers() headers: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.updateOrderV2(data, id, req, headers);
  }

  // @Put('update-product/:id')
  // @Auth(Permission.createOrder)
  // async updateProductOrder(
  //   @Body() data: UpdateProductDto,
  //   @Req() req: Record<string, any>,
  //   @Param('id', ParseIntPipe) id,
  // ): Promise<Order> {
  //   return this.ordersService.updateProduct(data, id, req);
  // }

  // @Put('update-warehouse/:id')
  // @Auth(Permission.createOrder)
  // async updateWarehouseOrder(
  //   @Body() data: UpdateWarehouseDto,
  //   @Req() req: Record<string, any>,
  //   @Param('id', ParseIntPipe) id,
  // ): Promise<Order> {
  //   return this.ordersService.updateWarehouse(data, id, req);
  // }

  @Put('update-carrier/:id')
  @Auth(Permission.createOrder)
  async updateCarrierOrder(
    @Body() data: UpdateCarrierDto,
    @Req() req: Record<string, any>,
    @Param('id', ParseIntPipe) id,
    @Headers() headers: Record<string, any>,
  ): Promise<Order> {
    return this.ordersService.updateCarrier(data, id, req, headers);
  }

  // @Put('update-recipient/:id')
  // @Auth(Permission.createOrder)
  // async updateRecipientOrder(
  //   @Body() data: UpdateRecipientDto,
  //   @Req() req: Record<string, any>,
  //   @Param('id', ParseIntPipe) id,
  // ): Promise<Order> {
  //   return this.ordersService.updateRecipient(data, id, req);
  // }

  @Post('note')
  @Auth(Permission.createOrder)
  async createNote(
    @Body() data: CreateNoteDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Note> {
    return this.ordersService.createNote(data, req, headers);
  }

  @Post('bulk/update-message')
  @Auth(Permission.bulkUpdateOrderMessage)
  async bulkUpdateMessage(
    @Body() data: CreateMultiNoteDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.bulkUpdateMessage(data, req, headers);
  }

  @Post('bulk/status')
  @Auth(Permission.createOrder, Permission.order)
  async bulkStatus(
    @Body() data: MultiStatusDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    // if(!process.env.ENV || process.env.ENV!='dev') throw new Error("");
    return this.ordersService.updateMultiStatusOrder(data, req, headers);
  }

  @Post('bulk/extended-update-status')
  @Auth(Permission.extendedUpdate)
  // @Auth()
  async bulkExtendedUpdateStatus(
    @Body() data: MultiStatusDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    // if(!process.env.ENV || process.env.ENV!='dev') throw new Error("");
    const extendUpdate = true;
    return this.ordersService.updateMultiStatusOrder(data, req, headers, extendUpdate);
  }

  @Post('bulk/update-warehouse')
  @Auth(Permission.createOrder, Permission.order)
  async bulkUpdateWarehouse(
    @Body() data: MultiUpdateWarehouseDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.updateMultiWarehouseOrder(data, req, headers);
  }

  // @Post('bulk/create')
  // @Auth(Permission.createOrder)
  // async bulkCreate(
  //   @Body() data: MultiOrderDto,
  //   @Req() req: Record<string, any>,
  // ): Promise<any[]> {
  //   return this.ordersService.createMultiOrder(data, req);
  // }

  @Post('bulk/download')
  @Auth(Permission.order)
  async bulkDownload(
    @Body() data: MultiDownloadDto,
    @Req() req: Record<string, any>,
    @Response() response,
    @Headers() headers: Record<string, any>,
  ): Promise<any> {
    // if(![CountryID.LA, CountryID.PH]?.includes(Number(headers['country-ids']))){
    //   throw new BadRequestException('Tính năng đang bảo trì');
    // }
    const buffer = await this.ordersService.bulkDownload(data, req, headers);
    response.attachment(
      `${data?.type}-${moment().valueOf()}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );
    return response.send(buffer);
  }

  @Post('sync/manual')
  @Auth(Permission.order)
  async syncManual(@Body() data: SyncManualDto, @Req() req: Record<string, any>): Promise<Order[]> {
    return this.ordersService.syncManual(data, req);
  }

  @Put('packing-order')
  @Auth(Permission.order)
  async updateOrderHandOver(
    @Body() data: UpdateOrderHandOverDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.updateOrderHandOver(data, headers, req);
  }

  @Put('order-hand-over')
  @Auth(Permission.order)
  async scanWaybill(
    @Body() data: ScanWaybill,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Order[]> {
    return this.ordersService.scanWaybill(data, headers, req);
  }

  @Post('/revert')
  @Auth()
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async importExcel(
    @Request() request,
    @Query() filters,
    @Body() body: [],
    @UploadedFile('file') file,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.ordersService.revertOrder(filters, buffer, request);
  }

  @Post('bulk/revert-status')
  @Auth()
  async revertOrderStatus(
    @Body() data: MultiStatusDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ): Promise<Record<string, any>> {
    // if(!process.env.ENV || process.env.ENV!='dev') throw new Error("");
    return this.ordersService.revertOrderStatus(data, req, headers);
  }

  @Post('mapping-address-3pl')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Auth()
  async mappingAddress3pl(
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
    @UploadedFile('file') file,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.ordersService.mappingAddress3pl(buffer, req, headers);
  }

  @Put('bulk-tags')
  @Auth(Permission.bulkTag)
  async bulkTagAction(
    @Body() data: MultiUpdateTagDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.bulkTagAction(data, headers, req);
  }

  @Put('bulk-warehouse-tags')
  @Auth(Permission.bulkWarehouseTag)
  async bulkWarehouseTagAction(
    @Body() data: MultiUpdateTagDto,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.bulkWhTagAction(data, headers, req);
  }

  @Put('api-support-revert-order')
  @Auth()
  async supportRevert(
    @Body() data: RevertOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.supportRevert(data, headers, req);
  }

  @Put('api-support-sync-order-to-pancake')
  @Auth()
  async supportSyncToPancake(
    @Body() data: RevertOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.supportSyncToPancake(data, headers, req);
  }

  @Post('print-waybill')
  @Auth(Permission.order)
  async printWaybill(@Body() data: PrintWaybillDto, @Req() req: Record<string, any>): Promise<any> {
    return this.ordersService.printWaybill(data, req);
  }

  @Post('print-waybill-ghtk')
  @Auth(Permission.order)
  async printWaybillGHTK(
    @Body() data: PrintWaybillGHTKDto,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.ordersService.printWaybillGHTK(data, req);
  }

  @Post('vtp')
  @HttpCode(200)
  async webhookVTP(@Body() data: WebhookVTPDto): Promise<any> {
    return this.ordersService.vtp(data);
  }

  @Post('vn-post')
  @HttpCode(200)
  async webhookVNPost(@Body() data: WebhookVNPDto): Promise<any> {
    return this.ordersService.vnpost(data);
  }

  @Post('spx')
  @HttpCode(200)
  async webhookSPX(@Body() data: WebhookSPXDto): Promise<any> {
    return this.ordersService.spx(data);
  }

  @Post('ghtk')
  @HttpCode(200)
  async webhookGHTK(@Body() data: WebhookGHTKDto): Promise<any> {
    return this.ordersService.ghtk(data);
  }

  @Post('suport-edit-cod')
  @Auth()
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async supportEditOrderCOD(
    @Request() request,
    @Query() filters,
    @Body() body: [],
    @UploadedFile('file') file,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.ordersService.supportEditOrderCOD(body, buffer, request);
  }

  @Post('suport-edit-weight')
  @Auth()
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async supportEditOrderWeight(
    @Request() request,
    @Query() filters,
    @Body() body: [],
    @UploadedFile('file') file,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.ordersService.supportEditOrderWeight(body, buffer, request);
  }

  @Post('suport-change-status')
  @Auth()
  async supportChangeStatus(
    @Body() data: SupportChangeOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.supportChangeStatus(data, headers, req);
  }

  @Post('bulk/upload-order-information')
  @Auth(Permission.bulkUpdateOrderTotalWeight)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadOrderInformation(
    @Request() request,
    @Headers() headers: Record<string, any>,
    @UploadedFile('file') file,
    // @Body() data: UploadOrderInformation,
    @Query() data: UploadOrderInformation,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.ordersService.uploadOrderInformation(buffer, data, headers, request);
  }

  @Post('suport-duplicate-order')
  @Auth()
  async supportDuplicateOrder(
    @Body() data: SupportDuplicateOrder,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.supportDuplicateOrder(data, headers, req);
  }

  @Post('expedited-reconciliation-valid')
  @Auth(Permission.expeditedReconciliation)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async expeditedReconciliationValidate(
    @Request() request,
    @Headers() headers: Record<string, any>,
    @Query() filters,
    @Body() body: [],
    @UploadedFile('file') file,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.ordersService.expeditedReconciliationValidate(body, buffer, headers, request);
  }

  @Post('expedited-reconciliation')
  @Auth(Permission.expeditedReconciliation)
  async expeditedReconciliation(
    @Body() data: ExpeditedReconciliationDto,
    @Headers() headers: Record<string, any>,
    @Req() request: Record<string, any>,
  ): Promise<Record<string, any>> {
    return this.ordersService.expeditedReconciliation(data, headers, request);
  }
}
