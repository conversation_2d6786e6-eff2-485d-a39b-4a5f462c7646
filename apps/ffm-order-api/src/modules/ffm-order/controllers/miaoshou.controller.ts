import {
  Body,
  CacheTTL,
  Controller,
  Get,
  Headers,
  Param,
  Post,
  Query,
  Req,
  Request,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';
import axios from 'axios';
import { aesEncrypt } from '../utils/miaoshou';
import { MiaoshouService } from '../services/miaoshou.service';
import { MiaoshouDto } from 'apps/ffm-order-api/src/dtos/miaoshou/miashou.dto';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { <PERSON><PERSON><PERSON><PERSON>ilt<PERSON>, <PERSON><PERSON>ouOrderFilter } from 'apps/ffm-order-api/src/filters/miashou.filter';
import { GlobalCache } from 'core/decorators/global-cache/global-cache.decorator';
import { CreateOrderDto } from 'apps/ffm-order-api/src/dtos/miaoshou/order.dto';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';

export interface TokenMiaoshou {
  acw_tc: string;
  bsClientId: string;
  mserp: string;
  firstClientId: string;
  accountId: string;
  isInsideLogin: string;
  mserp_sst: string;
  autoLoginToken: string;
}

@Controller('miaoshou')
@ApiTags('miaoshou')
export class MiaoshouController {
  constructor(private miaoshouService: MiaoshouService) {}

  @Post('login')
  @Auth()
  async login(
    @Body() data: MiaoshouDto,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, any>,
  ) {
    return this.miaoshouService.login(data, headers, req);
  }

  @Get(':id/orders')
  // @GlobalCache()
  // @CacheTTL(1 * 60)
  @Auth()
  async miashou(
    @Param('id') id,
    @Pagination() pagination: PaginationOptions,
    @Query() filters: MiaoshouOrderFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    return this.miaoshouService.getOrders(id, pagination, filters, headers, req);
  }
  
  @Get('tags')
  @GlobalCache()
  @CacheTTL(15 * 60)
  @Auth()
  async getTags(
    @Pagination() pagination: PaginationOptions = { limit: 1000, skip: 0 },
    @Query() filters: MiaoshouFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    return this.miaoshouService.getTags(pagination, filters, headers, req);
  }

  @Get('shops')
  @GlobalCache()
  @CacheTTL(15 * 60)
  @Auth()
  async shops(
    @Pagination() pagination: PaginationOptions,
    @Query() filters: MiaoshouFilter,
    @Headers() headers,
    @Req() req: Record<string, any>,
  ) {
    return this.miaoshouService.getShops(pagination, filters, headers, req);
  }

  @Post('order')
  @Auth()
  async createOrder(
    @Body() body: CreateOrderDto,
    @Req() req: Record<string, any>,
  ): Promise<Order> {
    return await this.miaoshouService.createOrder(body, req);
  }
}
