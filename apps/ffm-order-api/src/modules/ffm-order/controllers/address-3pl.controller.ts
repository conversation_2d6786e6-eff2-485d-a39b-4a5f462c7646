import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  Request,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { Province } from 'apps/ffm-order-api/src/entities/address_province_3pl.entity';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import { Address3PLService } from '../services/address-3pl.service';
import { FilterAddress3PL } from 'apps/ffm-order-api/src/filters/address-3pl.filter';
import { District } from 'apps/ffm-order-api/src/entities/address_district_3pl.entity';
import { Ward } from 'apps/ffm-order-api/src/entities/address_ward_3pl.entity';
import { PostCode } from 'apps/ffm-order-api/src/entities/address_post_code_3pl.entity';
// import { AddressMapping3PL } from 'apps/ffm-order-api/src/entities/address_mapping_3pl.entity';
import { CreateWard3plDto } from 'apps/ffm-order-api/src/dtos/address-3pl.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('address-3pl')
@ApiTags('address-3pl')
export class Address3PLController {
  constructor(private address3PLService: Address3PLService) {}

  @Get('mapping-ffm')
  @Auth()
  async getAddress3PL(
    @Query() filters: FilterAddress3PL,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<any> {
    return await this.address3PLService.getAddress3PLMapping(filters, req, headers);
  }

  @Get('provinces')
  @Auth()
  async getProvinces(
    @Query() filters: FilterAddress3PL,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<[Province[], number]> {
    return await this.address3PLService.getProvinces(filters, req, headers);
  }

  @Get('districts')
  @Auth()
  async getDistricts(
    @Query() filters: FilterAddress3PL,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<[District[], number]> {
    return await this.address3PLService.getDistricts(filters, req, headers);
  }

  @Get('wards')
  @Auth()
  async getWards(
    @Query() filters: FilterAddress3PL,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<[Ward[], number]> {
    return await this.address3PLService.getWards(filters, req, headers);
  }

  @Get('post-codes')
  @Auth()
  async getPostCodes(
    @Query() filters: FilterAddress3PL,
    @Req() req: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<[PostCode[], number]> {
    return await this.address3PLService.getPostCodes(filters, req, headers);
  }

  // @Get(':id')
  // @Auth()
  // async detail(
  //   @Req() req: Record<string, any>,
  //   @Param('id', ParseIntPipe) id,
  // ): Promise<AddressMapping3PL> {
  //   return this.address3PLService.getOneAddress(req, id);
  // }

  @Post('insert-address')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Auth()
  async insertAddress(
    @Req() req: Record<string, any>,
    @UploadedFile('file') file,
  ): Promise<Record<string, any>> {
    const { buffer } = file;
    return this.address3PLService.insertAddress(buffer, req);
  }
}
