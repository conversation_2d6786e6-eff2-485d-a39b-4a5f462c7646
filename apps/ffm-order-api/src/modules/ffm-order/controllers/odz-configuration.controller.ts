import { Body, Controller, Get, Headers, Param, Post, Put, Query, Req } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { CreateODZEnable, UpdateODZEnable } from "apps/ffm-order-api/src/dtos/odz.dto";
import { Auth } from "core/auth/decorators/auth/auth.decorator";
import { Pagination } from "core/decorators/pagination/pagination.decorator";
import { PaginationOptions } from "core/decorators/pagination/pagination.model";
import { ODZConfiguration } from "apps/ffm-order-api/src/entities/odz-configuration.entity";
import { ODZConfigurationService } from "../services/odz-configuration.service";
import { Permission } from "core/enums/permission-ffm.enum";

@Controller('odz-config')
@ApiTags('odz-config')
export class ODZConfigController {
  constructor(
    private ODZConfigurationService: ODZConfigurationService
  ) { }

  @Get()
  @Auth()
  async getODZs(
    @Pagination() pagination: PaginationOptions,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.ODZConfigurationService.getODZs(pagination, headers, req);
  }

  @Get(':id')
  @Auth()
  async getODZDetail(
    @Param('id') id,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<ODZConfiguration> {
    return this.ODZConfigurationService.getODZDetail(id, headers, req);
  }

  @Put()
  @Auth()
  async updateODZ(
    @Body() data: CreateODZEnable,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.ODZConfigurationService.updateODZEnable(data, req);
  }
}