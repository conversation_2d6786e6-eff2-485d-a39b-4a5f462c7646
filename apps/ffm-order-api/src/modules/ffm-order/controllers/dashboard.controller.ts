import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  Response,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DashboardService } from '../services/dashboard.service';
import { Auth } from 'core/auth/decorators/auth/auth.decorator';
import {
  FilterDashboardTicket,
  FilterDashboardTicketReturn,
  TypeFilter,
  FilterDashboardGeneralInformation,
  FilterDashboardPtPerformance,
} from 'apps/ffm-order-api/src/filters/dashboard.filter';
import { FilterCollection } from 'apps/ffm-order-api/src/entities/filter-collection.entity';
import { CreateOrdersFilterDto } from 'apps/ffm-order-api/src/dtos/filter.dto';
import { Permission } from 'core/enums/permission-ffm.enum';
import * as moment from 'moment-timezone';
import { DashboardPtPerformanceService } from '../services/dashboardPtPerformance.service';

@Controller('dashboard')
@ApiTags('dashboard')
export class DashBoardController {
  constructor(
    private dashboardService: DashboardService,
    private dashboardPtPerformanceService: DashboardPtPerformanceService,
  ) {}

  @Get('ticket')
  @Auth(Permission.dashboardPTPerformance)
  async getDashboardTicket(
    @Query() filters: FilterDashboardTicket,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getDashboardTicket(filters, headers, req);
  }

  @Get('ticket-by-staff')
  @Auth( Permission.dashboardPTPerformance )
  async getDashboardTicketByStaff(
    @Query() filters: FilterDashboardTicket,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getDashboardTicketByStaff(filters, headers, req);
  }

  @Get('manual-filter')
  @Auth()
  async getLeadsManualFilter(
    @Query() filters: TypeFilter,
    @Req() request,
    @Headers() headers,
  ): Promise<FilterCollection[]> {
    return await this.dashboardService.getLeadsManualFilter(filters, headers, request);
  }

  @Post('manual-filter')
  @Auth()
  async createLeadsManualFilter(
    @Req() request,
    @Headers() headers,
    @Body() data: CreateOrdersFilterDto,
  ): Promise<FilterCollection> {
    return await this.dashboardService.createLeadsManualFilter(data, headers, request);
  }

  @Delete('manual-filter/:id')
  @Auth()
  async deleteLeadsManualFilter(
    @Param('id', ParseIntPipe) filterId: number,
    @Req() request: Record<string, any>,
  ) {
    return await this.dashboardService.deleteLeadsManualFilter(filterId, request);
  }

  @Get('ticket-return')
  @Auth()
  async getDashboardTicketReturn(
    @Query() filters: FilterDashboardTicketReturn,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return await this.dashboardService.getDashboardTicketReturn(filters, headers, req);
  }

  @Get('ticket-return-by-reason')
  @Auth()
  async getDashboardTicketReturnByReason(
    @Query() filters: FilterDashboardTicketReturn,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getDashboardTicketReturnByReason(filters, headers, req);
  }

  @Get('ticket-return-by-reason-detail')
  @Auth()
  async getDashboardTicketReturnDetail(
    @Query() filters: FilterDashboardTicketReturn,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getDashboardTicketReturnDetail(filters, headers, req);
  }

  @Get('ticket-return-export')
  @Auth()
  async exportDashboardTicketReturn(
    @Query() filters: FilterDashboardTicketReturn,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Response() response,
  ): Promise<any> {
    const buffer = await this.dashboardService.exportDashboardTicketReturn(filters, headers, req);
    response.attachment(
      `Return reason-${moment().valueOf()}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );
    return response.send(buffer);
  }

  @Get('general-information')
  @Auth(Permission.dashboardOrderTracker)
  async getGeneralInformation(
    @Query() filters: FilterDashboardGeneralInformation,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getGeneralInformation(filters, headers, req);
  }

  @Get('carrier-information')
  @Auth(Permission.dashboardOrderTracker)
  async getCarrierInformation(
    @Query() filters: FilterDashboardGeneralInformation,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getCarrierInformation(filters, headers, req);
  }

  @Get('carrier-information-detail')
  @Auth(Permission.dashboardOrderTracker)
  async getCarrierInformationDetail(
    @Query() filters: FilterDashboardGeneralInformation,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getCarrierInformationDetail(filters, headers, req);
  }

  @Get('client-information-detail')
  @Auth(Permission.dashboardOrderTracker)
  async getClientInformationDetail(
    @Query() filters: FilterDashboardGeneralInformation,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getClientInformationDetail(filters, headers, req);
  }

  @Get('region-information')
  @Auth(Permission.dashboardOrderTracker)
  async getRegionInformation(
    @Query() filters: FilterDashboardGeneralInformation,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getRegionInformation(filters, headers, req);
  }

  @Get('product-information')
  @Auth(Permission.dashboardOrderTracker)
  async getProductInformation(
    @Query() filters: FilterDashboardGeneralInformation,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getProductInformation(filters, headers, req);
  }

  @Get('product-information-detail')
  @Auth(Permission.dashboardOrderTracker)
  async getProductInformationDetail(
    @Query() filters: FilterDashboardGeneralInformation,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardService.getProductInformationDetail(filters, headers, req);
  }

  @Get('carrier-client-information-export')
  @Auth(Permission.dashboardOrderTracker)
  async exportCarrierAndClientInformation(
    @Query() filters: FilterDashboardTicketReturn,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Response() response,
  ): Promise<any> {
    const buffer = await this.dashboardService.exportCarrierAndClientInformation(
      filters,
      headers,
      req,
    );
    response.attachment(
      `Order tracker_Carriers Clients_${moment().valueOf()}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );
    return response.send(buffer);
  }

  @Get('region-information-export')
  @Auth(Permission.dashboardOrderTracker)
  async exportRegionInformation(
    @Query() filters: FilterDashboardTicketReturn,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Response() response,
  ): Promise<any> {
    const buffer = await this.dashboardService.exportRegionInformation(filters, headers, req);
    response.attachment(
      `Order tracker_Regions_${moment().valueOf()}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );
    return response.send(buffer);
  }

  @Get('product-information-export')
  @Auth(Permission.dashboardOrderTracker)
  async exportProductInformation(
    @Query() filters: FilterDashboardTicketReturn,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Response() response,
  ): Promise<any> {
    const buffer = await this.dashboardService.exportProductInformation(filters, headers, req);
    response.attachment(
      `Order tracker_Products_${moment().valueOf()}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );
    return response.send(buffer);
  }

  @Get('rescue-rate-by-date')
  @Auth()
  async getRescueRateByDate(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return await this.dashboardPtPerformanceService.getRescueRateByDate(filters, headers, req);
  }

  @Get('rescue-rate-by-staff')
  @Auth()
  async getRescueRateByStaff(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return await this.dashboardPtPerformanceService.getRescueRateByStaff(filters, headers, req);
  }

  @Get('export-rescue-rate')
  @Auth()
  async exportRescueRate(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Response() response,
    ): Promise<any> {
    const buffer = await this.dashboardPtPerformanceService.exportRescueRate(filters, headers, req);
    response.attachment(
      `PT_rescuerate_${moment().valueOf()}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );
    return response.send(buffer);
  }
  @Get('ticket-type-by-date')
  @Auth()
  async getTicketTypeByDate(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return await this.dashboardPtPerformanceService.getTicketTypeByDate(filters, headers, req);
  }

  @Get('ticket-type-by-staff')
  @Auth()
  async getTicketTypeByStaff(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return await this.dashboardPtPerformanceService.getTicketTypeByStaff(filters, headers, req);
  }

  @Get('export-ticket-type')
  @Auth()
  async exportTicketType(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
    @Response() response,
    ): Promise<any> {
    const buffer = await this.dashboardPtPerformanceService.exportTicketType(filters, headers, req);
    response.attachment(
      `PT_tickettype_${moment().valueOf()}-${moment().format('DD-MM-YYYY')}.xlsx`,
    );
    return response.send(buffer);
  }

  @Get('pt-performance-overview')
  @Auth()
  async getPTPerformanceOverview(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardPtPerformanceService.getPTPerformanceOverview(filters, headers, req);
  }

  @Get('staff-assignment')
  @Auth()
  async getStaffAssignment(
    @Query() filters: FilterDashboardPtPerformance,
    @Headers() headers: Record<string, any>,
    @Req() req: Record<string, any>,
  ): Promise<any> {
    return this.dashboardPtPerformanceService.getStaffAssignment(filters, headers, req);
  }

}
