import { SPX_ADDRESS } from 'apps/ffm-order-api/src/constants/spx-vn-address';
import StringUtils from 'core/utils/StringUtils';
import { filter, find, split, trim, trimStart } from 'lodash';

export interface IData {
  address: string;
  province: string;
  district: string;
  ward: string;
}

export interface IVNPOSTData {
  provinceData: any;
  districtData: any;
  wardData: any;
  province: string;
  district: string;
  ward: string;
}

export function parseAddress(data: IData): string {
  let result = data.address;
  if (!!data?.ward && data.address.search(data?.ward) == -1) {
    result = result.replace(data?.district, '');
    result = result.replace(data?.province, '');
    result += ` ${data?.ward}, ${data?.district}, ${data?.province}`;
  } else if (!!data?.district && data.address.search(data?.district) == -1) {
    result = result.replace(data?.province, '');
    result += `, ${data?.district}, ${data?.province}`;
  } else if (!!data?.province && data.address.search(data?.province) == -1) {
    result += `, ${data?.province}`;
  }
  return result;
}

export function parseDataVNPOST(
  data: IVNPOSTData,
): {
  province: string;
  district: string;
  ward: string;
} {
  const { provinceData, districtData, wardData } = data;
  let { province, district, ward } = data;

  province = province.replace(
    /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_/gi,
    ' ',
  );
  district = district.replace(
    /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_/gi,
    ' ',
  );
  ward = ward.replace(
    /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_/gi,
    ' ',
  );
  const searchProvince = StringUtils.normalize(province).toLowerCase();

  const searchWard = StringUtils.normalize(ward).toLowerCase();

  const searchDistrict = StringUtils.normalize(district).toLowerCase();

  const senderProvince = find(provinceData, function(o) {
    return searchProvince.search(StringUtils.normalize(o?.provinceName).toLowerCase()) > -1;
  });

  let senderWard: Record<string, any> = {};
  if (senderProvince) {
    const lstDistrict = filter(districtData, function(o) {
      return (
        o?.provinceCode == senderProvince?.provinceCode &&
        searchDistrict.search(StringUtils.normalize(o?.districtName).toLowerCase()) > -1
      );
    });
    const lstWard = filter(wardData, function(o) {
      return searchWard.search(StringUtils.normalize(o?.communeName).toLowerCase()) > -1;
    });
    const lookupDistricts = [];
    lstDistrict.forEach((item: any) => {
      lookupDistricts[item?.districtCode] = item;
    });

    lstWard.forEach((item: any) => {
      if (lookupDistricts[item?.districtCode]) senderWard = item;
    });
  }
  return {
    province: senderProvince?.provinceCode,
    district: senderWard?.districtCode,
    ward: senderWard?.communeCode,
  };
}

export function parseDataSPXVN(data: any) {
  let { province, district, ward } = data;

  province = province.replace(
    /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_|\s-\s/gi,
    ' ',
  );
  district = district.replace(
    /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_|\s-\s/gi,
    ' ',
  );
  ward = ward.replace(
    /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_|\s-\s/gi,
    ' ',
  );

  const searchProvince = StringUtils.normalize(province).toLowerCase();

  let searchDistrict = StringUtils.normalize(district).toLowerCase();
  if(searchProvince == 'ho chi minh' && (searchDistrict == 'quan 2'|| searchDistrict == 'quan 9' )) {
    searchDistrict = 'thanh pho thu duc';
  }
  const splitDistrict = searchDistrict.split(' ');
  if (splitDistrict[0] == 'thi' && splitDistrict[1] == 'xa') {
    searchDistrict = splitDistrict.slice(2).join(' ');
  }
  if (splitDistrict[0] == 'thanh' && splitDistrict[1] == 'pho') {
    searchDistrict = splitDistrict.slice(2).join(' ');
  }
  if (splitDistrict[0] == 'quan' || splitDistrict[0] == 'huyen') {
    searchDistrict = splitDistrict.slice(1).join(' ');
  }

  let searchWard = StringUtils.normalize(ward).toLowerCase();
  const splitWard = searchWard.split(' ');
  if (splitWard[0] == 'thi' && splitWard[1] == 'tran') {
    searchWard = splitWard.slice(2).join(' ');
    const specialSplitWard = searchWard.split(' ');
    if (specialSplitWard[0] == 'nong' && specialSplitWard[1] == 'truong') {
      searchWard = 'nt ' + specialSplitWard.slice(2).join(' ');
    }
    if (specialSplitWard[0] == 'plei' && specialSplitWard[1] == 'can') {
      searchWard = specialSplitWard.join(' ').replace('c','k');
    }
  }
  if (splitWard[0] == 'phuong' || splitWard[0] == 'xa') {
    searchWard = splitWard.slice(1).join(' ');
  }

  let result_province = null;
  let result_district = null;
  let result_ward = null;
  let result_district_list = [];

  for (const spx_province in SPX_ADDRESS) {
    if (
      StringUtils.normalize(
        spx_province.replace(
          /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_|\s-\s/gi,
          ' ',
        ),
      )
        .toLowerCase()
        .includes(searchProvince)
    ) {
      result_province = spx_province;
      break;
    }
  }

  if (result_province) {
    for (const spx_district in SPX_ADDRESS[result_province]) {
      if (
        StringUtils.normalize(
          spx_district.replace(
            /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_|\s-\s/gi,
            ' ',
          ),
        )
          .toLowerCase()
          .includes(searchDistrict)
      ) {
        result_district_list.push(spx_district);
      }
    }

    if (result_district_list.length > 0) {
      for (const spx_district of result_district_list) {
        for (const spx_ward in SPX_ADDRESS[result_province][spx_district]) {
          if (
            StringUtils.normalize(
              spx_ward.replace(
                /\-|\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_|\s-\s/gi,
                ' ',
              ),
            )
              .toLowerCase()
              .includes(searchWard)
          ) {
            result_ward = spx_ward;
            break;
          }
        }
        if(result_ward) {
          result_district = spx_district;
          break;
        }
      }
    }
  }

  return {
    province: result_province,
    district: result_district,
    ward: result_ward,
  };
}
