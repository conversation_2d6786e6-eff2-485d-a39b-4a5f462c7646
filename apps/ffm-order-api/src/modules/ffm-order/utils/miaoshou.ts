import * as crypto from 'crypto';

export function getAesKey() {
    const charAtCodes = [
      64, 51,  52,  51,  56, 106,
      106, 59, 115, 105, 100, 117,
      102, 56,  51,  50
    ];
    return String.fromCharCode(...charAtCodes);
}
  
export function aesEncrypt(content) {
    const key = Buffer.from(getAesKey(), 'utf-8'); // 16 bytes
    const iv = Buffer.alloc(16, 0); // 16-byte null IV
  
    const cipher = crypto.createCipheriv('aes-128-cbc', key, iv);
    let encrypted = cipher.update(content, 'utf8', 'base64');
    encrypted += cipher.final('base64');
  
    return encrypted;
}