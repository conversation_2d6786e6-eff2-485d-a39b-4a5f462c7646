import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  catalogFfmConnection,
  catalogFfmWriteConnection,
  identityConnection,
  orderAgConnection,
  orderConnection,
} from 'core/constants/database-connection.constant';
import { getRmqHost } from 'core/utils/loadEnv';
import { District as AddressDistrict3PL } from '../../entities/address_district_3pl.entity';
import { PostCode } from '../../entities/address_post_code_3pl.entity';
import { Province as AddressProvince3PL } from '../../entities/address_province_3pl.entity';
import { Ward as AddressWard3PL } from '../../entities/address_ward_3pl.entity';
import { CarrierConfiguration } from '../../entities/carrier-configuration.entity';
import { Carrier } from '../../entities/carrier.entity';
import { Customer } from '../../entities/customer.entity';
import { FilterCollection } from '../../entities/filter-collection.entity';
import { HandOver } from '../../entities/hand-over.entity';
import { LeadTime } from '../../entities/lead-time.entity';
import { Logs } from '../../entities/logs.entity';
import { MarketplaceIntegration } from '../../entities/marketplace-integration.entity';
import { Note } from '../../entities/note.entity';
import { ODZConfigurationItem } from '../../entities/odz-configuration-item.entity';
import { ODZConfiguration } from '../../entities/odz-configuration.entity';
import { ODZ } from '../../entities/odz.entity';
import { OrderCarrierHistories } from '../../entities/order-carrier-history.entity';
import { OrderCarrier } from '../../entities/order-carrier.entity';
import { OrderProduct } from '../../entities/order-product.entity';
import { OrderStatusHistories } from '../../entities/order-status-history.entity';
import { OrderSyncFailedMPI } from '../../entities/order-sync-failed-mki.entity';
import { OrderTag } from '../../entities/order-tag.entity';
import { Order } from '../../entities/order.entity';
import { OrderWarning } from '../../entities/order_warning.entity';
import { RaiseTicketNote } from '../../entities/raise-ticket-note.entity';
import { RaiseTicket } from '../../entities/raise-ticket.entity';
import { ReImportItem } from '../../entities/re-import-item.entity';
import { ReImport } from '../../entities/re-import.entity';
import { Reason } from '../../entities/reason.entity';
import { Reconciliation } from '../../entities/reconciliation.entity';
import { Region } from '../../entities/regions.entity';
import { Remittance } from '../../entities/remittance.entity';
import { SortFieldManagement } from '../../entities/sort-field-management.entity';
import { SyncOrderSourceHistory } from '../../entities/sync-order-source-history.entity';
import { Tag } from '../../entities/tags.entity';
import { TemplateExport } from '../../entities/template-export.entity';
import { TicketTag } from '../../entities/ticket-tag.entity';
import { UserPancakeShop } from '../../entities/user-pancake-shop.entity';
import { WaybillReconciliation } from '../../entities/waybill_reconciliation.entity';
import { District } from '../../read-entities/ag-order/district.entity';
import { Province } from '../../read-entities/ag-order/province.entity';
import { Ward } from '../../read-entities/ag-order/ward.entity';
import { Country } from '../../read-entities/ffm-catalog/country.entity';
import { ProductVariation } from '../../read-entities/ffm-catalog/product-variation.entity';
import { Product } from '../../read-entities/ffm-catalog/product.entity';
import { StockInventory } from '../../read-entities/ffm-catalog/stock-inventory.entity';
import { SlotWarehouses } from '../../read-entities/ffm-catalog/warehouses.entity';
import { FulfillmentApiKey } from '../../read-entities/identity/fulfillment-api-key.entity';
import { FulfillmentPartnerClient } from '../../read-entities/identity/fulfillment-partner-client.entity';
import { FulfillmentPartner } from '../../read-entities/identity/fulfillment-partner.entity';
import { User } from '../../read-entities/identity/user.entity';
import { Address3PLController } from './controllers/address-3pl.controller';
import { CarrierConfigurationsController } from './controllers/carrier-configurations.controller';
import { CarrierController } from './controllers/carriers.controller';
import { CustomerController } from './controllers/customers.controller';
import { DashBoardController } from './controllers/dashboard.controller';
import { HandOverController2 } from './controllers/hand-over-v2.controller';
import { LeadTimeController } from './controllers/lead-time.controller';
import { ODZConfigController } from './controllers/odz-configuration.controller';
import { ODZController } from './controllers/odz.controller';
import { OrderPartnersController } from './controllers/order-partners.controller';
import { OrderSourceController } from './controllers/order-source.controller';
import { OrdersController } from './controllers/orders.controller';
import { PancakeController } from './controllers/pancake.controller';
import { RaiseTicketController } from './controllers/raise-ticket.controller';
import { ReImportController } from './controllers/re-import.controller';
import { ReasonController } from './controllers/reason.controller';
import { ReconciliationController } from './controllers/reconciliation.controller';
import { SortFieldController } from './controllers/sort-field-management.controller';
import { TagController } from './controllers/tags.controller';
import { TemplateExportController } from './controllers/template-export.controller';
import { HandOverProcessor } from './processors/hand-over.processor';
import { OrderSourceProcessor } from './processors/order-source.processor';
import { Address3PLService } from './services/address-3pl.service';
import { CarrierConfigurationsService } from './services/carrier-configurations.service';
import { CarrierService } from './services/carriers.service';
import { CustomerService } from './services/customers.service';
import { DashboardService } from './services/dashboard.service';
import { DashboardPtPerformanceService } from './services/dashboardPtPerformance.service';
import { HandOverService2 } from './services/hand-over-v2.service';
import { LeadTimeService } from './services/lead-time.service';
import { ODZConfigurationService } from './services/odz-configuration.service';
import { ODZService } from './services/odz.service';
import { OrderCarriersService } from './services/order-partners.service';
import { OrderSourceService } from './services/order-source.service';
import { OrderService } from './services/order.service';
import { PancakeService } from './services/pancake.service';
import { RaiseTicketService } from './services/raise-ticket.service';
import { ReImportService } from './services/re-import.service';
import { ReasonService } from './services/reason.service';
import { ffmOrderChannel, ReconciliationService } from './services/reconciliation.service';
import { SortFieldService } from './services/sort-field-management.service';
import { TagService } from './services/tags.service';
import { TemplateExportService } from './services/template-export.service';
import { Departments } from '../../entities/department.entity';
import { JourneyZaloOrder } from '../../entities/journey-zalo-orders.entity';
import { SystemSettingService } from './services/system-setting.service';
import { SystemSettingController } from './controllers/system-setting.controller';
import { SystemSetting } from '../../entities/system-setting.entity';
import { FFMQueueExternalService } from './services/queue-external.service';
import { FFMQueueTrackingOrderService } from './services/queue-tracking-order.service';
import { TicketLogs } from '../../entities/ticket-logs.entity';
import { OrderProductComboVariant } from '../../entities/order-product-combo-variant.entity';
import { TicketAppointment } from '../../entities/ticket-appointment.entity';
import { RaiseTicketProcessor } from './processors/ticket.processor';
import { OrderProcessor } from './processors/order.processor';
import { WarehouseTag } from '../../entities/warehouse-tag.entity';
import { CollectingPackage } from '../../entities/collecting-package.entity';
import { CollectingPackageController } from './controllers/collecting-package.controller';
import { CollectingPackageService } from './services/collecting-package.service';
import { CollectingPackageProcessor } from './processors/collecting-package.processor';
import { InventoryLineItem } from '../../ffm-catalog-entities/inventory-line-item.entity';
import { InventoryLogs } from '../../ffm-catalog-entities/inventory-logs.entity';
import { InventoryManagement } from '../../ffm-catalog-entities/inventory-management.entity';
import { ProductComboVariant } from '../../read-entities/ffm-catalog/product-combo-variant.entity';
import { StockInventoryInOrder } from './services/stock-inventory.service';
import { MiaoshouController } from './controllers/miaoshou.controller';
import { MiaoshouService } from './services/miaoshou.service';
import { ProductsService } from './services/products.service';
import { WarehouseClientAllocation } from '../../read-entities/ffm-catalog/warehouse_client_allocation.entity';

@Module({
  imports: [
    RabbitMQModule.forRootAsync(RabbitMQModule, {
      useFactory: () => ({
        exchanges: [
          {
            name: 'ffm-order',
            type: 'direct',
          },
          {
            name: 'ffm-sync-order',
            type: 'direct',
          },
          {
            name: 'ffm-order-external',
            type: 'direct',
          },
          {
            name: 'ffm-order-tracking-data',
            type: 'direct',
          },
          {
            name: 'order-service',
            type: 'direct',
          },
          {
            name: 'stock-inventory-service',
            type: 'direct',
          },
          {
            name: 'ffm-catalog-service',
            type: 'direct',
          },
        ],
        uri: getRmqHost(),
        prefetchCount: Number(process.env.QUEUE_LENGTH || 20),
        connectionInitOptions: { wait: false },
        registerHandlers: !process.env.CONSUMER || process.env.CONSUMER == 'true' ? true : false,
      }),
    }),
    TypeOrmModule.forFeature(
      [User, FulfillmentApiKey, FulfillmentPartner, FulfillmentPartnerClient],
      identityConnection,
    ),
    TypeOrmModule.forFeature(
      [
        Order,
        Carrier,
        Note,
        Customer,
        OrderCarrier,
        OrderProduct,
        Logs,
        HandOver,
        CarrierConfiguration,
        UserPancakeShop,
        Tag,
        Reason,
        OrderCarrierHistories,
        RaiseTicket,
        RaiseTicketNote,
        ODZ,
        ODZConfiguration,
        ReImport,
        ReImportItem,
        FilterCollection,
        // AddressMapping3PL,
        AddressProvince3PL,
        AddressDistrict3PL,
        AddressWard3PL,
        PostCode,
        OrderTag,
        LeadTime,
        TicketTag,
        Remittance,
        Reconciliation,
        WaybillReconciliation,
        SortFieldManagement,
        OrderSyncFailedMPI,
        MarketplaceIntegration,
        SyncOrderSourceHistory,
        TemplateExport,
        OrderWarning,
        ODZConfigurationItem,
        Region,
        OrderStatusHistories,
        Departments,
        SystemSetting,
        JourneyZaloOrder,
        SystemSetting,
        TicketLogs,
        OrderProductComboVariant,
        TicketAppointment,
        WarehouseTag,
        CollectingPackage,
      ],
      orderConnection,
    ),
    TypeOrmModule.forFeature(
      [StockInventory, SlotWarehouses, WarehouseClientAllocation, Country, Product, ProductVariation, ProductComboVariant],
      catalogFfmConnection,
    ),
    TypeOrmModule.forFeature(
      [InventoryLineItem, InventoryLogs, InventoryManagement],
      catalogFfmWriteConnection,
    ),
    TypeOrmModule.forFeature([Ward, District, Province], orderAgConnection),
    BullModule.registerQueue({ name: 'order-ffm' }),
    BullModule.registerQueue({ name: 'order-source' }),
    BullModule.registerQueue({ name: 'hand-over' }),
    BullModule.registerQueue({ name: 'raise-ticket' }),
    BullModule.registerQueue({ name: 'orders' }),
    BullModule.registerQueue({ name: 'collecting-package' }),
  ],
  controllers: [
    OrdersController,
    CarrierController,
    CustomerController,
    HandOverController2,
    CarrierConfigurationsController,
    OrderPartnersController,
    PancakeController,
    TagController,
    ReasonController,
    RaiseTicketController,
    ODZController,
    ODZConfigController,
    ReImportController,
    DashBoardController,
    Address3PLController,
    LeadTimeController,
    ReconciliationController,
    SortFieldController,
    OrderSourceController,
    TemplateExportController,
    SystemSettingController,
    CollectingPackageController,
    MiaoshouController
  ],
  providers: [
    OrderService,
    CarrierService,
    CustomerService,
    HandOverService2,
    CarrierConfigurationsService,
    OrderCarriersService,
    PancakeService,
    TagService,
    ReasonService,
    RaiseTicketService,
    ODZService,
    ODZConfigurationService,
    HandOverProcessor,
    ReImportService,
    DashboardService,
    Address3PLService,
    DashboardPtPerformanceService,
    LeadTimeService,
    ProductsService,
    ReconciliationService,
    LeadTimeService,
    SortFieldService,
    ffmOrderChannel.Provider,
    OrderSourceService,
    OrderSourceProcessor,
    TemplateExportService,
    SystemSettingService,
    FFMQueueExternalService,
    FFMQueueTrackingOrderService,
    RaiseTicketProcessor,
    OrderProcessor,
    CollectingPackageService,
    CollectingPackageProcessor,
    StockInventoryInOrder,
    MiaoshouService
  ],
  exports: [BullModule.registerQueue({ name: 'order-ffm' })],
})
export class FFMOrderModule {}
