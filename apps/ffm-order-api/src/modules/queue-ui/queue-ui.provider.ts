import { BullAdapter, setQueues } from 'bull-board';
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class QueueUIProvider {
  constructor(
    @InjectQueue('order-ffm') private readonly orderQueue: Queue,
    @InjectQueue('hand-over') private readonly hoQueue: Queue,
  ) {
    setQueues([
      new BullAdapter(this.orderQueue),
      new BullAdapter(this.hoQueue),
    ]);
  }
}
