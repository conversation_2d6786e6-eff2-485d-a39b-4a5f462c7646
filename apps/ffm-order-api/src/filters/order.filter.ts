import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { isNil } from 'lodash';
import {
  OrderScenarioType,
  OrderSearchRecipient,
  OrderTypeSearch,
  TagMethodType,
  TagOperatorType,
  WhTagMethodType,
} from '../enums/order-search-recipient.enum';
import {
  SortType,
  TypeOrder,
  TypeSearchOrderFilter,
  TypeSortOrder,
  WarningType,
} from '../enums/order-status.enum';
import { CasesFilter, CasesProcessedFilter, TypeReimportFilter } from '../enums/re-import.enum';
import { RemoveEscapeTransform } from 'core/decorators/remove-escape-transform.decorator';
import { NumberTransform } from 'core/decorators/number-transform.decorator';

export class FilterOrder {
  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  @RemoveEscapeTransform()
  query?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  @RemoveEscapeTransform()
  search?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  searchRecipient?: string;

  @ApiProperty({ required: false, enum: OrderSearchRecipient })
  @IsEnum(OrderSearchRecipient)
  @IsOptional()
  @EnumTransform(OrderSearchRecipient)
  typeSearchRecipient?: OrderSearchRecipient;

  @ApiProperty({ required: false, enum: OrderTypeSearch })
  @IsEnum(OrderTypeSearch)
  @IsOptional()
  @EnumTransform(OrderTypeSearch)
  typeSearch?: OrderTypeSearch;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  externalId?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  countryCode?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  product?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  creatorId?: number;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  waybillNumber?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  carrierFrom?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  carrierTo?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  customerFrom?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  customerTo?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  carrierEDD?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  customerEDD?: Date;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  clientIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  projectIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  carrierIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  tagIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  whTagIds?: number[];

  @ApiProperty({ required: false, enum: TagMethodType })
  @IsEnum(TagMethodType)
  @IsOptional()
  @EnumTransform(TagMethodType)
  tagMethod?: TagMethodType;

  @ApiProperty({ required: false, enum: WhTagMethodType })
  @IsEnum(WhTagMethodType)
  @IsOptional()
  @EnumTransform(WhTagMethodType)
  whTagMethod?: WhTagMethodType;

  @ApiProperty({ required: false, enum: TagOperatorType })
  @IsEnum(TagOperatorType)
  @IsOptional()
  @EnumTransform(TagOperatorType)
  operator?: TagOperatorType;

  @ArrayTransform()
  @IsOptional()
  warehouseIds?: number[];

  @ArrayTransform()
  @IsOptional()
  returnWarehouseIds?: number[];

  @ArrayTransform()
  @IsOptional()
  productIds?: number[];

  @ArrayTransform()
  @IsOptional()
  categoryIds?: number[];

  @IsOptional()
  @Transform(({ value }) => {
    return !isNil(value) ? (Array.isArray(value) ? value : value.split(',')) : undefined;
  })
  ids?: string[] | string;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  waybill?: boolean;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  printWaybill?: boolean;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  warningOrder?: boolean;

  @ApiProperty({ required: false, enum: WarningType, isArray: true })
  // @IsEnum(WarningType, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(WarningType)
  warningType?: WarningType[];

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  handOver?: boolean;

  @ApiProperty({ required: false, enum: OrderFFMStatus, isArray: true })
  @IsEnum(OrderFFMStatus, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(OrderFFMStatus)
  status?: OrderFFMStatus[];

  @ApiProperty({ required: false, enum: OrderFFMStatus, isArray: true })
  @IsEnum(OrderFFMStatus, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(OrderFFMStatus)
  tabStatus?: OrderFFMStatus[];

  @ApiProperty({ required: false, enum: OrderScenarioType })
  @IsEnum(OrderScenarioType)
  @IsOptional()
  @EnumTransform(OrderScenarioType)
  typeScenario?: OrderScenarioType;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  excludeTicket?: boolean;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  searchBySttChanged?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  areaCode?: string[];

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  limitScenario?: number;

  @ApiProperty({ required: false, enum: TypeSearchOrderFilter })
  @IsOptional()
  @EnumTransform(TypeSearchOrderFilter)
  typeFilter?: TypeSearchOrderFilter;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  provinceIds?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  districtIds?: string[];

  @ApiProperty({ required: false, enum: TypeSortOrder })
  @IsOptional()
  @EnumTransform(TypeSortOrder)
  sortBy?: TypeSortOrder;

  @ApiProperty({ required: false, enum: SortType })
  @IsOptional()
  // @IsNotEmpty()
  @EnumTransform(SortType)
  sortType: SortType;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @NumberTransform()
  minWeight: number;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @NumberTransform()
  maxWeight: number;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  searchExport?: boolean;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  isCollect?: boolean;

  @ApiProperty({ required: false, enum: TagMethodType })
  @IsEnum(TagMethodType)
  @IsOptional()
  @EnumTransform(TagMethodType)
  productMethod?: TagMethodType;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  warehouseId?: number;

  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  warehouseSeasonIds?: number[];
  
  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  notInSeason?: boolean;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  prioritizedSorting?: boolean;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  isReimport?: boolean;

  @ApiProperty({ required: false, enum: TypeOrder, isArray: true })
  @IsEnum(TypeOrder, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(TypeOrder)
  typeOrder?: TypeOrder[];
}

export class FilterStatisCustomerOrder {
  @ApiProperty()
  @IsNotEmpty()
  recipientPhone?: string;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  isSort?: boolean;
}
export class FilterReImport {
  @ApiProperty({ required: true, isArray: true })
  @ArrayTransform()
  @IsNotEmpty()
  warehouseIds?: number[];

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  @RemoveEscapeTransform()
  query?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  carrierIds?: number[];

  @ApiProperty({ required: false, enum: CasesFilter, isArray: true })
  @IsEnum(CasesFilter, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(CasesFilter)
  typeFilters?: CasesFilter[];

  @ApiProperty({ required: false, enum: CasesProcessedFilter, isArray: true })
  @IsEnum(CasesProcessedFilter, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(CasesProcessedFilter)
  typeProcessedFilters?: CasesProcessedFilter[];

  @ApiProperty({ required: false, enum: TypeReimportFilter })
  @IsOptional()
  @EnumTransform(TypeReimportFilter)
  type: TypeReimportFilter;
}

export class FilterInvalidOrder {
  @IsOptional()
  @Transform(({ value }) => {
    return !isNil(value) ? (Array.isArray(value) ? value : value.split(',')) : undefined;
  })
  ids?: string[] | string;

  @ApiProperty({ required: false })
  @IsOptional()
  carrierId?: number;
}

export class FilterDuplicateOrder {
  @ApiProperty()
  @IsNotEmpty()
  orderId?: number;

  @ArrayTransform()
  @IsNotEmpty()
  productIds?: number[];

  @ApiProperty()
  @IsNotEmpty()
  recipientPhone?: string;

  @ApiProperty()
  @IsNotEmpty()
  countryId?: string;
}
