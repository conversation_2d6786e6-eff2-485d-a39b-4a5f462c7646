import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { isNil } from 'lodash';
import {
  OrderScenarioType,
  OrderSearchRecipient,
  OrderTypeSearch,
  TagMethodType,
  TagOperatorType,
} from '../enums/order-search-recipient.enum';
import { SortType, TypeSearchOrderFilter, TypeSortOrder } from '../enums/order-status.enum';
import { CasesFilter } from '../enums/re-import.enum';
import { StatusMIEnum, TypeMIEnum } from '../enums/marketplace-integration.enum';
import { RemoveEscapeTransform } from 'core/decorators/remove-escape-transform.decorator';
import { PlatformMiaoshouStatus } from '../enums/miaoshou/platform.enum';

export enum OrderSourceSortBy {
  shopName = 'shopName',
  clientId = 'clientId',
  updatedAt = 'updatedAt',
  createdAt = 'createdAt'
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC'
}

export class FilterOrderSource {
  @IsOptional()
  @ApiProperty({ required: false, isArray: true })
  @ArrayTransform()
  @RemoveEscapeTransform()
  query?: string[];

  @ArrayTransform()
  @IsOptional()
  @ApiProperty({ required: false, isArray: true  })
  countryIds?: string[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  clientIds?: number[];

  @ApiProperty({ required: false, enum: StatusMIEnum, isArray: true })
  @IsOptional()
  @EnumTransform(StatusMIEnum)
  @ArrayTransform()
  status: StatusMIEnum[];
  
  @ApiProperty({ required: false, enum: TypeMIEnum, isArray: true })
  @IsOptional()
  @EnumTransform(TypeMIEnum)
  @ArrayTransform()
  type?: TypeMIEnum[];

  @ApiProperty({ required: false, enum: PlatformMiaoshouStatus, isArray: true })
  @IsOptional()
  @EnumTransform(PlatformMiaoshouStatus)
  @ArrayTransform()
  platform?: PlatformMiaoshouStatus[];

  @ApiProperty({ required: false, enum: OrderSourceSortBy })
  @IsOptional()
  @EnumTransform(OrderSourceSortBy)
  sortBy?: OrderSourceSortBy;

  @ApiProperty({ required: false, enum: SortOrder })
  @IsOptional()
  @EnumTransform(SortOrder)
  sortType?: SortOrder;
}

export class FilterSyncOrderFailed {
  // @IsOptional()
  // @ApiProperty({ required: false, isArray: true  })
  // query?: string;

  // @ArrayTransform()
  // @IsOptional()
  // @ApiProperty({ required: false, isArray: true  })
  // countryIds?: string[];

  // @ArrayTransform()
  // @ApiProperty({ required: false, isArray: true })
  // @IsOptional()
  // clientIds?: number[];

  @ApiProperty({ required: false, enum: TypeMIEnum, isArray: true })
  @IsOptional()
  @EnumTransform(TypeMIEnum)
  @ArrayTransform()
  type?: TypeMIEnum[];

}
