import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import {
  RaiseTicketNoteEnum,
  RaiseTicketStatusEnum,
  RaiseTicketTypeEnum,
  TicketAppointmentTypeSearch,
  TicketTypeSearch,
  TypeSearchTicketFilter,
  TypeSortTicket,
} from '../enums/raise-ticket.enum';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { TagMethodType, TagOperatorType } from '../enums/order-search-recipient.enum';
import { SortType } from '../enums/order-status.enum';
import { RemoveEscapeTransform } from 'core/decorators/remove-escape-transform.decorator';

export class FilterRaiseTicket {
  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  @RemoveEscapeTransform()
  query?: string;

  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  @RemoveEscapeTransform()
  search?: string;

  @ApiProperty({ required: false, enum: RaiseTicketTypeEnum })
  // @IsEnum(RaiseTicketTypeEnum)
  @IsOptional()
  @EnumTransform(RaiseTicketTypeEnum)
  @ArrayTransform()
  types?: RaiseTicketTypeEnum[];

  @ApiProperty({ required: false, enum: RaiseTicketStatusEnum, isArray: true })
  // @IsEnum(RaiseTicketStatusEnum)
  @IsOptional()
  @EnumTransform(RaiseTicketStatusEnum)
  @ArrayTransform()
  status?: RaiseTicketStatusEnum[];

  @ApiProperty({ required: false, enum: OrderFFMStatus, isArray: true })
  @IsOptional()
  @EnumTransform(OrderFFMStatus)
  @ArrayTransform()
  orderStatus?: OrderFFMStatus[];

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  clientIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  carrierIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @IsNumber(undefined, { each: true })
  ids?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  assigneeIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  tagIds?: number[];

  @ApiProperty({ required: false, enum: TagMethodType })
  @IsEnum(TagMethodType)
  @IsOptional()
  @EnumTransform(TagMethodType)
  tagMethod?: TagMethodType;

  @ApiProperty({ required: false, enum: TagOperatorType })
  @IsEnum(TagOperatorType)
  @IsOptional()
  @EnumTransform(TagOperatorType)
  operator?: TagOperatorType;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  orderId?: number;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  limit?: number;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  page?: number;

  @ApiProperty({ required: false, enum: RaiseTicketNoteEnum, isArray: true })
  @IsOptional()
  @EnumTransform(RaiseTicketNoteEnum)
  @ArrayTransform()
  resultIds?: RaiseTicketNoteEnum[];

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  isResultNull?: boolean;

  @ApiProperty({ required: false, type: 'boolean' })
  @IsOptional()
  @BooleanTransform()
  isAssigneeNull?: boolean;

  @ApiProperty({ required: false, enum: TicketTypeSearch })
  @IsEnum(TicketTypeSearch)
  @IsOptional()
  @EnumTransform(TicketTypeSearch)
  typeSearch?: TicketTypeSearch;

  @ApiProperty({ required: false, enum: TypeSortTicket })
  @IsOptional()
  @EnumTransform(TypeSortTicket)
  sortBy?: TypeSortTicket;

  @ApiProperty({ required: false, enum: SortType })
  @IsOptional()
  // @IsNotEmpty()
  @EnumTransform(SortType)
  sortType: SortType;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  productIds?: number[];

  @ApiProperty({ required: false, enum: TypeSearchTicketFilter })
  @IsOptional()
  @EnumTransform(TypeSearchTicketFilter)
  typeFilter?: TypeSearchTicketFilter;

  @ApiProperty({ required: false, enum: TicketAppointmentTypeSearch })
  @IsOptional()
  @EnumTransform(TicketAppointmentTypeSearch)
  ticketAppointmentTypeSearch?: TicketAppointmentTypeSearch;

  @ApiProperty({ required: false })
  @IsOptional()
  ticketAppointmentType?: string;
}
