import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { TypeOfDashBoardReturnTicket } from '../enums/dashboard.enum';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { TypeOfFilter } from '../constants/filter.contants';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { TagMethodType } from '../enums/order-search-recipient.enum';

export class FilterDashboardTicket {
  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  clientIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  carrierIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  productIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  staffIds?: number[];

}

export class FilterDashboardTicketReturn {
  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  productIds?: string[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  clientIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  carrierIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  regionIds?: string[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  ptIds?: number[];

  @ApiProperty({ required: false, enum: TypeOfDashBoardReturnTicket })
  @IsOptional()
  @EnumTransform(TypeOfDashBoardReturnTicket)
  type?: TypeOfDashBoardReturnTicket;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isFilterByOrderCreatedAt?: boolean;
}

export class FilterDashboardGeneralInformation {
  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  productIds?: string[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  clientIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  projectIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  carrierIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  tagIds?: number[];
  
  @ApiProperty({ required: false, enum: TagMethodType })
  @IsEnum(TagMethodType)
  @IsOptional()
  @EnumTransform(TagMethodType)
  tagMethod?: TagMethodType;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  regionIds?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isFilterByOrderCreatedAt?: boolean;
}

export class TypeFilter {
  @ApiProperty({ required: false, enum: TypeOfFilter })
  @IsEnum(TypeOfFilter, { each: true })
  @IsOptional()
  @EnumTransform(TypeOfFilter)
  type?: TypeOfFilter;
}

export class FilterDashboardPtPerformance {
  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  ptIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  clientIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  carrierIds?: number[];

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  productIds?: string[];

  @ApiProperty({ required: false, enum: TypeOfDashBoardReturnTicket })
  @IsOptional()
  @EnumTransform(TypeOfDashBoardReturnTicket)
  type?: TypeOfDashBoardReturnTicket;

  @ArrayTransform()
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  whIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isTotal?: boolean;
}
