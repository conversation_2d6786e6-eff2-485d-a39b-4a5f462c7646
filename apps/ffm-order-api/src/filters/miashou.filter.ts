import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { RemoveEscapeTransform } from 'core/decorators/remove-escape-transform.decorator';
import { PlatformMiaoshouStatus } from '../enums/miaoshou/platform.enum';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';

export class MiaoshouFilter {
  // @IsOptional()
  // @ApiProperty({ required: false, type: 'string' })
  // @RemoveEscapeTransform()
  // keyword?: string;

  // @ApiProperty()
  // @IsNotEmpty()
  // pageSize: string;

  // @ApiProperty()
  // @IsNotEmpty()
  // countryId: number;

  @ApiProperty({ required: false, enum: PlatformMiaoshouStatus })
  @IsOptional()
  @EnumTransform(PlatformMiaoshouStatus)
  platform: PlatformMiaoshouStatus;

  @ApiProperty({ required: false, type: 'string', isArray: true })
  @IsOptional()
  @ArrayTransform()
  tags?: string[];
}

export class MiaoshouOrderFilter {
  @IsOptional()
  @ApiProperty({ required: false, type: 'string' })
  @RemoveEscapeTransform()
  keyword?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isSync?: boolean;

  @ApiProperty({ required: false, type: 'string', isArray: true })
  @IsOptional()
  @ArrayTransform()
  tags?: string[];

  @ApiProperty()
  @IsOptional()
  page?: number;

  @ApiProperty()
  @IsOptional()
  limit?: number;
}
