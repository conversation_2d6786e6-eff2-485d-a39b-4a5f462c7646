import { Controller, Get, Headers, Query, Req } from '@nestjs/common';
import { OrderStatisticsFilter } from 'apps/analytics-api/src/filters/order-statistics.filter';
import { TagGroupStatisticsFilter } from 'apps/analytics-api/src/filters/tag-group-statistics.filter';
import { TopTagStatisticsFilter } from 'apps/analytics-api/src/filters/top-tag-statistics.filter';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { Permission } from 'core/enums/permission.enum';
import { OrderStatisticsService } from '../services/order-statistics.service';
import { FbOrderStatisticsFilter } from 'apps/analytics-api/src/filters/fb-order-statistics.filter';
import { ApiTags } from '@nestjs/swagger';

@Controller('order-statistics')
@ApiTags('Order Stats')
export class OrderStatisticsController {
  constructor(private statisticsService: OrderStatisticsService) {}

  @Get('revenue')
  @Auth()
  async getRevenue(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getRevenue(filter, header, req);
  }

  @Get('general-revenue')
  @Auth()
  async getGeneralRevenue(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getGeneralRevenue(filter, header, req);
  }

  @Get('revenue/me')
  @Auth()
  async getMyRevenue(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.userId = req.user.id;
    return await this.statisticsService.getRevenue(filter, header, req);
  }

  @Get('general-revenue/me')
  @Auth()
  async getMyGeneralRevenue(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.userId = req.user.id;
    filter.groupBy = ['createdAt'];
    return await this.statisticsService.getGeneralRevenue(filter, header, req);
  }

  @Get('returned-stats')
  @Auth()
  async getReturnedStats(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getReturnedStats(filter, header, req);
  }

  @Get('returned-stats/detail')
  @Auth()
  async getReturnedDetailStats(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getReturnedDetailStats(filter, header, req);
  }

  @Get('returned-stats/me')
  @Auth()
  async getMyReturnedStats(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.userId = req.user.id;
    return await this.statisticsService.getReturnedStats(filter, header, req);
  }

  @Get('returned-stats/detail/me')
  @Auth()
  async getMyReturnedDetailStats(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.userId = req.user.id;
    return await this.statisticsService.getReturnedDetailStats(filter, header, req);
  }

  @Get('tag-groups')
  @Auth()
  async getTagGroups(
    @Query() filter: TagGroupStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getTagGroups(filter, header, req);
  }

  @Get('tag-groups/detail')
  @Auth()
  async getDetailTagGroups(
    @Query() filter: TagGroupStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getDetailTagGroups(filter, header, req);
  }

  @Get('top-tags')
  @Auth()
  async getTopTags(
    @Query() filter: TopTagStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getTopTags(filter, header, req);
  }

  @Get('fb-orders-revenue')
  @Auth()
  async getFbOrdersRevenue(
    @Query() filter: FbOrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getFbOrdersRevenue(filter, header, req);
  }
}
