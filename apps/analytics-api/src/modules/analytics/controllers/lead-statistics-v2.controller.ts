import { Controller, Get, Headers, Query, Req } from '@nestjs/common';
import { LeadStatisticsV2Service } from '../services/lead-statistics-v2.service';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { LeadPerformanceStatisticsFilter, LeadStatisticFilter } from 'apps/analytics-api/src/filters/lead-statistics-v2.filter';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { SalePermission } from 'core/enums/sale-permissions';
import { DashboardPermission } from 'core/enums/sale-permissions/dashboard-permission.enum';

@Controller('lead-statistics/v2')
@ApiTags('TLS Stats v2')
export class LeadStatisticsV2Controller {
  constructor(private statisticsService: LeadStatisticsV2Service) {}

  @Get('overview')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.telesalesOverview],
    [SalePermission.dashboard, DashboardPermission.telesalesPerformance],
    [SalePermission.dashboard, DashboardPermission.telesalesReasons],
  )
  @ApiOperation({
    summary: 'Dữ liệu Telesales',
    description: `Có thể dùng API này để lấy dữ liệu báo cáo Telesales. Bao gồm những báo cáo sau: 
      \n
      - Tổng quan (Overview) 
      - CVR by Projects 
      - Sales by Dates
      - Dữ liệu theo Ngày (Lead by Dates)`,
  })
  async getOverview(
    @Query() filter: LeadStatisticFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getOverview(filter, header, req);
  }

  @Get('overview/products')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.telesalesOverview],
    [SalePermission.dashboard, DashboardPermission.telesalesPerformance],
    [SalePermission.dashboard, DashboardPermission.telesalesReasons],
  )
  @ApiOperation({
    summary: 'Dữ liệu theo Sản phẩm (Lead by Products)',
    description: `Params\n
      - groupBy: date (Để lấy dữ liệu theo từng ngày)
      - groupBy: projectId (Để lấy dữ liệu theo dự án)
      - groupBy: state (Để lấy dữ liệu theo trạng thái chăm sóc)
    `,
  })
  async getProductsOverview(
    @Query() filter: LeadStatisticFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getProductsOverview(filter, header, req);
  }

  @Get('overview/performance')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.telesalesPerformance],
  )
  @ApiOperation({
    summary: 'Hiệu suất nhân sự (Performance)',
    description: `Params\n
      - groupBy: date (Để lấy dữ liệu theo từng ngày)
      - groupBy: userId (Để lấy dữ liệu theo người phụ trách)
    `,
  })
  async getPerformanceReport(
    @Query() filter: LeadPerformanceStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getPerformance(filter, header, req);
  }

  @Get('overview/reasons')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.telesalesReasons],
  )
  @ApiOperation({
    summary: 'Lý do chăm sóc (Care Reasons)',
    description: `Params\n
      - groupBy: date (Để lấy dữ liệu theo từng ngày)
      - groupBy: state (Để lấy dữ liệu theo trạng thái chăm sóc)
    `,
  })
  async getReasonsReport(
    @Query() filter: LeadStatisticFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getReasonsReport(filter, header, req);
  }
}
