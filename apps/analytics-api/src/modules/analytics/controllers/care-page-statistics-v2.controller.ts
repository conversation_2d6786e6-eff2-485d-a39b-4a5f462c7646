import { Controller, Get, Headers, Query, Req } from '@nestjs/common';
import { CarePageStatisticsV2Service } from '../services/care-page-statistics-v2.service';
import {
  CarePagePerformanceStatisticsFilter,
  CarePageStatisticsFilter,
  OverviewCarePageStatisticsFilter,
} from 'apps/analytics-api/src/filters/care-page-statistics.filter';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { SalePermission } from 'core/enums/sale-permissions';
import { DashboardPermission } from 'core/enums/sale-permissions/dashboard-permission.enum';
import { getRequestContext } from '../../../../../../core/hooks/request-context.hook';

@Controller('care-page-statistics/v2')
@ApiTags('Care Page Stats v2')
export class CarePageStatisticsV2Controller {
  constructor(private carePageStatisticsService: CarePageStatisticsV2Service) {
  }

  @Get('overview')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.carePageOverview],
    [SalePermission.dashboard, DashboardPermission.carePagePerformance],
    [SalePermission.dashboard, DashboardPermission.carePageReasons],
  )
  @ApiOperation({
    summary: 'Dữ liệu Carepage',
    description: `Có thể dùng API này để lấy dữ liệu báo cáo Carepage`,
  })
  async getOverview(
    @Query() filter: OverviewCarePageStatisticsFilter,
    @Headers() headers: Record<string, string>,
    @Req() request?: Record<string, any>,
  ) {
    // console.time('process_overview_' + getRequestContext().requestId);
    const res = await this.carePageStatisticsService.getOverview(filter, headers, request);
    // console.timeEnd('process_overview_' + getRequestContext().requestId);
    // console.log('overview', res);
    return res;
  }

  @Get('overview/comments')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.carePageOverview],
    [SalePermission.dashboard, DashboardPermission.carePagePerformance],
    [SalePermission.dashboard, DashboardPermission.carePageReasons],
  )
  @ApiOperation({
    summary: 'Dữ liệu về các cuộc hội thoại là bình luận',
    description: `Có thể dùng API này để lấy dữ liệu báo cáo Carepage`,
  })
  getCommentsOverview(
    @Query() filter: CarePageStatisticsFilter,
    @Headers() headers: Record<string, string>,
    @Req() request?: Record<string, any>,
  ) {
    return this.carePageStatisticsService.getCommentsOverview(filter, headers, request);
  }

  @Get('overview/reasons')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.carePageReasons],
  )
  @ApiOperation({
    summary: 'Lý do chăm sóc (Care Reasons)',
    description: `Params\n
      - groupBy: date (Để lấy dữ liệu theo từng ngày)
      - groupBy: state (Để lấy dữ liệu theo trạng thái chăm sóc)
    `,
  })
  async getReasonsReport(
    @Query() filter: CarePageStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.carePageStatisticsService.getReasonsReport(filter, header, req);
  }

  @Get('overview/performance')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.carePagePerformance],
  )
  @ApiOperation({
    summary: 'Hiệu suất nhân sự (Performance)',
    description: `Params\n
      - groupBy: date (Để lấy dữ liệu theo từng ngày)
      - groupBy: userId (Để lấy dữ liệu theo người phụ trách)
    `,
  })
  async getPerformance(
    @Query() filter: CarePagePerformanceStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.carePageStatisticsService.getPerformance(filter, header, req);
  }
}
