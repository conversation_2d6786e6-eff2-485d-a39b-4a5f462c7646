import { Controller, Get, Headers, Query, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LeadStatisticFilter } from 'apps/analytics-api/src/filters/lead-statistics.filter';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { LeadStatisticsService } from '../services/lead-statistics.service';

@Controller('lead-statistics')
@ApiTags('TLS Stats')
export class LeadStatisticsController {
  constructor(private statisticsService: LeadStatisticsService) {}

  @Get('general-report')
  @Auth()
  getGeneralReport(
    @Query() filter: LeadStatisticFilter,
    @Headers() headers: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.statisticsService.getGeneralReport(filter, headers, req);
  }

  @Get('detail-report')
  @Auth()
  getDetailReport(
    @Query() filter: LeadStatisticFilter,
    @Headers() headers: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.statisticsService.getDetailReport(filter, headers, req);
  }

  @Get('lead-reasons-report')
  @Auth()
  getLeadReasonsReport(
    @Query() filter: LeadStatisticFilter,
    @Headers() headers: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.statisticsService.getLeadReasonsReport(filter, headers, req);
  }
}
