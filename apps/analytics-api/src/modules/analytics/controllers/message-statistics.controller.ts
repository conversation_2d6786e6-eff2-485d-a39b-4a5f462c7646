import { Controller, Get, Headers, Query, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CarePageStatisticsFilter } from 'apps/analytics-api/src/filters/care-page-statistics.filter';
import { CloseRateStatisticsFilter } from 'apps/analytics-api/src/filters/close-rate-statistics.filter';
import { ConversationStatisticsFilter } from 'apps/analytics-api/src/filters/conversation-statistics.filter';
import { NewConversationStatisticsFilter } from 'apps/analytics-api/src/filters/new-conversation-statistics.filter';
import { TotalConversationStatisticsFilter } from 'apps/analytics-api/src/filters/total-conversation-statistics.filter';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { CarePageStatisticsService } from '../services/care-page-statistics.service';
import { MessageStatisticsService } from '../services/message-statistics.service';

@Controller('message-statistics')
@ApiTags('Care Page Stats')
export class MessageStatisticsController {
  constructor(
    private messStatsService: MessageStatisticsService,
    private carePageStatsService: CarePageStatisticsService,
  ) {}

  @Get('total-conversations-stats')
  @Auth()
  getTotalConversationsStats(
    @Query() filter: TotalConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = filter.assignedTo || filter.creatorIds;
    return this.messStatsService.getTotalConversationsStats(filter, header, req);
  }

  @Get('total-conversations-by-date')
  @Auth()
  getTotalConversationsByDate(
    @Query() filter: TotalConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getTotalConversationsByDate(filter, header, req);
  }

  @Get('total-conversations-by-assignees')
  @Auth()
  getTotalConversationsByAssignees(
    @Query() filter: TotalConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getTotalConversationsByAssignees(filter, header, req);
  }

  @Get('new-conversations-stats')
  @Auth()
  getNewConversationsStats(
    @Query() filter: NewConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = filter.assignedTo || filter.creatorIds;
    return this.messStatsService.getNewConversationsStats(filter, header, req);
  }

  @Get('orders-stats')
  @Auth()
  getNewOrdersStats(
    @Query() filter: NewConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = filter.assignedTo || filter.creatorIds;
    return this.messStatsService.getOrdersStats(filter, header, req);
  }

  @Get('new-conversations-by-date')
  @Auth()
  getNewConversationsByDate(
    @Query() filter: NewConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getNewConversationsByDate(filter, header, req);
  }

  @Get('new-conversations-by-assignees')
  @Auth()
  getNewConversationsByAssignees(
    @Query() filter: NewConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getNewConversationsByAssignees(filter, header, req);
  }

  @Get('new-conversations-by-assignees-without-date-group')
  @Auth()
  getNewConversationsByAssigneesWithoutDateGroup(
    @Query() filter: NewConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getNewConversationsByAssigneesWithoutDateGroup(
      filter,
      header,
      req,
    );
  }

  @Get('new-conversations-tags-by-date')
  @Auth()
  getNewConversationsTagsByDate(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getNewConversationsTagsByDate(filter, header, req);
  }

  @Get('new-conversations-tags-by-assignees')
  @Auth()
  getNewConversationsTagsByAssignees(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getNewConversationsTagsByAssignees(filter, header, req);
  }

  @Get('new-conversations-by-date/me')
  @Auth()
  getMyNewConversations(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = [req.user.id];
    return this.messStatsService.getNewConversationsByDate(filter, header, req);
  }

  @Get('average-new-conversations')
  @Auth()
  getAverageNewConversations(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getAverageNewConversation(filter, header, req);
  }

  @Get('average-new-conversations/me')
  @Auth()
  getMyAverageNewConversations(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = [req.user.id];
    return this.messStatsService.getAverageNewConversation(filter, header, req);
  }

  @Get('average-replied-conversations')
  @Auth()
  getAverageRepliedConversations(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.messStatsService.getAverageRepliedConversations(filter, header, req);
  }

  @Get('average-replied-conversations/me')
  @Auth()
  getMyAverageRepliedConversations(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = [req.user.id];
    return this.messStatsService.getAverageRepliedConversations(filter, header, req);
  }

  @Get('close-rate')
  @Auth()
  async getCloseRate(
    @Query() filter: CloseRateStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.messStatsService.getCloseRate(filter, header, req);
  }

  @Get('close-rate/detail')
  @Auth()
  async getCloseRateDetail(
    @Query() filter: CloseRateStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.messStatsService.getCloseRateDetail(filter, header, req);
  }

  @Get('close-rate/me')
  @Auth()
  async getMyCloseRate(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = [req.user.id];
    return await this.messStatsService.getCloseRate(filter, header, req);
  }

  @Get('close-rate/detail/me')
  @Auth()
  async getMyCloseRateDetail(
    @Query() filter: ConversationStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    filter.assignedTo = [req.user.id];
    return await this.messStatsService.getCloseRateDetail(filter, header, req);
  }

  @Get('care-pages-stats')
  @Auth()
  async getCarePageStats(
    @Query() filter: CarePageStatisticsFilter,
    @Headers() headers: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.carePageStatsService.getCarePageStats(filter, headers, req);
  }

  @Get('care-reason-stats')
  @Auth()
  async getCareReasonStats(
    @Query() filter: CarePageStatisticsFilter,
    @Headers() headers: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return this.carePageStatsService.getCareReasonStats(filter, headers, req);
  }
}
