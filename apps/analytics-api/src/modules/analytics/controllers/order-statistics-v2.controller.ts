import { Controller, Get, Headers, Query, Req } from '@nestjs/common';
import { ApiExtension, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OrderStatisticsFilter } from 'apps/analytics-api/src/filters/order-statistics.filter';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { SalePermission } from 'core/enums/sale-permissions';
import { DashboardPermission } from 'core/enums/sale-permissions/dashboard-permission.enum';
import { OrderStatisticsV2Service } from '../services/order-statistics-v2.service';

@Controller('order-statistics/v2')
@ApiTags('Order Stats v2')
export class OrderStatisticsV2Controller {
  constructor(private statisticsService: OrderStatisticsV2Service) {}

  @Get('overview')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.orderShipmentOverview],
    [SalePermission.dashboard, DashboardPermission.orderShipmentCarrier],
    [SalePermission.dashboard, DashboardPermission.orderShipmentSaleReps],
  )
  @ApiOperation({
    summary: '<PERSON><PERSON> liệu <PERSON>ơn hàng',
    description: `Có thể dùng API này để lấy dữ liệu báo cáo Đơn hàng + Vận đơn. Bao gồm những báo cáo sau:
      \n
      - Tổng quan (Overview) 
      - Doanh số theo Dự án (Sales by Projects)
      - Đơn hàng theo Ngày (Lead by Dates)`,
  })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiExtension('x-foo', { test: 'bar ' })
  async getOverview(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getOverview(filter, header, req);
  }

  @Get('overview/products')
  @Auth(
    'sale',
    [SalePermission.dashboard, DashboardPermission.orderShipmentOverview],
    [SalePermission.dashboard, DashboardPermission.orderShipmentCarrier],
    [SalePermission.dashboard, DashboardPermission.orderShipmentSaleReps],
  )
  @ApiOperation({
    summary: 'Đơn hàng theo Sản phẩm (Order by Products)',
  })
  async getProductsOverview(
    @Query() filter: OrderStatisticsFilter,
    @Headers() header: Record<string, string>,
    @Req() req: Record<string, any>,
  ) {
    return await this.statisticsService.getProductsOverview(filter, header, req);
  }
}
