import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ACTUAL_REVENUE_STATUSES,
  EXPECTED_REVENUE_STATUSES,
  RETURNED_SALES_STATUSES,
  SALE_AND_REVENUE_STATUSES,
} from 'apps/analytics-api/src/constants/orders/order-statuses.constant';
import { OrderProduct } from 'apps/analytics-api/src/entities/order/order-product.entity';
import { Order } from 'apps/analytics-api/src/entities/order/order.entity';
import { OrderStatisticsFilter } from 'apps/analytics-api/src/filters/order-statistics.filter';
import { OrderSource } from 'apps/analytics-api/src/entities/order/order-source.entity';
import { orderConnection } from 'core/constants/database-connection.constant';
import { OrderStatus } from 'core/enums/order-status.enum';
import FilterUtils from 'core/utils/FilterUtils';
import { isEmpty, isNil, snakeCase } from 'lodash';
import { Brackets, getConnection, Repository, SelectQueryBuilder, TreeRepository } from 'typeorm';
import { Redis } from 'ioredis';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import AnalyticsUtils from 'apps/analytics-api/src/utils/utils';
import { DASHBOARD_CACHE_TYPE } from 'apps/analytics-api/src/constants/dashboard-cache.constant';

@Injectable()
export class OrderStatisticsV2Service {
  constructor(
    @InjectRepository(Order, orderConnection)
    private ordersRepo: Repository<Order>,
    @InjectRepository(OrderProduct, orderConnection)
    private orderProductsRepo: Repository<OrderProduct>,
    @InjectRepository(OrderSource, orderConnection)
    private externalSourceRepo: TreeRepository<OrderSource>,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async getOverview(
    filter: OrderStatisticsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';

    const qb = this.getOrdersQuery(filter, headers, request);
    if (!qb) return {};

    const params: Record<string, any> = {
      ...filter,
      timezone,
      companyId: request?.user?.companyId,
      countryId: filter.countryId ?? headers['country-ids'],
      projectIds: FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds),
      userIdRequest: request?.user?.id,
    };
    const paramsToMd5 = AnalyticsUtils.generateCacheKeyFromObject(params);
    const cacheData: string = await this.redis.get(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_SHIPMENT}-${paramsToMd5}`,
    );
    if (cacheData) {
      return JSON.parse(cacheData);
    }

    const countProductsQb = qb
      .clone()
      .innerJoin(OrderProduct, 'opd', 'o.id = opd.order_id')
      .select(['DISTINCT o.id::VARCHAR AS o_id', 'SUM(opd.quantity) AS quantity'])
      .groupBy('o_id');

    qb.leftJoin(
      '(' + countProductsQb.getQuery() + ')',
      'order_items',
      'order_items.o_id::INT = o.id AND o.status IN (:...saleAndRevenueStatuses)',
    )
      .select('COUNT (DISTINCT o.id)', 'count')
      .addSelect('SUM(order_items.quantity)', 'total_product_items')
      .addSelect(
        'SUM(CASE WHEN o.status IN (:...saleAndRevenueStatuses) THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'sale_and_revenue',
      )
      .addSelect(
        'SUM(CASE WHEN o.status = :newStatus THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'new_sales',
      )
      .addSelect(
        'SUM(CASE WHEN o.status IN (:...returnedSalesStatuses) THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'returned_sales',
      )
      .addSelect(
        'SUM(CASE WHEN o.status IN (:...expectedRevenueStatuses) THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'expected_revenue',
      )
      .addSelect(
        'SUM(CASE WHEN o.status IN (:...actualRevenueStatuses) THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'actual_revenue',
      )
      .addSelect(
        'SUM(CASE WHEN o.status = :canceledStatus THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'canceled_sales',
      )
      .setParameters({
        saleAndRevenueStatuses: SALE_AND_REVENUE_STATUSES,
        returnedSalesStatuses: RETURNED_SALES_STATUSES,
        expectedRevenueStatuses: EXPECTED_REVENUE_STATUSES,
        actualRevenueStatuses: ACTUAL_REVENUE_STATUSES,
        newStatus: OrderStatus.New,
        canceledStatus: OrderStatus.Canceled,
      });

    const { groupBy, dateRangeType } = filter;
    if (groupBy) {
      for (const item of groupBy) {
        if (item === 'date') {
          switch (dateRangeType) {
            case 'confirmationTime':
              qb.addSelect(`DATE(o.confirmed_at AT TIME ZONE '${timezone}')`, item);
              qb.addGroupBy(item);
              break;
            case 'creationTime':
              qb.addSelect(
                `DATE((COALESCE(l.created_at, o.confirmed_at)) AT TIME ZONE '${timezone}')`,
                item,
              );
              qb.addGroupBy(item);
              break;
            default:
              qb.addSelect(`DATE(l.created_at AT TIME ZONE '${timezone}')`, item);
              qb.addGroupBy(item);
              break;
          }
          continue;
        }
        if (item === 'carrier') {
          qb.innerJoin(
            'orders_carriers',
            'cr',
            'cr.order_id = o.id AND cr.carrier_code IS NOT NULL AND cr.waybill_number IS NOT NULL',
          )
            .addSelect(`cr.carrier_code`, item)
            .addGroupBy(item);
          continue;
        }
        if (item === 'userId') {
          qb.addSelect('COALESCE(o.care_page_id, o.sale_id)', item).addGroupBy(`"${item}"`);
          continue;
        }
        const snakeKey = `o.${snakeCase(item)}`;
        qb.addGroupBy(snakeKey);
        qb.addSelect(snakeKey, item);
      }
    }

    const data = await qb.getRawMany();
    for (const item of data) {
      if (!isNil(item.status)) {
        item.status = OrderStatus[item.status];
      }
      item.count = Number(item.count);
      item.average_order_value =
        item.sale_and_revenue && item.count ? item.sale_and_revenue / item.count : 0;
    }

    // cache lại data
    await this.redis.set(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_SHIPMENT}-${paramsToMd5}`,
      JSON.stringify(data),
      'EX',
      5 * 60,
    );

    return data;
  }

  async getProductsOverview(
    filter: OrderStatisticsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';

    const qb = this.getOrdersQuery(filter, headers, request);
    if (!qb) return {};

    const params: Record<string, any> = {
      ...filter,
      timezone,
      companyId: request?.user?.companyId,
      countryId: filter.countryId ?? headers['country-ids'],
      projectIds: FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds),
      userIdRequest: request?.user?.id,
    };
    const paramsToMd5 = AnalyticsUtils.generateCacheKeyFromObject(params);
    const cacheData: string = await this.redis.get(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_SHIPMENT_PRODUCT}-${paramsToMd5}`,
    );
    if (cacheData) {
      return JSON.parse(cacheData);
    }

    qb.innerJoin('order_products', 'p', 'p.order_id = o.id')
      .select('COUNT (DISTINCT o.id)', 'count')
      .addSelect('SUM(p.quantity)', 'quantity')
      .addSelect('p.product_id', 'productId')
      .groupBy('p.product_id');

    const { groupBy, dateRangeType } = filter;
    if (groupBy) {
      for (const item of groupBy) {
        if (item === 'date') {
          switch (dateRangeType) {
            case 'confirmationTime':
              qb.addSelect(`DATE(o.confirmed_at AT TIME ZONE '${timezone}')`, item);
              qb.addGroupBy(item);
              break;
            case 'creationTime':
              qb.addSelect(
                `DATE((COALESCE(l.created_at, o.confirmed_at)) AT TIME ZONE '${timezone}')`,
                item,
              );
              qb.addGroupBy(item);
              break;
            default:
              qb.addSelect(`DATE(l.created_at AT TIME ZONE '${timezone}')`, item);
              qb.addGroupBy(item);
              break;
          }
          continue;
        }
        if (item === 'carrier') {
          qb.innerJoin(
            'orders_carriers',
            'cr',
            'cr.order_id = o.id AND cr.carrier_code IS NOT NULL AND cr.waybill_number IS NOT NULL',
          )
            .addSelect(`cr.carrier_code`, item)
            .addGroupBy(item);
          continue;
        }
        if (item === 'userId') {
          qb.addSelect('COALESCE(o.care_page_id, o.sale_id)', item).addGroupBy(`"${item}"`);
          continue;
        }
        const snakeKey = `o.${snakeCase(item)}`;
        qb.addGroupBy(snakeKey);
        qb.addSelect(snakeKey, item);
      }
    }

    const data = await qb.getRawMany();
    for (const item of data) {
      if (!isNil(item.status)) item.status = OrderStatus[item.status];
      item.count = Number(item.count);
    }

    // cache lại data
    await this.redis.set(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_SHIPMENT_PRODUCT}-${paramsToMd5}`,
      JSON.stringify(data),
      'EX',
      5 * 60,
    );

    return data;
  }

  getOrdersQuery(
    filter: OrderStatisticsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): SelectQueryBuilder<Order> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const countryId = filter.countryId ?? headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);

    const {
      dateRangeType,
      from,
      to,
      marketerIds,
      productIds,
      tagIds,
      sources,
      carriers,
      userId,
      userIds,
      status,
      excludesStatus,
    } = filter;

    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return;

    const qb = this.ordersRepo
      .createQueryBuilder('o')
      .andWhere('o.country_id = :countryId')
      .andWhere('o.company_id = :companyId')
      .setParameters({ countryId, companyId });

    if (dateRangeType && (from || to)) {
      let change: OrderStatus;
      switch (dateRangeType) {
        case 'confirmationTime': {
          qb.andWhere('o.confirmed_at IS NOT NULL');
          if (from) qb.andWhere('o.confirmed_at >= :from', { from });
          if (to) qb.andWhere('o.confirmed_at <= :to', { to });
          break;
        }
        case 'creationTime':
          qb.leftJoin(
            'order_status_histories',
            'l',
            `l.order_id = o.id AND l.status = '${OrderStatus.New}' AND (l.before_status IS NULL OR l.before_status < ${OrderStatus.New})`,
          );
          if (from)
            qb.andWhere(
              new Brackets(sqb => {
                sqb
                  .where('(l.created_at IS NOT NULL AND l.created_at >= :from)')
                  .orWhere(
                    '(l.created_at IS NULL AND o.confirmed_at IS NOT NULL AND o.confirmed_at >= :from)',
                  );
              }),
              { from },
            );
          if (to)
            qb.andWhere(
              new Brackets(sqb => {
                sqb
                  .where('(l.created_at IS NOT NULL AND l.created_at <= :to)')
                  .orWhere(
                    '(l.created_at IS NULL AND o.confirmed_at IS NOT NULL AND o.confirmed_at <= :to)',
                  );
              }),
              { to },
            );
          break;
        case 'handoverTime':
        default:
          change = OrderStatus.HandlingOver;
          break;
      }
      if (!isNil(change)) {
        qb.innerJoin(
          'order_status_histories',
          'l',
          `l.order_id = o.id AND l.status = :change`,
        ).setParameters({ change });

        if (from) qb.andWhere('l.created_at >= :from', { from });
        if (to) qb.andWhere('l.created_at <= :to', { to });
      }
    }
    if (!isEmpty(userIds) || userId) {
      qb.andWhere('(o.care_page_id IN (:...userIds) OR o.sale_id IN (:...userIds))', {
        userIds: !isEmpty(userIds) ? userIds : [userId],
      });
    }
    if (projectIds) qb.andWhere('o.project_id IN (:...projectIds)', { projectIds });
    if (marketerIds)
      qb.andWhere(
        new Brackets(qb => {
          qb.where('o.marketer_id IN (:...marketerIds)', { marketerIds });
          if (marketerIds.includes(-1)) qb.orWhere('o.marketer_id IS NULL');
        }),
      );
    if (!isEmpty(productIds)) {
      const subQb = this.orderProductsRepo
        .createQueryBuilder('od')
        .select('od.order_id')
        .where('od.product_id IN (:...productIds)', { productIds });
      qb.andWhere(`o.id IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
    }
    if (!isEmpty(tagIds)) {
      const subQb = this.ordersRepo
        .createQueryBuilder('o')
        .innerJoin('order_tags', 't', 't.order_id = o.id')
        .select('t.order_id')
        .where('t.tag_id IN (:...tagIds)', { tagIds });
      qb.andWhere(`o.id IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
    }
    if (sources) {
      const keyPairs = sources.map(source => {
        const [sourceType, sourceId] = source.split(',');
        return `('${sourceType}', '${sourceId}')`;
      });
      const extSourcesQb = this.externalSourceRepo
        .createQueryBuilder('es')
        .where(`(es.entity, entity_id) IN (${keyPairs.join(', ')})`)
        .select(['es.id']);

      const dbConnection = getConnection(orderConnection);
      const ancestorIdQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where(`osc.id_descendant IN (${extSourcesQb.getQuery()})`)
        .select(['osc.id_ancestor']);
      const descendantsQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where(`osc.id_ancestor IN (${ancestorIdQb.getQuery()})`)
        .select('osc.id_descendant');
      qb.andWhere(`o.externalSourceId IN (${descendantsQb.getQuery()})`);
    }
    if (!isEmpty(carriers)) {
      qb.innerJoin(
        'orders_carriers',
        'c',
        'c.order_id = o.id AND c.carrier_code IN (:...carriers)',
        { carriers },
      );
    }
    if (status) qb.andWhere('o.status IN (:...status)', { status });
    if (excludesStatus) qb.andWhere('o.status NOT IN (:...excludesStatus)', { excludesStatus });
    return qb;
  }
}
