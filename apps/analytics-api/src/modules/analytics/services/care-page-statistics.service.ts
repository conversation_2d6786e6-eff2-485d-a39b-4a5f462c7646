import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  DELIVERED_STATUSES,
  RETURN_STATUSES,
  REVENUE_WITHOUT_RETURNED_STATUSES,
} from 'apps/analytics-api/src/constants/orders/order-statuses.constant';
import { ConversationOrder } from 'apps/analytics-api/src/entities/message/conversation-order.entity';
import { Conversation } from 'apps/analytics-api/src/entities/message/conversation.entity';
import { FanPage } from 'apps/analytics-api/src/entities/message/fanpage.entity';
import { ScopedUserCareItem } from 'apps/analytics-api/src/entities/message/scoped-user-care-item.entity';
import { ScopedUserCare } from 'apps/analytics-api/src/entities/message/scoped-user-care.entity';
import { Order } from 'apps/analytics-api/src/entities/order/order.entity';
import { CarePageStatisticsFilter } from 'apps/analytics-api/src/filters/care-page-statistics.filter';
import { PageScopedUserCareItem } from 'apps/analytics-api/src/entities/message/page-scoped-user-care-item.entity';
import { messageConnection, orderConnection } from 'core/constants/database-connection.constant';
import { OrderStatus } from 'core/enums/order-status.enum';
import { isEmpty } from 'lodash';
import { Repository, getConnection } from 'typeorm';

@Injectable()
export class CarePageStatisticsService {
  constructor(
    @InjectRepository(Conversation, messageConnection)
    private conversationsRepo: Repository<Conversation>,
    @InjectRepository(ConversationOrder, messageConnection)
    private conversationOrdersRepo: Repository<ConversationOrder>,
    @InjectRepository(FanPage, messageConnection)
    private pagesRepo: Repository<FanPage>,
    @InjectRepository(Order, orderConnection)
    private ordersRepo: Repository<Order>,
    @InjectRepository(PageScopedUserCareItem, messageConnection)
    private suCareItemsRepo: Repository<PageScopedUserCareItem>,
  ) {}

  async getCarePageStats(
    filter: CarePageStatisticsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const countryId = headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);

    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';
    const { from, to, userId, userIds, groupBy, projectIds, pageIds, productIds } = filter;

    const sqb = this.conversationsRepo
      .createQueryBuilder('c')
      .innerJoin('c.page', 'p')
      .innerJoin('scoped_user_cares', 'cares', 'c.scoped_user_id = cares.scoped_user_id')
      .innerJoin('scoped_user_care_items', 'ci', 'cares.id = ci.care_id')
      .leftJoin(
        'conversation_orders',
        'co',
        `c.scoped_user_id = co.fb_scoped_user_id 
        AND co.creator_id = cares.care_page_id 
        AND co.confirmed_at IS NOT NULL 
        AND DATE("co"."confirmed_at" AT TIME ZONE '${timezone}') = DATE("ci"."created_at" AT TIME ZONE '${timezone}')
        AND DATE("co"."created_at" AT TIME ZONE '${timezone}') = DATE("ci"."created_at" AT TIME ZONE '${timezone}')`,
      )
      .select('c.scoped_user_id', 'su_id')
      .addSelect(`DATE ( ci.created_at AT TIME ZONE '${timezone}' )`, 'session_date')
      .addSelect(`DATE ( c.created_at AT TIME ZONE '${timezone}' )`, 'c_date')
      .addSelect(`COUNT (DISTINCT cares.id )`, 'care_sessions')
      .addSelect(`co.order_id`, 'confirmed_order_id')

      .where('p.country_id = :countryId')
      .andWhere('p.company_id = :companyId')
      .andWhere(`c.feed_id = ''`)
      .andWhere('c.created_at IS NOT NULL')
      .groupBy('su_id')
      .addGroupBy('session_date')
      .addGroupBy('c_date')
      .addGroupBy('confirmed_order_id')
      .orderBy('session_date', 'ASC')
      .setParameters({
        countryId,
        companyId,
      });

    if (groupBy?.includes('userId'))
      sqb.addSelect('cares.care_page_id', 'cp_id').addGroupBy('cp_id');
    if (from) sqb.andWhere('ci.created_at >= :from', { from });
    if (to) sqb.andWhere('ci.created_at <= :to', { to });
    if (userId) sqb.andWhere('cares.care_page_id = :userId').setParameters({ userId });
    if (userIds) sqb.andWhere('cares.care_page_id IN (:...userIds)').setParameters({ userIds });
    if (pageIds) sqb.andWhere('p.id IN (:...pageIds)', { pageIds });
    if (projectIds) sqb.andWhere('p.projectId IN (:...projectIds)', { projectIds });
    if (productIds) sqb.andWhere('p.productId IN (:...productIds)', { productIds });

    const connection = getConnection(messageConnection);
    const qb = connection
      .createQueryBuilder()
      .addSelect(
        'COUNT ( CASE WHEN ( sub.c_date = sub.session_date AND sub.care_sessions = 1 ) THEN sub.* END )',
        'new_replied',
      )
      .addSelect(
        'COUNT ( CASE WHEN ( sub.c_date = sub.session_date AND sub.care_sessions > 1 ) THEN sub.* END )',
        'revoked_replied',
      )
      .addSelect(
        'COUNT ( CASE WHEN ( sub.c_date <> sub.session_date ) THEN sub.* END )',
        'old_replied',
      )
      .addSelect(
        'COUNT ( DISTINCT CASE WHEN ( sub.c_date = sub.session_date AND sub.care_sessions = 1 ) THEN sub.confirmed_order_id END )',
        'new_confirmed_orders',
      )
      .addSelect(
        'COUNT ( CASE WHEN ( sub.c_date = sub.session_date AND sub.care_sessions > 1 ) THEN sub.confirmed_order_id END )',
        'revoked_confirmed_orders',
      )
      .addSelect(
        'COUNT ( CASE WHEN ( sub.c_date <> sub.session_date ) THEN sub.confirmed_order_id END )',
        'old_confirmed_orders',
      )
      .addSelect(`COUNT (DISTINCT sub.confirmed_order_id)`, 'confirmed_orders')
      .addSelect(
        'SUM (COALESCE(con_orders.total_price, 0) + COALESCE(con_orders.surcharge, 0) + COALESCE(con_orders.shipping_fee, 0) - COALESCE(con_orders.discount, 0))',
        'revenue',
      )
      .addSelect(
        'SUM (CASE WHEN con_orders.status IN (:...revenueWithoutReturnedStatuses) THEN COALESCE(con_orders.total_price, 0) + COALESCE(con_orders.surcharge, 0) + COALESCE(con_orders.shipping_fee, 0) - COALESCE(con_orders.discount, 0) ELSE 0 END)',
        'revenue_without_returned',
      )
      .addSelect(
        `SUM(CASE WHEN con_orders.status = ${OrderStatus.DeliveredCompleted} THEN COALESCE(con_orders.total_price, 0) + COALESCE(con_orders.surcharge, 0) + COALESCE(con_orders.shipping_fee, 0) - COALESCE(con_orders.discount, 0) ELSE 0 END)`,
        'delivered_revenue',
      )
      .addSelect(
        'COUNT(DISTINCT CASE WHEN con_orders.status IN (:...deliveredStatuses) THEN con_orders.orderId END)',
        'total_delivered_orders',
      )
      .addSelect(
        'COUNT(DISTINCT CASE WHEN con_orders.status IN (:...returnedStatuses) THEN con_orders.orderId END)',
        'total_returned_orders',
      )
      .addSelect(
        'COUNT(DISTINCT CASE WHEN con_orders.status IN (:...cancelStatuses) THEN con_orders.orderId END)',
        'total_canceled_orders',
      )
      .from(`(${sqb.getQuery()})`, 'sub')
      .leftJoin(ConversationOrder, 'con_orders', 'con_orders.orderId = sub.confirmed_order_id')
      .setParameters(sqb.getParameters())
      .setParameters({
        revenueWithoutReturnedStatuses: REVENUE_WITHOUT_RETURNED_STATUSES,
        returnedStatuses: RETURN_STATUSES,
        deliveredStatuses: DELIVERED_STATUSES,
        cancelStatuses: [OrderStatus.Canceled],
      })

    if (groupBy) {
      for (const item of groupBy) {
        switch (item) {
          case 'date': {
            qb.addSelect('sub.session_date', 'date').addGroupBy('sub.session_date');
            break;
          }
          case 'userId': {
            qb.addSelect('sub.cp_id', 'userId').addGroupBy('sub.cp_id');
            break;
          }
          default:
            break;
        }
      }
    }

    return await qb.getRawMany();
  }

  async getCareReasonStats(
    filter: CarePageStatisticsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const countryId = headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);

    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';
    const { from, to, userId, userIds, groupBy, projectIds, pageIds, productIds } = filter;

    const reasonKeys = [
      'unread',
      'seen',
      'price_factor',
      'legit_factor',
      'effect_factor',
      '3pl_factor',
      'awaiting_cofirmation',
      'confirmed',
      'mind_changing_or_need_changing',
      'no_money',
      'product_does_not_meet_expectation',
      'purchase_else_where',
      'joking',
    ];

    const latestCareItemSubQb = this.suCareItemsRepo
      .createQueryBuilder('ci')
      .innerJoin('conversations', 'c', `c.scoped_user_id = ci.scoped_user_id`)
      .innerJoin('c.page', 'p')
      .select('MAX(ci.id)', 'id')
      .addSelect('c.scopedUserId', 'scoped_user_id')
      .where('p.country_id = :countryId')
      .andWhere('p.company_id = :companyId')
      .andWhere(`c.feed_id = ''`)
      .andWhere('c.created_at IS NOT NULL')
      .groupBy('c.scopedUserId');

    const qb = this.conversationsRepo
      .createQueryBuilder('c')
      .innerJoin('c.page', 'p')
      .where('p.country_id = :countryId')
      .andWhere('p.company_id = :companyId')
      .andWhere(`c.feed_id = ''`)
      .andWhere('c.created_at IS NOT NULL')
      .setParameters({
        countryId,
        companyId,
      });

    if (from) {
      latestCareItemSubQb.andWhere('c.created_at >= :from', { from });
      qb.andWhere('c.created_at >= :from', { from });
    }
    if (to) {
      latestCareItemSubQb.andWhere('c.created_at <= :to', { to });
      qb.andWhere('c.created_at <= :to', { to });
    }
    if (pageIds) {
      latestCareItemSubQb.andWhere('p.id IN (:...pageIds)', { pageIds });
      qb.andWhere('p.id IN (:...pageIds)', { pageIds });
    }
    if (projectIds) {
      latestCareItemSubQb.andWhere('p.projectId IN (:...projectIds)', { projectIds });
      qb.andWhere('p.projectId IN (:...projectIds)', { projectIds });
    }
    if (productIds) {
      latestCareItemSubQb.andWhere('p.productId IN (:...productIds)', { productIds });
      qb.andWhere('p.productId IN (:...productIds)', { productIds });
    }
    if (!isEmpty(userIds) || userId) {
      latestCareItemSubQb.innerJoin(
        'scoped_users',
        'su',
        'su.id = c.scoped_user_id AND su.careUserId IN (:...userIds)',
        { userIds: !isEmpty(userIds) ? userIds : [userId] },
      );
      qb.innerJoin('c.user', 'su', 'su.careUserId IN (:...userIds)', {
        userIds: !isEmpty(userIds) ? userIds : [userId],
      });
    }

    qb.leftJoin(
      `(${latestCareItemSubQb.getQuery()})`,
      'latest_ci',
      'c.scoped_user_id = latest_ci.scoped_user_id',
    )
      .leftJoin(ScopedUserCareItem, 'ci', 'latest_ci.id = ci.id')
      .leftJoin('ci.reason', 're');

    qb.select('COUNT (DISTINCT c.scoped_user_id)', 'total_data');
    qb.addSelect(
      'COUNT (DISTINCT CASE WHEN ci.id IS NOT NULL THEN c.scoped_user_id END)',
      'replies_data',
    );

    for (const reasonKey of reasonKeys) {
      qb.addSelect(
        `COUNT (DISTINCT CASE WHEN re.key = '${reasonKey}' THEN c.scoped_user_id END)`,
        reasonKey,
      );
    }

    if (groupBy) {
      for (const item of groupBy) {
        switch (item) {
          case 'date': {
            qb.addSelect(`DATE ( c.created_at AT TIME ZONE '${timezone}' )`, 'date').addGroupBy(
              'date',
            );
            break;
          }
          // case 'userId': {
          //   qb.addSelect('sub.cp_id', 'userId').addGroupBy('sub.cp_id');
          //   break;
          // }
          default:
            break;
        }
      }
    }
    return qb.getRawMany();
  }
}
