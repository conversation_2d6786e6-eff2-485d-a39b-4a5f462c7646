import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  DASHBOARD_CACHE_TYPE,
  PREFIX_DASHBOARD_CACHE,
  SET_SAVE_QUERY_PARAMS,
} from 'apps/analytics-api/src/constants/dashboard-cache.constant';
import { CONFIRMED_STATES } from 'apps/analytics-api/src/constants/orders/lead-states.constant';
import {
  ACTUAL_REVENUE_STATUSES,
  CACULATE_REVENUE_STATUSES,
  DELIVERED_STATUSES,
  RETURN_STATUSES,
  RETURNED_SALES_STATUSES,
} from 'apps/analytics-api/src/constants/orders/order-statuses.constant';
import { CareReason } from 'apps/analytics-api/src/entities/order/care-reason.entity';
import { LeadCareItem } from 'apps/analytics-api/src/entities/order/lead-care-item.entity';
import { LeadCare } from 'apps/analytics-api/src/entities/order/lead-care.entity';
import { Lead } from 'apps/analytics-api/src/entities/order/lead.entity';
import { OrderProduct } from 'apps/analytics-api/src/entities/order/order-product.entity';
import { OrderSource } from 'apps/analytics-api/src/entities/order/order-source.entity';
import { CareState } from 'apps/analytics-api/src/enums/orders/care-state.enum';
import {
  LeadPerformanceStatisticsFilter,
  LeadStatisticFilter,
} from 'apps/analytics-api/src/filters/lead-statistics-v2.filter';
import { OverviewTelesalesQueryParams } from 'apps/analytics-api/src/types/dashboard.type';
import AnalyticsUtils from 'apps/analytics-api/src/utils/utils';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';
import { Order } from 'apps/order-api/src/entities/order.entity';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { orderConnection } from 'core/constants/database-connection.constant';
import { OrderStatus } from 'core/enums/order-status.enum';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import FilterUtils from 'core/utils/FilterUtils';
import { Redis } from 'ioredis';
import { isEmpty, isNil, snakeCase } from 'lodash';
import { Brackets, Repository, SelectQueryBuilder, getConnection } from 'typeorm';

@Injectable()
export class LeadStatisticsV2Service {
  constructor(
    @InjectRepository(Lead, orderConnection)
    private leadsRepo: Repository<Lead>,
    @InjectRepository(LeadCare, orderConnection)
    private leadCaresRepo: Repository<LeadCare>,
    @InjectRepository(LeadCareItem, orderConnection)
    private leadCareItemsRepo: Repository<LeadCareItem>,
    @InjectRepository(OrderProduct, orderConnection)
    private orderProductsRepo: Repository<OrderProduct>,
    @InjectRepository(OrderSource, orderConnection)
    private externalSourceRepo: Repository<OrderSource>,
    @InjectRepository(Order, orderConnection)
    private ordersRepo: Repository<Order>,
    @InjectRedis() private readonly redis: Redis,
    private redisCache: RedisCacheService,
  ) {}

  joinLatestCare(qb: SelectQueryBuilder<Lead>) {
    qb.innerJoin('lead_cares', 'latest_care', 'latest_care.id = l.last_care_id');
  }

  getLeadsQueryBuilder(
    filter: LeadStatisticFilter = {},
    headers: Record<string, string>,
    request: Record<string, any>,
  ): SelectQueryBuilder<Lead> | undefined {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const countryId = filter.countryId ?? headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);

    let joinedLatestCare = false;
    let joinedCurrentCare = false;

    const {
      dateRangeType,
      from,
      to,
      marketerIds,
      productIds,
      userId,
      userIds,
      state,
      excludesState,
      sources,
      carriers,
      collectType,
      isProcessed,
      groupBy,
    } = filter;

    const projectIds = FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds);
    if (projectIds?.length === 0) return;

    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')
      .andWhere(`o.company_id = ${companyId}`)
      .andWhere(`o.country_id = ${countryId}`)
      .setParameters({
        companyId,
        countryId,
      });

    if (dateRangeType && (from || to)) {
      switch (dateRangeType) {
        case 'processingTime': {
          if (!joinedLatestCare) {
            this.joinLatestCare(qb);
            joinedLatestCare = true;
          }
          if (from) qb.andWhere('latest_care.created_at >= :from', { from });
          if (to) qb.andWhere('latest_care.created_at <= :to', { to });
          break;
        }
        case 'creationTime':
        default: {
          if (from) qb.andWhere('l.created_at >= :from', { from });
          if (to) qb.andWhere('l.created_at <= :to', { to });
          break;
        }
      }
    }
    if (!isEmpty(userIds) || userId) {
      if (!joinedLatestCare) {
        this.joinLatestCare(qb);
        joinedLatestCare = true;
      }
      qb.andWhere('latest_care.userId IN (:...userIds)', {
        userIds: !isEmpty(userIds) ? userIds : [userId],
      });
    }
    if (projectIds) qb.andWhere(`o.project_id IN (${projectIds.join(',')})`);
    if (marketerIds)
      qb.andWhere(
        new Brackets(qb => {
          qb.where('o.marketer_id IN (:...marketerIds)', { marketerIds });
          if (marketerIds.includes(-1)) qb.orWhere('order.marketer_id IS NULL');
        }),
      );
    if (productIds) {
      const subQb = this.orderProductsRepo
        .createQueryBuilder('od')
        .select('od.order_id')
        .where('od.product_id IN (:...productIds)', { productIds });
      qb.andWhere(`o.id IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
    }
    if (sources) {
      const keyPairs = sources.map(source => {
        const [sourceType, sourceId] = source.split(',');
        return `('${sourceType}', '${sourceId}')`;
      });
      const extSourcesQb = this.externalSourceRepo
        .createQueryBuilder('es')
        .where(`(es.entity, entity_id) IN (${keyPairs.join(', ')})`)
        .select(['es.id']);

      const dbConnection = getConnection(orderConnection);
      const ancestorIdQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where(`osc.id_descendant IN (${extSourcesQb.getQuery()})`)
        .select(['osc.id_ancestor']);
      const descendantsQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where(`osc.id_ancestor IN (${ancestorIdQb.getQuery()})`)
        .select('osc.id_descendant');
      qb.andWhere(`o.externalSourceId IN (${descendantsQb.getQuery()})`);
    }
    if (!isEmpty(carriers)) {
      qb.innerJoin(
        'orders_carriers',
        'c',
        'c.order_id = o.id AND c.carrier_code IN (:...carriers)',
        { carriers },
      );
    }
    if (state) qb.andWhere(`l.state IN (${state.join(',')})`);
    if (excludesState) qb.andWhere(`l.state NOT IN (${excludesState.join(',')})`);
    if (!isEmpty(collectType)) {
      qb.andWhere(
        new Brackets(sqb => {
          for (const type of collectType) {
            switch (type) {
              case 'convertFromFacebook':
                sqb.orWhere('o.crossCare = TRUE');
                break;
              case 'captureForm':
                sqb.orWhere('l.form_captured_at IS NOT NULL');
                break;
              case 'manualKeying':
              default:
                sqb.orWhere('l.form_captured_at IS NULL');
                break;
            }
          }
        }),
      );
    }
    if (!isNil(isProcessed)) {
      if (isProcessed) qb.andWhere('l.last_care_item_id IS NOT NULL');
      else qb.andWhere('l.last_care_item_id IS NULL');
    }
    if (groupBy?.includes('userId')) {
      if (!joinedLatestCare) {
        this.joinLatestCare(qb);
        joinedLatestCare = true;
      }
    }
    if (groupBy?.includes('currentUserId')) {
      if (!joinedCurrentCare) {
        qb.leftJoin('lead_cares', 'current_care', 'l.current_care_id = current_care.id');
        joinedCurrentCare = true;
      }
    }
    return qb;
  }

  async getOverview(
    filter: LeadStatisticFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';

    const qb = this.getLeadsQueryBuilder(filter, headers, request);
    if (!qb) return [];
    const params: Record<string, any> = {
      ...filter,
      timezone,
      companyId: request?.user?.companyId,
      countryId: filter.countryId ?? headers['country-ids'],
      projectIds: FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds),
      userIdRequest: request?.user?.id,
    };
    const paramsToMd5 = AnalyticsUtils.generateCacheKeyFromObject(params);
    const cacheData: string = await this.redis.get(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_TELESALES}-${paramsToMd5}`,
    );
    if (cacheData) {
      return JSON.parse(cacheData);
    }

    const countProductsQb = qb
      .clone()
      .andWhere('l.state IN (:...confirmedStates)')
      .innerJoin(OrderProduct, 'opd', 'o.id = opd.order_id')
      .select(['DISTINCT o.id AS o_id', 'SUM(opd.quantity) AS quantity'])
      .groupBy('o_id');

    qb.leftJoin('(' + countProductsQb.getQuery() + ')', 'order_items', 'order_items.o_id = o.id')
      .select('COUNT (l.id)', 'total_leads')
      .addSelect(
        'COUNT (CASE WHEN l.state IN (:...confirmedStates) THEN l.id END)',
        'confirmed_leads',
      )
      .addSelect('COUNT (CASE WHEN l.state = :canceledState THEN l.id END)', 'canceled_leads')
      .addSelect(
        'COUNT (CASE WHEN o.status IN (:...deliveredStatus) THEN o.id END)',
        'delivered_orders',
      )
      .addSelect(
        'COUNT (CASE WHEN o.status IN (:...returnedStatus) THEN o.id END)',
        'returned_orders',
      )
      .addSelect(
        'SUM (CASE WHEN l.state IN (:...confirmedStates) THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'sale_and_revenue',
      )
      .addSelect('SUM(order_items.quantity)', 'total_product_items')
      .addSelect(
        'COUNT (DISTINCT CASE WHEN l.last_care_item_id IS NOT NULL THEN l.id END)',
        'processed_leads',
      )
      .setParameters({
        confirmedStates: CONFIRMED_STATES,
        canceledState: CareState.lost,
        deliveredStatus: DELIVERED_STATUSES,
        returnedStatus: RETURNED_SALES_STATUSES,
      });

    const { groupBy, dateRangeType } = filter;
    if (groupBy) {
      for (const item of groupBy) {
        switch (item) {
          case 'date': {
            if (item === 'date') {
              switch (dateRangeType) {
                case 'processingTime':
                  qb.addSelect(`DATE(latest_care.created_at AT TIME ZONE '${timezone}')`, item);
                  qb.addGroupBy(item);
                  break;
                case 'creationTime':
                default:
                  qb.addSelect(`DATE(l.created_at AT TIME ZONE '${timezone}')`, item);
                  qb.addGroupBy(item);
                  break;
              }
              continue;
            }
            break;
          }
          case 'projectId': {
            const snakeKey = `o.${snakeCase(item)}`;
            qb.addGroupBy(snakeKey);
            qb.addSelect(snakeKey, item);
            break;
          }
          case 'userId': {
            qb.andWhere('latest_care.user_id IS NOT NULL')
              .addSelect(`latest_care.user_id`, item)
              .addGroupBy(`latest_care.user_id`);
            break;
          }
          case 'currentUserId': {
            qb.andWhere('current_care.user_id IS NOT NULL')
              .addSelect(`current_care.user_id`, item)
              .addGroupBy(`current_care.user_id`);
            break;
          }
          default: {
            const snakeKey = `l.${snakeCase(item)}`;
            qb.andWhere(`${snakeKey} IS NOT NULL`)
              .addGroupBy(snakeKey)
              .addSelect(snakeKey, item);
            break;
          }
        }
      }
    }

    const data = await qb.getRawMany();

    for (const item of data) {
      if (!isNil(item.state)) {
        item.state = CareState[item.state];
      }
      item.total_leads = Number(item.total_leads);
      item.processed_leads = Number(item.processed_leads);
      item.confirmed_leads = Number(item.confirmed_leads);
      item.canceled_leads = Number(item.canceled_leads);
      item.delivered_orders = Number(item.delivered_orders);
      item.returned_orders = Number(item.returned_orders);
      if (item.total_product_items && item.confirmed_leads)
        item.combo_rate = item.total_product_items / item.confirmed_leads;
      if (item.sale_and_revenue && item.confirmed_leads)
        item.aov = item.sale_and_revenue / item.confirmed_leads;
    }

    // cache lại data
    await this.redis.set(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_TELESALES}-${paramsToMd5}`,
      JSON.stringify(data),
      'EX',
      5 * 60,
    );

    return data;
  }

  async getProductsOverview(
    filter: LeadStatisticFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';

    const { groupBy, dateRangeType, productIds } = filter;

    const qb = this.getLeadsQueryBuilder(filter, headers, request);
    if (!qb) return [];

    qb.innerJoin(
      'order_products',
      'p',
      `p.order_id = o.id ${!isEmpty(productIds) ? 'AND p.product_id IN (:...productIds)' : ''}`,
    )
      .select('COUNT (DISTINCT l.id)', 'total_leads')
      .addSelect(
        'COUNT (DISTINCT CASE WHEN l.state IN (:...confirmedStates) THEN l.id END)',
        'confirmed_leads',
      )
      .addSelect(
        'COUNT (DISTINCT CASE WHEN l.state = :canceledState THEN l.id END)',
        'canceled_leads',
      )
      .addSelect(
        'COUNT (DISTINCT CASE WHEN o.status IN (:...deliveredStatus) THEN o.id END)',
        'delivered_orders',
      )
      .addSelect(
        'COUNT (DISTINCT CASE WHEN o.status IN (:...returnedStatus) THEN o.id END)',
        'returned_orders',
      )
      .addSelect(
        'SUM(CASE WHEN l.state IN (:...confirmedStates) THEN COALESCE(o.total_price, 0) + COALESCE(o.surcharge, 0) + COALESCE(o.shipping_fee, 0) - COALESCE(o.discount, 0) ELSE 0 END)',
        'sale_and_revenue',
      )
      .addSelect('SUM(p.quantity)', 'quantity')
      .addSelect(
        'COUNT (DISTINCT CASE WHEN l.last_care_item_id IS NOT NULL THEN l.id END)',
        'processed_leads',
      )
      .addSelect('p.product_id')
      .groupBy('p.product_id')
      .setParameters({
        confirmedStates: CONFIRMED_STATES,
        canceledState: CareState.lost,
        deliveredStatus: DELIVERED_STATUSES,
        returnedStatus: RETURNED_SALES_STATUSES,
      });

    if (groupBy) {
      for (const item of groupBy) {
        switch (item) {
          case 'date': {
            if (item === 'date') {
              switch (dateRangeType) {
                case 'processingTime':
                  qb.addSelect(`DATE(latest_care.created_at AT TIME ZONE '${timezone}')`, item);
                  qb.addGroupBy(item);
                  break;
                case 'creationTime':
                default:
                  qb.addSelect(`DATE(l.created_at AT TIME ZONE '${timezone}')`, item);
                  qb.addGroupBy(item);
                  break;
              }
              continue;
            }
            break;
          }
          case 'projectId': {
            const snakeKey = `o.${snakeCase(item)}`;
            qb.addGroupBy(snakeKey);
            qb.addSelect(snakeKey, item);
            break;
          }
          case 'userId': {
            qb.andWhere('latest_care.user_id IS NOT NULL')
              .addSelect(`latest_care.user_id`, item)
              .addGroupBy(`latest_care.user_id`);
            break;
          }
          case 'currentUserId': {
            qb.andWhere('current_care.user_id IS NOT NULL')
              .addSelect(`current_care.user_id`, item)
              .addGroupBy(`current_care.user_id`);
            break;
          }
          default: {
            const snakeKey = `l.${snakeCase(item)}`;
            qb.andWhere(`${snakeKey} IS NOT NULL`)
              .addGroupBy(snakeKey)
              .addSelect(snakeKey, item);
            break;
          }
        }
      }
    }

    const data = await qb.getRawMany();

    for (const item of data) {
      if (!isNil(item.state)) {
        item.state = CareState[item.state];
      }
      item.total_leads = Number(item.total_leads);
      item.processed_leads = Number(item.processed_leads);
      item.confirmed_leads = Number(item.confirmed_leads);
      item.canceled_leads = Number(item.canceled_leads);
      item.delivered_orders = Number(item.delivered_orders);
      item.returned_orders = Number(item.returned_orders);
    }
    return data;
  }

  async getReasonsReport(
    filter: LeadStatisticFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';

    filter.isProcessed = true;
    const qb = this.getLeadsQueryBuilder(filter, headers, request);
    if (!qb) return [];

    const params: Record<string, any> = {
      ...filter,
      timezone,
      companyId: request?.user?.companyId,
      countryId: filter.countryId ?? headers['country-ids'],
      projectIds: FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds),
      userIdRequest: request?.user?.id,
    };
    const paramsToMd5 = AnalyticsUtils.generateCacheKeyFromObject(params);

    const cacheData: string = await this.redis.get(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_CARE_REASONS_TELESALES}-${paramsToMd5}`,
    );
    if (cacheData) {
      return JSON.parse(cacheData);
    }

    const { groupBy, dateRangeType } = filter;

    qb.innerJoin(LeadCareItem, 'latest_care_item', 'latest_care_item.id = l.last_care_item_id')
      .innerJoin(CareReason, 're', 'latest_care_item.reason_id = re.id')
      .select('re.reason_key')
      .groupBy('reason_key')
      .addSelect(`COUNT (DISTINCT l.id)`, 'total_leads');

    if (groupBy) {
      for (const item of groupBy) {
        switch (item) {
          case 'date': {
            if (item === 'date') {
              switch (dateRangeType) {
                case 'processingTime':
                  qb.addSelect(`DATE(latest_care.created_at AT TIME ZONE '${timezone}')`, item);
                  qb.addGroupBy(item);
                  break;
                case 'creationTime':
                default:
                  qb.addSelect(`DATE(l.created_at AT TIME ZONE '${timezone}')`, item);
                  qb.addGroupBy(item);
                  break;
              }
              continue;
            }
            break;
          }
          case 'projectId': {
            const snakeKey = `o.${snakeCase(item)}`;
            qb.addGroupBy(snakeKey);
            qb.addSelect(snakeKey, item);
            break;
          }
          case 'userId': {
            qb.andWhere('latest_care.user_id IS NOT NULL')
              .addSelect(`latest_care.user_id`, item)
              .addGroupBy(`latest_care.user_id`);
            break;
          }
          case 'currentUserId': {
            qb.andWhere('current_care.user_id IS NOT NULL')
              .addSelect(`current_care.user_id`, item)
              .addGroupBy(`current_care.user_id`);
            break;
          }
          default: {
            const snakeKey = `l.${snakeCase(item)}`;
            qb.andWhere(`${snakeKey} IS NOT NULL`)
              .addGroupBy(snakeKey)
              .addSelect(snakeKey, item);
            break;
          }
        }
      }
    }

    const data = await qb.getRawMany();

    for (const item of data) {
      if (!isNil(item.state)) item.state = CareState[item.state];
      item.total_leads = Number(item.total_leads);
    }

    await this.redis.set(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_CARE_REASONS_TELESALES}-${paramsToMd5}`,
      JSON.stringify(data),
      'EX',
      5 * 60,
    );

    return data;
  }

  async getPerformance(
    filter: LeadPerformanceStatisticsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';

    const userIds: number[] = [];
    if (!isEmpty(filter.userIds)) {
      userIds.push(...filter.userIds);
      delete filter.userIds;
    }
    if (filter.userId) {
      userIds.push(filter.userId);
      delete filter.userId;
    }

    delete filter.dateRangeType;
    filter.isProcessed = true;
    const sqb = this.getLeadsQueryBuilder(filter, headers, request)
      .select('l.id', 'l_id')
      .addSelect(`DATE ( l.created_at AT TIME ZONE '${timezone}' )`, 'date')
      .groupBy('l_id')
      .addGroupBy('date')
      .setParameters({ from: filter.from, to: filter.to });

    const params: Record<string, any> = {
      ...filter,
      timezone,
      userIds,
      companyId: request?.user?.companyId,
      countryId: filter.countryId ?? headers['country-ids'],
      projectIds: FilterUtils.getProjectIds(headers['project-ids'], filter.projectIds),
      userIdRequest: request?.user?.id,
    };
    const paramsToMd5 = AnalyticsUtils.generateCacheKeyFromObject(params);
    // const cacheData: string = await this.redis.get(
    //   `${DASHBOARD_CACHE_TYPE.OVERVIEW_PERFORMANCE_TELESALES}-${paramsToMd5}`,
    // );
    // if (cacheData) {
    //   return JSON.parse(cacheData);
    // }

    const { groupBy, field } = filter;

    switch (field) {
      case 'assigned_count': {
        sqb
          .innerJoin(
            'lead_cares',
            'lead_care',
            'lead_care.lead_id = l.id AND lead_care.created_at >= :from AND lead_care.created_at <= :to',
          )
          .addSelect(`DATE ( "lead_care"."created_at" AT TIME ZONE '${timezone}' )`, 'process_date')
          .addGroupBy('"process_date"')
          .andWhere('l.state != :junkState', { junkState: CareState.junk });
        break;
      }
      case 'processed_count': {
        sqb
          .innerJoin(
            LeadCareItem,
            'ci',
            'l.id = ci.lead_id AND ci.created_at >= :from AND ci.created_at <= :to',
          )
          .leftJoin(
            LeadCare,
            'lead_care',
            'lead_care.lead_id = l.id AND lead_care.created_at >= :from AND lead_care.created_at <= :to',
          )
          .addSelect(`DATE ( "lead_care"."created_at" AT TIME ZONE '${timezone}' )`, 'assign_date')
          .addGroupBy('"assign_date"')
          .addSelect(`DATE ( ci.created_at AT TIME ZONE '${timezone}' )`, 'process_date')
          .addGroupBy('"process_date"')
          .andWhere('l.state != :junkState', { junkState: CareState.junk });          
        break;
      }
      case 'reasoned_count':
      default:
        sqb
          .innerJoin(
            LeadCareItem,
            'ci',
            'l.id = ci.lead_id AND ci.created_at >= :from AND ci.created_at <= :to',
          )
          .innerJoin(CareReason, 'cr', `ci.reason_id = cr.id`)
          .addSelect(`DATE ( ci.created_at AT TIME ZONE '${timezone}' )`, 'process_date')
          .addSelect(
            `CASE WHEN cr.state IN (${CONFIRMED_STATES.join(',')}) THEN o.id END`,
            'confirmed_order_id',
          )
          .addSelect(
            `CASE WHEN o.status IN (:...returnedStatus) AND cr.id IS NOT NULL AND o.sale_id=ci.creator_id AND o.confirmed_at >= DATE_TRUNC('day', ci.created_at) AT TIME ZONE 'UTC' AND o.confirmed_at <= (DATE_TRUNC('day', ci.created_at) AT TIME ZONE 'UTC') + INTERVAL '1 day' - INTERVAL '1 second' THEN o.id END`,
            'returned_order_id',
          )
          .addSelect(
            `CASE WHEN o.status IN (:...deliveredStatus) AND cr.id IS NOT NULL AND o.sale_id=ci.creator_id AND o.confirmed_at >= DATE_TRUNC('day', ci.created_at) AT TIME ZONE 'UTC' AND o.confirmed_at <= (DATE_TRUNC('day', ci.created_at) AT TIME ZONE 'UTC') + INTERVAL '1 day' - INTERVAL '1 second' THEN o.id END`,
            'delivered_order_id',
          )
          .addGroupBy('process_date')
          .addGroupBy(`"confirmed_order_id"`)
          .addGroupBy(`"returned_order_id"`)
          .addGroupBy(`"delivered_order_id"`)
          .setParameters({
            returnedStatus: RETURNED_SALES_STATUSES,
            deliveredStatus: ACTUAL_REVENUE_STATUSES,
          });
        break;
    }
    sqb.andWhere('l.state != :junkState', { junkState: CareState.junk });          

    const user: AuthUser = request.user;
    for (const profile of user.profiles) {
      const [dataAccessLevel, moduleInCharge] = profile;
      if (
        dataAccessLevel === DataAccessLevel.personal &&
        moduleInCharge?.includes(ModuleInCharge.telesale)
      ) {
        sqb.unScope('o');
        switch (field) {
          case 'assigned_count':
            sqb.andWhere('lead_care.user_id = :uid', { uid: user.id });
            break;
          case 'processed_count':
          case 'reasoned_count':
          default:
            sqb.andWhere('ci.creator_id = :uid', { uid: user.id });
            break;
        }
      }
    }

    if (!isEmpty(userIds)) {
      switch (field) {
        case 'assigned_count':
          sqb.andWhere('lead_care.user_id IN (:...userIds)', { userIds });
          break;
        case 'processed_count':
        case 'reasoned_count':
        default:
          sqb.andWhere('ci.creator_id IN (:...userIds)', { userIds });
          break;
      }
    }
    if (groupBy?.includes('userId')) {
      switch (field) {
        case 'assigned_count':
          sqb.addSelect('lead_care.user_id', 'u_id').addGroupBy('u_id');
          break;
        case 'processed_count':
        case 'reasoned_count':
        default:
          sqb.addSelect('ci.creator_id', 'u_id').addGroupBy('u_id');
          break;
      }
    }


    const qb = getConnection(orderConnection)
      .createQueryBuilder()
      .from(`(${sqb.getQuery()})`, 'sub')
      // .leftJoin('orders', 'orders', 'orders.id = sub.confirmed_order_id')
      // .unScope('orders')
      .setParameters(sqb.getParameters());

      
    switch (field) {
      case 'assigned_count': {
        qb.select(
          'COUNT ( DISTINCT CASE WHEN sub.date = sub.process_date THEN sub.l_id END )',
          'new_assigned',
        ).addSelect(
          'COUNT ( DISTINCT CASE WHEN sub.date <> sub.process_date THEN sub.l_id END )',
          'old_assigned',
        );
        break;
      }
      case 'processed_count': {
        qb.select(
          'COUNT ( DISTINCT CASE WHEN sub.date = sub.process_date THEN sub.l_id END )',
          'new_processed',
        )
          .addSelect(
            'COUNT ( DISTINCT CASE WHEN sub.date <> sub.process_date THEN sub.l_id END )',
            'old_processed',
          )
          .addSelect(
            'COUNT ( DISTINCT CASE WHEN sub.date <> sub.process_date AND sub.process_date = sub.assign_date THEN sub.l_id END )',
            'old_processed_with_assign',
          );
        break;
      }
      case 'reasoned_count':
      default:
        qb.select(
          'COUNT ( DISTINCT CASE WHEN sub.date = sub.process_date THEN (sub.l_id) END )',
          'new_processed',
        )
          .addSelect(
            'COUNT ( DISTINCT CASE WHEN sub.date = sub.process_date THEN (sub.confirmed_order_id) END )',
            'new_confirmed',
          )
          .addSelect(
            'COUNT ( DISTINCT CASE WHEN sub.date <> sub.process_date THEN (sub.l_id) END )',
            'old_processed',
          )
          .addSelect(
            'COUNT ( DISTINCT CASE WHEN sub.date <> sub.process_date THEN (sub.confirmed_order_id) END )',
            'old_confirmed',
          )
          .addSelect(
            'SUM (COALESCE(orders.total_price, 0) + COALESCE(orders.surcharge, 0) + COALESCE(orders.shipping_fee, 0) - COALESCE(orders.discount, 0))',
            'sales_revenue',
          )
          .addSelect(
            `SUM (CASE WHEN orders.status IN (:...caculateRevenueStatus) THEN COALESCE(orders.total_price, 0) + COALESCE(orders.surcharge, 0) + COALESCE(orders.shipping_fee, 0) - COALESCE(orders.discount, 0) ELSE 0 END)`,
            'revenue',
          )
          .addSelect('COUNT ( DISTINCT sub.returned_order_id )', 'returned_orders')
          .addSelect('COUNT ( DISTINCT sub.delivered_order_id )', 'delivered_orders')
          .setParameter('caculateRevenueStatus', CACULATE_REVENUE_STATUSES)
          .leftJoin('orders', 'orders', 'orders.id = sub.confirmed_order_id')
          .unScope('orders');
        break;
    }
    if (groupBy) {
      for (const item of groupBy) {
        switch (item) {
          case 'date': {
            qb.addSelect('sub.process_date', 'date').addGroupBy('sub.process_date');
            break;
          }
          case 'userId': {
            qb.addSelect('sub.u_id', 'userId').addGroupBy('sub.u_id');
            break;
          }
          default:
            break;
        }
      }
    }

    const data = await qb.getRawMany();

    for (const item of data) {
      if (item.new_assigned) item.new_assigned = Number(item.new_assigned);
      if (item.old_assigned) item.old_assigned = Number(item.old_assigned);
      if (item.new_processed) item.new_processed = Number(item.new_processed);
      if (item.new_confirmed) item.new_confirmed = Number(item.new_confirmed);
      if (item.old_processed) item.old_processed = Number(item.old_processed);
      if (item.old_confirmed) item.old_confirmed = Number(item.old_confirmed);
      if (item.delivered_orders) item.delivered_orders = Number(item.delivered_orders);
      if (item.returned_orders) item.returned_orders = Number(item.returned_orders);
      if (item.old_processed_with_assign)
        item.old_processed_with_assign = Number(item.old_processed_with_assign);
      if (item.new_processed && item.new_confirmed)
        item.new_leads_conversation_rate = item.new_confirmed / item.new_processed;
      if (item.old_processed && item.old_confirmed)
        item.old_leads_conversation_rate = item.old_confirmed / item.old_processed;
    }

    // cache lại data
    await this.redis.set(
      `${DASHBOARD_CACHE_TYPE.OVERVIEW_PERFORMANCE_TELESALES}-${paramsToMd5}`,
      JSON.stringify(data),
      'EX',
      5 * 60,
    );

    return data;
  }
}
