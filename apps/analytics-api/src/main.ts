import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { TransformInterceptor } from 'core/interceptors/transfrom.interceptor';
import { AppModule } from './app.module';
import 'core/extensions';
import { Logger } from 'core/interceptors/logger.interceptors';
import * as basicAuth from 'express-basic-auth';
import { router } from 'bull-board';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors({
    origin: [/https?:\/\/.*$/],
    credentials: true,
    allowedHeaders: '*',
  });
  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));
  app.useGlobalInterceptors(
    new TransformInterceptor(),
    process.env.SEQ_KEY ? new Logger('identity-api') : undefined,
  );

  const options = new DocumentBuilder()
    .setTitle('Analytics')
    .setDescription('Analytics backend writing in Node.js')
    .setVersion('0.1.0')
    .addServer(process.env.BASE_API_URL || '')
    .addBearerAuth({
      description: `Please enter token in following format: Bearer <JWT>`,
      name: 'Authorization',
      bearerFormat: 'Bearer',
      scheme: 'Bearer',
      type: 'http',
      in: 'Header',
    })
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('swagger', app, document);
  app.use(
    '/queues',
    basicAuth({
      users: { admin: process.env.BULL_PASSWORD },
      challenge: true,
    }),
    (req, res, next) => {
      req.proxyUrl = (process.env.API_SUBPATH || '') + '/queues';
      next();
    },
    router,
  );

  try {
    await app.listen(process.env.WEBSITES_PORT || process.env.PORT || 3000);
  } catch (e) {
    console.log('e', e);
    await app.close();
  }
}

bootstrap();
