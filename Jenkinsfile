pipeline {
    agent any
    
    environment {
        // Core configuration
        SERVICE_NAME = extractServiceName()
        SERVICE_BASE_NAME = getServiceBaseName(SERVICE_NAME)
        DOCKER_IMAGE = "agbiz/swarm-${SERVICE_NAME}"
        //DOCKER_TAG = "${env.BUILD_NUMBER}"
        DOCKER_TAG = "${BRANCH_NAME}-${env.GIT_COMMIT[0..7]}-${env.BUILD_NUMBER}"
        DEPLOY_BRANCH_NAME = "update-image-${SERVICE_NAME}-${BUILD_NUMBER}"
        DOCKER_FILE = getDockerfile(SERVICE_NAME)
        
        // Repository settings
        DEPLOYMENT_REPO = '**************:a7923/go-svc/swarm2k8s.git'
        DEPLOYMENT_REPO_ID = '64978021'
        // branchName == 'production' ? 'k8s-manifests-production' : 'k8s-manifests-staging'}/${SERVICE_NAME}/deployment.yaml
        DEPLOYMENT_FILE = "${BRANCH_NAME == 'production' ? 'k8s-manifests-production' : 'k8s-manifests-staging'}/${SERVICE_NAME}/kustomization.yaml"
        
        
        ARGOCD_CONFIG = getArgoConfig(BRANCH_NAME)
        ARGOCD_SERVER = "${ARGOCD_CONFIG.server}"
        ARGOCD_APP = "${ARGOCD_CONFIG.app}"
        ARGOCD_CREDENTIALS = "${ARGOCD_CONFIG.credentials}"
        
        AUTO_MERGE = 'true'
    }

    stages {
        stage('Build and Push Docker Image') {
            steps {
                withCredentials([usernamePassword(credentialsId: 'docker-hub-credentials', 
                               usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
                    // Login to Docker Hub
                    sh '''
                        echo "${DOCKER_PASSWORD}" | docker login -u "${DOCKER_USERNAME}" --password-stdin
                    '''
                    
                    // Build and push image
                    sh """
                        docker build -f ${DOCKER_FILE} \
                            --build-arg SERVICE_BASE_NAME=${SERVICE_BASE_NAME} \
                            -t ${DOCKER_IMAGE}:${DOCKER_TAG} .
                        docker push ${DOCKER_IMAGE}:${DOCKER_TAG}
                    """
                }
            }
        }

        stage('Update Deployment') {
            steps {
                // Set a timeout for the entire stage to prevent hanging
                timeout(time: 1, unit: 'MINUTES') {
                    sshagent(credentials: ['gitlab-ssh-key']) {
                        script {
                            try {
                                // Clone and prepare repository with retry logic
                                retry(3) {
                                    sh """
                                        echo "Setting up SSH configuration..."
                                        mkdir -p ~/.ssh
                                        chmod 700 ~/.ssh
                                        ssh-keyscan -t rsa gitlab.com >> ~/.ssh/known_hosts 2>/dev/null || { echo "Failed to update known_hosts"; exit 1; }
                                        chmod 600 ~/.ssh/known_hosts
                                        
                                        echo "Testing SSH connection..."
                                        ssh -vT ************** || echo "SSH connection test failed, proceeding anyway..."

                                        echo "Removing existing directory..."
                                        rm -rf ${SERVICE_NAME}

                                        echo "Cloning repository..."
                                        git clone --depth 1 --verbose ${DEPLOYMENT_REPO} ${SERVICE_NAME}
                                    """
                                }

                                // Change to the cloned repository directory
                                dir(SERVICE_NAME) {
                                    // Verify deployment file exists
                                    def deploymentFile = "${DEPLOYMENT_FILE}"
                                    if (!fileExists(deploymentFile)) {
                                        error "Deployment file not found: ${deploymentFile}"
                                    }

                                    // Update deployment file and push changes
                                    retry(3) {
                                        sh """
                                            echo "Updating deployment file..."
                                            git checkout -b ${DEPLOY_BRANCH_NAME} || git checkout ${DEPLOY_BRANCH_NAME}
                                            sed -i "s/newTag: .*/newTag: '${env.DOCKER_TAG}'/" ${deploymentFile}
                                            git add ${deploymentFile}
                                            git commit -m 'Update ${SERVICE_NAME} image to ${DOCKER_TAG}' || echo "No changes to commit"
                                            git push -uf origin ${DEPLOY_BRANCH_NAME}
                                        """
                                    }
                                }
                            } catch (Exception e) {
                                // Log the error and fail the build
                                echo "Error in Update Deployment stage: ${e.message}"
                                error "Failed to update deployment: ${e.message}"
                            } finally {
                                // Clean up SSH agent
                                // sh """
                                //     echo "Cleaning up SSH environment..."
                                //     ssh-agent -k || echo "SSH agent cleanup failed, continuing..."
                                // """
                            }
                        }
                    }
                }
            }
        }

        stage('Create and Process Merge Request') {
            steps {
                withCredentials([usernamePassword(credentialsId: 'gitlab-credentials', 
                            usernameVariable: 'USERNAME', passwordVariable: 'GITLAB_TOKEN')]) {
                    script {
                        // Create merge request
                        def mrId = createMergeRequest(GITLAB_TOKEN)
                        echo "Created merge request #${mrId}"
                        
                        if (env.AUTO_MERGE == 'true') {
                            // Wait for merge request to be ready with timeout
                            def startTime = System.currentTimeMillis()
                            def timeoutMillis = 60000 // 30 seconds
                            def isReady = false
                            
                            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                                def mrStatus = sh(
                                    script: """
                                        curl --silent --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" \
                                            https://gitlab.com/api/v4/projects/${env.DEPLOYMENT_REPO_ID}/merge_requests/${mrId} \
                                            | jq -r '.merge_status'
                                    """,
                                    returnStdout: true
                                ).trim()
                                
                                if (mrStatus == 'can_be_merged') {
                                    isReady = true
                                    mergeMergeRequest(GITLAB_TOKEN, mrId)
                                    echo "Merge request #${mrId} successfully merged"
                                    break
                                }
                                
                                sleep(5) // Wait 5 seconds before next check
                            }
                            
                            if (!isReady) {
                                error "Timeout: Merge request #${mrId} was not ready to merge within 30 seconds"
                            }
                        }
                    }
                }
            }
        }

        stage('Sync ArgoCD') {
            steps {
                withCredentials([usernamePassword(credentialsId: "${ARGOCD_CREDENTIALS}", 
                               usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                    sh """
                        argocd login ${ARGOCD_SERVER} \
                            --username ${USERNAME} \
                            --password ${PASSWORD} \
                            --grpc-web
                        argocd app sync ${ARGOCD_APP} \
                            --resource apps:Deployment:${SERVICE_NAME}-app \
                            --grpc-web
                    """
                }
            }
        }
    }
}

// Helper functions
def extractServiceName() {
    def matcher = env.JOB_URL =~ /\/swarm-services\/job\/([^\/]+)\//
    if (!matcher) {
        error "Failed to extract service name from URL: ${env.JOB_URL}"
    }
    return matcher[0][1]
}

def createMergeRequest(token) {
    def response = sh(
        script: """
            curl --silent --header "PRIVATE-TOKEN: ${token}" \
                --data "remove_source_branch=true&source_branch=${env.DEPLOY_BRANCH_NAME}&target_branch=main&title=Update ${env.SERVICE_NAME} image to ${DOCKER_TAG}" \
                https://gitlab.com/api/v4/projects/${env.DEPLOYMENT_REPO_ID}/merge_requests \
                | jq -r '.iid'
        """,
        returnStdout: true
    ).trim()
    return response
}

def mergeMergeRequest(token, mrId) {
    sh """
        curl --silent --header "PRIVATE-TOKEN: ${token}" \
            --request PUT \
            https://gitlab.com/api/v4/projects/${env.DEPLOYMENT_REPO_ID}/merge_requests/${mrId}/merge
    """
}

def getDockerfile(serviceName) {
    return 'Dockerfile.new'
}

def getArgoConfig(branchName) {
    def config = [:]
    if (branchName == 'production') {
        config.server = 'argocd.agbiz.tech'
        config.app = 'production-app'
        config.credentials = 'argo_cd_admin_prod'
        return config
    }
    config.server = 'argo-stg.agbiz.vn'
    config.app = 'staging-app'
    config.credentials = 'argo_cd_admin'
    return config
}

def getServiceBaseName(serviceName) {
    if (serviceName.startsWith('facebook-bot')) {
        return "facebook-bot" // f*k
    }

    if (serviceName.startsWith('secret')) {
        return "facebook-bot" // f*k
    }
    
    if (serviceName.endsWith('-consumer')) {
        def baseServiceName = serviceName.replaceAll('-consumer$', '')
        return "${baseServiceName}-api"
    }
    return serviceName
}