stages:
  - core
  - build
variables:
  token: $token
  stgtoken: $stgtoken
  K8S_TOKEN: ${K8S_TOKEN}
#build_core:
#  stage: core
#  only:
#    refs:
#      - develop
#    changes:
#      - Dockerfile.builder
#      - Dockerfile.production
#      - package.json
#      - yarn.lock
#      - patches/*
#  tags:
#    - dev
#  script:
#    - sh bin/docker-push-server-api-core-dev.sh
build_server_identity_prod:
  stage: build
  only:
    refs:
      - main
    changes:
      - apps/identity-api/**/*
      - core/**/*
      - Dockerfile.identity-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh identity-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pnm_api/redeploy"'
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/identity-2_identity-consumer/redeploy"'
build_server_identity_prod_v2:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/identity-api/**/*
      - core/**/*
      - Dockerfile.identity-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh identity-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_identity-v2/redeploy"'
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/identity-2_identity-consumer/redeploy"'
build_server_order_prod:
  stage: build
  only:
    refs:
      - main
    changes:
      - apps/order-api/**/*
      - core/**/*
      - Dockerfile.order-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh order-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pnm_order-api/redeploy"'
build_server_order_prod_v2:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/order-api/**/*
      - core/**/*
      - Dockerfile.order-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh order-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_order-consumer/redeploy"'
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_order-v2/redeploy"'
build_server_catalog_prod:
  stage: build
  only:
    refs:
      - main
    changes:
      - apps/catalog-api/**/*
      - core/**/*
      - Dockerfile.catalog-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh catalog-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pnm_catalog-api/redeploy"'
build_server_catalog_prod_v2:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/catalog-api/**/*
      - core/**/*
      - Dockerfile.catalog-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh catalog-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_catalog-v2/redeploy"'
# build_server_crawl_prod:
#   stage: build
#   only:
#     refs:
#       - main
#     changes:
#       - apps/crawler/**/*
#       - core/**/*
#       - Dockerfile.crawler
#   tags:
#     - dev
#   script:
#     - sh bin/docker-push-server-api.sh crawler
#     - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/crawler/redeploy"'
build_server_facebook_bot_prod:
  stage: build
  only:
    refs:
      - main
    changes:
      - apps/facebook-bot/**/*
      - core/**/*
      - Dockerfile.facebook-bot
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh facebook-bot
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pnm_facebook-bot/redeploy"'
build_server_facebook_bot_prod_v2:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/facebook-bot/**/*
      - core/**/*
      - Dockerfile.facebook-bot
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh facebook-bot
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_facebook-bot-v2/redeploy"'
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_facebook-bot-consumer/redeploy"'
build_server_identity_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/identity-api/**/*
      - core/**/*
      - Dockerfile.identity-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh identity-api
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/pnm-api'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/pnm_api/redeploy"'
build_server_ffm_order_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/ffm-order-api/**/*
      - core/**/*
      - Dockerfile.ffm-order-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh ffm-order-api
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/ffm-order-api'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/ffm-order-api/redeploy"'
build_server_ffm_order_prod:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/ffm-order-api/**/*
      - core/**/*
      - Dockerfile.ffm-order-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh ffm-order-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_ffm-order-api-consumer/redeploy"'
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_ffm-order-api-v2/redeploy"'
build_server_order_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/order-api/**/*
      - core/**/*
      - Dockerfile.order-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh order-api
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/pnm-order-api'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/pnm_order-api/redeploy"'
build_server_catalog_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/catalog-api/**/*
      - core/**/*
      - Dockerfile.catalog-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh catalog-api
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/pnm-catalog-api'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/pnm_catalog-api/redeploy"'
build_server_ffm_catalog_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/ffm-catalog-api/**/*
      - core/**/*
      - Dockerfile.ffm-catalog-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh ffm-catalog-api
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/ffm-catalog-api'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/ffm-catalog-api/redeploy"'
build_server_ffm_catalog_prod:
  stage: build
  only:
    refs:
      - main
    changes:
      - apps/ffm-catalog-api/**/*
      - core/**/*
      - Dockerfile.ffm-catalog-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh ffm-catalog-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/ffm-catalog-api/redeploy"'
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/ffm-catalog-consumer/redeploy"'
build_server_ffm_catalog_prod_v2:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/ffm-catalog-api/**/*
      - core/**/*
      - Dockerfile.ffm-catalog-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh ffm-catalog-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_ffm-catalog-api-v2/redeploy"'
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_ffm-catalog-consumer/redeploy"'
build_server_facebook_bot_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/facebook-bot/**/*
      - core/**/*
      - Dockerfile.facebook-bot
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh facebook-bot
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/pnm-facebook-bot'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/pnm_facebook-bot/redeploy"'
build_server_analytics_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/analytics-api/**/*
      - core/**/*
      - Dockerfile.analytics-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh analytics-api
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/analytics-api'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/analytics-api/redeploy"'
build_server_analytics_prod:
  stage: build
  only:
    refs:
      - main
    changes:
      - apps/analytics-api/**/*
      - core/**/*
      - Dockerfile.analytics-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh analytics-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/analytics-api/redeploy"'
build_server_analytics_prod_v2:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/analytics-api/**/*
      - core/**/*
      - Dockerfile.analytics-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh analytics-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/pilot_analytics-api-v2/redeploy"'
build_server_nlp_dev:
  stage: build
  only:
    refs:
      - develop
    changes:
      - apps/nlp-api/**/*
      - core/**/*
      - Dockerfile.nlp-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api-dev.sh nlp-api
    - >-
      curl --location --request PATCH 'https://**************:6443/apis/apps/v1/namespaces/staging/deployments/nlp-api'
      --header "Authorization: Bearer $K8S_TOKEN"
      --header 'Content-Type: application/strategic-merge-patch+json'
      --data "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date -u +%FT%TZ)\"}}}}}"
      -k
    #- 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $stgtoken" "https://swarm-staging.agbiz.vn/api/services/nlp-api/redeploy"'
build_server_nlp_prod:
  stage: build
  only:
    refs:
      - pilots/v2.0
    changes:
      - apps/nlp-api/**/*
      - core/**/*
      - Dockerfile.nlp-api
  tags:
    - dev
  script:
    - sh bin/docker-push-server-api.sh nlp-api
    - 'curl -X POST --header "Content-Type: application/json" --header "Accept: application/json" --header "authorization: Bearer $token" "https://swarm.agbiz.vn/api/services/nlp-api/redeploy"'
